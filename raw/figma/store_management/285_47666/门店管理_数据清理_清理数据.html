<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">数据清理</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="09001800310 w-[361px] left-[16px] top-[458px] absolute text-[#fe3939] text-xs font-normal font-['PingFang SC'] leading-[18px]">门店创建三个月内，可由门店所有者发起数据清理。<br/>可发起数据清理的时间为每天09:00~18:00。<br/>可清理营业数据、库存数据、会员数据、存酒数据，其中营业数据为必须清理。<br/>可清理门店创建时间到当前时间的数据。<br/>每天可发起3次数据清理，发起成功后的10分钟内不可再次发起。<br/>数据清理需主收银机在线，否则将会导致门店数据错乱</div>
  <div className="Frame3465220 w-[361px] pl-6 pr-2.5 py-6 left-[16px] top-[114px] absolute bg-white rounded-[10px] justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465283 w-[313px] justify-center items-start gap-[227px] flex">
      <div className=" text-right text-black text-base font-medium font-['PingFang SC']">营业数据</div>
      <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px] flex-col justify-start items-start flex" />
    </div>
  </div>
  <div className="Frame3465221 w-[361px] pl-6 pr-2.5 py-6 left-[16px] top-[200px] absolute bg-white rounded-[10px] justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465283 w-[313px] justify-center items-start gap-[227px] flex">
      <div className=" text-right text-black text-base font-medium font-['PingFang SC']">库存数据</div>
      <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#f0f0f0] rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465222 w-[361px] pl-6 pr-2.5 py-6 left-[16px] top-[286px] absolute bg-white rounded-[10px] justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465283 w-[313px] justify-center items-start gap-[227px] flex">
      <div className=" text-right text-black text-base font-medium font-['PingFang SC']">会员数据</div>
      <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#f0f0f0] rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465223 w-[361px] pl-6 pr-2.5 py-6 left-[16px] top-[372px] absolute bg-white rounded-[10px] justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465283 w-[313px] justify-center items-start gap-[227px] flex">
      <div className=" text-right text-black text-base font-medium font-['PingFang SC']">存酒数据</div>
      <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#f0f0f0] rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465296 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">清理</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>