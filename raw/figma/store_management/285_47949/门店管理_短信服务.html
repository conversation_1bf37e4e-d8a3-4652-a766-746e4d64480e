<div className=" w-[393px] h-[1239px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">短信充值</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame2090051598 w-[361px] h-[185px] left-[16px] top-[114px] absolute bg-[#5855ff] rounded-[10px]">
    <div className=" left-[62px] top-[64px] absolute text-white/40 text-xs font-medium font-['PingFang SC']">短信剩余条数</div>
    <div className=" left-[221px] top-[64px] absolute text-center text-white/40 text-xs font-medium font-['PingFang SC']">本月已发送条数</div>
    <div className=" w-[49px] left-[74px] top-[89px] absolute text-center text-white text-lg font-semibold font-['MiSans W']">220</div>
    <div className=" w-[49px] left-[239px] top-[89px] absolute text-center text-white text-lg font-semibold font-['MiSans W']">7</div>
    <div className="Frame2090051599 w-12 h-12 left-[74px] top-[16px] absolute">
      <div className="Rectangle462286869 w-[32.73px] h-[27.27px] left-[7.64px] top-[10.36px] absolute rounded-md border-2 border-white/40" />
    </div>
    <div className="Frame2090051600 w-12 h-12 left-[239px] top-[16px] absolute">
      <img className="Subtract w-[35.45px] h-[30px] left-[6.27px] top-[9px] absolute" src="https://via.placeholder.com/35x30" />
    </div>
    <img className="Subtract w-[21px] h-4 left-[267px] top-[43px] absolute" src="https://via.placeholder.com/21x16" />
    <div className="Rectangle462286873 w-[361px] h-px left-0 top-[132px] absolute bg-white/10" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[325px] top-[149px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[20px] top-[150px] absolute text-white/40 text-xs font-normal font-['PingFang SC']">账户预警 </div>
    <div className="200 left-[177px] top-[150px] absolute text-white text-xs font-normal font-['PingFang SC']">剩余短信小于200条时提醒</div>
  </div>
  <div className="Frame2090051599 p-3.5 left-[16px] top-[315px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-3 inline-flex">
    <img className="Subtract w-[0px] h-[0px]" src="https://via.placeholder.com/0x0" />
    <div className=" text-black/30 text-[10px] font-medium font-['PingFang SC']">短信充值</div>
    <div className="Frame2090051608 self-stretch h-[184px] flex-col justify-start items-start gap-2.5 inline-flex">
      <div className="Frame2090051603 self-stretch justify-start items-start gap-2.5 inline-flex">
        <div className="Frame2090051601 pl-2.5 pr-[10.33px] py-[12.50px] bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">100000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-2xl font-medium font-['MiSans W']">6688</div>
              <div className="Frame2090051605 w-2.5 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="1000 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">1000</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
        <div className="Frame2090051602 pl-[10.66px] pr-[11.67px] py-[12.50px] bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">50000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-2xl font-medium font-['MiSans W']">3368</div>
              <div className="Frame2090051605 w-2.5 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="500 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">500</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
        <div className="Frame2090051603 pl-[16.84px] pr-[18.50px] py-[12.50px] bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">10000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-2xl font-medium font-['MiSans W']">688</div>
              <div className="Frame2090051605 w-2.5 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="100 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">100</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
      </div>
      <div className="Frame2090051604 self-stretch justify-start items-start gap-2.5 inline-flex">
        <div className="Frame2090051601 pl-[18px] pr-[18.33px] py-[12.50px] bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">5000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-2xl font-medium font-['MiSans W']">388</div>
              <div className="Frame2090051605 w-2.5 pb-0.5 flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="50 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">50</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
        <div className="Frame2090051602 pl-[15.66px] pr-[16.67px] py-3.5 opacity-0 bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">50000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-[5px] flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-xl font-medium font-['MiSans W']">3368</div>
              <div className="Frame2090051605 w-2.5 pb-[5px] flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="500 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">500</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
        <div className="Frame2090051603 pl-[20.84px] pr-[22.50px] py-3.5 opacity-0 bg-neutral-100 rounded-md justify-center items-center flex">
          <div className="Frame2090051607 self-stretch flex-col justify-start items-center gap-0.5 inline-flex">
            <div className=" self-stretch text-center text-black text-[10px] font-medium font-['MiSans W']">10000条</div>
            <div className="Frame2090051604 justify-start items-center gap-px inline-flex">
              <div className="Frame2090051606 w-2.5 h-6 pb-[5px] flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/0 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
              <div className=" text-[#5855ff] text-xl font-medium font-['MiSans W']">688</div>
              <div className="Frame2090051605 w-2.5 pb-[5px] flex-col justify-center items-center gap-2.5 inline-flex">
                <div className=" self-stretch h-6 text-black/40 text-[10px] font-medium font-['MiSans W']">元</div>
              </div>
            </div>
            <div className="100 self-stretch text-center"><span style="text-black/40 text-[10px] font-medium font-['MiSans W']">送 </span><span style="text-black text-[10px] font-medium font-['MiSans W']">100</span><span style="text-black/40 text-[10px] font-medium font-['MiSans W']"> 条</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div className="Frame2090051600 w-[361px] h-[630px] left-[15px] top-[569px] absolute bg-white rounded-[10px]">
    <img className="Subtract w-[0px] h-[0px] left-0 top-0 absolute" src="https://via.placeholder.com/0x0" />
    <div className=" left-[14px] top-[14px] absolute text-black/30 text-[10px] font-medium font-['PingFang SC']">充值记录</div>
    <div className="Frame3465228 w-16 h-[52px] p-1 left-[283px] top-[37px] absolute bg-black rounded justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-center items-center gap-1 flex">
        <div className=" text-white text-xs font-medium font-['PingFang SC']">查询</div>
      </div>
    </div>
    <div className="Frame3465237 pl-[9px] pr-[26px] py-3.5 left-[14px] top-[37px] absolute bg-neutral-100 rounded border border-neutral-100 justify-start items-center gap-2 inline-flex">
      <div className="Frame3465276 w-6 h-6 relative opacity-20 flex-col justify-start items-start flex">
        <div className="Rectangle3469207 w-0.5 h-1 bg-black rounded-[3px]" />
        <div className="Rectangle3469208 w-0.5 h-1 bg-black rounded-[3px]" />
        <div className="Rectangle3469206 w-2.5 h-[7px] rounded-[1px] border-2 border-black" />
        <div className="Rectangle3469209 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469210 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469211 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469212 w-0.5 h-0.5 bg-black rounded-[1px]" />
      </div>
      <div className="0806 text-black text-sm font-medium font-['PingFang SC']">2024-08-06</div>
      <div className="0806 text-black text-sm font-medium font-['PingFang SC']">2024-08-06</div>
    </div>
    <div className="Frame3465275 left-[162.50px] top-[20px] absolute" />
    <div className="Frame2090051610 h-[222px] left-[14px] top-[113px] absolute flex-col justify-start items-start gap-3 inline-flex">
      <div className=" text-[#fe3939] text-xl font-medium font-['PingFang SC']">待支付</div>
      <div className="Frame3465410 self-stretch h-[182px] flex-col justify-start items-start gap-1.5 flex">
        <div className="Frame3465406 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">订单编号:</div>
          <div className="Po117270772998170010 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">PO117270772998170010</div>
        </div>
        <div className="Frame3465415 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">充值日期:</div>
          <div className="0923154139 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">2024.09.23 15:41:39</div>
        </div>
        <div className="Frame3465416 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">充值条数:</div>
          <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">10000</div>
        </div>
        <div className="Frame3465407 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">赠送条数:</div>
          <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">100</div>
        </div>
        <div className="Frame3465408 w-[305px] justify-start items-start gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">支付金额(元):</div>
          <div className="00 grow shrink basis-0 text-[#5855ff] text-xs font-medium font-['PingFang SC']">688.00</div>
        </div>
        <div className="Frame3465413 justify-start items-start gap-2.5 inline-flex">
          <div className="Frame3465412 flex-col justify-start items-start gap-2.5 inline-flex">
            <div className="Frame3465409 w-[305px] justify-start items-center gap-3 inline-flex">
              <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">支付状态:</div>
              <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']"> 5</div>
            </div>
            <div className="Frame3465411 w-[305px] justify-start items-center gap-3 inline-flex">
              <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">是否赠送:</div>
              <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">否</div>
            </div>
          </div>
        </div>
        <div className="Frame3465410 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">备注:</div>
          <div className="10000100 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">买10000条送100</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051611 h-[222px] left-[14px] top-[384px] absolute flex-col justify-start items-start gap-3 inline-flex">
      <div className=" text-black/40 text-xl font-medium font-['PingFang SC']">已支付</div>
      <div className="Frame3465410 self-stretch h-[182px] flex-col justify-start items-start gap-1.5 flex">
        <div className="Frame3465406 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">订单编号:</div>
          <div className="Po117270772998170010 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">PO117270772998170010</div>
        </div>
        <div className="Frame3465415 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">充值日期:</div>
          <div className="0923154139 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">2024.09.23 15:41:39</div>
        </div>
        <div className="Frame3465416 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">充值条数:</div>
          <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">10000</div>
        </div>
        <div className="Frame3465407 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">赠送条数:</div>
          <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">100</div>
        </div>
        <div className="Frame3465408 w-[305px] justify-start items-start gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">支付金额(元):</div>
          <div className="00 grow shrink basis-0 text-[#5855ff] text-xs font-medium font-['PingFang SC']">688.00</div>
        </div>
        <div className="Frame3465413 justify-start items-start gap-2.5 inline-flex">
          <div className="Frame3465412 flex-col justify-start items-start gap-2.5 inline-flex">
            <div className="Frame3465409 w-[305px] justify-start items-center gap-3 inline-flex">
              <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">支付状态:</div>
              <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']"> 5</div>
            </div>
            <div className="Frame3465411 w-[305px] justify-start items-center gap-3 inline-flex">
              <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">是否赠送:</div>
              <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">否</div>
            </div>
          </div>
        </div>
        <div className="Frame3465410 w-[305px] justify-start items-center gap-3 inline-flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">备注:</div>
          <div className="10000100 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">买10000条送100</div>
        </div>
      </div>
    </div>
    <div className="Rectangle462286872 w-[333px] h-px left-[14px] top-[359px] absolute bg-black/5" />
  </div>
</div>