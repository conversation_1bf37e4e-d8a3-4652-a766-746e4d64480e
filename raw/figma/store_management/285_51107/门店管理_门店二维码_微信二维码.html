<div className=" w-[393px] h-[852px] relative bg-black">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">供应商绑定</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[140px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[5px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">打开小程序</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465400 w-[393px] h-[641px] left-0 top-[90px] absolute bg-[#819491]">
    <div className="Frame3465299 h-[249px] px-5 pt-5 pb-[13.03px] left-[88px] top-[87px] absolute bg-white rounded-[18px] flex-col justify-end items-center gap-[11px] inline-flex">
      <img className="12 w-[178px] h-[178px]" src="https://via.placeholder.com/178x178" />
      <div className="Frame14220 self-stretch justify-start items-center gap-[4.90px] inline-flex">
        <div className=" w-[26.97px] h-[26.97px] relative" />
        <div className=" text-black text-sm font-semibold font-['MiSans W']"> 打开微信「扫一扫」</div>
      </div>
    </div>
    <div className="Frame3465189 w-6 h-6 pr-[0.11px] left-[341px] top-[52px] absolute origin-top-left rotate-90 justify-center items-center inline-flex">
      <div className="Group3465008 w-[23.89px] h-6 relative">
        <img className="Subtract w-[19.94px] h-[22.11px] left-0 top-[1.89px] absolute" src="https://via.placeholder.com/20x22" />
        <img className="Subtract w-[19.95px] h-[22.11px] left-[23.89px] top-[22.11px] absolute origin-top-left rotate-180" src="https://via.placeholder.com/20x22" />
      </div>
    </div>
    <div className="Ktv left-[63px] top-[437px] absolute text-white text-[26px] font-semibold font-['PingFang SC']">桉树KTV</div>
    <div className=" left-[127px] top-[533px] absolute text-center text-white text-sm font-semibold font-['PingFang SC']">长按二维码保存到手机</div>
  </div>
</div>