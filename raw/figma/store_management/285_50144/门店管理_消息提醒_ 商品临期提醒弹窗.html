<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">消息提醒</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465397 w-[393px] p-2.5 left-0 top-[90px] absolute bg-[#ff8034]/10 justify-center items-center inline-flex">
    <div className="Frame2090051591 grow shrink basis-0 self-stretch justify-start items-center gap-1 inline-flex">
      <div className="Frame3465398 w-5 h-5 relative">
        <div className="Ellipse985 w-3 h-3 left-[4px] top-[4px] absolute bg-[#ff8034] rounded-full border" />
      </div>
      <div className="Frame2090051619 grow shrink basis-0 h-[17px] justify-start items-center gap-4 flex">
        <div className=" text-[#ff8034] text-xs font-medium font-['PingFang SC']">员工绑定微信后才可收到消息提醒</div>
        <div className=" text-[#3692ff] text-xs font-medium font-['PingFang SC']">去绑定</div>
      </div>
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[154px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">去设置</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">包厢高消费提醒金额</div>
    </div>
  </div>
  <div className="Frame3465398 w-[361px] h-20 left-[16px] top-[250px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">包厢高消费提醒</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
  </div>
  <div className="Frame3465400 w-[361px] h-20 left-[16px] top-[442px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">会员营销日报提醒</div>
    </div>
  </div>
  <div className="Frame3465414 w-[361px] h-20 left-[16px] top-[1018px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">收入提醒</div>
    </div>
  </div>
  <div className="Frame3465416 w-[361px] h-20 left-[16px] top-[1210px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">存酒过期提醒(商户)</div>
    </div>
  </div>
  <div className="Frame3465418 w-[361px] h-20 left-[16px] top-[1498px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute" />
    <div className="Frame3465279 left-[24px] top-[21px] absolute flex-col justify-center items-start inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">自动调拨异常提醒</div>
      <div className=" text-black/40 text-xs font-normal font-['PingFang SC']">调拨后未达到配置的库存数量微信通知</div>
    </div>
  </div>
  <div className="Frame3465415 w-[361px] h-20 left-[16px] top-[1114px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">每日业绩提醒</div>
    </div>
  </div>
  <div className="Frame3465419 w-[361px] h-20 left-[16px] top-[1402px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[21px] absolute flex-col justify-center items-start inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">商品临期提醒</div>
      <div className="20250113 text-black/40 text-xs font-normal font-['PingFang SC']">已授权-（到期时间：2025-01-13）</div>
    </div>
  </div>
  <div className="Frame3465417 w-[361px] h-20 left-[16px] top-[1306px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">存酒过期提醒(代订人)</div>
    </div>
  </div>
  <div className="Frame3465420 w-[361px] h-20 left-[16px] top-[1594px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">库存告警提醒</div>
    </div>
  </div>
  <div className="Frame3465399 w-[361px] h-20 left-[16px] top-[346px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">挂账归还提醒</div>
    </div>
  </div>
  <div className="Frame3465401 w-[361px] h-20 left-[16px] top-[538px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">未选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">到店会员生日提醒</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
  </div>
  <div className="Frame3465410 h-20 px-6 left-[16px] top-[634px] absolute bg-white rounded-[10px] border justify-center items-start gap-[119px] inline-flex">
    <div className="Frame3465356 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">代订客户生日提醒</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[5px] pr-[23px] py-[5px] bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465411 h-20 px-6 left-[16px] top-[730px] absolute bg-white rounded-[10px] border justify-center items-start gap-[119px] inline-flex">
    <div className="Frame3465356 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">代订客户到店提醒</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[5px] pr-[23px] py-[5px] bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465412 h-20 px-6 left-[16px] top-[826px] absolute bg-white rounded-[10px] border justify-center items-start gap-[119px] inline-flex">
    <div className="Frame3465356 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">代订客户结账提醒</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[5px] pr-[23px] py-[5px] bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465413 h-20 px-6 left-[16px] top-[922px] absolute bg-white rounded-[10px] border justify-center items-start gap-[135px] inline-flex">
    <div className="Frame3465356 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">免授权赠送通知</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[5px] pr-[23px] py-[5px] bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465296 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">返回</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465207 h-[852px] pt-[378px] left-0 top-0 absolute bg-black/60 flex-col justify-end items-start inline-flex">
    <div className="Frame3465208 w-[393px] h-[453px] relative bg-white rounded-tl-[20px] rounded-tr-[20px] flex-col justify-start items-start flex">
      <div className="Frame3465209 w-9 h-9 relative bg-neutral-100 rounded-[18px]" />
      <div className=" text-center text-black text-lg font-medium font-['PingFang SC']">接收人员设置</div>
      <div className="Frame3465220 w-[393px] pl-6 pr-2.5 py-6 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[22px] justify-between items-center flex">
          <div className="1 text-right text-black text-base font-medium font-['PingFang SC']">员工 1</div>
          <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465219 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465222 h-[70px] p-6 justify-center items-start gap-[276px] inline-flex">
        <div className="2 text-black text-base font-medium font-['PingFang SC']">员工 2</div>
        <div className="Frame3465233 w-[22px] h-[22px] relative bg-black/5 rounded-[20px]" />
      </div>
      <div className="Frame3465223 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465224 h-[70px] p-6 justify-center items-start gap-[276px] inline-flex">
        <div className="3 text-black text-base font-medium font-['PingFang SC']">员工 3</div>
        <div className="Frame3465233 w-[22px] h-[22px] relative bg-black/5 rounded-[20px]" />
      </div>
    </div>
    <div className="HomeIndicator self-stretch h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>