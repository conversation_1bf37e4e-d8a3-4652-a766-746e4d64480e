<div className=" w-[393px] h-[989px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">节假日设置</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[968px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465244 p-1 left-[16px] top-[215px] absolute bg-black/5 rounded-[10px] justify-center items-center inline-flex">
    <div className="Frame3465248 self-stretch justify-start items-center gap-1 inline-flex">
      <div className="Frame3465246 px-2.5 pt-1.5 pb-[5px] bg-[#5855ff] rounded-md justify-center items-center flex">
        <div className=" text-white text-xs font-medium font-['PingFang SC']">法定节假日</div>
      </div>
      <div className="Frame3465248 px-1 pt-1.5 pb-[5px] rounded-md justify-center items-center flex">
        <div className=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">自定义节假日</div>
      </div>
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">节假日设置</div>
  <div className="Frame3465221 pl-1.5 pr-3 py-2 left-[303px] top-[137px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-start items-center gap-0.5 flex">
      <div className="Frame3465222 w-[18px] h-[18px] relative">
        <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
        <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
      </div>
      <div className=" text-white text-xs font-medium font-['PingFang SC']">自定义</div>
    </div>
  </div>
  <div className="Frame11 w-[175px] h-[155px] left-[16px] top-[317px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">元旦</div>
    <div className="123020240101 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2023/12/30 - 2024/01/01</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="3 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 3 天</div>
    </div>
  </div>
  <div className="Frame3465246 w-[175px] h-[155px] left-[16px] top-[483px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">春节</div>
    <div className="123020240101 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/12/30 - 2024/01/01</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="8 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 8 天</div>
    </div>
  </div>
  <div className="Frame3465248 w-[175px] h-[155px] left-[16px] top-[649px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">端午节</div>
    <div className="060820240610 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/06/08 - 2024/06/10</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="3 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 3 天</div>
    </div>
  </div>
  <div className="Frame3465250 w-[175px] h-[155px] left-[16px] top-[815px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']"> 中秋节</div>
    <div className="091520240917 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/09/15 - 2024/09/17</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="3 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 3 天</div>
    </div>
  </div>
  <div className="Frame3465245 w-[175px] h-[155px] left-[202px] top-[317px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">清明节</div>
    <div className="040420240406 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/04/04 - 2024/04/06</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="3 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 3 天</div>
    </div>
  </div>
  <div className="Frame3465247 w-[175px] h-[155px] left-[202px] top-[483px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">劳动节</div>
    <div className="050120240505 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/05/01 - 2024/05/05</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="5 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 5 天</div>
    </div>
  </div>
  <div className="Frame3465249 w-[175px] h-[155px] left-[202px] top-[649px] absolute bg-white rounded-xl">
    <div className=" w-[135px] left-[20px] top-[20px] absolute text-black text-lg font-semibold font-['PingFang SC']">国庆节</div>
    <div className="100120241007 w-[135px] left-[20px] top-[49px] absolute text-black/30 text-sm font-semibold font-['PingFang SC']">2024/10/01 - 2024/10/07</div>
    <div className="Frame3465285 left-[20px] top-[113px] absolute justify-start items-center gap-0.5 inline-flex">
      <div className="7 text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">共 7 天</div>
    </div>
  </div>
  <div className="Frame3465397 pl-2.5 pr-[159px] py-2.5 left-[16px] top-[261px] absolute bg-white rounded-[10px] justify-start items-center inline-flex">
    <div className="Frame2090051591 self-stretch justify-start items-center gap-1 inline-flex">
      <div className="Frame3465398 w-5 h-5 relative">
        <div className="Ellipse985 w-3 h-3 left-[4px] top-[4px] absolute bg-[#ff8034] rounded-full border" />
      </div>
      <div className=" text-[#ff8034] text-xs font-medium font-['PingFang SC']">将每年根据国务院通知进行更新</div>
    </div>
  </div>
</div>