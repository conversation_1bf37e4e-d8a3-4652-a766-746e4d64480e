<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <img className="1 w-[504px] h-[1090px] left-[-87px] top-[-61px] absolute" src="https://via.placeholder.com/504x1090" />
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">详细地址</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame2090051587 w-[393px] h-[58px] px-4 py-2 left-0 top-[90px] absolute bg-white justify-center items-center inline-flex">
    <div className="Frame11 grow shrink basis-0 self-stretch pl-3.5 pr-[141px] py-[7px] bg-black/5 rounded-[10px] border justify-start items-center gap-2.5 inline-flex">
      <div className="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
        <img className="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
      </div>
      <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入省份/城市/县区搜索</div>
    </div>
  </div>
  <div className="Frame2090051588 w-[38px] h-[38px] left-[339px] top-[397px] absolute bg-white rounded-[10px] shadow">
    <div className="Rectangle462286854 w-0.5 h-[22px] left-[18px] top-[8px] absolute bg-black rounded-[0.50px] border border-[#999999]" />
    <div className="Rectangle462286855 w-0.5 h-[22px] left-[30px] top-[18px] absolute origin-top-left rotate-90 bg-black rounded-[0.50px] border border-[#999999]" />
    <div className="Ellipse993 w-4 h-4 left-[11px] top-[11px] absolute bg-white rounded-full border-2 border-[#999999]" />
    <div className="Ellipse994 w-1.5 h-1.5 left-[16px] top-[16px] absolute bg-black rounded-full" />
  </div>
  <div className="Frame2090051589 h-[391px] pt-1.5 left-[16px] top-[445px] absolute bg-white rounded-[10px] shadow flex-col justify-end items-center inline-flex">
    <div className="Frame2090051590 self-stretch h-[431px] flex-col justify-start items-center inline-flex">
      <div className="Frame3465210 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465219 w-[337px] h-px relative bg-black/5" />
      <div className="Frame3465220 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff]/0 rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465221 w-[337px] h-px relative bg-black/5" />
      <div className="Frame3465222 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff]/0 rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465223 w-[337px] h-px relative bg-black/5" />
      <div className="Frame3465224 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff]/0 rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465225 w-[337px] h-px relative bg-black/5" />
      <div className="Frame3465226 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff]/0 rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465227 w-[337px] h-px relative bg-black/5" />
      <div className="Frame3465228 self-stretch p-4 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[39px] justify-between items-center flex">
          <div className="Frame3465445 flex-col justify-start items-start inline-flex">
            <div className=" text-right text-black text-base font-medium font-['PingFang SC']">北京市朝阳区奥运村街道天畅园</div>
            <div className="220 w-[290px] text-black/40 text-xs font-medium font-['PingFang SC']">天畅园南面220米</div>
          </div>
          <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff]/0 rounded-[20px]" />
        </div>
      </div>
    </div>
  </div>
  <div className="211 left-[179px] top-[295.21px] absolute flex-col justify-end items-center inline-flex">
    <div className="ClipPathGroup w-[1562.79px] h-[879.07px] relative">
      <div className="Group w-[34.66px] h-[44.77px] left-[68.54px] top-[221.21px] absolute">
        <div className="Group w-[34.66px] h-[44.77px] left-0 top-0 absolute">
          <div className="ClipPathGroup w-[11.55px] h-[7.22px] left-[11.55px] top-[37.55px] absolute">
          </div>
          <div className="ClipPathGroup w-[34.66px] h-[41.88px] left-0 top-0 absolute">
            <div className="Group w-[78.95px] h-[86.28px] left-[-21.98px] top-[-18.72px] absolute">
            </div>
          </div>
          <div className="ClipPathGroup w-[9.83px] h-[7.94px] left-[12.41px] top-[33.94px] absolute">
          </div>
        </div>
        <div className="ClipPathGroup w-[13px] h-[13px] left-[10.83px] top-[10.83px] absolute">
        </div>
      </div>
    </div>
  </div>
</div>