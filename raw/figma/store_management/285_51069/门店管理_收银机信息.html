<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">收银机信息</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">收银机信息</div>
  <div className="Frame36 w-[172px] h-[186px] left-[201px] top-[217px] absolute rounded-xl">
    <div className="Rectangle462286859 w-[158px] h-[39px] left-[7px] top-[142px] absolute bg-gradient-to-b from-[#dfdfdf] to-[#b8b8b8] rounded-[9.52px] shadow shadow-inner" />
    <div className="Rectangle462286862 w-[150px] h-[29px] left-[11px] top-[147px] absolute bg-gradient-to-b from-white to-[#d8d8d8] rounded-md shadow-inner border" />
    <div className="Rectangle462286861 w-[150px] h-[29px] left-[11px] top-[147px] absolute bg-gradient-to-b from-white to-[#d8d8d8] rounded-md shadow-inner border border-[#797979]" />
    <div className="2 left-[25px] top-[21px] absolute text-white text-base font-semibold font-['PingFang SC']">收银机 2</div>
    <div className="10015013 left-[25px] top-[45px] absolute text-white/60 text-xs font-semibold font-['PingFang SC']">主收银机 ***********</div>
    <div className="015013 left-[25px] top-[112px] absolute text-white/60 text-[13px] font-semibold font-['PingFang SC']">***********</div>
    <div className="Frame2090051593 w-4 h-4 left-[131px] top-[113px] absolute bg-black/10 rounded-[14px]" />
    <div className="Ellipse984 w-1.5 h-1.5 left-[145px] top-[159px] absolute bg-gradient-to-b from-[#3bffa3] to-[#07ca6f] rounded-full shadow border border-[#23ab6b]" />
    <div className="Frame2090051592 px-2 py-[7px] left-[130px] top-[2px] absolute bg-[#21e589] rounded-tr-[13px] rounded-bl-[13px] justify-center items-center inline-flex">
      <div className=" text-white text-[9px] font-semibold font-['PingFang SC']">在线</div>
    </div>
  </div>
  <div className="Frame37 w-[172px] h-[186px] left-[20px] top-[217px] absolute rounded-xl">
    <div className="Rectangle462286859 w-[158px] h-[39px] left-[7px] top-[142px] absolute bg-gradient-to-b from-[#dfdfdf] to-[#b8b8b8] rounded-[9.52px] shadow shadow-inner" />
    <div className="Rectangle462286862 w-[150px] h-[29px] left-[11px] top-[147px] absolute bg-gradient-to-b from-white to-[#d8d8d8] rounded-md shadow-inner border" />
    <div className="Rectangle462286861 w-[150px] h-[29px] left-[11px] top-[147px] absolute bg-gradient-to-b from-white to-[#d8d8d8] rounded-md shadow-inner border border-[#797979]" />
    <div className="2 left-[25px] top-[21px] absolute text-white text-base font-semibold font-['PingFang SC']">收银机 2</div>
    <div className="10015013 left-[25px] top-[45px] absolute text-white/40 text-xs font-semibold font-['PingFang SC']">主收银机 ***********</div>
    <div className="015013 left-[25px] top-[112px] absolute text-white/40 text-[13px] font-semibold font-['PingFang SC']">***********</div>
    <div className="Frame2090051593 w-4 h-4 left-[131px] top-[113px] absolute bg-black/10 rounded-[14px]" />
    <div className="Ellipse984 w-1.5 h-1.5 left-[145px] top-[159px] absolute bg-[#9c9c9c] rounded-full border" />
    <div className="Frame2090051592 px-2 py-[7px] left-[130px] top-[2px] absolute bg-[#858585] rounded-tr-[13px] rounded-bl-[13px] justify-center items-center inline-flex">
      <div className=" text-white text-[9px] font-semibold font-['PingFang SC']">离线</div>
    </div>
  </div>
</div>