<div className=" w-[393px] h-[2416px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">门店配置</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3465271 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">门店编号</div>
  </div>
  <div className="Frame3465272 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">门店类型</div>
  </div>
  <div className="Frame3465274 left-[20px] top-[362px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">注册时间</div>
  </div>
  <div className="Frame11 w-[361px] pl-[18px] pr-[252px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">110105560814</div>
  </div>
  <div className="Frame3465273 w-[361px] pl-[18px] pr-[315px] py-[18px] left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">量贩</div>
  </div>
  <div className="Frame3465275 w-[361px] pl-[18px] pr-[261px] pt-[18px] left-[16px] top-[400px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="1025 text-black/20 text-sm font-medium font-['PingFang SC']">2022-10-25<br/></div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[282px] py-[18px] left-[16px] top-[524px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="Ktv text-black text-sm font-medium font-['PingFang SC']">桉树 KTV</div>
  </div>
  <div className="Frame3465303 w-[361px] pl-[18px] pr-[261px] py-[18px] left-[16px] top-[1119px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/95 text-sm font-medium font-['PingFang SC']">18222121212</div>
  </div>
  <div className="Frame3465308 w-[361px] pl-[18px] pr-[249px] py-[18px] left-[16px] top-[2199px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">1022111232113</div>
  </div>
  <div className="Frame3465301 pl-[18px] pr-[172px] py-[18px] left-[16px] top-[995px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="83Ktv text-black text-sm font-medium font-['PingFang SC']">天场元8号楼 3 层桉树 KTV</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[486px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465304 left-[20px] top-[1081px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">电话</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465309 left-[20px] top-[2161px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">连锁店号</div>
  </div>
  <div className="Frame3465302 left-[20px] top-[955px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">详细地址</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465300 left-[20px] top-[610px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className="Logo text-black text-base font-medium font-['PingFang SC']">LOGO 图片</div>
  </div>
  <div className="Frame17 px-6 pt-6 pb-[25px] left-[16px] top-[648px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
    <div className="100k text-black text-base font-normal font-['PingFang SC']">图片大小不能超过100K</div>
    <div className="Frame3465205 self-stretch flex-col justify-start items-start gap-1.5 inline-flex">
      <div className="Frame3465203 w-[313px] justify-start items-start gap-1.5 inline-flex">
        <div className="Frame3465199 w-[74px] h-[74px] p-[23px] bg-neutral-100 rounded-md border justify-center items-center flex">
          <div className="1 grow shrink basis-0 self-stretch justify-start items-center inline-flex">
            <div className="ClipPathGroup w-[2814.61px] h-[3537.13px] relative">
              <div className="Group w-[22.84px] h-[19.48px] left-[2067.74px] top-[1969.74px] absolute">
                <div className="ClipPathGroup w-[22.84px] h-[19.48px] left-0 top-0 absolute">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="Frame3465200 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465201 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465202 h-[74px] relative opacity-0 rounded border border-black" />
      </div>
    </div>
  </div>
  <div className="Frame3465306 left-[20px] top-[1731px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className="6 text-black text-base font-medium font-['PingFang SC']">照片(最多6张)</div>
  </div>
  <div className="Frame3465307 px-6 pt-6 pb-[25px] left-[16px] top-[1769px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
    <div className="100k text-black text-base font-normal font-['PingFang SC']">图片大小不能超过100K</div>
    <div className="Frame3465205 self-stretch flex-col justify-start items-start gap-1.5 inline-flex">
      <div className="Frame3465203 w-[313px] justify-start items-start gap-1.5 inline-flex">
        <div className="Frame3465199 w-[74px] h-[74px] p-[23px] bg-neutral-100 rounded-md border justify-center items-center flex">
          <div className="1 grow shrink basis-0 self-stretch justify-start items-center inline-flex">
            <div className="ClipPathGroup w-[2814.61px] h-[3537.13px] relative">
              <div className="Group w-[22.84px] h-[19.48px] left-[2067.74px] top-[1969.74px] absolute">
                <div className="ClipPathGroup w-[22.84px] h-[19.48px] left-0 top-0 absolute">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="Frame3465200 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465201 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465202 h-[74px] relative opacity-0 rounded border border-black" />
      </div>
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[843px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[133px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">北京市 / 市辖区 / 朝阳区</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">所在地区</div>
    </div>
  </div>
  <div className="Frame3465228 pl-2.5 py-1 left-[321px] top-[953px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-start items-center flex">
      <div className=" text-white text-xs font-medium font-['PingFang SC']">选择</div>
      <div className="Frame3465222 w-[18px] h-[18px] relative" />
    </div>
  </div>
  <div className="Frame3465295 left-[20px] top-[1205px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">营业时间</div>
  </div>
  <div className="Frame15 w-[361px] px-3.5 py-[18px] left-[16px] top-[1243px] absolute bg-white rounded-[10px] border justify-center items-center gap-3.5 inline-flex">
    <div className="Frame3465249 grow shrink basis-0 self-stretch py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="00 text-center text-black text-sm font-medium font-['PingFang SC']">06:00</div>
    </div>
    <div className="Frame3465251 self-stretch justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465250 grow shrink basis-0 self-stretch pl-4 pr-9 pt-3.5 pb-[13px] bg-neutral-100 rounded-md justify-start items-center gap-[19px] inline-flex">
      <div className=" text-black/40 text-[10px] font-medium font-['PingFang SC']">次日</div>
      <div className="00 text-black text-lg font-medium font-['PingFang SC']">03:00</div>
    </div>
  </div>
  <div className="Frame3465305 w-[361px] h-[360px] left-[16px] top-[1347px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465237 w-[140px] pl-[46px] pr-[45px] pt-3.5 pb-[13px] left-[14px] top-[60px] absolute bg-neutral-100 rounded border border-neutral-100 justify-center items-center inline-flex">
      <div className="00 text-black text-lg font-medium font-['PingFang SC']">06:00</div>
    </div>
    <div className="Frame3465243 w-[140px] pl-12 pr-[47px] pt-3.5 pb-[13px] left-[207px] top-[60px] absolute bg-neutral-100 rounded border border-neutral-100 justify-center items-center inline-flex">
      <div className="00 text-center text-black text-lg font-medium font-['PingFang SC']">12:00</div>
    </div>
    <div className=" left-[14px] top-[136px] absolute text-black text-sm font-medium font-['PingFang SC']">中班时间</div>
    <div className=" left-[14px] top-[248px] absolute text-black text-sm font-medium font-['PingFang SC']">晚班时间</div>
    <div className=" left-[14px] top-[24px] absolute text-black text-sm font-medium font-['PingFang SC']">早班时间</div>
    <div className="Frame3465249 w-[140px] pl-12 pr-[47px] pt-3.5 pb-[13px] left-[14px] top-[172px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="00 text-center text-black text-lg font-medium font-['PingFang SC']">12:00</div>
    </div>
    <div className="Frame3465250 w-[140px] pl-12 pr-[47px] pt-3.5 pb-[13px] left-[207px] top-[172px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="00 text-black text-lg font-medium font-['PingFang SC']">18:00</div>
    </div>
    <div className="Frame3465251 left-[168px] top-[197px] absolute justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465276 w-[140px] pl-12 pr-[47px] pt-3.5 pb-[13px] left-[14px] top-[284px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="00 text-center text-black text-lg font-medium font-['PingFang SC']">18:00</div>
    </div>
    <div className="Frame3465277 pl-4 pr-9 pt-3.5 pb-[13px] left-[207px] top-[284px] absolute bg-neutral-100 rounded-md justify-start items-center gap-[19px] inline-flex">
      <div className=" text-black/40 text-[10px] font-medium font-['PingFang SC']">次日</div>
      <div className="00 text-center text-black text-lg font-medium font-['PingFang SC']">06:00</div>
    </div>
    <div className="Frame3465278 left-[168px] top-[309px] absolute justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465275 left-[168px] top-[85px] absolute justify-start items-center gap-0.5 inline-flex" />
  </div>
  <div className="Frame3465297 left-[20px] top-[1964px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">门店介绍</div>
  </div>
  <div className="Frame3465298 w-[361px] h-[129px] px-[18px] pt-[18px] pb-[91px] left-[16px] top-[2002px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Ktv w-[325px] text-black/95 text-sm font-medium font-['PingFang SC']">曲库最全的 KTV，欢迎您的您的朋友到此</div>
  </div>
  <div className="Frame3 h-[121px] left-0 top-[2295px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>