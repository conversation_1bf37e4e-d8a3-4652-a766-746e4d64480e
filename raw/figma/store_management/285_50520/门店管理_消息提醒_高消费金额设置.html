<div className=" w-[393px] h-[945px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">消息提醒</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[924px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465296 h-[121px] left-0 top-[824px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465397 w-[393px] p-2.5 left-0 top-[90px] absolute bg-[#ff8034]/10 justify-center items-center inline-flex">
    <div className="Frame2090051591 grow shrink basis-0 self-stretch justify-start items-center gap-1 inline-flex">
      <div className="Frame3465398 w-5 h-5 relative">
        <div className="Ellipse985 w-3 h-3 left-[4px] top-[4px] absolute bg-[#ff8034] rounded-full border" />
      </div>
      <div className="Frame2090051619 grow shrink basis-0 h-[17px] justify-start items-center gap-4 flex">
        <div className="0 text-[#ff8034] text-xs font-medium font-['PingFang SC']">金额为0或空则不会提醒</div>
      </div>
    </div>
  </div>
  <div className="Frame16 w-[361px] h-16 left-[16px] top-[192px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">/元</div>
    <div className="Frame3465198 px-1.5 py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-right text-black text-base font-medium font-['PingFang SC']">200000</div>
    </div>
  </div>
  <div className="Frame3465398 w-[361px] h-16 left-[16px] top-[324px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">/元</div>
    <div className="Frame3465198 w-[70px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-right text-black/40 text-base font-medium font-['PingFang SC']">0</div>
    </div>
  </div>
  <div className="Frame3465293 left-[20px] top-[154px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">小包</div>
  </div>
  <div className="Frame3465399 left-[20px] top-[286px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">中包</div>
  </div>
  <div className="Frame3465400 w-[361px] h-16 left-[16px] top-[456px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">/元</div>
    <div className="Frame3465198 w-[70px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-right text-black/40 text-base font-medium font-['PingFang SC']">0</div>
    </div>
  </div>
  <div className="Frame3465401 w-[361px] h-16 left-[16px] top-[588px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">/元</div>
    <div className="Frame3465198 w-[70px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-right text-black/40 text-base font-medium font-['PingFang SC']">0</div>
    </div>
  </div>
  <div className="Frame3465404 w-[361px] h-16 left-[16px] top-[720px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">/元</div>
    <div className="Frame3465198 w-[70px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-right text-black/40 text-base font-medium font-['PingFang SC']">0</div>
    </div>
  </div>
  <div className="Frame3465403 left-[20px] top-[550px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">凯撒厅</div>
  </div>
  <div className="Frame3465402 left-[20px] top-[418px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">大包</div>
  </div>
  <div className="Frame3465405 left-[20px] top-[682px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">总统包</div>
  </div>
</div>