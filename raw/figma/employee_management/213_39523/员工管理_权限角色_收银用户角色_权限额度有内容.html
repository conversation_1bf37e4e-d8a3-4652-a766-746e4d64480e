<div className=" w-[393px] h-[1779px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">收银用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[1758px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[259px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">收银用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[1146px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[-31px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[99px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[229px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[27px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[157px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[287px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">权限额度</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[305px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame3465335 left-[20px] top-[76px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">赠送权限</div>
    </div>
    <div className="Frame3465356 pl-3 pr-3.5 py-4 left-[16px] top-[114px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">赠送时长</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465357 pl-3 pr-3.5 py-4 left-[184px] top-[114px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">赠送商品</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className=" left-[94px] top-[286px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/日</div>
    <div className="Frame3465198 px-[19px] py-[9px] left-[16px] top-[265px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">不限</div>
    </div>
    <div className="Frame3465423 left-[20px] top-[227px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">日可赠送房台数</div>
    </div>
    <div className="Rectangle462286878 w-[329px] h-px left-[16px] top-[196px] absolute bg-black/5" />
    <div className="Rectangle462286879 w-[329px] h-px left-[16px] top-[381px] absolute bg-black/5" />
    <div className="Rectangle462286880 w-[329px] h-px left-[16px] top-[541px] absolute bg-black/5" />
    <div className="Rectangle462286881 w-[329px] h-px left-[16px] top-[680px] absolute bg-black/5" />
    <div className="Rectangle462286882 w-[329px] h-px left-[16px] top-[840px] absolute bg-black/5" />
    <div className="Rectangle462286883 w-[329px] h-px left-[16px] top-[1000px] absolute bg-black/5" />
    <div className="Frame3465424 left-[16px] top-[329px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">同级可重复赠送</div>
    </div>
    <div className="Frame3465196 w-10 h-[22px] pl-[5px] pr-[23px] py-[5px] left-[305px] top-[329px] absolute bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
    <div className="Frame3465428 left-[20px] top-[711px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">营业额查询天数 </div>
    </div>
    <div className="Frame3465430 left-[20px] top-[871px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">业绩查询天数 </div>
    </div>
    <div className="Frame3465419 left-[20px] top-[412px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">减免折扣免单总额</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
    <div className="Frame3465432 left-[20px] top-[1031px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">收银机可查询天数 </div>
    </div>
    <div className="Frame3465425 pl-[21px] pr-5 py-[9px] left-[16px] top-[471px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">200</div>
    </div>
    <div className=" left-[94px] top-[492px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/月</div>
    <div className="Frame3465429 w-[70px] py-[9px] left-[16px] top-[770px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">20</div>
    </div>
    <div className=" left-[94px] top-[791px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">天</div>
    <div className=" left-[20px] top-[737px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">对掌柜营业额页面查询天数进行限制，不配表示不限</div>
    <div className="Frame3465431 w-[70px] py-[9px] left-[16px] top-[930px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">20</div>
    </div>
    <div className=" left-[94px] top-[951px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">天</div>
    <div className=" left-[20px] top-[897px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">对个人工台中的业绩查询天数进行限制，不配表示不限</div>
    <div className="1 left-[20px] top-[438px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">每月1号重置</div>
    <div className="Frame3465426 left-[20px] top-[572px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">最低折扣</div>
    </div>
    <div className="Frame3465433 w-[70px] py-[9px] left-[16px] top-[1090px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">20</div>
    </div>
    <div className=" left-[94px] top-[1111px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">天</div>
    <div className=" left-[20px] top-[1057px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">对收银机交班、销售统计的查询天数进行限制，不配表示不限</div>
    <div className="Frame3465427 w-[70px] py-[9px] left-[16px] top-[610px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black text-base font-medium font-['PingFang SC']">20</div>
    </div>
    <div className=" left-[94px] top-[631px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">%</div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[1658px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>