<div className=" w-[393px] h-[852px] relative bg-white">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">员工信息</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame11 pl-3.5 pr-7 py-3.5 left-[16px] top-[114px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
      <img className="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
    </div>
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">拼音、首字母或工号搜索员工</div>
  </div>
  <div className="Frame2090051624 left-[16px] top-[180px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className="Frame2090051621 px-2.5 py-1 rounded justify-center items-center gap-2.5 flex">
      <div className=" text-[#5855ff] text-sm font-semibold font-['PingFang SC']">全部</div>
    </div>
    <div className="Frame2090051622 px-2.5 py-1 rounded justify-center items-center gap-2.5 flex">
      <div className=" text-black/30 text-sm font-medium font-['PingFang SC']">手机认证员工</div>
    </div>
    <div className="Frame2090051623 px-2.5 py-1 rounded justify-center items-center gap-2.5 flex">
      <div className=" text-black/30 text-sm font-medium font-['PingFang SC']">工号员工</div>
    </div>
  </div>
  <div className="Frame3465228 w-[90px] h-14 p-1 left-[287px] top-[114px] absolute bg-black rounded-[10px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-center items-center gap-1 flex">
      <div className="Frame3465222 w-[18px] h-[18px] relative">
        <div className="Rectangle3469066 w-0.5 h-3.5 left-[8px] top-[2px] absolute bg-white rounded-lg" />
        <div className="Rectangle3469067 w-0.5 h-3.5 left-[16px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
      </div>
      <div className=" text-white text-sm font-medium font-['PingFang SC']">新增</div>
    </div>
  </div>
  <div className="Frame3465329 h-[648px] left-0 top-[218px] absolute flex-col justify-start items-start inline-flex">
    <div className="Frame3465322 px-5 py-[16.50px] bg-white justify-center items-center inline-flex">
      <div className="Frame2090051626 grow shrink basis-0 self-stretch flex-col justify-start items-start gap-0.5 inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">直属分组</div>
        <div className="1 self-stretch text-center text-black/40 text-xs font-medium font-['PingFang SC']">(1位)</div>
      </div>
    </div>
    <div className="Frame3465324 h-[72px] pl-[23px] pr-[22px] bg-neutral-100 rounded-tr-[10px] justify-center items-center inline-flex">
      <div className="2 text-black/40 text-sm font-medium font-['PingFang SC']">员工2组</div>
    </div>
    <div className="Frame3465325 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div className=" text-black/0 text-sm font-medium font-['PingFang SC']">果盘</div>
    </div>
    <div className="Frame3465326 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div className=" text-black/0 text-sm font-medium font-['PingFang SC']">小吃</div>
    </div>
    <div className="Frame3465327 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div className=" text-black/0 text-sm font-medium font-['PingFang SC']">其他</div>
    </div>
    <div className="Frame3465328 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div className=" text-black/0 text-sm font-medium font-['PingFang SC']">烧烤</div>
    </div>
    <div className="Frame3465329 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465335 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465334 h-[72px] relative bg-neutral-100" />
  </div>
  <div className="Frame3465323 w-[297px] h-[634px] pb-[544px] left-[96px] top-[218px] absolute bg-white flex-col justify-start items-center inline-flex">
    <div className="Frame3465353 self-stretch h-[90px] flex-col justify-start items-start inline-flex">
      <div className="Frame3465352 h-[90px] relative">
        <div className=" left-[20px] top-[20px] absolute opacity-95 text-black text-base font-semibold font-['PingFang SC']">李托尼</div>
        <div className=" left-[80px] top-[20px] absolute text-black text-base font-semibold font-['PingFang SC']">15701059320</div>
        <div className="Frame3465432 px-2 py-1 left-[20px] top-[48px] absolute bg-[#5855ff] rounded-[17px] justify-center items-center gap-2.5 inline-flex">
          <div className=" text-white text-[10px] font-medium font-['PingFang SC']">管理员</div>
        </div>
        <div className="Frame2090051625 w-12 h-10 px-3.5 py-2.5 left-[229px] top-[25px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
          <div className="13 grow shrink basis-0 self-stretch justify-start items-center inline-flex">
            <div className="ClipPathGroup w-[984.62px] h-[3515.77px] relative">
              <div className="Group w-[18.85px] h-[18.46px] left-[578.46px] top-[911.92px] absolute">
                <div className="ClipPathGroup w-[2.31px] h-[2.31px] left-[13.07px] top-[2.31px] absolute">
                </div>
                <div className="ClipPathGroup w-[2.31px] h-[2.31px] left-[2.32px] top-[13.08px] absolute">
                </div>
                <div className="ClipPathGroup w-[1.92px] h-[5.38px] left-[10.02px] top-[13.08px] absolute">
                </div>
                <div className="ClipPathGroup w-[1.92px] h-[1.92px] left-[10.02px] top-[10px] absolute">
                </div>
                <div className="ClipPathGroup w-[5.38px] h-[1.92px] left-[13.46px] top-[16.54px] absolute">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>