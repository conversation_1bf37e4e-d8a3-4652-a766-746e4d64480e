<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">新增员工信息</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5855ff] text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className=" w-[361px] left-[16px] top-[224px] absolute text-black/40 text-xs font-normal font-['PingFang SC'] leading-[18px]">此类型员工无法登录收银机，只能作为代订人或销售员</div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[301px] py-[18px] left-[16px] top-[310px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="Frame3465295 w-[361px] pl-[18px] pr-[301px] py-[18px] left-[16px] top-[558px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="Frame3465300 w-[361px] pl-[18px] pr-[301px] py-[18px] left-[16px] top-[1072px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="Frame3465296 left-[20px] top-[520px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">姓名</div>
  </div>
  <div className="Frame3465297 w-[361px] pl-[18px] pr-[301px] py-[18px] left-[16px] top-[792px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="Frame3465301 left-[20px] top-[1034px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">证件号码</div>
  </div>
  <div className="Frame3465292 left-[20px] top-[272px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">手机号码</div>
  </div>
  <div className="Frame3465298 left-[20px] top-[754px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">密码</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465293 w-[361px] pl-[18px] pr-[301px] py-[18px] left-[16px] top-[434px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="Frame3465294 left-[20px] top-[396px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">工号</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[644px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">性别</div>
    </div>
  </div>
  <div className="Frame3465302 w-[361px] h-20 left-[16px] top-[1158px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">营销角色</div>
    </div>
  </div>
  <div className="Frame3465304 w-[361px] h-20 left-[16px] top-[1350px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">收银机权限</div>
    </div>
  </div>
  <div className="Frame3465303 w-[361px] h-20 left-[16px] top-[1254px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">权限角色</div>
    </div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[1446px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">汇金掌柜权限</div>
    </div>
  </div>
  <div className="Frame3465306 w-[361px] h-20 left-[16px] top-[1638px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">点单屏权限</div>
    </div>
  </div>
  <div className="Frame3465307 w-[361px] h-20 left-[16px] top-[1542px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">移动点单权限</div>
    </div>
  </div>
  <div className="Frame3465308 w-[361px] h-20 left-[16px] top-[1734px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">赠送额度</div>
    </div>
  </div>
  <div className="Frame3465299 left-[20px] top-[878px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">入职时间</div>
  </div>
  <div className="Frame15 w-[361px] h-[88px] left-[16px] top-[916px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465251 left-[174.50px] top-[43px] absolute" />
    <div className="Frame3465249 pl-[9px] pr-[158px] py-3.5 left-[14px] top-[18px] absolute bg-neutral-100 rounded-md justify-start items-center gap-[9px] inline-flex">
      <div className="Frame3465276 w-6 h-6 relative opacity-20 flex-col justify-start items-start flex">
        <div className="Rectangle3469207 w-0.5 h-1 bg-black rounded-[3px]" />
        <div className="Rectangle3469208 w-0.5 h-1 bg-black rounded-[3px]" />
        <div className="Rectangle3469206 w-2.5 h-[7px] rounded-[1px] border-2 border-black" />
        <div className="Rectangle3469209 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469210 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469211 w-0.5 h-0.5 bg-black rounded-[1px]" />
        <div className="Rectangle3469212 w-0.5 h-0.5 bg-black rounded-[1px]" />
      </div>
      <div className="0924 text-black text-sm font-medium font-['PingFang SC']">2024 年 09 月 24 日</div>
    </div>
  </div>
  <div className="Frame3465309 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black/20 rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465207 h-[852px] pt-[378px] left-0 top-0 absolute bg-black/60 flex-col justify-end items-start inline-flex">
    <div className="Frame3465208 w-[393px] h-[453px] relative bg-white rounded-tl-[20px] rounded-tr-[20px] flex-col justify-start items-start flex">
      <div className="Frame3465209 w-9 h-9 relative bg-neutral-100 rounded-[18px]" />
      <div className=" text-center text-black text-lg font-medium font-['PingFang SC']">选择性别</div>
      <div className="Frame3465220 w-[393px] pl-6 pr-2.5 py-6 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[22px] justify-between items-center flex">
          <div className=" text-right text-black text-base font-medium font-['PingFang SC']">男</div>
          <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465219 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465211 w-[393px] h-[70px] pl-6 pr-[353px] py-6 justify-start items-center inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">女</div>
      </div>
      <div className="Frame3465221 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465222 w-[393px] h-[70px] pl-6 pr-[337px] py-6 justify-start items-center inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">未知</div>
      </div>
    </div>
    <div className="HomeIndicator self-stretch h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>