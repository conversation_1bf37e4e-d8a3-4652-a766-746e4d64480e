<div className=" w-[393px] h-[5943px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">落单用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[5922px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[247px] pt-[18px] pb-4 left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">落单用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[5310px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[18px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[148px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[278px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[76px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[206px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[336px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[159px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame2090051624 left-[16px] top-[79px] absolute justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051621 px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-start items-center gap-2.5 flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">首页</div>
      </div>
      <div className="Frame2090051622 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">财务对账</div>
      </div>
      <div className="Frame2090051624 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">提成统计</div>
      </div>
      <div className="Frame2090051625 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">营业分析</div>
      </div>
      <div className="Frame2090051626 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">经营分析</div>
      </div>
    </div>
    <div className="Frame3465335 left-[20px] top-[138px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">首页</div>
    </div>
    <div className="Frame3465334 w-[329px] p-2 left-[16px] top-[176px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051625 left-[20px] top-[330px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">财务对账</div>
    </div>
    <div className="Frame2090051650 left-[20px] top-[1112px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">提成统计</div>
    </div>
    <div className="Frame2090051652 left-[20px] top-[1540px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">营业分析</div>
    </div>
    <div className="Frame2090051654 left-[20px] top-[1850px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">经营分析</div>
    </div>
    <div className="Frame2090051656 left-[20px] top-[2219px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">会员管理</div>
    </div>
    <div className="Frame2090051660 left-[20px] top-[2662px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">服务开通</div>
    </div>
    <div className="Frame2090051662 left-[20px] top-[2972px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">短信管理</div>
    </div>
    <div className="Frame2090051666 left-[20px] top-[3769px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">包厢管理</div>
    </div>
    <div className="Frame2090051664 left-[20px] top-[3223px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">库存管理</div>
    </div>
    <div className="Frame2090051668 left-[20px] top-[4020px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">商品管理</div>
    </div>
    <div className="Frame2090051670 left-[20px] top-[4350px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">大玩家</div>
    </div>
    <div className="Frame2090051672 left-[20px] top-[4719px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">操作管理</div>
    </div>
    <div className="Frame2090051674 left-[20px] top-[4970px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">员工管理</div>
    </div>
    <div className="Frame2090051658 left-[20px] top-[2470px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">分销管理</div>
    </div>
    <div className="Frame2090051651 w-[329px] p-2 left-[16px] top-[1150px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051653 w-[329px] p-2 left-[16px] top-[1578px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051655 w-[329px] p-2 left-[16px] top-[1888px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051657 w-[329px] p-2 left-[16px] top-[2257px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051661 w-[329px] p-2 left-[16px] top-[2700px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051626 w-[329px] p-2 left-[16px] top-[368px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051663 w-[329px] p-2 left-[16px] top-[3010px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051665 w-[329px] p-2 left-[16px] top-[3261px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051667 w-[329px] p-2 left-[16px] top-[3807px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051669 w-[329px] p-2 left-[16px] top-[4058px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051671 w-[329px] p-2 left-[16px] top-[4388px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051673 w-[329px] p-2 left-[16px] top-[4757px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051675 w-[329px] p-2 left-[16px] top-[5008px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051659 w-[329px] p-2 left-[16px] top-[2508px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051649 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[248px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">概览</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看概览</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
  </div>
  <div className="Frame3465229 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[912px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">营业日报</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看营业日报</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame3465335 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1030px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品收入明细</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看商品收入明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame3465231 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[971px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品销售统计</div>
      <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看商品销售统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465336 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1089px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">优惠赠送</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看优惠赠送</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465345 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1325px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">异常单报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看异常单报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465353 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1753px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">办卡储值</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看办卡储值</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465368 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2491px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢类型分析</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看包厢类型分析</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465390 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3864px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">供应商报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看供应商报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465406 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4661px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品导入</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看商品导入</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465413 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4991px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">小程序内容管理</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看小程序内容管理</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465423 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5360px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">报表导出日志</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看报表导出日志</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465419 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5050px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">公众号菜单管理</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看公众号菜单管理</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465421 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5109px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">公众号粉丝管理</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看公众号粉丝管理</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465398 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4041px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">续存记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看包厢类型分析</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465376 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2860px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员操作记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员操作记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465382 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3303px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">机器人租赁</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看机器人租赁</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465387 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3613px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">短信报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看短信报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465403 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4410px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">沙盘图设置</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">预定沙盘图设置</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465386 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3362px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">抖音服务</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">开通汇金抖音服务</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465362 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2181px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员消费排行</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员消费排行</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465341 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1207px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">挂账明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看挂账明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465346 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1443px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">退单报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看退单报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465354 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1871px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">落单明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看落单明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465391 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3982px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">充公记录</div>
      <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看充公记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465369 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2609px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务超时</div>
      <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看服务超时</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465399 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4159px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">辅料进销存统计</div>
      <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看辅料进销存统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465339 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1148px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465392 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3923px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">出库记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看出库记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465347 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1384px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">券包销售报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看券包销售报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465407 w-[162px] h-[72px] left-[32px] top-[4720px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[132px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品批量修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">批量修改商品价格和会员卡结账限制</div>
    </div>
  </div>
  <div className="Frame3465412 w-[162px] h-[72px] left-[200px] top-[4720px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[132px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">套餐批量修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">批量修改套餐价格和会员卡结账限制</div>
    </div>
  </div>
  <div className="Frame3465355 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1812px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">现抽统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看现抽统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465370 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2550px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">客户管理</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看客户管理</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465400 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4100px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">保质期预警</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看保质期预警</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465363 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2240px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员余额日报</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员余额日报</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465342 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1266px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">进销存报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看进销存报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465356 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1694px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">代订提成</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看代订提成</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465377 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2801px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员卡导入</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员卡导入</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465393 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3805px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">库存汇总</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看库存汇总</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465383 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3244px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">开通网络支付</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看开通网络支付</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465371 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2432px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">活动时段分析</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看活动时段分析</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465408 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4602px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品列表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看商品列表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465416 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4932px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">大玩家总览</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看大玩家总览</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465424 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5301px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">操作日志</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看操作日志</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465426 w-[162px] h-[72px] left-[32px] top-[5552px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[132px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工信息</div>
      <div className="Id self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">下载员工二维码、设置员工卡ID</div>
    </div>
  </div>
  <div className="Frame3465388 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3554px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">短信充值</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看短信充值</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465404 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4351px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢二维码</div>
      <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">下载包厢二维码</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465380 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3052px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">分销返利统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">分销返利统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465381 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3052px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">粉丝列表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看粉丝列表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465364 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[2122px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">班次开台率</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看班次开台率</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465348 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1502px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">拼团报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看拼团报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465357 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[1930px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务奖赏统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看服务奖赏统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465230 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[912px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">交班报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看交班报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465337 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1030px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">账单明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看账单明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465394 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3864px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">入库记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看入库记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465334 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[971px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢收入明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看包厢收入明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465338 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1089px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">预订报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看预订报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465349 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1325px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">优惠券报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看优惠券报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465409 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4661px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存酒导入</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看存酒导入</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465417 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4991px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">微信公众号设置</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看微信公众号设置</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465420 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5050px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">公众号模板消息</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看公众号模板消息</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465422 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5109px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">小程序主题</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看小程序主题</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465378 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2860px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员删除</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">会员删除</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465358 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1753px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">提成统计明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看提成统计明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465372 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2491px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员转化率</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员转化率</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465401 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4041px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">报废记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看存酒报废记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465384 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3303px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">微客-企业微信</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">开通汇金微客系统</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465365 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2181px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员余额统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员余额统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465343 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1207px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存取酒报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看存取酒报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465396 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3923px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">调拨记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看调拨记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465350 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1443px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看服务报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465359 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1871px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工分销</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看员工分销</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465373 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2609px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务评价</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看服务评价</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465395 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3982px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">盘点记录</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看盘点记录</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465340 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1148px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">次卡报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看次卡报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465351 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1384px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">积分报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看积分报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465360 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1812px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">落单统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看落单统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465374 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2550px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">客群标签分析</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看客群标签分析</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465402 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4100px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">辅料消耗明细</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看辅料消耗明细</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465366 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2240px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">储值日留存率</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看储值日留存率</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465397 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3805px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">实时库存</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看实时库存</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465379 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2801px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员列表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看会员列表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465344 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1266px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">支付报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看支付报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465385 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3244px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">汇金云服务</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看汇金云服务</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465361 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1694px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品提成</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看商品提成</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465375 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2432px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢酒水分析</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看包厢酒水分析</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465411 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4602px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">套餐列表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看套餐列表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465418 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4932px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">大玩家开通向导</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看大玩家开通向导</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465425 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5301px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">支付授权日志</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看支付授权日志</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465389 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3554px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">短信设置</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看短信设置</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465405 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4351px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢价格批量修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">批量修改包厢价格</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465367 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[2122px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">班次营业情况</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看班次营业情况</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465352 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[1502px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">抖音报表</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看抖音报表</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[5822px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465427 left-[36px] top-[5661px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">高级权限</div>
  </div>
  <div className="Frame3465428 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5699px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">客户信息导出</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">客户管理导出客户信息</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
</div>