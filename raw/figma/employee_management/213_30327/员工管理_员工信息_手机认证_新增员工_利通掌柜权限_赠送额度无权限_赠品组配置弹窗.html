<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">赠送额度</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3465334 w-[361px] p-2 left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 bg-white rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">无权限</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">赠送商品</div>
      </div>
    </div>
  </div>
  <div className="Frame3465335 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">赠送权限</div>
  </div>
  <div className="Frame3465401 w-[361px] h-20 left-[16px] top-[224px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[267px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">赠品组配置</div>
    </div>
  </div>
  <div className="Frame3465417 w-[361px] h-20 left-[16px] top-[751px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">不限</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">每台赠送商品限额</div>
    </div>
  </div>
  <div className="Frame3465411 w-[361px] h-16 left-[16px] top-[393px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/月</div>
    <div className="Frame3465198 px-[11px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">请输入</div>
    </div>
  </div>
  <div className="Frame3465412 left-[20px] top-[334px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">月赠送商品限额</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="1 left-[20px] top-[360px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">每月1号重置</div>
  <div className="Frame3465418 w-[361px] h-16 left-[16px] top-[920px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">份/月</div>
    <div className="Frame3465198 px-[11px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">请输入</div>
    </div>
  </div>
  <div className="Frame3465419 left-[20px] top-[861px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">月赠送商品数量总限额</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="1 left-[20px] top-[887px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">全部商品，每月1号重置</div>
  <div className="Frame3465413 w-[361px] h-16 left-[16px] top-[525px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/月</div>
    <div className="Frame3465198 px-[19px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">不限</div>
    </div>
  </div>
  <div className="Frame3465420 w-[361px] h-16 left-[16px] top-[1052px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">份/台</div>
    <div className="Frame3465198 px-[19px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">不限</div>
    </div>
  </div>
  <div className="Frame3465414 left-[20px] top-[487px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">周赠送商品限额</div>
  </div>
  <div className="Frame3465421 left-[20px] top-[1014px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">每台赠送商品数量限额</div>
  </div>
  <div className="Frame3465415 w-[361px] h-16 left-[16px] top-[657px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/日</div>
    <div className="Frame3465198 px-[19px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">不限</div>
    </div>
  </div>
  <div className="Frame3465422 w-[361px] h-16 left-[16px] top-[1386px] absolute bg-white rounded-[10px] border">
    <div className=" left-[90px] top-[33px] absolute text-black/20 text-xs font-medium font-['PingFang SC']">元/日</div>
    <div className="Frame3465198 px-[19px] py-[9px] left-[12px] top-[12px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/20 text-base font-medium font-['PingFang SC']">不限</div>
    </div>
  </div>
  <div className="Frame3465416 left-[20px] top-[619px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">日赠送商品限额</div>
  </div>
  <div className="Frame3465423 left-[20px] top-[1348px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">日可赠送房台数</div>
  </div>
  <div className="Frame19 w-[361px] h-[172px] left-[16px] top-[1146px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute justify-start items-center inline-flex">
      <div className=" text-black text-sm font-medium font-['PingFang SC']">月赠送商品数量限额</div>
    </div>
    <div className="Frame3465228 pl-1 pr-2.5 py-1 left-[289px] top-[14px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className=" left-[153px] top-[76px] absolute text-black/20 text-sm font-medium font-['PingFang SC']">暂无商品</div>
  </div>
  <div className="Frame3465424 h-20 px-6 left-[16px] top-[1480px] absolute bg-white rounded-[10px] border justify-center items-start gap-[161px] inline-flex">
    <div className="Frame3465356 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">同级可重复赠送</div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[5px] pr-[23px] py-[5px] bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">返回</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465207 h-[852px] pt-[378px] left-0 top-0 absolute bg-black/60 flex-col justify-end items-start inline-flex">
    <div className="Frame3465208 w-[393px] h-[453px] relative bg-white rounded-tl-[20px] rounded-tr-[20px] flex-col justify-start items-start flex">
      <div className="Frame3465209 w-9 h-9 relative bg-neutral-100 rounded-[18px]" />
      <div className=" text-center text-black text-lg font-medium font-['PingFang SC']">赠品组配置</div>
      <div className="Frame3465220 w-[393px] pl-6 pr-2.5 py-6 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465283 h-[22px] justify-between items-center flex">
          <div className="1 text-right text-black text-base font-medium font-['PingFang SC']">赠品组 1</div>
          <div className="Frame3465233 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px]" />
        </div>
      </div>
      <div className="Frame3465219 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465211 w-[393px] h-[70px] pl-6 pr-[306px] py-6 justify-start items-center inline-flex">
        <div className="2 text-black text-base font-medium font-['PingFang SC']">赠品组 2</div>
      </div>
      <div className="Frame3465221 w-[345px] h-px relative bg-black/5" />
      <div className="Frame3465222 w-[393px] h-[70px] pl-6 pr-[306px] py-6 justify-start items-center inline-flex">
        <div className="3 text-black text-base font-medium font-['PingFang SC']">赠品组 3</div>
      </div>
    </div>
    <div className="HomeIndicator self-stretch h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>