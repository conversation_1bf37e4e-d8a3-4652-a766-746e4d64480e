<div className=" w-[393px] h-[7325px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">收银用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[7304px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[259px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">收银用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[6692px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[18px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[148px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[278px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[76px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[206px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[336px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[29px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame3465244 p-1 left-[16px] top-[62px] absolute bg-black/5 rounded-[10px] justify-center items-center inline-flex">
      <div className="Frame3465248 self-stretch justify-start items-center gap-1 inline-flex">
        <div className="Frame3465248 px-4 pt-1.5 pb-[5px] rounded-md justify-center items-center flex">
          <div className=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">模块权限</div>
        </div>
        <div className="Frame3465246 px-4 pt-1.5 pb-[5px] bg-[#5855ff] rounded-md justify-center items-center flex">
          <div className=" text-center text-white text-xs font-medium font-['PingFang SC']">业务权限</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051624 left-[16px] top-[131px] absolute justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051621 px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-start items-center gap-2.5 flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">开台权限</div>
      </div>
      <div className="Frame2090051622 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">预订权限</div>
      </div>
      <div className="Frame2090051624 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">商品权限</div>
      </div>
      <div className="Frame2090051625 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">结账权限</div>
      </div>
      <div className="Frame2090051626 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">会员权限</div>
      </div>
    </div>
    <div className="Frame3465335 left-[20px] top-[190px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">开台权限</div>
    </div>
    <div className="Frame2090051671 left-[20px] top-[3346px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">预订权限</div>
    </div>
    <div className=" left-[20px] top-[3597px] absolute text-black text-base font-medium font-['PingFang SC']">商品权限</div>
    <div className=" left-[20px] top-[3966px] absolute text-black text-base font-medium font-['PingFang SC']">结账权限</div>
    <div className=" left-[20px] top-[4453px] absolute text-black text-base font-medium font-['PingFang SC']">会员权限</div>
    <div className=" left-[20px] top-[5192px] absolute text-black text-base font-medium font-['PingFang SC']">交班权限</div>
    <div className=" left-[20px] top-[5443px] absolute text-black text-base font-medium font-['PingFang SC']">存取酒权限</div>
    <div className=" left-[20px] top-[6103px] absolute text-black text-base font-medium font-['PingFang SC']">活动(拼团)权限</div>
    <div className=" left-[20px] top-[6394px] absolute text-black text-base font-medium font-['PingFang SC']">其他权限</div>
    <div className=" left-[20px] top-[5911px] absolute text-black text-base font-medium font-['PingFang SC']">落单权限</div>
    <div className="Frame2090051625 w-[329px] p-2 left-[16px] top-[228px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465334 w-[329px] p-2 left-[16px] top-[228px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051672 w-[329px] p-2 left-[16px] top-[3384px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051673 w-[329px] p-2 left-[16px] top-[3635px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051675 w-[329px] p-2 left-[16px] top-[4491px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051676 w-[329px] p-2 left-[16px] top-[5230px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051677 w-[329px] p-2 left-[16px] top-[5481px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051674 w-[329px] p-2 left-[16px] top-[4004px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051679 w-[329px] p-2 left-[16px] top-[6141px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051680 w-[329px] p-2 left-[16px] top-[6432px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051678 w-[329px] p-2 left-[16px] top-[5949px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051626 w-[329px] h-[113px] left-[16px] top-[300px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">开台</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">含开台、预订开台、机器人领客</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051627 w-[329px] h-[98px] left-[16px] top-[429px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">取消开台</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[57px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[57px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051653 w-[329px] h-[98px] left-[16px] top-[1168px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">并房</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[57px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[57px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051654 w-[329px] h-[98px] left-[16px] top-[1278px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">续房</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[57px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[57px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051657 w-[329px] h-[98px] left-[16px] top-[1638px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">联台</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[57px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[57px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051660 w-[329px] h-[98px] left-[16px] top-[1998px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">消费明细</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[57px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051665 w-[329px] h-[98px] left-[16px] top-[2608px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">关房</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[57px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051667 w-[329px] h-[98px] left-[16px] top-[2843px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">开台自定义低消金额修改</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[57px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051628 w-[329px] h-[113px] left-[16px] top-[543px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">结账</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">后结或挂单的结账</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可选择</div>
      </div>
    </div>
    <div className="Frame2090051649 w-[329px] h-[113px] left-[16px] top-[668px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black text-sm font-medium font-['PingFang SC']">重开</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">开台单重开</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051650 w-[329px] h-[113px] left-[16px] top-[793px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">账单还原</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">还原已结账单</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051651 w-[329px] h-[113px] left-[16px] top-[918px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">换房换套餐</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">换房换套餐含换房、换套餐、互换</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051652 w-[329px] h-[113px] left-[16px] top-[1043px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">带客</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">带客、取消带客</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051655 w-[329px] h-[113px] left-[16px] top-[1388px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">包厢锁房</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包厢锁房</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051656 w-[329px] h-[113px] left-[16px] top-[1513px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">包厢解锁</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">对已锁包厢解锁</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051658 w-[329px] h-[113px] left-[16px] top-[1748px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">取消联台</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">对已联台包厢取消联台</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051662 w-[329px] h-[113px] left-[16px] top-[2233px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">免授权赠送时长</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">赠送时长时，无需授权密码</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051666 w-[329px] h-[113px] left-[16px] top-[2718px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">跨包厢计费调整</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">开台支持跨包厢计费</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051668 w-[329px] h-[113px] left-[16px] top-[2953px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">消费打印</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">打印消费明细</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051669 w-[329px] h-[113px] left-[16px] top-[3078px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">续房退单</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可退包厢续房订单及例点、例赠，不可单独退例点、例赠</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051664 w-[329px] h-[113px] left-[16px] top-[2483px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">免授权赠送商品</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">赠送商品时，无需授权密码</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
    <div className="Frame2090051661 w-[329px] h-[113px] left-[16px] top-[2108px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">赠送时长</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包厢赠送时长</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[72px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051663 w-[329px] h-[113px] left-[16px] top-[2358px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">赠送商品</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包厢赠送商品</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[72px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051670 w-[329px] h-[113px] left-[16px] top-[3203px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">客户信息可见</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看预订、开台客户完整手机号</div>
      </div>
      <div className="Frame2090051661 w-[304px] left-[12px] top-[72px] absolute justify-between items-center inline-flex">
        <div className="Frame2090051622 h-[29px] px-2.5 py-1.5 opacity-40 bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可操作</div>
        </div>
        <div className="Frame2090051621 h-[29px] px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
          <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可见</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051659 w-[329px] h-[113px] left-[16px] top-[1873px] absolute rounded-md border border-black/5">
      <div className="Frame2090051630 left-[12px] top-[12px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black text-sm font-medium font-['PingFang SC']">故障</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">故障、解除故障</div>
      </div>
      <div className="Frame2090051621 w-[150px] px-2.5 py-1.5 left-[12px] top-[72px] absolute bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">可操作</div>
      </div>
      <div className="Frame2090051622 w-[150px] px-2.5 py-1.5 left-[166px] top-[72px] absolute bg-[#f2f2f2] rounded-lg justify-center items-center gap-2.5 inline-flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">可见</div>
      </div>
    </div>
  </div>
  <div className="Frame3465229 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3928px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">场所代订</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">收银机代订</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051630 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4179px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品退单</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含已结、未结商品退单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051638 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4548px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">减免</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单减免</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051649 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5035px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">会员卡修改</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">基本信息修改</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051639 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4666px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">减免超免差额</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">未达到额度以超免结账</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051634 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4297px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品置换</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">商品置换</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051650 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[32px] top-[5153px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">补卡</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051640 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4607px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">免单</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单免单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051628 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[3987px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">代订人修改</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">修改开台单代订人</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051641 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4725px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">强制结账</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包厢结账失败可强制结账</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051631 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4238px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品沽清</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">商品沽清</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051651 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5094px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">绑定</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">含绑定、解绑、换绑 </div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051642 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4548px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">折扣</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单折扣</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051635 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4356px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品退库确认</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">商品退库确认</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051643 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4666px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">结账修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">修改已结账单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051652 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[32px] top-[5212px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">卡等级调整</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051667 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5774px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">代交班</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可代他人交班</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051671 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[32px] top-[6025px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">存酒权限</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051681 w-[161px] h-[72px] left-[32px] top-[6685px] absolute bg-[#5855ff] rounded-md border">
    <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">过期核销</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">拼团、次卡、积分商品过期后有权限可以核销</div>
    </div>
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute bg-white rounded-sm" />
  </div>
  <div className="Frame2090051687 w-[161px] h-[72px] left-[32px] top-[6764px] absolute bg-[#5855ff] rounded-md border">
    <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">积分商品订单核销</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">支持收银机上无需核销码核销积分商品订单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute bg-white rounded-sm" />
  </div>
  <div className="Frame2090051686 w-[161px] h-[72px] left-[200px] top-[6685px] absolute bg-[#5855ff] rounded-md border">
    <div className="Frame2090051630 h-[35px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">预售退单</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">拼团退单、分销商品退单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute bg-white rounded-sm" />
  </div>
  <div className="Frame2090051684 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[6976px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className="M1 self-stretch text-white text-sm font-medium font-['PingFang SC']">M1卡初始化</div>
      <div className="M1 self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">初始化M1可读写卡</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051680 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[6493px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">落单全见</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含所有落单查看</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
  <div className="Frame2090051675 w-[161px] h-[92px] left-[32px] top-[6143px] absolute bg-[#5855ff] rounded-md border">
    <div className="Frame2090051630 h-[70px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">存取酒作废、过期取酒</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可作废他人存取酒记录,可取过期存酒</div>
    </div>
    <div className="Frame3465232 w-4 h-4 left-[133px] top-[14px] absolute bg-white rounded-sm" />
  </div>
  <div className="Frame3465230 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3928px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">收银机代订人</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">收银机上可选为代订人</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051644 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4607px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">挂账</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单挂账</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051632 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4179px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">未结商品退单</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">只能退未结商品退单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051645 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4725px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">减免低消差额</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">未达到低消以消费结账</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051647 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[200px] top-[4784px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">挂账归还</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051646 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4784px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">销售员修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">修改订单销售员</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051653 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[200px] top-[5035px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员卡密码修改</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051636 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4297px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品变更包厢</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可变更包厢</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051654 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5153px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">冻结</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">含冻结、解冻</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051629 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[3987px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">预订退款</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">在线预订退款</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051633 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4238px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品销售统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看收银机商品销售统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051655 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5094px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">挂失</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">含挂失、撤销挂失</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051637 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[4356px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">收银机销售员</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">收银机上可选为销售员</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051672 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[200px] top-[6025px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">取酒权限</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051656 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[200px] top-[5212px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">转账</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051668 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5774px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">交班历史</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">查看本人日结账交班历史</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051676 w-[161px] h-[92px] left-[200px] top-[6143px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[18px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[65px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">取酒超管权限</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含取酒、作废、过期、替换、超过期不可取天数的存酒支取</div>
    </div>
  </div>
  <div className="Frame2090051673 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[200px] top-[6084px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">取酒替换</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051657 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5271px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">自定义充值赠金</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">定义赠送金额及赠送类型</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051674 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[6084px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存酒续存</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">即将过期的存酒进行续存</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051669 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5833px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">历史单据打印</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查询和打印历史单据</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051677 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[6242px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存取酒统计</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查询存取酒统计</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051661 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5429px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">次卡修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">次卡基本信息修改</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051658 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[32px] top-[5271px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">自定义充值本金</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051670 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5833px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">交班历史全见</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">日结总账他人交班历史</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051678 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[6242px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">存酒信息修改</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">存酒手机号修改等</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051679 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[6301px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存取酒历史</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查询存取酒历史</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051662 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5429px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">次卡开卡</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">次卡开卡</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051659 w-[161px] h-[92px] left-[200px] top-[5330px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[70px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">充值、制卡、续卡退单</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">会员卡充值、制卡、续卡退单、充负数</div>
    </div>
  </div>
  <div className="Frame2090051663 pl-3 pr-3.5 pt-[9px] pb-2 left-[200px] top-[5488px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">优惠券作废</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">优惠券作废、还原</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051665 w-[161px] h-[87px] left-[200px] top-[5547px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[18px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[35px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员信息可见</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看会员详细信息</div>
    </div>
  </div>
  <div className="Frame2090051660 w-[161px] h-[92px] left-[32px] top-[5330px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[70px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">自定义本金赠金支付</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">会员卡自定义本金赠金支付金额</div>
    </div>
  </div>
  <div className="Frame2090051664 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[5488px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">积分变更</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">积分变更</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame2090051666 w-[161px] h-[87px] left-[32px] top-[5547px] absolute rounded-md border border-black/10">
    <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
    <div className="Frame2090051630 h-[65px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">会员模糊查询</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">充值、开台、结账优惠、结账支付、存取酒支持会员模糊查询</div>
    </div>
  </div>
  <div className="Frame2090051648 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[4843px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">现金退单</div>
      <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">线上支付现金退单</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[7204px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465337 left-[36px] top-[7058px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">高级权限</div>
  </div>
  <div className="Frame2090051685 pl-3 pr-3.5 pt-[9px] pb-2 left-[32px] top-[7096px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
    <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">免验证取酒</div>
      <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">取酒不需要验证</div>
    </div>
    <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
  </div>
</div>