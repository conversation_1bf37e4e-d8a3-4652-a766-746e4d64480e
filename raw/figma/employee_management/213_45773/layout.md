```scss
ROOT {
  display: vertical;

  A {
    display: group;
    A1 { type: text; content: "角色名称"; }
    A2 { type: input; content: "收银用户角色"; }
  }

  B {
    display: group;
    B1 { type: text; content: "员工类型"; }
    
    BA {
      display: horizontal;
      BA1 { type: button; content: "手机认证"; }
      BA2 { type: button; content: "工号添加"; }
    }
  }

  C {
    display: horizontal;
    C1 { type: text; content: "可点单商品类型"; }
    C2 { type: text; content: "全部"; }
  }

  D {
    display: vertical;
    
    DA {
      display: horizontal;
      DA1 { type: text; content: "收银机"; }
      DA2 { type: text; content: "汇金掌柜"; }
      DA3 { type: text; content: "招财猫"; }
      DA4 { type: text; content: "移动点单"; }
      DA5 { type: text; content: "点单屏"; }
      DA6 { type: text; content: "移动点单"; }
    }

    DB {
      display: horizontal;
      DB1 { type: button; content: "首页"; }
      DB2 { type: button; content: "财务对账"; }
      DB3 { type: button; content: "提成统计"; }
      DB4 { type: button; content: "营业分析"; }
      DB5 { type: button; content: "经营分析"; }
    }

    DC {
      display: group;
      DC1 { type: text; content: "首页"; }
      
      DCA {
        display: horizontal;
        DCA1 { type: button; content: "全开启"; }
        DCA2 { type: button; content: "自定义"; }
        DCA3 { type: button; content: "全关闭"; }
      }

      DCB {
        display: vertical;
        visible: when(#DCA2.selected);
        
        DCB1 {
          display: horizontal;
          DCB11 { type: text; content: "概览"; }
          DCB12 { type: text; content: "查看概览"; }
          DCB13 { type: checkbox; }
        }

        DCB2 {
          display: horizontal;
          DCB21 { type: text; content: "营业日报"; }
          DCB22 { type: text; content: "查看营业日报"; }
          DCB23 { type: checkbox; }
        }

        DCB3 {
          display: horizontal;
          DCB31 { type: text; content: "商品销售统计"; }
          DCB32 { type: text; content: "查看商品销售统计"; }
          DCB33 { type: checkbox; }
        }
      }
    }

    DD {
      display: group;
      DD1 { type: text; content: "财务对账"; }
      
      DDA {
        display: horizontal;
        DDA1 { type: button; content: "全开启"; }
        DDA2 { type: button; content: "自定义"; }
        DDA3 { type: button; content: "全关闭"; }
      }

      DDB {
        display: vertical;
        visible: when(#DDA2.selected);
        
        DDB1 {
          display: horizontal;
          DDB11 { type: text; content: "会员报表"; }
          DDB12 { type: text; content: "查看会员报表"; }
          DDB13 { type: checkbox; }
        }

        DDB2 {
          display: horizontal;
          DDB21 { type: text; content: "挂账明细"; }
          DDB22 { type: text; content: "查看挂账明细"; }
          DDB23 { type: checkbox; }
        }

        DDB3 {
          display: horizontal;
          DDB31 { type: text; content: "进销存报表"; }
          DDB32 { type: text; content: "查看进销存报表"; }
          DDB33 { type: checkbox; }
        }
      }
    }

    DE {
      display: group;
      DE1 { type: text; content: "提成统计"; }
      
      DEA {
        display: horizontal;
        DEA1 { type: button; content: "全开启"; }
        DEA2 { type: button; content: "自定义"; }
        DEA3 { type: button; content: "全关闭"; }
      }

      DEB {
        display: vertical;
        visible: when(#DEA2.selected);
        
        DEB1 {
          display: horizontal;
          DEB11 { type: text; content: "券包销售报表"; }
          DEB12 { type: text; content: "查看券包销售报表"; }
          DEB13 { type: checkbox; }
        }

        DEB2 {
          display: horizontal;
          DEB21 { type: text; content: "退单报表"; }
          DEB22 { type: text; content: "查看退单报表"; }
          DEB23 { type: checkbox; }
        }

        DEB3 {
          display: horizontal;
          DEB31 { type: text; content: "异常单报表"; }
          DEB32 { type: text; content: "查看异常单报表"; }
          DEB33 { type: checkbox; }
        }
      }
    }

    DF {
      display: group;
      DF1 { type: text; content: "营业分析"; }
      
      DFA {
        display: horizontal;
        DFA1 { type: button; content: "全开启"; }
        DFA2 { type: button; content: "自定义"; }
        DFA3 { type: button; content: "全关闭"; }
      }

      DFB {
        display: vertical;
        visible: when(#DFA2.selected);
        
        DFB1 {
          display: horizontal;
          DFB11 { type: text; content: "现抽统计"; }
          DFB12 { type: text; content: "查看现抽统计"; }
          DFB13 { type: checkbox; }
        }

        DFB2 {
          display: horizontal;
          DFB21 { type: text; content: "落单明细"; }
          DFB22 { type: text; content: "查看落单明细"; }
          DFB23 { type: checkbox; }
        }

        DFB3 {
          display: horizontal;
          DFB31 { type: text; content: "办卡储值"; }
          DFB32 { type: text; content: "查看办卡储值"; }
          DFB33 { type: checkbox; }
        }
      }
    }

    DG {
      display: group;
      DG1 { type: text; content: "经营分析"; }
      
      DGA {
        display: horizontal;
        DGA1 { type: button; content: "全开启"; }
        DGA2 { type: button; content: "自定义"; }
        DGA3 { type: button; content: "全关闭"; }
      }

      DGB {
        display: vertical;
        visible: when(#DGA2.selected);
        
        DGB1 {
          display: horizontal;
          DGB11 { type: text; content: "会员管理"; }
          DGB12 { type: text; content: "查看会员管理"; }
          DGB13 { type: checkbox; }
        }

        DGB2 {
          display: horizontal;
          DGB21 { type: text; content: "分销管理"; }
          DGB22 { type: text; content: "查看分销管理"; }
          DGB23 { type: checkbox; }
        }

        DGB3 {
          display: horizontal;
          DGB31 { type: text; content: "服务开通"; }
          DGB32 { type: text; content: "查看服务开通"; }
          DGB33 { type: checkbox; }
        }
      }
    }

    DH {
      display: group;
      DH1 { type: text; content: "会员管理"; }
      
      DHA {
        display: horizontal;
        DHA1 { type: button; content: "全开启"; }
        DHA2 { type: button; content: "自定义"; }
        DHA3 { type: button; content: "全关闭"; }
      }

      DHB {
        display: vertical;
        visible: when(#DHA2.selected);
        
        DHB1 {
          display: horizontal;
          DHB11 { type: text; content: "会员列表"; }
          DHB12 { type: text; content: "查看会员列表"; }
          DHB13 { type: checkbox; }
        }

        DHB2 {
          display: horizontal;
          DHB21 { type: text; content: "会员等级"; }
          DHB22 { type: text; content: "查看会员等级"; }
          DHB23 { type: checkbox; }
        }

        DHB3 {
          display: horizontal;
          DHB31 { type: text; content: "会员卡"; }
          DHB32 { type: text; content: "查看会员卡"; }
          DHB33 { type: checkbox; }
        }
      }
    }

    DI {
      display: group;
      DI1 { type: text; content: "分销管理"; }
      
      DIA {
        display: horizontal;
        DIA1 { type: button; content: "全开启"; }
        DIA2 { type: button; content: "自定义"; }
        DIA3 { type: button; content: "全关闭"; }
      }

      DIB {
        display: vertical;
        visible: when(#DIA2.selected);
        
        DIB1 {
          display: horizontal;
          DIB11 { type: text; content: "分销商管理"; }
          DIB12 { type: text; content: "查看分销商管理"; }
          DIB13 { type: checkbox; }
        }

        DIB2 {
          display: horizontal;
          DIB21 { type: text; content: "分销订单"; }
          DIB22 { type: text; content: "查看分销订单"; }
          DIB23 { type: checkbox; }
        }
      }
    }

    DJ {
      display: group;
      DJ1 { type: text; content: "服务开通"; }
      
      DJA {
        display: horizontal;
        DJA1 { type: button; content: "全开启"; }
        DJA2 { type: button; content: "自定义"; }
        DJA3 { type: button; content: "全关闭"; }
      }

      DJB {
        display: vertical;
        visible: when(#DJA2.selected);
        
        DJB1 {
          display: horizontal;
          DJB11 { type: text; content: "服务列表"; }
          DJB12 { type: text; content: "查看服务列表"; }
          DJB13 { type: checkbox; }
        }

        DJB2 {
          display: horizontal;
          DJB21 { type: text; content: "开通记录"; }
          DJB22 { type: text; content: "查看开通记录"; }
          DJB23 { type: checkbox; }
        }
      }
    }

    DK {
      display: group;
      DK1 { type: text; content: "短信管理"; }
      
      DKA {
        display: horizontal;
        DKA1 { type: button; content: "全开启"; }
        DKA2 { type: button; content: "自定义"; }
        DKA3 { type: button; content: "全关闭"; }
      }

      DKB {
        display: vertical;
        visible: when(#DKA2.selected);
        
        DKB1 {
          display: horizontal;
          DKB11 { type: text; content: "短信记录"; }
          DKB12 { type: text; content: "查看短信记录"; }
          DKB13 { type: checkbox; }
        }

        DKB2 {
          display: horizontal;
          DKB21 { type: text; content: "短信模板"; }
          DKB22 { type: text; content: "查看短信模板"; }
          DKB23 { type: checkbox; }
        }
      }
    }

    DL {
      display: group;
      DL1 { type: text; content: "库存管理"; }
      
      DLA {
        display: horizontal;
        DLA1 { type: button; content: "全开启"; }
        DLA2 { type: button; content: "自定义"; }
        DLA3 { type: button; content: "全关闭"; }
      }

      DLB {
        display: vertical;
        visible: when(#DLA2.selected);
        
        DLB1 {
          display: horizontal;
          DLB11 { type: text; content: "库存查询"; }
          DLB12 { type: text; content: "查看库存查询"; }
          DLB13 { type: checkbox; }
        }

        DLB2 {
          display: horizontal;
          DLB21 { type: text; content: "入库管理"; }
          DLB22 { type: text; content: "查看入库管理"; }
          DLB23 { type: checkbox; }
        }

        DLB3 {
          display: horizontal;
          DLB31 { type: text; content: "出库管理"; }
          DLB32 { type: text; content: "查看出库管理"; }
          DLB33 { type: checkbox; }
        }
      }
    }

    DM {
      display: group;
      DM1 { type: text; content: "包厢管理"; }
      
      DMA {
        display: horizontal;
        DMA1 { type: button; content: "全开启"; }
        DMA2 { type: button; content: "自定义"; }
        DMA3 { type: button; content: "全关闭"; }
      }

      DMB {
        display: vertical;
        visible: when(#DMA2.selected);
        
        DMB1 {
          display: horizontal;
          DMB11 { type: text; content: "包厢列表"; }
          DMB12 { type: text; content: "查看包厢列表"; }
          DMB13 { type: checkbox; }
        }

        DMB2 {
          display: horizontal;
          DMB21 { type: text; content: "包厢预订"; }
          DMB22 { type: text; content: "查看包厢预订"; }
          DMB23 { type: checkbox; }
        }
      }
    }

    DN {
      display: group;
      DN1 { type: text; content: "商品管理"; }
      
      DNA {
        display: horizontal;
        DNA1 { type: button; content: "全开启"; }
        DNA2 { type: button; content: "自定义"; }
        DNA3 { type: button; content: "全关闭"; }
      }

      DNB {
        display: vertical;
        visible: when(#DNA2.selected);
        
        DNB1 {
          display: horizontal;
          DNB11 { type: text; content: "商品列表"; }
          DNB12 { type: text; content: "查看商品列表"; }
          DNB13 { type: checkbox; }
        }

        DNB2 {
          display: horizontal;
          DNB21 { type: text; content: "商品分类"; }
          DNB22 { type: text; content: "查看商品分类"; }
          DNB23 { type: checkbox; }
        }

        DNB3 {
          display: horizontal;
          DNB31 { type: text; content: "商品规格"; }
          DNB32 { type: text; content: "查看商品规格"; }
          DNB33 { type: checkbox; }
        }
      }
    }

    DO {
      display: group;
      DO1 { type: text; content: "大玩家"; }
      
      DOA {
        display: horizontal;
        DOA1 { type: button; content: "全开启"; }
        DOA2 { type: button; content: "自定义"; }
        DOA3 { type: button; content: "全关闭"; }
      }

      DOB {
        display: vertical;
        visible: when(#DOA2.selected);
        
        DOB1 {
          display: horizontal;
          DOB11 { type: text; content: "游戏管理"; }
          DOB12 { type: text; content: "查看游戏管理"; }
          DOB13 { type: checkbox; }
        }

        DOB2 {
          display: horizontal;
          DOB21 { type: text; content: "游戏记录"; }
          DOB22 { type: text; content: "查看游戏记录"; }
          DOB23 { type: checkbox; }
        }
      }
    }

    DP {
      display: group;
      DP1 { type: text; content: "操作管理"; }
      
      DPA {
        display: horizontal;
        DPA1 { type: button; content: "全开启"; }
        DPA2 { type: button; content: "自定义"; }
        DPA3 { type: button; content: "全关闭"; }
      }

      DPB {
        display: vertical;
        visible: when(#DPA2.selected);
        
        DPB1 {
          display: horizontal;
          DPB11 { type: text; content: "操作日志"; }
          DPB12 { type: text; content: "查看操作日志"; }
          DPB13 { type: checkbox; }
        }
      }
    }

    DQ {
      display: group;
      DQ1 { type: text; content: "员工管理"; }
      
      DQA {
        display: horizontal;
        DQA1 { type: button; content: "全开启"; }
        DQA2 { type: button; content: "自定义"; }
        DQA3 { type: button; content: "全关闭"; }
      }

      DQB {
        display: vertical;
        visible: when(#DQA2.selected);
        
        DQB1 {
          display: horizontal;
          DQB11 { type: text; content: "员工列表"; }
          DQB12 { type: text; content: "查看员工列表"; }
          DQB13 { type: checkbox; }
        }

        DQB2 {
          display: horizontal;
          DQB21 { type: text; content: "角色管理"; }
          DQB22 { type: text; content: "查看角色管理"; }
          DQB23 { type: checkbox; }
        }
      }
    }
  }
}
```