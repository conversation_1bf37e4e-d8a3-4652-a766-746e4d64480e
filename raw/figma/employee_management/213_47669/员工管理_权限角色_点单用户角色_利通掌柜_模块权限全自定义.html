<div className=" w-[393px] h-[6633px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">点单用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[6612px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[259px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">收银用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[6000px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[18px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[148px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[278px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[76px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[206px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[336px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[94px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame3465244 p-1 left-[16px] top-[62px] absolute bg-black/5 rounded-[10px] justify-center items-center inline-flex">
      <div className="Frame3465248 self-stretch justify-start items-center gap-1 inline-flex">
        <div className="Frame3465246 px-4 pt-1.5 pb-[5px] bg-[#5855ff] rounded-md justify-center items-center flex">
          <div className=" text-center text-white text-xs font-medium font-['PingFang SC']">模块权限</div>
        </div>
        <div className="Frame3465248 px-4 pt-1.5 pb-[5px] rounded-md justify-center items-center flex">
          <div className=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">业务权限</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051624 left-[16px] top-[131px] absolute justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051621 px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">门店管理</div>
      </div>
      <div className="Frame2090051622 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">收银管理</div>
      </div>
      <div className="Frame2090051623 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">线上经营</div>
      </div>
      <div className="Frame2090051624 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">包厢管理</div>
      </div>
      <div className="Frame2090051625 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">商品管理</div>
      </div>
    </div>
    <div className="Frame2090051642 left-[20px] top-[4042px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">营业分析</div>
    </div>
    <div className="Frame2090051758 left-[20px] top-[4603px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">营销管理</div>
    </div>
    <div className="Frame2090051761 left-[20px] top-[4795px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">实时包厢管理</div>
    </div>
    <div className="Frame2090051749 left-[20px] top-[4234px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">业绩管理</div>
    </div>
    <div className="Frame3465335 left-[20px] top-[190px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">预订权限</div>
    </div>
    <div className="Frame2090051641 left-[20px] top-[2699px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">会员管理</div>
    </div>
    <div className="Frame2090051644 left-[20px] top-[5180px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">订单管理</div>
    </div>
    <div className="Frame2090051632 left-[20px] top-[1474px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">包厢管理</div>
    </div>
    <div className="Frame2090051645 left-[20px] top-[3319px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">库存管理</div>
    </div>
    <div className="Frame2090051646 left-[20px] top-[5392px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">排队管理</div>
    </div>
    <div className="Frame2090051776 left-[20px] top-[5643px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">平台对接服务管理</div>
    </div>
    <div className="Frame2090051626 left-[20px] top-[677px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">收银管理</div>
    </div>
    <div className="Frame2090051643 left-[20px] top-[3068px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">优惠券管理</div>
    </div>
    <div className="Frame2090051633 left-[20px] top-[1843px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">商品管理</div>
    </div>
    <div className="Frame2090051628 left-[20px] top-[1164px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">线上经营</div>
    </div>
    <div className="Frame2090051634 left-[20px] top-[2389px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">员工管理</div>
    </div>
    <div className="Frame3465337 left-[20px] top-[5835px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">个人工台</div>
    </div>
    <div className="Frame3465229 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[5873px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">业绩排行</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">个人工台查看业绩排行</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465231 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[5932px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">客户存酒</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">个人工台查看客户存酒</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465230 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[5873px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">提成业绩</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">个人工台查看提成业绩</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465334 w-[329px] p-2 left-[16px] top-[228px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051665 w-[329px] p-2 left-[16px] top-[715px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051680 w-[329px] p-2 left-[16px] top-[1202px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051686 w-[329px] p-2 left-[16px] top-[1512px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051694 w-[329px] p-2 left-[16px] top-[1881px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051709 w-[329px] p-2 left-[16px] top-[2427px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051715 w-[329px] p-2 left-[16px] top-[2737px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051723 w-[329px] p-2 left-[16px] top-[3106px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051759 w-[329px] p-2 left-[16px] top-[4641px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051775 w-[329px] p-2 left-[16px] top-[5430px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051769 w-[329px] p-2 left-[16px] top-[5218px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051777 w-[329px] p-2 left-[16px] top-[5681px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051747 w-[329px] p-2 left-[16px] top-[4080px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051762 w-[329px] p-2 left-[16px] top-[4833px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051727 w-[329px] p-2 left-[16px] top-[3357px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051750 w-[329px] p-2 left-[16px] top-[4272px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame2090051650 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[300px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">门店配置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051657 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[359px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">收银机</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051658 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[300px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">节假日设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051656 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[359px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">内网穿透服务</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051659 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[418px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">门店二维码</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051661 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[477px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">消息提醒</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051663 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[536px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务市场</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051666 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[595px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">数据清理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051660 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[418px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">施工协助</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051664 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[536px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">短信服务</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051662 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[477px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商家服务</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051667 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[595px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">手机号黑名单</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051668 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[787px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">经营设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051669 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[846px] absolute bg-[#5855ff] rounded-md border justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">网络收款</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051670 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[787px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">支付方式</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051671 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[846px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">打印单据</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051672 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[905px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">挂账单位</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051687 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1584px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢新增</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051695 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1953px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品新增</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051716 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2809px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工信息</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051728 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3429px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">库存分析</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051734 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3606px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">自动调拨</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051751 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[4344px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">业绩排行</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051772 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[5502px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">号段规则</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051778 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[5753px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">抖音平台</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051752 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[4521px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务奖赏报表</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051740 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3783px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">库存设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051710 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2499px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工信息</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051724 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3178px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工信息</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051701 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2130px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">时段管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051688 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1643px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢类型</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051729 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3488px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">出入库类型</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051717 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2868px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">权限角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051753 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[4403px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">现抽设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051696 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2012px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品类型</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051773 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[5561px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">排号打印模板</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051735 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3665px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">续存记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051741 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3842px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">叫货档口</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051711 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2558px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">权限角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051673 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[964px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">客群标签</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051725 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3237px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">权限角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051702 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2189px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">出品管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051689 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1702px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢价格统计分类</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051697 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2071px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品统计分类</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051712 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2617px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营销角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051718 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2927px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营销角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051730 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3547px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">出库记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051681 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1274px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">线上预订</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051721 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2927px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营销角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051674 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1023px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">报表设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051754 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[4462px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">落单业绩支付方式</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051736 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3724px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">盘点记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051731 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3547px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">调拨记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051742 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3901px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">供应商管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051746 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[3960px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">报废记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051748 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[4152px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营业分析</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051722 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2986px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营销角色</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051755 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[4462px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务奖赏设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051737 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3724px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">成本调整</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051743 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3901px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">辅料管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051703 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2248px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存酒设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051707 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[2307px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">多商品累计买赠</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051708 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2307px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">取酒替换组</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051682 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1333px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">代订设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051675 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1082px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">常用备注</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051691 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1584px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢价格</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051732 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3429px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">仓库新增</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051719 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2809px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工分组</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051690 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1761px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢二维码</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051692 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1702px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢区域</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051756 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[4344px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">业绩报表</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051698 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1953px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">套餐新增</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051713 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2499px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工分组</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051774 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[5502px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">叫号短语</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051738 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3606px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">存酒记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051744 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3783px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">叫货模板</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051683 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[16px] top-[1392px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">服务评价</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051676 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[905px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">客户来源</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051693 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1643px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢主题</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051726 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3178px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">员工分组</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051704 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2130px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">口味管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051677 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1023px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">包厢显示设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051700 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2012px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">套餐类型</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051733 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3488px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">入库记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051720 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2868px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">赠送额度临时调整</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051714 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2558px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">赠送额度临时调整</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051699 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2071px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品展示分类</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051705 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2248px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">赠品管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051684 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1333px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">外送设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051678 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[964px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">营业额重算</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051757 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[4403px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">现抽报表</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051739 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3665px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">充公记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051745 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[3842px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">叫货记录</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051706 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[2189px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品绑定</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051685 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1274px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">线上经营设置</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051679 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1082px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">云打印机</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051760 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[4713px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">营销管理</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含分销、提成等管理</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051763 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[4905px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">全部可见</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含所有实时包厢查看</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame2090051764 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[4905px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">空闲包厢可见</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看空闲包厢</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051770 w-[161px] h-[72px] left-[16px] top-[5290px] absolute bg-[#5855ff] rounded-md border">
      <div className="Frame2090051630 h-[35px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">历史订单全见</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含所有历史订单查看</div>
      </div>
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute bg-white rounded-sm" />
    </div>
    <div className="Frame2090051771 w-[161px] h-[72px] left-[184px] top-[5290px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11.50px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">历史订单</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">只可查看代订人或销售员为本人的历史订单</div>
      </div>
      <div className="Frame2090051631 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">历史订单</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">只可查看代订人或销售员为本人的历史订单</div>
      </div>
    </div>
    <div className="Frame2090051766 w-[161px] h-[87px] left-[16px] top-[4964px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[65px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">代订人可见</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">仅能查看代订人或轮房人为本人的包厢、组长可查看组员的代订的包厢</div>
      </div>
    </div>
    <div className="Frame2090051768 w-[161px] h-[87px] left-[184px] top-[4964px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">代订人非本人可见</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看代订人或轮房人非本人的包厢仅能看到房态</div>
      </div>
    </div>
    <div className="Frame2090051767 w-[161px] h-[92px] left-[16px] top-[5058px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[70px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">无代订人和轮房人可见</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看无代订人和轮房人的包厢</div>
      </div>
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[6512px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>