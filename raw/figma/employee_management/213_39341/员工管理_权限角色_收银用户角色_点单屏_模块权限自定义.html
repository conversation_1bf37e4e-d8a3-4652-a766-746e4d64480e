<div className=" w-[393px] h-[907px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">收银用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[886px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[259px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">收银用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[274px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[-31px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[99px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[229px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[27px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[157px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[287px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">权限额度</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[240px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame3465334 w-[329px] p-2 left-[16px] top-[114px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465335 left-[20px] top-[76px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">模块权限</div>
    </div>
    <div className="Frame3465356 w-[161px] h-[72px] left-[16px] top-[186px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">包厢点单用户权限</div>
        <div className="Vod self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包含包厢点单屏、VOD点单权限</div>
      </div>
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[786px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>