<div className=" w-[393px] h-[3946px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">咨客用户角色</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[3925px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465291 w-[361px] pl-[18px] pr-[247px] pt-[18px] pb-4 left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">咨客用户角色</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">角色名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">手机认证</div>
      </div>
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">工号添加</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">员工类型</div>
  </div>
  <div className="Frame3465305 w-[361px] h-20 left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-black text-base font-medium font-['PingFang SC']">全部</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可点单商品类型</div>
    </div>
  </div>
  <div className="Frame3465332 w-[361px] h-[3313px] left-[16px] top-[472px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className=" left-[18px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">收银机</div>
    <div className=" left-[148px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">招财猫</div>
    <div className=" left-[278px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
    <div className=" left-[76px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">汇金掌柜</div>
    <div className=" left-[206px] top-[18px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">移动点单</div>
    <div className=" left-[336px] top-[18px] absolute text-black/40 text-sm font-medium font-['PingFang SC']">权限额度</div>
    <div className="Rectangle462286875 w-5 h-0.5 left-[224px] top-[44px] absolute bg-[#5855ff] rounded-[7px]" />
    <div className="Frame3465244 p-1 left-[16px] top-[62px] absolute bg-black/5 rounded-[10px] justify-center items-center inline-flex">
      <div className="Frame3465248 self-stretch justify-start items-center gap-1 inline-flex">
        <div className="Frame3465248 px-4 pt-1.5 pb-[5px] rounded-md justify-center items-center flex">
          <div className=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">模块权限</div>
        </div>
        <div className="Frame3465246 px-4 pt-1.5 pb-[5px] bg-[#5855ff] rounded-md justify-center items-center flex">
          <div className=" text-center text-white text-xs font-medium font-['PingFang SC']">业务权限</div>
        </div>
      </div>
    </div>
    <div className="Frame3465335 left-[20px] top-[131px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">开台权限</div>
    </div>
    <div className="Frame3465343 left-[20px] top-[633px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">商品权限</div>
    </div>
    <div className="Frame3465348 left-[20px] top-[884px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">结账权限</div>
    </div>
    <div className="Frame3465353 left-[20px] top-[1135px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">会员权限</div>
    </div>
    <div className="Frame3465357 left-[20px] top-[1347px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">存取酒权限</div>
    </div>
    <div className="Frame3465361 left-[20px] top-[1539px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">活动(拼团)权限</div>
    </div>
    <div className="Frame3465364 left-[20px] top-[1751px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">高级权限</div>
    </div>
    <div className="Frame3465368 left-[20px] top-[1978px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">可见包厢权限</div>
    </div>
    <div className="Frame3465334 w-[329px] p-2 left-[16px] top-[169px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465344 w-[329px] p-2 left-[16px] top-[671px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465349 w-[329px] p-2 left-[16px] top-[922px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465354 w-[329px] p-2 left-[16px] top-[1173px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465358 w-[329px] p-2 left-[16px] top-[1385px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465362 w-[329px] p-2 left-[16px] top-[1577px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465365 w-[329px] p-2 left-[16px] top-[1789px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465369 w-[329px] p-2 left-[16px] top-[2016px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465340 left-[20px] top-[441px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">预订权限</div>
    </div>
    <div className="Frame3465341 w-[329px] p-2 left-[16px] top-[479px] absolute bg-[#f7f7ff] rounded-[10px] border justify-center items-center inline-flex">
      <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
        </div>
        <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
        </div>
        <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
          <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
        </div>
      </div>
    </div>
    <div className="Frame3465229 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[241px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">开台</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">开台立结、开台后结</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465345 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[743px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">商品点单</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">商品点单</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465350 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[994px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">减免</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单减免</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465355 h-[72px] pl-3 pr-3.5 pt-[11px] pb-[41px] left-[16px] top-[1245px] absolute bg-[#5855ff] rounded-md border justify-center items-end gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" text-white text-sm font-medium font-['PingFang SC']">自定义充值本金</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465359 pl-3 pr-3.5 py-4 left-[16px] top-[1457px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" text-white text-sm font-medium font-['PingFang SC']">存酒权限</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465342 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[551px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">场所代订</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">移动点单代订</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465338 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[359px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">关房</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">关房</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465231 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[300px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">换房换套餐</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">含换房、换套餐</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465346 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[802px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">赠送商品</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">包厢赠送商品</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465351 pl-3 pr-3.5 pt-[9px] pb-2 left-[16px] top-[1053px] absolute rounded-md border border-black/10 justify-center items-center gap-1.5 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">免单</div>
        <div className=" text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单免单</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465230 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[241px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">结账</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">后结或挂单的结账</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465347 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[743px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">商品退单</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">商品退单</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465352 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[994px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">折扣</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">账单折扣</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465360 pl-3 pr-3.5 pt-[16.50px] pb-[15.50px] left-[184px] top-[1457px] absolute rounded-md border border-black/10 justify-center items-start gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">取酒权限</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465356 w-[161px] h-[72px] left-[184px] top-[1245px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">自定义充值赠金</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">自定义充值赠送金额及赠送类型</div>
      </div>
    </div>
    <div className="Frame3465363 w-[161px] h-[72px] left-[16px] top-[1649px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">过期核销</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">拼团、次卡、积分商品过期后有权限可以核销</div>
      </div>
    </div>
    <div className="Frame3465366 w-[161px] h-[87px] left-[16px] top-[1861px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[50px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">免验证取酒</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">取酒时不需要短信或刷卡验证</div>
      </div>
    </div>
    <div className="Frame3465367 w-[161px] h-[87px] left-[184px] top-[1861px] absolute rounded-md border border-black/10">
      <div className="Frame3465232 w-4 h-4 left-[131px] top-[14px] absolute rounded-sm border border-black/20" />
      <div className="Frame2090051630 h-[65px] left-[12px] top-[11px] absolute flex-col justify-start items-start inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">会员模糊查询</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">充值、开台、结账优惠、结账支付、存取酒支持会员模糊查询</div>
      </div>
    </div>
    <div className="Frame3465337 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[359px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">代订人修改</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">修改开台单代订人</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465339 pl-3 pr-3.5 pt-[9px] pb-2 left-[184px] top-[300px] absolute rounded-md border border-black/10 justify-center items-center gap-[5px] inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">续房</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">续房</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame2090051779 left-[16px] top-[2084px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className="Frame2090051622 px-2.5 py-1.5 bg-black/5 rounded-lg justify-center items-center gap-2.5 flex">
        <div className=" text-black/30 text-xs font-medium font-['PingFang SC']">区域</div>
      </div>
      <div className="Frame2090051621 px-2.5 py-1.5 bg-[#5956ff]/5 rounded-lg justify-start items-center gap-2.5 flex">
        <div className=" text-[#5855ff] text-xs font-semibold font-['PingFang SC']">包厢类型</div>
      </div>
    </div>
    <div className="Frame19 p-4 left-0 top-[2117px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
      <div className="Frame3465256 self-stretch justify-start items-center inline-flex">
        <div className="A text-black text-sm font-medium font-['PingFang SC']">小包厢-A</div>
      </div>
      <div className="Frame2090051633 self-stretch h-[316px] flex-col justify-start items-start gap-2 inline-flex">
        <div className="Frame22 h-[100px] justify-center items-start gap-2 inline-flex">
          <div className="Frame20 grow shrink basis-0 self-stretch pl-3 pr-[67.33px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A01 text-white text-sm font-semibold font-['PingFang SC']">A01</div>
            <div className="A text-white text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame22 grow shrink basis-0 self-stretch pl-[11.66px] pr-[65.67px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A02 text-white text-sm font-semibold font-['PingFang SC']">A02</div>
            <div className="A text-white text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame23 grow shrink basis-0 self-stretch pl-[11.67px] pr-[65.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A03 text-black text-sm font-semibold font-['PingFang SC']">A03</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
        </div>
        <div className="Frame3465257 h-[100px] justify-center items-start gap-2 inline-flex">
          <div className="Frame20 grow shrink basis-0 self-stretch pl-3 pr-[65.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A05 text-black text-sm font-semibold font-['PingFang SC']">A05</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame22 grow shrink basis-0 self-stretch pl-[11.66px] pr-[65.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A06 text-black text-sm font-semibold font-['PingFang SC']">A06</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame23 grow shrink basis-0 self-stretch pl-[11.67px] pr-[66.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A07 text-black text-sm font-semibold font-['PingFang SC']">A07</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
        </div>
        <div className="Frame3465258 h-[100px] justify-center items-start gap-2 inline-flex">
          <div className="Frame20 grow shrink basis-0 self-stretch pl-3 pr-[65.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A08 text-black text-sm font-semibold font-['PingFang SC']">A08</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame22 grow shrink basis-0 self-stretch pl-[11.66px] pr-[65.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A09 text-black text-sm font-semibold font-['PingFang SC']">A09</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
          <div className="Frame23 grow shrink basis-0 self-stretch pl-[11.67px] pr-[67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="A10 text-black text-sm font-semibold font-['PingFang SC']">A10</div>
            <div className="A text-black/40 text-xs font-semibold font-['PingFang SC']">A 区</div>
          </div>
        </div>
      </div>
    </div>
    <div className="Frame24 p-4 left-0 top-[2521px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
      <div className="Frame3465256 self-stretch justify-start items-center inline-flex">
        <div className="B text-black text-sm font-medium font-['PingFang SC']">中包厢-B</div>
      </div>
      <div className="Frame2090051632 self-stretch h-[316px] flex-col justify-start items-start gap-2 inline-flex">
        <div className="Frame22 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[67.33px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B01 text-white text-sm font-medium font-['PingFang SC']">B01</div>
            <div className="B text-white text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[65.67px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B02 text-white text-sm font-medium font-['PingFang SC']">B02</div>
            <div className="B text-white text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.34px] pr-[66px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B03 text-black text-sm font-medium font-['PingFang SC']">B03</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
        </div>
        <div className="Frame3465257 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[65.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B04 text-black text-sm font-medium font-['PingFang SC']">B04</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[65.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B05 text-black text-sm font-medium font-['PingFang SC']">B05</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.34px] pr-[66px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B06 text-black text-sm font-medium font-['PingFang SC']">B06</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
        </div>
        <div className="Frame3465258 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[66.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B07 text-black text-sm font-medium font-['PingFang SC']">B07</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[65.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B08 text-black text-sm font-medium font-['PingFang SC']">B08</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.34px] pr-[66px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="B09 text-black text-sm font-medium font-['PingFang SC']">B09</div>
            <div className="B text-black/40 text-xs font-semibold font-['PingFang SC']">B 区</div>
          </div>
        </div>
      </div>
    </div>
    <div className="Frame25 p-4 left-0 top-[2925px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
      <div className="Frame3465256 self-stretch justify-start items-center inline-flex">
        <div className="C text-black text-sm font-medium font-['PingFang SC']">大包厢-C</div>
      </div>
      <div className="Frame2090051631 self-stretch h-[316px] flex-col justify-start items-start gap-2 inline-flex">
        <div className="Frame22 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[67.33px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C09 text-white text-sm font-semibold font-['PingFang SC']">C01</div>
            <div className="C text-white text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[64.67px] py-3 bg-[#5855ff] rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C08 text-white text-sm font-semibold font-['PingFang SC']">C02</div>
            <div className="C text-white text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.67px] pr-[64.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C07 text-black text-sm font-semibold font-['PingFang SC']">C03</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
        </div>
        <div className="Frame3465257 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[64.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C06 text-black text-sm font-semibold font-['PingFang SC']">C04</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[64.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C05 text-black text-sm font-semibold font-['PingFang SC']">C05</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.67px] pr-[64.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C04 text-black text-sm font-semibold font-['PingFang SC']">C06</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
        </div>
        <div className="Frame3465258 self-stretch h-[100px] justify-start items-start gap-2 inline-flex">
          <div className="Frame20 w-[104.33px] pl-3 pr-[65.33px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C03 text-black text-sm font-semibold font-['PingFang SC']">C07</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame22 w-[104.33px] pl-[11.66px] pr-[64.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C02 text-black text-sm font-semibold font-['PingFang SC']">C08</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
          <div className="Frame23 w-[104.33px] pl-[11.67px] pr-[64.67px] py-3 bg-neutral-100 rounded-md flex-col justify-center items-start gap-[39px] inline-flex">
            <div className="C01 text-black text-sm font-semibold font-['PingFang SC']">C09</div>
            <div className="C text-black/40 text-xs font-semibold font-['PingFang SC']">C 区</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[3825px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">删除</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>