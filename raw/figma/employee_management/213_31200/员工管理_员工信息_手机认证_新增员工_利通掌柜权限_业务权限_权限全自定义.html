<div className=" w-[393px] h-[1078px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">汇金掌柜权限</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame11 w-[361px] p-2 left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-2 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">模块权限</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">业务权限</div>
      </div>
    </div>
  </div>
  <div className="Frame3465290 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">权限类型</div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[957px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">返回</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465334 w-[361px] p-2 left-[16px] top-[276px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
      </div>
      <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
      </div>
    </div>
  </div>
  <div className="Frame3465335 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">预订权限</div>
  </div>
  <div className="Frame3465336 w-[361px] p-2 left-[16px] top-[553px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
      </div>
      <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
      </div>
    </div>
  </div>
  <div className="Frame3465338 w-[361px] p-2 left-[16px] top-[769px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame2090051629 grow shrink basis-0 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className="Frame2090051627 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全开启</div>
      </div>
      <div className="Frame2090051628 grow shrink basis-0 h-10 p-2.5 bg-white rounded border border-[#5855ff] justify-center items-center gap-2.5 flex">
        <div className=" text-[#5956ff] text-sm font-medium font-['PingFang SC']">自定义</div>
      </div>
      <div className="Frame2090051629 grow shrink basis-0 h-10 p-2.5 rounded border border-black/5 justify-center items-center gap-2.5 flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">全关闭</div>
      </div>
    </div>
  </div>
  <div className="Frame3465337 left-[20px] top-[515px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">库存权限</div>
  </div>
  <div className="Frame3465339 left-[20px] top-[731px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">实时包厢权限</div>
  </div>
  <div className="Frame14 w-[361px] h-[137px] left-[16px] top-[348px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465229 pl-3 pr-3.5 pt-[9px] pb-2 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-2 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">掌柜代订</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">掌柜端可代订包厢</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465231 pl-3 pr-3.5 pt-[9px] pb-2 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-2 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">掌柜代订人修改</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">掌柜端可修改代订人</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465230 pl-3 pr-3.5 pt-[9px] pb-2 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-2 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-black/40 text-sm font-medium font-['PingFang SC']">掌柜代订取消</div>
        <div className=" self-stretch text-black/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">掌柜端可取消代订包厢</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
  </div>
  <div className="Frame3465340 h-[76px] pl-3 pr-[185px] py-3 left-[16px] top-[625px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="Frame3465229 w-[164px] self-stretch pl-3 pr-3.5 pt-[9px] pb-2 bg-[#5855ff] rounded-md border justify-center items-center gap-2 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">入库单修改</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">修改入库价格</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
  </div>
  <div className="Frame3465341 h-[76px] pl-3 pr-[185px] py-3 left-[16px] top-[841px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="Frame3465229 w-[164px] self-stretch pl-3 pr-3.5 pt-[9px] pb-2 bg-[#5855ff] rounded-md border justify-center items-center gap-2 inline-flex">
      <div className="Frame2090051630 w-[114px] self-stretch flex-col justify-start items-start inline-flex">
        <div className=" self-stretch text-white text-sm font-medium font-['PingFang SC']">客户档案查看</div>
        <div className=" self-stretch text-white/40 text-[10px] font-normal font-['PingFang SC'] leading-[15px]">可查看实时包厢客户档案</div>
      </div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
  </div>
</div>