<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">盘点详情</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] p-6 left-[16px] top-[120px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame3465381 grow shrink basis-0 self-stretch flex-col justify-start items-start gap-4 inline-flex">
      <div className="Frame3465379 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">盘点仓库：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">辅料仓库</div>
      </div>
      <div className="Frame3465382 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">经手人：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465383 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">盘点时间：</div>
        <div className="0906104226 text-right text-black text-sm font-semibold font-['PingFang SC']">2024.09.06 10:42:26</div>
      </div>
      <div className="Frame3465385 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">操作人：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465387 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">备注：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">无</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 w-[361px] h-[290px] left-[16px] top-[362px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465391 h-36 left-0 top-0 absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465388 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 pb-[7px] bg-white/5 justify-center items-center inline-flex">
          <div className="Frame3465374 grow shrink basis-0 self-stretch justify-end items-center inline-flex">
            <div className="Frame3465375 self-stretch bg-white/5 justify-start items-center inline-flex">
              <div className="Frame3465370 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">商品名称</div>
              </div>
              <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">单位</div>
              </div>
              <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">成本价格</div>
              </div>
              <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">库存数量</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="Frame3465389 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 pb-[7px] bg-white/5 justify-center items-center inline-flex">
          <div className="Frame3465374 grow shrink basis-0 self-stretch justify-end items-center inline-flex">
            <div className="Frame3465375 self-stretch bg-white/5 justify-start items-center inline-flex">
              <div className="Frame3465370 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className="28l w-[72px] text-center text-black text-xs font-medium font-['PingFang SC']">微醺白葡萄果酒2.8L</div>
              </div>
              <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">克</div>
              </div>
              <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className="100 text-black text-xs font-medium font-['PingFang SC']">¥ 1.00</div>
              </div>
              <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">2</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="Rectangle3469239 w-[361px] h-px left-0 top-[144px] absolute bg-neutral-100" />
    <div className=" left-[329px] top-[170px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
    <div className=" left-[329px] top-[213px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
    <div className=" left-[329px] top-[252px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/元</div>
    <div className="00 left-[309px] top-[253px] absolute opacity-80 text-right text-[#f52525] text-xs font-semibold font-['PingFang SC']">.00</div>
    <div className=" left-[293px] top-[208px] absolute opacity-80 text-right text-black text-lg font-semibold font-['PingFang SC']">200</div>
    <div className=" left-[274px] top-[247px] absolute opacity-80 text-right text-[#f52525] text-lg font-semibold font-['PingFang SC']">200</div>
    <div className=" left-[14px] top-[166.50px] absolute text-[#f52626] text-base font-semibold font-['PingFang SC']">盘盈</div>
    <div className=" left-[14px] top-[208px] absolute text-[#22c66d] text-base font-semibold font-['PingFang SC']">盘亏</div>
    <div className=" left-[14px] top-[248px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">盈亏</div>
    <div className=" left-[293px] top-[165px] absolute opacity-80 text-right text-black text-lg font-semibold font-['PingFang SC']">200</div>
    <div className=" left-[49px] top-[173px] absolute opacity-80 text-black/40 text-[10px] font-semibold font-['PingFang SC']">数量合计：</div>
    <div className=" left-[49px] top-[214.50px] absolute opacity-80 text-black/40 text-[10px] font-semibold font-['PingFang SC']">数量合计：</div>
    <div className=" left-[49px] top-[254.50px] absolute opacity-80 text-black/40 text-[10px] font-semibold font-['PingFang SC']">金额合计：</div>
  </div>
</div>