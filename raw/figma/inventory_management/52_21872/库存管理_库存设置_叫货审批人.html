<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">库存设置</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">库存设置</div>
  <div className="Frame3465279 w-[361px] h-20 left-[16px] top-[409px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-black/40 text-base font-medium font-['PingFang SC']">未设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">叫货审批人</div>
      </div>
    </div>
  </div>
  <div className="Frame3465202 h-20 pl-6 pr-[18px] left-[16px] top-[217px] absolute bg-white rounded-[10px] border justify-end items-start gap-[167px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">充公计算成本价</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465332 h-20 pl-6 pr-[18px] left-[16px] top-[505px] absolute bg-white rounded-[10px] border justify-end items-start gap-[93px] inline-flex">
    <div className="Frame3465399 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">是否开启超价入库限制</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465272 h-20 pl-6 pr-[18px] left-[16px] top-[313px] absolute bg-white rounded-[10px] border justify-end items-start gap-[109px] inline-flex">
    <div className="Frame3465399 self-stretch justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">库存、验货操作审核</div>
      <div className="Frame3465335 w-5 h-5 relative">
        <div className="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full" />
        <div className="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]" />
      </div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465207 h-[852px] pt-[360px] pb-[21px] left-0 top-0 absolute bg-black/60 flex-col justify-end items-center inline-flex">
    <div className="Frame3465208 w-[393px] h-[471px] relative bg-white rounded-tl-[20px] rounded-tr-[20px] flex-col justify-start items-start flex">
      <div className="Frame3465209 w-9 h-9 relative bg-neutral-100 rounded-[18px]" />
      <div className=" text-center text-black text-lg font-medium font-['PingFang SC']">叫货审批人</div>
      <div className="Frame11 pl-2 pr-[209px] py-2 bg-white rounded-[10px] border border-black/10 justify-start items-center gap-2.5 inline-flex">
        <div className="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
          <img className="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
        </div>
        <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">输入关键词搜索</div>
      </div>
      <div className="Frame3465215 h-[151px] flex-col justify-start items-center gap-[3px] inline-flex">
        <div className="Frame3465210 self-stretch pl-6 pr-2.5 py-6 justify-start items-center gap-2.5 inline-flex">
          <div className="Frame3465283 h-[22px] justify-between items-center flex">
            <div className="15145940258 text-right text-black text-base font-medium font-['PingFang SC']">桉树  15145940258</div>
            <div className="Frame3465232 w-[22px] h-[22px] relative bg-[#5855ff] rounded-[20px]" />
          </div>
        </div>
        <div className="Frame3465219 w-[345px] h-px relative bg-black/5" />
        <div className="Frame3465211 self-stretch pl-6 pr-2.5 py-6 justify-start items-center gap-2.5 inline-flex">
          <div className="15145940258 text-right text-black text-base font-medium font-['PingFang SC']">桉树  15145940258</div>
        </div>
        <div className="Frame3465220 w-[345px] h-px relative bg-black/5" />
      </div>
    </div>
  </div>
  <div className="Frame3465333 h-[21px] left-0 top-[831px] absolute bg-white flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465301 px-12 py-3.5 left-[133px] top-[766px] absolute bg-neutral-100 rounded-[7px] justify-center items-center gap-2.5 inline-flex">
    <div className=" text-center text-black text-base font-medium font-['PingFang SC']">确定</div>
  </div>
</div>