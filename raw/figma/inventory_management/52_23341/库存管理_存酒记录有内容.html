<div className=" w-[393px] h-[1292px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">存酒记录</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame14 h-[21px] left-0 top-[1271px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] h-[292px] left-[16px] top-[217px] absolute bg-white rounded-[10px] border">
    <div className=" left-[34px] top-[170px] absolute text-black text-sm font-medium font-['PingFang SC']">操作人</div>
    <div className=" left-[34px] top-[102px] absolute text-black text-sm font-medium font-['PingFang SC']">手机号</div>
    <div className=" left-[250px] top-[102px] absolute text-right text-black text-sm font-medium font-['PingFang SC']">16122111221</div>
    <div className=" left-[277px] top-[170px] absolute text-right text-black text-sm font-medium font-['PingFang SC']">桉树</div>
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[307px] top-[170px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465404 px-5 py-4 left-[14px] top-[18px] absolute bg-neutral-100 rounded-md justify-center items-start gap-[166px] inline-flex">
      <div className=" text-black text-sm font-medium font-['PingFang SC']">单号</div>
      <div className=" text-right text-black text-sm font-medium font-['PingFang SC']">11029192121211</div>
    </div>
    <div className="Frame3465251 left-[168px] top-[247px] absolute justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465249 pl-7 pr-[27px] py-4 left-[14px] top-[222px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0902 text-center text-black text-sm font-medium font-['PingFang SC']">2024-09-02</div>
    </div>
    <div className="Frame3465250 pl-7 pr-[27px] py-4 left-[207px] top-[222px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0902 text-black text-sm font-medium font-['PingFang SC']">2024-09-02</div>
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">存酒记录</div>
  <div className="Frame3465229 w-[361px] h-[159px] left-[16px] top-[539px] absolute bg-white rounded-[10px] border-4">
    <div className=" left-[24px] top-[24px] absolute text-black text-[26px] font-semibold font-['PingFang SC']">桉树</div>
    <div className="Rectangle3469200 w-[361px] h-px left-0 top-[102px] absolute bg-black/5" />
    <div className=" left-[74px] top-[121px] absolute text-black/40 text-sm font-semibold font-['PingFang SC']">详情</div>
    <div className=" left-[255px] top-[121px] absolute text-black text-sm font-semibold font-['PingFang SC']">续存</div>
    <div className="Rectangle3469249 w-px h-14 left-[176px] top-[103px] absolute bg-[#f2f2f2]" />
    <div className="Frame3465354 w-6 h-6 left-[313px] top-[30px] absolute bg-neutral-100 rounded-[21px]" />
    <div className="Frame3465406 w-[305px] left-[24px] top-[60px] absolute justify-start items-center gap-3 inline-flex">
      <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">寄存时间:</div>
      <div className="0902140913 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">2024-09-02 14:09:13</div>
    </div>
    <div className="Rectangle3469250 w-[3px] h-[22px] left-0 top-[31px] absolute bg-[#5855ff]" />
  </div>
  <div className="Frame3465335 w-[361px] h-[403px] left-[16px] top-[728px] absolute bg-white rounded-[10px] border-4">
    <div className=" left-[24px] top-[24px] absolute text-black text-[26px] font-semibold font-['PingFang SC']">刘先生</div>
    <div className="Frame3465410 h-[242px] left-[24px] top-[80px] absolute flex-col justify-start items-start gap-2.5 inline-flex">
      <div className="Frame3465406 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">寄存时间:</div>
        <div className="0902140913 grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">2024-09-02 14:09:13</div>
      </div>
      <div className="Frame3465415 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">单号:</div>
        <div className="W0240910140913354698 grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']">W0240910140913354698</div>
      </div>
      <div className="Frame3465416 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">操作人:</div>
        <div className=" grow shrink basis-0 text-black text-xs font-medium font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465407 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">客户:</div>
        <div className="16522233282 grow shrink basis-0"><span style="text-black text-[13px] font-medium font-['PingFang SC']">桉树 </span><span style="text-[#5855ff] text-[13px] font-medium font-['PingFang SC']">16522233282</span></div>
      </div>
      <div className="Frame3465408 w-[305px] justify-start items-start gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">寄存商品:</div>
        <div className="750ml grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']">长城海岸品丽750ml瓶装</div>
      </div>
      <div className="Frame3465413 justify-start items-start gap-2.5 inline-flex">
        <div className="Frame3465412 flex-col justify-start items-start gap-2.5 inline-flex">
          <div className="Frame3465409 w-[305px] justify-start items-center gap-3 inline-flex">
            <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">数量:</div>
            <div className=" grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']"> 5</div>
          </div>
          <div className="Frame3465411 w-[305px] justify-start items-center gap-3 inline-flex">
            <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">单位:</div>
            <div className=" grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']">瓶</div>
          </div>
        </div>
      </div>
      <div className="Frame3465410 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">单号:</div>
        <div className="W0240910140913354698 grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']">W0240910140913354698</div>
      </div>
      <div className="Frame3465414 w-[305px] justify-start items-center gap-3 inline-flex">
        <div className=" text-black/30 text-[13px] font-medium font-['PingFang SC']">到期时间:</div>
        <div className="1109 grow shrink basis-0 text-black text-[13px] font-medium font-['PingFang SC']">2024-11-09</div>
      </div>
    </div>
    <div className="Rectangle3469200 w-[361px] h-px left-0 top-[346px] absolute bg-black/5" />
    <div className=" left-[74px] top-[365px] absolute text-black/40 text-sm font-semibold font-['PingFang SC']">详情</div>
    <div className=" left-[255px] top-[365px] absolute text-black text-sm font-semibold font-['PingFang SC']">续存</div>
    <div className="Rectangle3469249 w-px h-14 left-[176px] top-[347px] absolute bg-[#f2f2f2]" />
    <div className="Frame3465354 w-6 h-6 left-[313px] top-[30px] absolute bg-neutral-100 rounded-[21px]" />
    <div className="Rectangle3469250 w-[3px] h-[22px] left-0 top-[31px] absolute bg-[#5855ff]" />
  </div>
  <div className="Frame3465334 h-[121px] left-0 top-[1171px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[148px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[13px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">一键续存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>