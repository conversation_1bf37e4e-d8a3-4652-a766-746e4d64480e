<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">出库详情</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] p-6 left-[16px] top-[120px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame3465381 grow shrink basis-0 self-stretch flex-col justify-start items-start gap-4 inline-flex">
      <div className="Frame3465379 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">单号：</div>
        <div className="W0240910140913354698 text-right text-black text-sm font-semibold font-['PingFang SC']">W0240910140913354698</div>
      </div>
      <div className="Frame3465380 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">手机号：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">18616618822</div>
      </div>
      <div className="Frame3465381 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">客户姓名：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">张磊</div>
      </div>
      <div className="Frame3465383 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">包厢：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">109</div>
      </div>
      <div className="Frame3465382 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">操作人：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465384 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">存酒时间：</div>
        <div className="0910140913 text-right text-black text-sm font-semibold font-['PingFang SC']">2024.09.10 14:09:13</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 w-[361px] h-[273px] left-[16px] top-[398px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465391 h-[216px] left-0 top-0 absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465388 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 bg-white/5 justify-end items-center inline-flex">
          <div className="Frame3465392 self-stretch justify-start items-center inline-flex">
            <div className="Frame3465370 w-[72px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
              <div className="Frame3465262 w-6 h-6 p-1 rounded-[35px] justify-center items-center flex">
                <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
              </div>
            </div>
            <div className="Frame3465375 w-px h-[72px] p-2.5 bg-[#ebebf6]" />
            <div className="Frame3465374 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">寄存商品</div>
            </div>
            <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">单位</div>
            </div>
            <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">数量</div>
            </div>
            <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">审批状态</div>
            </div>
          </div>
        </div>
      </div>
      <div className="Frame3465389 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 bg-white/5 justify-end items-center inline-flex">
          <div className="Frame3465393 self-stretch justify-start items-center inline-flex">
            <div className="Frame3465370 w-[72px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className="Frame3465394 w-6 h-6 p-1 rounded-[35px] justify-center items-center flex">
                <div className="Frame3465232 w-4 h-4 relative bg-[#5855ff] rounded-sm flex-col justify-start items-start flex" />
              </div>
            </div>
            <div className="Frame3465375 w-px h-[72px] p-2.5 bg-black/5" />
            <div className="Frame3465374 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className="750ml w-[72px] text-black text-xs font-medium font-['PingFang SC']">长城海岸品丽珠750ml瓶装</div>
            </div>
            <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">瓶</div>
            </div>
            <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">5</div>
            </div>
            <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">已通过</div>
            </div>
          </div>
        </div>
      </div>
      <div className="Frame3465390 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 bg-white/5 justify-end items-center inline-flex">
          <div className="Frame3465393 self-stretch justify-start items-center inline-flex">
            <div className="Frame3465370 w-[72px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className="Frame3465262 w-6 h-6 p-1 rounded-[35px] justify-center items-center flex">
                <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
              </div>
            </div>
            <div className="Frame3465375 w-px h-[72px] p-2.5 bg-black/5" />
            <div className="Frame3465374 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className="750ml w-[72px] text-black text-xs font-medium font-['PingFang SC']">长城海岸品丽珠750ml瓶装</div>
            </div>
            <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">瓶</div>
            </div>
            <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">5</div>
            </div>
            <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
              <div className=" text-black text-xs font-medium font-['PingFang SC']">已通过</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="5 left-[278px] top-[233px] absolute text-right text-black text-xs font-medium font-['PingFang SC']">数量合计:  5</div>
    <div className=" left-[16px] top-[233px] absolute text-black/40 text-xs font-medium font-['PingFang SC']">合计</div>
  </div>
  <div className="Rectangle3469251 w-[361px] h-px left-[16px] top-[614px] absolute bg-[#f0f0f0]" />
  <div className="Frame3465334 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">续存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>