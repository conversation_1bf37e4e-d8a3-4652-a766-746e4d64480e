<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className="1 left-[137px] top-[56px] absolute opacity-80 text-center"><span style="text-black text-base font-semibold font-['PingFang SC']">供应商1</span><span style="text-black text-base font-medium font-['PingFang SC']">供货商品</span></div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame11 pl-3.5 pr-[31px] py-3.5 left-[16px] top-[114px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
      <img className="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
    </div>
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">拼音、首字母或名称搜索商品</div>
  </div>
  <div className="Frame3465228 w-[90px] h-14 p-1 left-[287px] top-[114px] absolute bg-black rounded-[10px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-center items-center gap-1 flex">
      <div className="Frame3465222 w-[18px] h-[18px] relative">
        <div className="Rectangle3469066 w-0.5 h-3.5 left-[8px] top-[2px] absolute bg-white rounded-lg" />
        <div className="Rectangle3469067 w-0.5 h-3.5 left-[16px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
      </div>
      <div className=" text-white text-sm font-medium font-['PingFang SC']">新增</div>
    </div>
  </div>
  <div className="Frame19 w-[361px] h-[282px] left-[16px] top-[194px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465346 w-[361px] h-[266px] left-0 top-[10px] absolute">
      <div className="1 w-[213px] left-[14px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">辅料 1</div>
      <div className=" left-[14px] top-[66px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">采购单价：</div>
      <div className="Frame3465366 pl-[9px] pr-1.5 py-2 left-[275px] top-[58px] absolute bg-neutral-100 rounded justify-center items-start gap-[3px] inline-flex">
        <div className=" opacity-80 text-center text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
        <div className=" opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      </div>
      <div className=" left-[323px] top-[112px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">采购单位：</div>
      <div className=" left-[14px] top-[119px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存单位：</div>
      <div className="Frame3465368 pl-2.5 pr-1.5 py-2 left-[275px] top-[111px] absolute bg-neutral-100 rounded justify-end items-start gap-0.5 inline-flex">
        <div className=" opacity-80 text-center text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
        <div className=" opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      </div>
      <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[327px] top-[165px] absolute">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
      <div className=" left-[277px] top-[164px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
      <div className="Frame3465369 w-6 h-6 left-[323px] top-[4px] absolute rounded-[35px]">
        <div className="Rectangle3469202 w-2.5 h-0.5 left-[7px] top-[11px] absolute bg-black/20 rounded-[9px]" />
        <div className="Rectangle3469205 w-2.5 h-0.5 left-[7px] top-[7px] absolute bg-black/20 rounded-[9px]" />
        <div className="Rectangle3469204 w-2.5 h-0.5 left-[7px] top-[15px] absolute bg-black/20 rounded-[9px]" />
      </div>
      <div className="Rectangle3469247 w-[361px] h-px left-0 top-[206px] absolute bg-black/5" />
    </div>
    <div className=" left-[167px] top-[239px] absolute text-[#ff4747] text-sm font-semibold font-['PingFang SC']">删除</div>
  </div>
  <div className="Frame3465333 w-[361px] h-[282px] left-[16px] top-[506px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465346 w-[361px] h-[266px] left-0 top-[10px] absolute">
      <div className="1 w-[213px] left-[14px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">辅料 1</div>
      <div className=" left-[14px] top-[66px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">采购单价：</div>
      <div className="Frame3465366 pl-[9px] pr-[27px] py-2 left-[275px] top-[58px] absolute bg-neutral-100 rounded justify-start items-center inline-flex">
        <div className=" opacity-80 text-center text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[323px] top-[112px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">采购单位：</div>
      <div className=" left-[14px] top-[119px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存单位：</div>
      <div className="Frame3465368 pl-2.5 pr-1.5 py-2 left-[275px] top-[111px] absolute bg-neutral-100 rounded justify-end items-start gap-0.5 inline-flex">
        <div className=" opacity-80 text-center text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
        <div className=" opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      </div>
      <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[327px] top-[165px] absolute">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
      <div className=" left-[277px] top-[164px] absolute text-right text-black/40 text-base font-medium font-['PingFang SC']">请选择</div>
      <div className="Frame3465369 w-6 h-6 left-[323px] top-[4px] absolute rounded-[35px]">
        <div className="Rectangle3469202 w-2.5 h-0.5 left-[7px] top-[11px] absolute bg-black/20 rounded-[9px]" />
        <div className="Rectangle3469205 w-2.5 h-0.5 left-[7px] top-[7px] absolute bg-black/20 rounded-[9px]" />
        <div className="Rectangle3469204 w-2.5 h-0.5 left-[7px] top-[15px] absolute bg-black/20 rounded-[9px]" />
      </div>
      <div className="Rectangle3469247 w-[361px] h-px left-0 top-[206px] absolute bg-black/5" />
    </div>
    <div className=" left-[167px] top-[239px] absolute text-[#ff4747] text-sm font-semibold font-['PingFang SC']">删除</div>
  </div>
  <div className="Frame3465332 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>