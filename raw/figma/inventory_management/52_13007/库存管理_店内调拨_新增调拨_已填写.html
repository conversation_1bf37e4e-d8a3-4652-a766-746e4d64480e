<div className=" w-[393px] h-[1396px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">新增调拨</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[121px] left-0 top-[1275px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[114px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="1 left-[271px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">仓库 1</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">调出仓库</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465200 w-[361px] h-20 left-[16px] top-[210px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">采购</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">调入类型</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465201 w-[361px] h-20 left-[16px] top-[306px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">桉树</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">经手人</div>
    </div>
  </div>
  <div className="Frame3465295 left-[20px] top-[416px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">调拨时间</div>
  </div>
  <div className="Frame15 w-[361px] px-3.5 py-[18px] left-[16px] top-[454px] absolute bg-white rounded-[10px] border justify-center items-center gap-[20.50px] inline-flex">
    <div className="Frame3465249 grow shrink basis-0 self-stretch px-[29px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0913 text-center text-black text-sm font-medium font-['PingFang SC']">2024-09-13</div>
    </div>
    <div className="Frame3465251 self-stretch justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465250 grow shrink basis-0 self-stretch px-[45px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0010 text-black text-sm font-medium font-['PingFang SC']">11:00:10</div>
    </div>
  </div>
  <div className="Frame3465365 w-[393px] h-16 left-0 top-[1211px] absolute bg-white">
    <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
    <div className=" left-[20px] top-[36px] absolute text-black text-[11px] font-medium font-['PingFang SC']">调拨数量合计</div>
    <div className=" left-[20px] top-[18px] absolute text-black/40 text-[11px] font-semibold font-['PingFang SC']">调拨数量</div>
    <div className=" left-[95px] top-[27px] absolute text-black text-xl font-medium font-['PingFang SC']">0</div>
    <div className="00 left-[109px] top-[37px] absolute text-black/40 text-[11px] font-medium font-['PingFang SC']">.00</div>
    <div className=" left-[87px] top-[36px] absolute text-center text-black text-[11px] font-medium font-['PingFang SC']">：</div>
  </div>
  <div className=" left-[20px] top-[572px] absolute text-black text-base font-medium font-['PingFang SC']">商品</div>
  <div className="Frame19 w-[361px] h-[561px] left-[16px] top-[610px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 pl-1 pr-2.5 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className="Frame11 pl-0.5 pr-[159px] py-0.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-start items-center inline-flex">
      <div className="IcScan w-9 h-9 relative flex-col justify-start items-start flex">
        <img className="Subtract w-4 h-4" src="https://via.placeholder.com/16x16" />
        <div className="Rectangle3469226 w-4 h-0.5 bg-black/40" />
      </div>
      <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索商品</div>
    </div>
    <div className="Frame3465346 w-[361px] h-[230px] left-0 top-[80px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className="Frame3465395 w-[70px] px-2.5 py-1.5 left-[14px] top-[122px] absolute bg-[#ff4747]/10 rounded-[32px] justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#ff4747] text-xs font-semibold font-['PingFang SC']">出仓库</div>
      </div>
      <div className="Frame3465396 w-[70px] px-2.5 py-1.5 left-[14px] top-[171px] absolute bg-[#23cc5d]/10 rounded-[32px] justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#23cc5d] text-xs font-semibold font-['PingFang SC']">入仓库</div>
      </div>
      <div className="Frame3465367 w-12 h-[33px] px-[15.50px] pt-1.5 pb-[5px] left-[267px] top-[120px] absolute rounded border border-black/5 justify-center items-center inline-flex">
        <div className=" w-[17px] opacity-80 text-center text-[#f52525] text-base font-semibold font-['PingFang SC']">1</div>
      </div>
      <div className=" left-[327px] top-[128px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
      <div className=" left-[231px] top-[128px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存</div>
      <div className=" left-[231px] top-[177px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存</div>
      <div className="Frame3465368 px-3.5 pt-1.5 pb-[5px] left-[267px] top-[169px] absolute rounded border border-black/5 justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-[#149b43] text-base font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[177px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">2</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
    </div>
    <div className="Frame3465347 w-[361px] h-[230px] left-0 top-[331px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className="Frame3465395 w-[70px] px-2.5 py-1.5 left-[14px] top-[122px] absolute bg-[#ff4747]/10 rounded-[32px] justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#ff4747] text-xs font-semibold font-['PingFang SC']">出仓库</div>
      </div>
      <div className="Frame3465396 w-[70px] px-2.5 py-1.5 left-[14px] top-[171px] absolute bg-[#23cc5d]/10 rounded-[32px] justify-center items-center gap-2.5 inline-flex">
        <div className=" text-[#23cc5d] text-xs font-semibold font-['PingFang SC']">入仓库</div>
      </div>
      <div className="Frame3465367 w-12 h-[33px] px-[15.50px] pt-1.5 pb-[5px] left-[267px] top-[120px] absolute rounded border border-black/5 justify-center items-center inline-flex">
        <div className=" w-[17px] opacity-80 text-center text-[#f52525] text-base font-semibold font-['PingFang SC']">1</div>
      </div>
      <div className=" left-[327px] top-[128px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
      <div className=" left-[231px] top-[128px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存</div>
      <div className=" left-[231px] top-[177px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">库存</div>
      <div className="Frame3465368 px-3.5 pt-1.5 pb-[5px] left-[267px] top-[169px] absolute rounded border border-black/5 justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-[#149b43] text-base font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[177px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">/瓶</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">2</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
    </div>
    <div className="Rectangle3469238 w-[333px] h-px left-[14px] top-[310px] absolute bg-black/5" />
  </div>
</div>