<div className=" w-[393px] h-[852px] relative bg-white">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">商品辅料</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame11 pl-3.5 pr-[157px] py-3.5 left-[16px] top-[114px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center gap-2.5 inline-flex">
    <div className="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
      <img className="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
    </div>
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索辅料</div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465329 h-[720px] left-0 top-[194px] absolute flex-col justify-start items-start inline-flex">
    <div className="Frame3465322 h-[72px] px-[31px] bg-white justify-center items-center inline-flex">
      <div className="1 text-black text-sm font-medium font-['PingFang SC']">分类1</div>
    </div>
    <div className="Frame3465324 h-[72px] pl-[30px] pr-[29px] bg-neutral-100 rounded-tr-[10px] justify-center items-center inline-flex">
      <div className="2 text-center text-black/40 text-sm font-medium font-['PingFang SC']">分类2</div>
    </div>
    <div className="Frame3465329 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465330 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465331 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465332 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465333 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465334 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465335 h-[72px] relative bg-neutral-100" />
    <div className="Frame3465336 h-[72px] relative bg-neutral-100" />
  </div>
  <div className="Frame3465323 w-[297px] h-[670px] pb-[490px] left-[96px] top-[194px] absolute bg-white flex-col justify-start items-start inline-flex">
    <div className="Frame3465353 self-stretch h-[90px] flex-col justify-start items-start inline-flex">
      <div className="Frame3465345 h-[90px] relative">
        <div className=" left-[14px] top-[10px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">葱姜蒜</div>
        <div className=" left-[14px] top-[58px] absolute text-black/40 text-base font-semibold font-['PingFang SC']">克</div>
        <div className="Frame3465262 w-6 h-6 p-1 left-[259px] top-[33px] absolute rounded-[35px] justify-center items-center inline-flex">
          <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
        </div>
      </div>
    </div>
    <div className="Frame3465354 self-stretch h-[90px] flex-col justify-start items-start inline-flex">
      <div className="Frame3465345 h-[90px] relative">
        <div className=" left-[14px] top-[10px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">辣椒</div>
        <div className=" left-[14px] top-[58px] absolute text-black/40 text-base font-semibold font-['PingFang SC']">克</div>
        <div className="Frame3465262 w-6 h-6 p-1 left-[259px] top-[33px] absolute rounded-[35px] justify-center items-center inline-flex">
          <div className="Frame3465232 w-4 h-4 relative bg-[#5855ff] rounded-sm flex-col justify-start items-start flex" />
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465228 w-[90px] h-14 p-1 left-[287px] top-[114px] absolute bg-black rounded-[10px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-center items-center gap-1 flex">
      <div className=" text-white text-sm font-medium font-['PingFang SC']">保存</div>
    </div>
  </div>
</div>