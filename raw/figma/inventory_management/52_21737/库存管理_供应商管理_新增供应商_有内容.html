<div className=" w-[393px] h-[991px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">供应商管理</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[970px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame11 w-[361px] pl-[18px] pr-[295px] py-[18px] left-[16px] top-[158px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="1 text-black text-sm font-medium font-['PingFang SC']">供应商1</div>
  </div>
  <div className="Frame3465331 w-[361px] pl-[18px] pr-[317px] py-[18px] left-[16px] top-[282px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">1011</div>
  </div>
  <div className="Frame3465290 left-[20px] top-[120px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465332 left-[20px] top-[244px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">编号</div>
  </div>
  <div className="Frame19 w-[361px] h-[352px] left-[16px] top-[368px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-sm font-medium font-['PingFang SC']">联系人</div>
    </div>
    <div className="Frame3465228 pl-1 pr-2.5 py-1 left-[289px] top-[14px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className="Frame3465249 w-[329px] h-[264px] left-[16px] top-[64px] absolute bg-neutral-100 rounded-md">
      <div className="Rectangle3469241 w-[329px] h-px left-0 top-[105px] absolute bg-white" />
      <div className="Rectangle3469243 w-[329px] h-px left-0 top-[52px] absolute bg-white" />
      <div className="Rectangle3469242 w-[329px] h-px left-0 top-[158px] absolute bg-white" />
      <div className="Rectangle3469244 w-[329px] h-px left-0 top-[211px] absolute bg-white" />
      <div className=" left-[16px] top-[16px] absolute text-black text-sm font-medium font-['PingFang SC']">姓名</div>
      <div className=" left-[285px] top-[16px] absolute text-right text-black/40 text-sm font-medium font-['PingFang SC']">桉树</div>
      <div className=" left-[226px] top-[69px] absolute text-right text-black/40 text-sm font-medium font-['PingFang SC']">15144266252</div>
      <div className="LeishiLeishiCom left-[200px] top-[122px] absolute text-right text-black/40 text-sm font-medium font-['PingFang SC']"><EMAIL></div>
      <div className=" left-[16px] top-[69px] absolute text-black text-sm font-medium font-['PingFang SC']">联系电话</div>
      <div className=" left-[16px] top-[122px] absolute text-black text-sm font-medium font-['PingFang SC']">邮箱</div>
      <div className=" left-[16px] top-[175px] absolute text-black text-sm font-medium font-['PingFang SC']">绑定微信接收叫货单提醒</div>
      <div className=" left-[249px] top-[175px] absolute text-[#5855ff] text-sm font-medium font-['PingFang SC']">已绑定</div>
      <div className=" left-[151px] top-[228px] absolute text-right text-[#ff3e32] text-sm font-medium font-['PingFang SC']">删除</div>
      <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[293px] top-[175px] absolute">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465202 h-20 pl-6 pr-[18px] left-[16px] top-[750px] absolute bg-white rounded-[10px] border justify-end items-start gap-[99px] inline-flex">
    <div className="Frame3465362 w-[180px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black text-base font-medium font-['PingFang SC']">启用</div>
    </div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465333 h-[121px] left-0 top-[870px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>