<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">报废详情</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] p-6 left-[16px] top-[120px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className="Frame3465381 grow shrink basis-0 self-stretch flex-col justify-start items-start gap-4 inline-flex">
      <div className="Frame3465379 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">审批状态：</div>
        <div className=" text-right text-[#5855ff] text-sm font-semibold font-['PingFang SC']">已通过</div>
      </div>
      <div className="Frame3465382 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">经手人：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465383 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">报废时间：</div>
        <div className="0906104226 text-right text-black text-sm font-semibold font-['PingFang SC']">2024.09.06 10:42:26</div>
      </div>
      <div className="Frame3465385 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">操作人：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">桉树</div>
      </div>
      <div className="Frame3465386 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">操作时间：</div>
        <div className="0906104426 text-right text-black text-sm font-semibold font-['PingFang SC']">2024.09.06 10:44:26</div>
      </div>
      <div className="Frame3465387 self-stretch justify-between items-center inline-flex">
        <div className=" text-black/40 text-sm font-semibold font-['PingFang SC']">备注：</div>
        <div className=" text-right text-black text-sm font-semibold font-['PingFang SC']">无</div>
      </div>
    </div>
  </div>
  <div className="Frame3465331 w-[361px] h-[194px] left-[16px] top-[398px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465391 h-36 left-0 top-0 absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465388 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 pb-[7px] bg-white/5 justify-center items-center inline-flex">
          <div className="Frame3465374 grow shrink basis-0 self-stretch justify-end items-center inline-flex">
            <div className="Frame3465375 self-stretch bg-white/5 justify-start items-center inline-flex">
              <div className="Frame3465370 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">商品名称</div>
              </div>
              <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">单位</div>
              </div>
              <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">数量</div>
              </div>
              <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-[#5855ff]/5 justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">备注</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="Frame3465389 self-stretch h-[72px] flex-col justify-start items-start flex">
        <div className="Frame3465369 pb-[7px] bg-white/5 justify-center items-center inline-flex">
          <div className="Frame3465374 grow shrink basis-0 self-stretch justify-end items-center inline-flex">
            <div className="Frame3465375 self-stretch bg-white/5 justify-start items-center inline-flex">
              <div className="Frame3465370 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className="28l w-[72px] text-center text-black text-xs font-medium font-['PingFang SC']">微醺白葡萄果酒2.8L</div>
              </div>
              <div className="Frame3465371 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className=" text-black text-xs font-medium font-['PingFang SC']">瓶</div>
              </div>
              <div className="Frame3465372 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex">
                <div className="Ml text-black text-xs font-medium font-['PingFang SC']"> 150ml</div>
              </div>
              <div className="Frame3465373 w-[100px] h-[72px] p-2.5 bg-white justify-center items-center gap-2.5 flex" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="Rectangle3469239 w-[361px] h-px left-0 top-[144px] absolute bg-neutral-100" />
    <div className="12 left-[273px] top-[161px] absolute text-right text-black text-xs font-medium font-['PingFang SC']">数量合计:  12</div>
  </div>
  <div className="Frame3465332 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">通过</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black text-base font-medium font-['PingFang SC']">驳回</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>