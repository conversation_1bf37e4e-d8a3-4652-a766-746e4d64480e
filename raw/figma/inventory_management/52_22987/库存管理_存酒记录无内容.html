<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">存酒记录</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame14 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div className="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465334 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black/20 rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] h-[292px] left-[16px] top-[217px] absolute bg-white rounded-[10px] border">
    <div className=" left-[34px] top-[170px] absolute text-black text-sm font-medium font-['PingFang SC']">操作人</div>
    <div className=" left-[34px] top-[102px] absolute text-black text-sm font-medium font-['PingFang SC']">手机号</div>
    <div className=" left-[215px] top-[102px] absolute opacity-30 text-black text-sm font-medium font-['PingFang SC']">请输入用户手机号</div>
    <div className=" left-[263px] top-[170px] absolute text-black/30 text-sm font-medium font-['PingFang SC']">请选择</div>
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[307px] top-[170px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465404 px-5 py-4 left-[14px] top-[18px] absolute bg-neutral-100 rounded-md justify-center items-start gap-[167px] inline-flex">
      <div className=" text-black text-sm font-medium font-['PingFang SC']">单号</div>
      <div className=" opacity-30 text-black text-sm font-medium font-['PingFang SC']">请输入存酒单号</div>
    </div>
    <div className="Frame3465251 left-[168px] top-[247px] absolute justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465249 px-[42px] py-4 left-[14px] top-[222px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/40 text-sm font-medium font-['PingFang SC']">开始时间</div>
    </div>
    <div className="Frame3465250 px-[42px] py-4 left-[207px] top-[222px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">结束时间</div>
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">存酒记录</div>
</div>