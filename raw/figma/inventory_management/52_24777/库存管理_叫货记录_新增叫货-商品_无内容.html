<div className=" w-[393px] h-[1149px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-medium font-['PingFang SC']">新增叫货</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[1128px] absolute justify-center items-center inline-flex">
    <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
  </div>
  <div className="Frame11 pl-[18px] pr-[74px] py-[18px] left-[16px] top-[932px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="120 text-black/20 text-sm font-medium font-['PingFang SC']">只能包含中文、英文字母、数字且为1-20位</div>
  </div>
  <div className="Frame3465297 left-[20px] top-[894px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">备注</div>
  </div>
  <div className=" left-[20px] top-[120px] absolute text-black text-base font-medium font-['PingFang SC']">叫货模板</div>
  <div className=" left-[20px] top-[360px] absolute text-black text-base font-medium font-['PingFang SC']">供货商品</div>
  <div className=" left-[20px] top-[600px] absolute text-black text-base font-medium font-['PingFang SC']">批量添加</div>
  <div className="Frame3465299 w-[361px] h-[172px] left-[16px] top-[398px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 pl-1 pr-2.5 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className=" left-[153px] top-[96px] absolute text-black/20 text-sm font-medium font-['PingFang SC']">暂无内容</div>
    <div className="Frame11 w-[253px] h-10 pl-2.5 pr-[159px] py-2.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-start items-start gap-2 inline-flex">
      <div className="Frame3465220 w-5 h-5 relative flex-col justify-start items-start flex">
        <img className="Union w-[12.57px] h-[12.41px]" src="https://via.placeholder.com/13x12" />
      </div>
      <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索商品</div>
    </div>
  </div>
  <div className="Frame3465298 w-[361px] h-[172px] left-[16px] top-[158px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 pl-1 pr-2.5 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className=" left-[153px] top-[96px] absolute text-black/20 text-sm font-medium font-['PingFang SC']">暂无内容</div>
    <div className="Frame11 h-10 pl-2.5 pr-[11px] py-2.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-center items-start gap-[156px] inline-flex">
      <div className=" w-14 text-black/20 text-sm font-medium font-['PingFang SC']">请选择</div>
      <div className="MaskGroup origin-top-left -rotate-180 opacity-80 w-5 h-5 relative">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left -rotate-180">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465300 w-[361px] h-[226px] left-[16px] top-[638px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[329px] h-10 pl-1 pr-2.5 py-1 left-[16px] top-[86px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center gap-1 flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className="Frame3465257 h-10 pl-2.5 pr-[11px] py-2.5 left-[16px] top-[30px] absolute bg-white rounded-md border border-black/10 justify-center items-start gap-[63px] inline-flex">
      <div className=" w-14 text-black/20 text-sm font-medium font-['PingFang SC']">请选择</div>
      <div className="MaskGroup origin-top-left -rotate-180 opacity-80 w-5 h-5 relative">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left -rotate-180">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
    </div>
    <div className="Frame3465258 h-10 pl-2.5 pr-[11px] py-2.5 left-[185px] top-[30px] absolute bg-white rounded-md border border-black/10 justify-center items-start gap-[63px] inline-flex">
      <div className=" w-14 text-black/20 text-sm font-medium font-['PingFang SC']">请选择</div>
      <div className="MaskGroup origin-top-left -rotate-180 opacity-80 w-5 h-5 relative">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left -rotate-180">
          <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
        </div>
      </div>
    </div>
    <div className=" left-[153px] top-[166px] absolute text-black/20 text-sm font-medium font-['PingFang SC']">暂无内容</div>
  </div>
  <div className="Frame3 h-[121px] left-0 top-[1028px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 w-44 h-[50px] left-[201px] top-[17px] absolute bg-black/20 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[56px] top-[14px] absolute text-center text-white text-base font-medium font-['PingFang SC']">提交审批</div>
      </div>
      <div className="Frame17 w-44 h-[50px] left-[16px] top-[17px] absolute bg-black/10 rounded-lg">
        <div className="Frame18 left-[24px] top-[14px] absolute" />
        <div className=" left-[72px] top-[14px] absolute text-center text-black/20 text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
</div>