{"codeType": "HTML", "figmaPageCode": "<div style=\"width: 393px; height: 852px; position: relative; background: white\">\n  <div style=\"width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white\">\n    <div style=\"width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9\"></div>\n    <div style=\"width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid\"></div>\n    <img style=\"width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/18x17\" />\n    <img style=\"width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/20x6\" />\n    <div style=\"width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid\"></div>\n  </div>\n  <div style=\"left: 157px; top: 56px; position: absolute; opacity: 0.80; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">商品新增</div>\n  <div style=\"width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n    <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n      <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n    </div>\n    <div style=\"width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid\"></div>\n  </div>\n  <div style=\"width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 28.43px; height: 11.09px; background: black\"></div>\n      </div>\n    </div>\n    <div style=\"width: 66.66px; height: 11.34px; position: relative\">\n      <div style=\"width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute\">\n        <div style=\"width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid\"></div>\n        <div style=\"width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black\"></div>\n        <div style=\"width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px\"></div>\n      </div>\n      <img style=\"width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute\" src=\"https://via.placeholder.com/15x11\" />\n      <img style=\"width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute\" src=\"https://via.placeholder.com/17x11\" />\n    </div>\n  </div>\n  <div style=\"height: 21px; left: 0px; top: 831px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n    <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n    </div>\n  </div>\n  <div style=\"padding-top: 14px; padding-bottom: 14px; padding-left: 14px; padding-right: 157px; left: 16px; top: 114px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex\">\n    <div style=\"width: 28px; height: 28px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n      <img style=\"width: 17.60px; height: 17.37px\" src=\"https://via.placeholder.com/18x17\" />\n    </div>\n    <div style=\"color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">搜索商品</div>\n  </div>\n  <div style=\"width: 90px; height: 56px; padding: 4px; left: 287px; top: 114px; position: absolute; background: black; border-radius: 10px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n    <div style=\"justify-content: center; align-items: center; gap: 4px; display: flex\">\n      <div style=\"width: 18px; height: 18px; position: relative\">\n        <div style=\"width: 2px; height: 14px; left: 8px; top: 2px; position: absolute; background: white; border-radius: 8px\"></div>\n        <div style=\"width: 2px; height: 14px; left: 16px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: white; border-radius: 8px\"></div>\n      </div>\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">新增</div>\n    </div>\n  </div>\n  <div style=\"height: 864px; left: 0px; top: 194px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n    <div style=\"width: 96px; height: 72px; background: white; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">酒水</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; background: #F5F5F5; border-top-left-radius: 10px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">饮料</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">果盘</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">小吃</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">其他</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">烧烤</div>\n    </div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n    <div style=\"width: 96px; height: 72px; position: relative; background: #F5F5F5\"></div>\n  </div>\n  <div style=\"height: 670px; left: 96px; top: 194px; position: absolute; background: white; flex-direction: column; justify-content: flex-end; align-items: center; display: inline-flex\">\n    <div style=\"align-self: stretch; height: 720px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; opacity: 0.20; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"padding-left: 5px; padding-right: 5px; padding-top: 1.50px; padding-bottom: 1.50px; left: 94px; top: 35px; position: absolute; background: rgba(0, 0, 0, 0.06); border-radius: 2px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 9px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">沽清</div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 8px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; padding-top: 15px; padding-bottom: 15px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 20px; height: 40px\" src=\"https://via.placeholder.com/20x40\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 259px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n      <div style=\"width: 297px; height: 90px; position: relative\">\n        <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n          <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n        </div>\n        <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n        <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n          <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n          <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n          <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n        </div>\n        <div style=\"width: 24px; height: 24px; left: 267px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden\">\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n          <div style=\"width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px\"></div>\n        </div>\n        <div style=\"width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden\">\n          <div style=\"width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"}