<div class=" w-[393px] h-[852px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">商品展示分类</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[281px] py-[18px] left-[16px] top-[158px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="1 text-black text-sm font-medium font-['PingFang SC']">展示类型1</div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465297 left-[20px] top-[120px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">展示分类名称</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465336 left-[20px] top-[244px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class="Frame3465266 justify-start items-center gap-1.5 flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">绑定类型</div>
    </div>
    <div class="Frame3465266 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465202 h-20 pl-6 pr-[18px] left-[16px] top-[510px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">展示二级分类</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame14 w-[361px] h-[198px] left-[16px] top-[282px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465231 pl-4 pr-3.5 py-4 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[90px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">果盘</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465234 pl-4 pr-3.5 py-4 left-[12px] top-[134px] absolute rounded-md border border-black/10 justify-center items-center gap-[90px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">其他</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465230 pl-4 pr-3.5 py-4 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-[90px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">饮料</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465232 pl-4 pr-3.5 py-4 left-[185px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[90px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">小吃</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465235 pl-4 pr-3.5 py-4 left-[185px] top-[134px] absolute rounded-md border border-black/10 justify-center items-center gap-[90px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">烧烤</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465229 pl-4 pr-3.5 py-4 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[90px] inline-flex">
      <div class=" text-white text-sm font-medium font-['PingFang SC']">酒水</div>
      <div class="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex"></div>
    </div>
  </div>
  <div class="Frame3465255 w-[361px] py-3.5 left-[16px] top-[630px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div class=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
</div>