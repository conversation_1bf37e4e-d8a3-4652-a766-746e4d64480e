<div className=" w-[393px] h-[852px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">区域价格</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[831px] absolute justify-center items-center inline-flex">
    <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
  </div>
  <div className="Frame3465230 w-[361px] h-[337px] left-[16px] top-[217px] absolute bg-white rounded-[10px] border-4">
    <div className="1 left-[24px] top-[24px] absolute text-black text-2xl font-semibold font-['PingFang SC']">大堂区域 1</div>
    <div className="Rectangle3469200 w-[361px] h-px left-0 top-[271px] absolute bg-black/5" />
    <div className=" left-[167px] top-[295px] absolute text-[#ff4747] text-sm font-semibold font-['PingFang SC']">删除</div>
    <div className="Frame3465233 pl-4 pr-3.5 py-4 left-[24px] top-[195px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-12 inline-flex">
      <div className=" text-white text-sm font-medium font-['PingFang SC']">商家折扣</div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465235 pl-4 pr-3.5 py-4 left-[187px] top-[195px] absolute rounded-md border border-black/10 justify-center items-center gap-12 inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">商家减免</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className=" left-[24px] top-[162px] absolute text-black/40 text-xs font-medium font-['PingFang SC']">支持折扣</div>
    <div className="Frame11 w-[313px] pl-[18px] pr-[267px] py-[18px] left-[24px] top-[82px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center inline-flex">
      <div className="Frame3465358 self-stretch justify-start items-center gap-2 inline-flex">
        <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">现价</div>
      </div>
    </div>
  </div>
  <div className="Frame3465231 w-[361px] h-[337px] left-[16px] top-[570px] absolute bg-white rounded-[10px] border-4">
    <div className="1 left-[24px] top-[24px] absolute text-black text-2xl font-semibold font-['PingFang SC']">大堂区域 1</div>
    <div className="Rectangle3469200 w-[361px] h-px left-0 top-[271px] absolute bg-black/5" />
    <div className=" left-[167px] top-[295px] absolute text-[#ff4747] text-sm font-semibold font-['PingFang SC']">删除</div>
    <div className="Frame3465233 pl-4 pr-3.5 py-4 left-[24px] top-[195px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-12 inline-flex">
      <div className=" text-white text-sm font-medium font-['PingFang SC']">商家折扣</div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465235 pl-4 pr-3.5 py-4 left-[187px] top-[195px] absolute rounded-md border border-black/10 justify-center items-center gap-12 inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">商家减免</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className=" left-[24px] top-[162px] absolute text-black/40 text-xs font-medium font-['PingFang SC']">支持折扣</div>
    <div className="Frame11 w-[313px] pl-[18px] pr-[267px] py-[18px] left-[24px] top-[82px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center inline-flex">
      <div className="Frame3465358 self-stretch justify-start items-center gap-2 inline-flex">
        <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">现价</div>
      </div>
    </div>
  </div>
  <div className="Frame3 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">确定</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">区域价格</div>
  <div className="Frame3465221 pl-1.5 pr-3 py-2 left-[310px] top-[137px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
    <div className="Frame3465223 justify-start items-center gap-0.5 flex">
      <div className="Frame3465222 w-[18px] h-[18px] relative">
        <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
        <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
      </div>
      <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
    </div>
  </div>
</div>