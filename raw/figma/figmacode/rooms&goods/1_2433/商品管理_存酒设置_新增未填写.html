<div style="width: 393px; height: 1331px; position: relative; background: #F5F5F5">
  <div class="MiniProgramsButtons" style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white">
    <div class="Rectangle938" style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div class="Stroke" style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img class="Union" style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img class="Union" style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div class="Separator" style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="left: 165px; top: 56px; position: absolute; opacity: 0.80; text-align: center; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">存酒设置</div>
  <div class="MaskGroup" style="width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
    <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
      <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
    </div>
    <div class="Rectangle3469044" style="width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame11" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 158px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/天</div>
  </div>
  <div class="Frame3465298" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 299px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/次</div>
  </div>
  <div class="Frame3465310" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 1114px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/天</div>
  </div>
  <div class="Frame3465300" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 423px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/天</div>
  </div>
  <div class="Frame3465302" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 547px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/天</div>
  </div>
  <div class="Frame3465304" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 272px; left: 16px; top: 671px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">/天</div>
  </div>
  <div class="Frame3" style="height: 121px; left: 0px; top: 1210px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="width: 393px; height: 100px; position: relative; background: white">
      <div class="Frame4" style="width: 393px; height: 1px; left: 0px; top: 0px; position: absolute; background: rgba(0, 0, 0, 0.10)"></div>
      <div class="Frame9" style="width: 393px; left: 0px; top: 1px; position: absolute"></div>
      <div class="Frame16" style="height: 50px; padding-top: 14px; padding-bottom: 14px; padding-left: 24px; padding-right: 164px; left: 16px; top: 17px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 8px; overflow: hidden; justify-content: flex-start; align-items: flex-start; gap: 29px; display: inline-flex">
        <div class="Frame18" style="align-self: stretch"></div>
        <div style="text-align: center; color: white; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">保存</div>
      </div>
    </div>
    <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
  <div class="Frame3465297" style="left: 20px; top: 120px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">存酒保存天数</div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465301" style="left: 20px; top: 385px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">每次续存天数</div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465303" style="left: 20px; top: 509px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div><span style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">提前通知 </span><span style="color: #FFAC2F; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">客户</span><span style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word"> 存酒过期天数</span></div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465299" style="left: 20px; top: 244px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">续存次数</div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465311" style="left: 20px; top: 1059px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">过期多少天后不可支取(超期)</div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465305" style="left: 20px; top: 633px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div><span style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">提前通知 </span><span style="color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商户 </span><span style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">存酒过期天数</span></div>
    <div class="Frame3465265" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="990" style="width: 353px; left: 20px; top: 266px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word"> 0~99次，默认0次，即不可续存</div>
  <div style="width: 353px; left: 20px; top: 1081px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">过期取酒权限不可超期取酒，取酒超管权限才可超期取酒</div>
  <div class="Frame3465199" style="width: 361px; height: 80px; left: 16px; top: 757px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465278" style="left: 24px; top: 29px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
      <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">存酒过期提醒(商户)</div>
    </div>
    <div style="left: 283px; top: 29px; position: absolute; text-align: right; color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商户</div>
  </div>
  <div class="Frame3465306" style="width: 361px; height: 80px; left: 16px; top: 853px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465278" style="left: 24px; top: 29px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
      <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">存酒过期提醒(代订人)</div>
    </div>
    <div style="left: 267px; top: 29px; position: absolute; text-align: right; color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">代订人</div>
  </div>
  <div class="Frame3465287" style="width: 361px; height: 80px; left: 16px; top: 949px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div style="left: 24px; top: 29px; position: absolute; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">自动充公</div>
    <div class="Frame3465196" style="width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 23px; left: 303px; top: 29px; position: absolute; background: #BABABA; border-radius: 40px; overflow: hidden; justify-content: flex-start; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
</div>