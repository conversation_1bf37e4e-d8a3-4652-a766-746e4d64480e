<div class=" w-[393px] h-[852px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">新增时段管理</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame11 pl-[18px] pr-[179px] py-[18px] left-[16px] top-[158px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="19000200 text-black text-sm font-medium font-['PingFang SC']">周末黄金时段19:00~02:00</div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[731px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465297 left-[20px] top-[120px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">时段名称</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465270 left-[20px] top-[244px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">时段类型</div>
  </div>
  <div class="Frame15 w-[361px] h-[254px] left-[16px] top-[282px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465237 px-3.5 py-[19px] left-[14px] top-[66px] absolute rounded border border-black/5 flex-col justify-center items-center inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">一</div>
    </div>
    <div class="Frame3465238 px-3.5 py-[19px] left-[62px] top-[66px] absolute rounded border border-black/5 flex-col justify-center items-center inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">二</div>
    </div>
    <div class="Frame3465239 px-3.5 py-[19px] left-[110px] top-[66px] absolute rounded border border-black/5 flex-col justify-center items-center inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">三</div>
    </div>
    <div class="Frame3465240 pl-[15px] pr-3.5 py-[19px] left-[158px] top-[66px] absolute rounded border border-black/5 flex-col justify-center items-center inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">四</div>
    </div>
    <div class="Frame3465241 px-3.5 py-[19px] left-[207px] top-[66px] absolute rounded border border-black/5 flex-col justify-center items-center inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">五</div>
    </div>
    <div class="Frame3465242 px-3.5 py-[19px] left-[255px] top-[66px] absolute bg-[#5855ff] rounded border border-[#5855ff] flex-col justify-center items-center inline-flex">
      <div class=" text-white text-base font-medium font-['PingFang SC']">六</div>
    </div>
    <div class="Frame3465243 px-3.5 py-[19px] left-[303px] top-[66px] absolute bg-[#5855ff] rounded border border-[#5855ff] flex-col justify-center items-center inline-flex">
      <div class=" text-white text-base font-medium font-['PingFang SC']">日</div>
    </div>
    <div class="Frame3465244 p-1 left-[105px] top-[14px] absolute bg-neutral-100 rounded-[10px] justify-center items-center inline-flex">
      <div class="Frame3465248 self-stretch justify-start items-center gap-1 inline-flex">
        <div class="Frame3465246 h-7 px-[23px] pt-1.5 pb-[5px] bg-[#5855ff] rounded-md justify-center items-center flex">
          <div class=" text-white text-xs font-medium font-['PingFang SC']">星期</div>
        </div>
        <div class="Frame3465247 h-7 px-[23px] pt-1.5 pb-[5px] justify-center items-center flex">
          <div class=" text-black/40 text-xs font-medium font-['PingFang SC']">日期</div>
        </div>
      </div>
    </div>
    <div class=" left-[14px] top-[150px] absolute text-black text-sm font-medium font-['PingFang SC']">消费时间</div>
    <div class="Frame3465251 left-[168px] top-[211px] absolute justify-start items-center gap-0.5 inline-flex"></div>
    <div class="Frame3465249 w-[140px] pl-12 pr-[47px] pt-3.5 pb-[13px] left-[14px] top-[186px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div class="00 text-center text-black text-lg font-medium font-['PingFang SC']">19:00</div>
    </div>
    <div class="Frame3465250 pl-4 pr-9 pt-3.5 pb-[13px] left-[207px] top-[186px] absolute bg-neutral-100 rounded-md justify-start items-center gap-[19px] inline-flex">
      <div class=" text-black/40 text-[10px] font-medium font-['PingFang SC']">次日</div>
      <div class="00 text-black text-lg font-medium font-['PingFang SC']">03:00</div>
    </div>
  </div>
  <div class="Frame3465255 w-[361px] py-3.5 left-[16px] top-[576px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div class=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
</div>