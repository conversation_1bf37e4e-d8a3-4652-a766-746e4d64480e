<div style="width: 393px; height: 3483px; position: relative; background: #F5F5F5">
  <div class="MiniProgramsButtons" style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white">
    <div class="Rectangle938" style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div class="Stroke" style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img class="Union" style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img class="Union" style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div class="Separator" style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="left: 157px; top: 56px; position: absolute; opacity: 0.80; text-align: center; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">套餐信息</div>
  <div class="MaskGroup" style="width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
    <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
      <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
    </div>
    <div class="Rectangle3469044" style="width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3465271" style="left: 20px; top: 114px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">套擦名称</div>
    <div class="Frame3465264" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465297" style="left: 20px; top: 2905px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">套餐介绍</div>
  </div>
  <div class="Frame3465299" style="left: 20px; top: 3119px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">套餐图片</div>
  </div>
  <div class="Frame3465274" style="left: 20px; top: 348px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品现价</div>
    <div class="Frame3465264" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465305" style="left: 20px; top: 2491px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">条形码</div>
  </div>
  <div class="Frame11" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 287px; left: 16px; top: 152px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品类型</div>
  </div>
  <div class="Frame3465295" style="left: 20px; top: 2211px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">上架时段</div>
  </div>
  <div class="Frame3465298" style="width: 361px; height: 129px; padding-top: 18px; padding-bottom: 91px; padding-left: 18px; padding-right: 245px; left: 16px; top: 2960px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入套餐介绍</div>
  </div>
  <div class="Frame3465275" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 301px; left: 16px; top: 386px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
  </div>
  <div class="Frame3465306" style="width: 361px; padding-top: 10px; padding-bottom: 10px; padding-left: 10px; padding-right: 263px; left: 16px; top: 2529px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
    <div class="IcScan" style="width: 36px; height: 36px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
      <img class="Subtract" style="width: 16px; height: 16px" src="https://via.placeholder.com/16x16" />
      <div class="Rectangle3469226" style="width: 16px; height: 2px; background: rgba(0, 0, 0, 0.40)"></div>
    </div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
  </div>
  <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; left: 0px; top: 3462px; position: absolute; justify-content: center; align-items: center; display: inline-flex">
    <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
  </div>
  <div class="Frame3" style="height: 121px; left: 0px; top: 3362px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="width: 393px; height: 100px; position: relative; background: white">
      <div class="Frame4" style="width: 393px; height: 1px; left: 0px; top: 0px; position: absolute; background: rgba(0, 0, 0, 0.10)"></div>
      <div class="Frame9" style="width: 393px; left: 0px; top: 1px; position: absolute"></div>
      <div class="Frame16" style="height: 50px; padding-top: 14px; padding-bottom: 14px; padding-left: 24px; padding-right: 164px; left: 16px; top: 17px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 8px; overflow: hidden; justify-content: flex-start; align-items: flex-start; gap: 29px; display: inline-flex">
        <div class="Frame18" style="align-self: stretch"></div>
        <div style="text-align: center; color: white; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">保存</div>
      </div>
    </div>
    <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
  <div class="Frame3465272" style="width: 361px; height: 80px; left: 16px; top: 238px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请选择</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">套餐分类</div>
      </div>
    </div>
  </div>
  <div class="Frame3465276" style="width: 361px; height: 80px; left: 16px; top: 616px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">区域价格</div>
      </div>
    </div>
  </div>
  <div class="Frame3465302" style="width: 361px; height: 80px; left: 16px; top: 1237px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">会员卡结账限制</div>
      </div>
    </div>
  </div>
  <div class="Frame3465277" style="width: 361px; height: 80px; left: 16px; top: 712px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">时段价格设置</div>
      </div>
    </div>
  </div>
  <div class="Frame3465310" style="width: 361px; height: 80px; left: 16px; top: 1127px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">可选组</div>
      </div>
    </div>
  </div>
  <div class="Frame3465266" style="left: 20px; top: 472px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持折扣</div>
  </div>
  <div class="Frame14" style="width: 361px; height: 76px; padding: 12px; left: 16px; top: 510px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: flex-start; gap: 9px; display: inline-flex">
    <div class="Frame3465234" style="flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商家折扣</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465230" style="flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商家减免</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
  </div>
  <div class="Frame3465300" style="padding-top: 21px; padding-bottom: 20px; padding-left: 24px; padding-right: 18px; left: 16px; top: 808px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: center; gap: 99px; display: inline-flex">
    <div class="Frame3465356" style="width: 180px; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">启用畅饮模式</div>
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">套餐内的商品，在关台前均可无限量畅饮</div>
    </div>
    <div class="Frame3465196" style="width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame15" style="width: 361px; padding-left: 14px; padding-right: 14px; padding-top: 18px; padding-bottom: 18px; left: 16px; top: 2249px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 14px; display: inline-flex">
    <div class="Frame3465249" style="flex: 1 1 0; align-self: stretch; padding-left: 42px; padding-right: 42px; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="text-align: center; color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">开始时间</div>
    </div>
    <div class="Frame3465251" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
      <div class="Rectangle3469069" style="width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)"></div>
      <div class="Rectangle3469070" style="width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)"></div>
    </div>
    <div class="Frame3465250" style="flex: 1 1 0; align-self: stretch; padding-left: 42px; padding-right: 42px; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">结束时间</div>
    </div>
  </div>
  <div class="Frame3465296" style="width: 361px; height: 80px; left: 16px; top: 2101px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">消费赠券</div>
      </div>
    </div>
  </div>
  <div style="width: 353px; left: 20px; top: 2927px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">提示：该套餐说明用于移动点单、大玩家上显示</div>
  <div class="Frame17" style="padding-top: 24px; padding-bottom: 25px; padding-left: 24px; padding-right: 24px; left: 16px; top: 3157px; position: absolute; background: white; border-radius: 10px; overflow: hidden; flex-direction: column; justify-content: center; align-items: flex-start; gap: 20px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">上传商品图片</div>
    <div class="Frame3465205" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
      <div class="Frame3465203" style="width: 313px; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
        <div class="Frame3465199" style="width: 74px; height: 74px; padding: 23px; background: #F5F5F5; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; display: flex">
          <div class="1" style="flex: 1 1 0; align-self: stretch; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ClipPathGroup" style="width: 2814.61px; height: 3537.13px; position: relative">
              <div class="Vector" style="width: 2814.61px; height: 3537.13px; left: 0px; top: 0px; position: absolute; background: black"></div>
              <div class="Group" style="width: 22.84px; height: 19.48px; left: 2067.74px; top: 1969.74px; position: absolute">
                <div class="ClipPathGroup" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute">
                  <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black"></div>
                  <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black"></div>
                </div>
                <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; border: 0.41px black solid"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="Frame3465200" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
        <div class="Frame3465201" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
        <div class="Frame3465202" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame19" style="width: 361px; height: 179px; left: 16px; top: 918px; position: absolute; background: white; border-radius: 10px; overflow: hidden">
    <div class="Frame3465256" style="left: 16px; top: 16px; position: absolute; justify-content: flex-start; align-items: center; display: inline-flex">
      <div style="color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">套餐商品</div>
    </div>
    <div class="Frame3465228" style="padding-top: 4px; padding-bottom: 4px; padding-left: 4px; padding-right: 10px; left: 289px; top: 13px; position: absolute; background: black; border-radius: 25px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
      <div class="Frame3465223" style="justify-content: flex-start; align-items: center; display: flex">
        <div class="Frame3465222" style="width: 18px; height: 18px; position: relative">
          <div class="Rectangle3469066" style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: white; border-radius: 8px"></div>
          <div class="Rectangle3469067" style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: white; border-radius: 8px"></div>
        </div>
        <div style="color: white; font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">新增</div>
      </div>
    </div>
    <div style="left: 153px; top: 76px; position: absolute; color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">添加商品</div>
    <div class="Rectangle3469201" style="width: 361px; height: 1px; left: 0px; top: 127px; position: absolute; background: rgba(0, 0, 0, 0.05)"></div>
    <div class="000" style="width: 102px; height: 19px; left: 243px; top: 144px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品总价: ¥0.00</div>
  </div>
  <div class="Frame3465285" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1333px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">上架展示</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465287" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1429px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持员工赠送</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465288" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1525px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">计入低消</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465289" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1621px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">计算业绩</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465293" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1909px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">指定投放区域 </div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 23px; background: #BABABA; border-radius: 40px; overflow: hidden; justify-content: flex-start; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465290" style="padding-top: 21px; padding-bottom: 20px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1717px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: center; gap: 99px; display: inline-flex">
    <div class="Frame3465356" style="width: 180px; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">推广</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">开启后将在线上点单推荐分类展示</div>
    </div>
    <div class="Frame3465196" style="width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465292" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1813px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 151px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">指定投放包厢类型</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465303" style="left: 20px; top: 2367px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">点单数量限制</div>
  </div>
  <div class="Frame3465304" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 301px; left: 16px; top: 2405px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">不限制</div>
  </div>
  <div class="Frame3465307" style="left: 20px; top: 2615px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">投放渠道</div>
  </div>
  <div class="Frame3465308" style="width: 361px; height: 198px; left: 16px; top: 2653px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465231" style="padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">点单屏</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465233" style="padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 134px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">落单机</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465230" style="padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 12px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">线上自助</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465232" style="padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">移动点单</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465229" style="padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 12px; position: absolute; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex">
      <div style="color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">收银机</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="Rectangle3469068" style="width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465309" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2005px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 199px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">满低消可点</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
</div>