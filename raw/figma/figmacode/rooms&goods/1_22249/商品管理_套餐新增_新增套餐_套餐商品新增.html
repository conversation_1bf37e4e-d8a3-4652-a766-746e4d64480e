<div style="width: 393px; height: 852px; position: relative; background: white">
  <div style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white">
    <div style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="left: 165px; top: 56px; position: absolute; opacity: 0.80; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">商品新增</div>
  <div style="width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
    <div style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
      <div style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
    </div>
    <div style="width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div style="width: 66.66px; height: 11.34px; position: relative">
      <div style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div style="height: 21px; left: 0px; top: 831px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; justify-content: center; align-items: center; display: inline-flex">
      <div style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
  <div style="padding-top: 14px; padding-bottom: 14px; padding-left: 14px; padding-right: 157px; left: 16px; top: 114px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
    <div style="width: 28px; height: 28px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
      <img style="width: 17.60px; height: 17.37px" src="https://via.placeholder.com/18x17" />
    </div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">搜索商品</div>
  </div>
  <div style="width: 90px; height: 56px; padding: 4px; left: 287px; top: 114px; position: absolute; background: black; border-radius: 10px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
    <div style="justify-content: center; align-items: center; gap: 4px; display: flex">
      <div style="color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">保存</div>
    </div>
  </div>
  <div style="height: 864px; left: 0px; top: 194px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="width: 96px; height: 72px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">酒水</div>
    </div>
    <div style="width: 96px; height: 72px; background: #F5F5F5; border-top-left-radius: 10px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">饮料</div>
    </div>
    <div style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">果盘</div>
    </div>
    <div style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">小吃</div>
    </div>
    <div style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">其他</div>
    </div>
    <div style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">烧烤</div>
    </div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
  </div>
  <div style="height: 670px; left: 96px; top: 194px; position: absolute; background: white; flex-direction: column; justify-content: flex-end; align-items: center; display: inline-flex">
    <div style="align-self: stretch; height: 720px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; opacity: 0.20; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="padding-left: 5px; padding-right: 5px; padding-top: 1.50px; padding-bottom: 1.50px; left: 94px; top: 35px; position: absolute; background: rgba(0, 0, 0, 0.06); border-radius: 2px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <div style="color: rgba(0, 0, 0, 0.40); font-size: 9px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">沽清</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 8px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">1</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; padding-top: 15px; padding-bottom: 15px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 20px; height: 40px" src="https://via.placeholder.com/20x40" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 198px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 232px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 259px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 297px; height: 90px; position: relative">
        <div style="width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
          <img style="width: 70px; height: 70px; border-radius: 7px" src="https://via.placeholder.com/70x70" />
        </div>
        <div style="left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">轩尼诗VSOP700ml</div>
        <div style="width: 41px; height: 22px; left: 94px; top: 58px; position: absolute">
          <div style="left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">¥</div>
          <div style="left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">/瓶</div>
          <div style="left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">98</div>
        </div>
        <div style="width: 24px; height: 24px; left: 267px; top: 33px; position: absolute; border-radius: 35px; overflow: hidden">
          <div style="width: 10px; height: 2px; left: 7px; top: 11px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px"></div>
          <div style="width: 10px; height: 2px; left: 7px; top: 7px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px"></div>
          <div style="width: 10px; height: 2px; left: 7px; top: 15px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 9px"></div>
        </div>
        <div style="width: 16px; height: 16px; left: 143px; top: 62px; position: absolute; background: #F5F5F5; border-radius: 14px; overflow: hidden">
          <div style="width: 6.93px; height: 5px; left: 11.46px; top: 11px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.40); border-radius: 0.80px"></div>
        </div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 200px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: rgba(0, 0, 0, 0.20); border-radius: 8px"></div>
            </div>
          </div>
        </div>
        <div style="width: 17px; left: 234px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">0</div>
        <div style="width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 261px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex">
          <div style="justify-content: flex-start; align-items: center; gap: 2px; display: flex">
            <div style="width: 18px; height: 18px; position: relative">
              <div style="width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px"></div>
              <div style="width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>