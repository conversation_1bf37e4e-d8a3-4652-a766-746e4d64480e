<div class=" w-[393px] h-[3764px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">套餐信息</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3465271 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">套擦名称</div>
    <div class="Frame3465264 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465297 left-[20px] top-[3186px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">套餐介绍</div>
  </div>
  <div class="Frame3465299 left-[20px] top-[3400px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">套餐图片</div>
  </div>
  <div class="Frame3465274 left-[20px] top-[348px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">商品现价</div>
    <div class="Frame3465264 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465305 left-[20px] top-[2772px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">条形码</div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[281px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="1 text-black text-sm font-medium font-['PingFang SC']">酒水套餐1</div>
  </div>
  <div class="Frame3465295 left-[20px] top-[2492px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">上架时段</div>
  </div>
  <div class="Frame3465298 w-[361px] h-[129px] px-[18px] pt-[18px] pb-[91px] left-[16px] top-[3241px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div class=" w-[325px] text-black text-sm font-medium font-['PingFang SC']">套餐介绍，这是文字最大宽度的示例。请查看数值。</div>
  </div>
  <div class="Frame3465275 w-[361px] pl-[18px] pr-[297px] py-[18px] left-[16px] top-[386px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465337 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">20</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/元</div>
    </div>
  </div>
  <div class="Frame3465306 pl-2.5 pr-[202px] py-2.5 left-[16px] top-[2810px] absolute bg-white rounded-[10px] border justify-start items-center gap-2.5 inline-flex">
    <div class="IcScan w-9 h-9 relative flex-col justify-start items-start flex">
      <img class="Subtract w-4 h-4" src="https://via.placeholder.com/16x16" />
      <div class="Rectangle3469226 w-4 h-0.5 bg-black/40"></div>
    </div>
    <div class=" text-black text-sm font-medium font-['PingFang SC']">1262256775651</div>
  </div>
  <div class="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[3743px] absolute justify-center items-center inline-flex">
    <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[3643px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465272 w-[361px] h-20 left-[16px] top-[238px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">套餐分类</div>
      </div>
    </div>
  </div>
  <div class="Frame3465276 w-[361px] h-20 left-[16px] top-[616px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">区域价格</div>
      </div>
    </div>
  </div>
  <div class="Frame3465302 w-[361px] h-20 left-[16px] top-[1351px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">会员卡结账限制</div>
      </div>
    </div>
  </div>
  <div class="Frame3465277 w-[361px] h-20 left-[16px] top-[712px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">时段价格设置</div>
      </div>
    </div>
  </div>
  <div class="Frame3465266 left-[20px] top-[472px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">支持折扣</div>
  </div>
  <div class="Frame14 w-[361px] h-[76px] p-3 left-[16px] top-[510px] absolute bg-white rounded-[10px] border justify-center items-start gap-[9px] inline-flex">
    <div class="Frame3465229 grow shrink basis-0 self-stretch pl-4 pr-3.5 py-4 bg-[#5855ff] rounded-md border justify-center items-center gap-[62px] inline-flex">
      <div class=" text-white text-sm font-medium font-['PingFang SC']">商家折扣</div>
      <div class="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex"></div>
    </div>
    <div class="Frame3465230 grow shrink basis-0 self-stretch pl-4 pr-3.5 py-4 rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">商家减免</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
  </div>
  <div class="Frame3465300 pl-6 pr-[18px] pt-[21px] pb-5 left-[16px] top-[808px] absolute bg-white rounded-[10px] border justify-end items-center gap-[99px] inline-flex">
    <div class="Frame3465356 w-[180px] self-stretch flex-col justify-start items-start inline-flex">
      <div class=" self-stretch text-black text-base font-medium font-['PingFang SC']">启用畅饮模式</div>
      <div class=" text-black/40 text-xs font-normal font-['PingFang SC']">套餐内的商品，在关台前均可无限量畅饮</div>
    </div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame15 w-[361px] px-3.5 py-[18px] left-[16px] top-[2530px] absolute bg-white rounded-[10px] border justify-center items-center gap-3.5 inline-flex">
    <div class="Frame3465249 grow shrink basis-0 self-stretch py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div class="00 text-center text-black text-sm font-medium font-['PingFang SC']">06:00</div>
    </div>
    <div class="Frame3465251 self-stretch justify-start items-center gap-0.5 inline-flex"></div>
    <div class="Frame3465250 grow shrink basis-0 self-stretch py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div class="00 text-black text-sm font-medium font-['PingFang SC']">08:00</div>
    </div>
  </div>
  <div class="Frame3465296 w-[361px] h-20 left-[16px] top-[2382px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">消费赠券</div>
      </div>
    </div>
  </div>
  <div class=" w-[353px] left-[20px] top-[3208px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">提示：该套餐说明用于移动点单、大玩家上显示</div>
  <div class="Frame17 px-6 pt-6 pb-[25px] left-[16px] top-[3438px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
    <div class=" text-black text-base font-normal font-['PingFang SC']">上传商品图片</div>
    <div class="Frame3465205 self-stretch flex-col justify-start items-start gap-1.5 inline-flex">
      <div class="Frame3465203 w-[313px] justify-start items-start gap-1.5 inline-flex">
        <div class="Frame3465199 w-[74px] h-[74px] p-[23px] bg-neutral-100 rounded-md border justify-center items-center flex">
          <div class="1 grow shrink basis-0 self-stretch justify-start items-center inline-flex">
            <div class="ClipPathGroup w-[2814.61px] h-[3537.13px] relative">
              <div class="Group w-[22.84px] h-[19.48px] left-[2067.74px] top-[1969.74px] absolute">
                <div class="ClipPathGroup w-[22.84px] h-[19.48px] left-0 top-0 absolute">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="Frame3465200 h-[74px] relative opacity-0 rounded border border-black"></div>
        <div class="Frame3465201 h-[74px] relative opacity-0 rounded border border-black"></div>
        <div class="Frame3465202 h-[74px] relative opacity-0 rounded border border-black"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465285 h-20 pl-6 pr-[18px] left-[16px] top-[1447px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">上架展示</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465287 h-20 pl-6 pr-[18px] left-[16px] top-[1543px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">支持员工赠送</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465288 h-20 pl-6 pr-[18px] left-[16px] top-[1639px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">计入低消</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465289 h-20 pl-6 pr-[18px] left-[16px] top-[1735px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">计算业绩</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465293 h-20 pl-6 pr-[18px] left-[16px] top-[2023px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">指定投放区域 </div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465290 pl-6 pr-[18px] pt-[21px] pb-5 left-[16px] top-[1831px] absolute bg-white rounded-[10px] border justify-end items-center gap-[99px] inline-flex">
    <div class="Frame3465356 w-[180px] self-stretch flex-col justify-start items-start inline-flex">
      <div class=" self-stretch text-black text-base font-medium font-['PingFang SC']">推广</div>
      <div class=" self-stretch text-black/40 text-xs font-normal font-['PingFang SC']">开启后将在线上点单推荐分类展示</div>
    </div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465292 h-20 pl-6 pr-[18px] left-[16px] top-[1927px] absolute bg-white rounded-[10px] border justify-end items-start gap-[151px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">指定投放包厢类型</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465303 left-[20px] top-[2648px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">点单数量限制</div>
  </div>
  <div class="Frame3465304 w-[361px] pl-[18px] pr-[317px] py-[18px] left-[16px] top-[2686px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class=" text-black text-sm font-medium font-['PingFang SC']">200</div>
  </div>
  <div class="Frame3465307 left-[20px] top-[2896px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">投放渠道</div>
  </div>
  <div class="Frame3465308 w-[361px] h-[198px] left-[16px] top-[2934px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465231 pl-4 pr-3.5 py-4 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[76px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465233 pl-4 pr-3.5 py-4 left-[12px] top-[134px] absolute rounded-md border border-black/10 justify-center items-center gap-[76px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">落单机</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465230 pl-4 pr-3.5 py-4 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">线上自助</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465232 pl-4 pr-3.5 py-4 left-[185px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465229 pl-4 pr-3.5 py-4 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[76px] inline-flex">
      <div class=" text-white text-sm font-medium font-['PingFang SC']">收银机</div>
      <div class="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex"></div>
    </div>
  </div>
  <div class="Frame3465309 h-20 pl-6 pr-[18px] left-[16px] top-[2286px] absolute bg-white rounded-[10px] border justify-end items-start gap-[199px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">满低消可点</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame19 w-[361px] h-[307px] left-[16px] top-[918px] absolute bg-white rounded-[10px]">
    <div class="Frame3465256 left-[16px] top-[16px] absolute justify-start items-center inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">套餐商品</div>
    </div>
    <div class="Frame3465228 pl-1 pr-2.5 py-1 left-[289px] top-[13px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
      <div class="Frame3465223 justify-start items-center flex">
        <div class="Frame3465222 w-[18px] h-[18px] relative">
          <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg"></div>
          <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg"></div>
        </div>
        <div class=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div class="Rectangle3469201 w-[361px] h-px left-0 top-[255px] absolute bg-black/5"></div>
    <div class="19600 left-[229px] top-[272px] absolute text-right text-black text-sm font-medium font-['PingFang SC']">商品总价: ¥196.00</div>
    <div class="Frame3465346 w-[361px] h-[90px] left-0 top-[59px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">1</div>
      <div class="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg"></div>
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
    <div class="Frame3465347 w-[361px] h-[90px] left-0 top-[149px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469059 w-[70px] h-[70px] rounded-[7px]" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465353 w-6 h-6 px-3 py-2 left-[264px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class=" w-[17px] left-[298px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">1</div>
      <div class="Frame3465221 w-6 h-6 px-3 py-2 left-[325px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg"></div>
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[325px] top-[10px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465310 w-[361px] h-20 left-[16px] top-[1255px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">可选组</div>
      </div>
    </div>
  </div>
  <div class="Frame3465311 w-[361px] h-[137px] left-[16px] top-[2119px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465233 pl-4 pr-3.5 py-4 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[76px] inline-flex">
      <div class="0 text-white text-sm font-medium font-['PingFang SC']">区域 0</div>
      <div class="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex"></div>
    </div>
    <div class="Frame3465234 pl-4 pr-3.5 py-4 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[81px] inline-flex">
      <div class="2 text-black/40 text-sm font-medium font-['PingFang SC']">区域2</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465235 pl-4 pr-3.5 py-4 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-[84px] inline-flex">
      <div class="1 text-black/40 text-sm font-medium font-['PingFang SC']">区域1</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465236 pl-4 pr-3.5 py-4 left-[185px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[81px] inline-flex">
      <div class="3 text-black/40 text-sm font-medium font-['PingFang SC']">区域3</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
  </div>
</div>