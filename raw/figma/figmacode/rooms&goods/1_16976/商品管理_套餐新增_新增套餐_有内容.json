{"codeType": "HTML", "figmaPageCode": "<div style=\"width: 393px; height: 3764px; position: relative; background: #F5F5F5\">\n  <div style=\"width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white\">\n    <div style=\"width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9\"></div>\n    <div style=\"width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid\"></div>\n    <img style=\"width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/18x17\" />\n    <img style=\"width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/20x6\" />\n    <div style=\"width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid\"></div>\n  </div>\n  <div style=\"left: 157px; top: 56px; position: absolute; opacity: 0.80; text-align: center; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">套餐信息</div>\n  <div style=\"width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n    <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n      <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n    </div>\n    <div style=\"width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid\"></div>\n  </div>\n  <div style=\"width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 28.43px; height: 11.09px; background: black\"></div>\n      </div>\n    </div>\n    <div style=\"width: 66.66px; height: 11.34px; position: relative\">\n      <div style=\"width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute\">\n        <div style=\"width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid\"></div>\n        <div style=\"width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black\"></div>\n        <div style=\"width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px\"></div>\n      </div>\n      <img style=\"width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute\" src=\"https://via.placeholder.com/15x11\" />\n      <img style=\"width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute\" src=\"https://via.placeholder.com/17x11\" />\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 114px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套擦名称</div>\n    <div style=\"width: 12px; height: 12px; position: relative\">\n      <div style=\"width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute\">\n        <div style=\"width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 3186px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐介绍</div>\n  </div>\n  <div style=\"left: 20px; top: 3400px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐图片</div>\n  </div>\n  <div style=\"left: 20px; top: 348px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品现价</div>\n    <div style=\"width: 12px; height: 12px; position: relative\">\n      <div style=\"width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute\">\n        <div style=\"width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 2772px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">条形码</div>\n  </div>\n  <div style=\"width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 281px; left: 16px; top: 152px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex\">\n    <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">酒水套餐1</div>\n  </div>\n  <div style=\"left: 20px; top: 2492px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">上架时段</div>\n  </div>\n  <div style=\"width: 361px; height: 129px; padding-top: 18px; padding-bottom: 91px; padding-left: 18px; padding-right: 18px; left: 16px; top: 3241px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; display: inline-flex\">\n    <div style=\"width: 325px; color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐介绍，这是文字最大宽度的示例。请查看数值。</div>\n  </div>\n  <div style=\"width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 297px; left: 16px; top: 386px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex\">\n    <div style=\"align-self: stretch; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">20</div>\n      <div style=\"color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">/元</div>\n    </div>\n  </div>\n  <div style=\"padding-top: 10px; padding-bottom: 10px; padding-left: 10px; padding-right: 202px; left: 16px; top: 2810px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex\">\n    <div style=\"width: 36px; height: 36px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n      <img style=\"width: 16px; height: 16px\" src=\"https://via.placeholder.com/16x16\" />\n      <div style=\"width: 16px; height: 2px; background: rgba(0, 0, 0, 0.40)\"></div>\n    </div>\n    <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">1262256775651</div>\n  </div>\n  <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; left: 0px; top: 3743px; position: absolute; justify-content: center; align-items: center; display: inline-flex\">\n    <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n  </div>\n  <div style=\"height: 121px; left: 0px; top: 3643px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n    <div style=\"width: 393px; height: 100px; position: relative; background: white\">\n      <div style=\"width: 393px; height: 1px; left: 0px; top: 0px; position: absolute; background: rgba(0, 0, 0, 0.10)\"></div>\n      <div style=\"width: 393px; left: 0px; top: 1px; position: absolute\"></div>\n      <div style=\"height: 50px; padding-top: 14px; padding-bottom: 14px; padding-left: 24px; padding-right: 164px; left: 16px; top: 17px; position: absolute; background: black; border-radius: 8px; overflow: hidden; justify-content: flex-start; align-items: flex-start; gap: 29px; display: inline-flex\">\n        <div style=\"align-self: stretch\"></div>\n        <div style=\"text-align: center; color: white; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">保存</div>\n      </div>\n    </div>\n    <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 238px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐分类</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 616px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域价格</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 1351px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">会员卡结账限制</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 712px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">时段价格设置</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 472px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">支持折扣</div>\n  </div>\n  <div style=\"width: 361px; height: 76px; padding: 12px; left: 16px; top: 510px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: flex-start; gap: 9px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家折扣</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家减免</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n  </div>\n  <div style=\"padding-top: 21px; padding-bottom: 20px; padding-left: 24px; padding-right: 18px; left: 16px; top: 808px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: center; gap: 99px; display: inline-flex\">\n    <div style=\"width: 180px; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">启用畅饮模式</div>\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word\">套餐内的商品，在关台前均可无限量畅饮</div>\n    </div>\n    <div style=\"width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"width: 361px; padding-left: 14px; padding-right: 14px; padding-top: 18px; padding-bottom: 18px; left: 16px; top: 2530px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 14px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"text-align: center; color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">06:00</div>\n    </div>\n    <div style=\"align-self: stretch; justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex\">\n      <div style=\"width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)\"></div>\n      <div style=\"width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)\"></div>\n    </div>\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">08:00</div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 2382px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">消费赠券</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 353px; left: 20px; top: 3208px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word\">提示：该套餐说明用于移动点单、大玩家上显示</div>\n  <div style=\"padding-top: 24px; padding-bottom: 25px; padding-left: 24px; padding-right: 24px; left: 16px; top: 3438px; position: absolute; background: white; border-radius: 10px; overflow: hidden; flex-direction: column; justify-content: center; align-items: flex-start; gap: 20px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word\">上传商品图片</div>\n    <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex\">\n      <div style=\"width: 313px; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex\">\n        <div style=\"width: 74px; height: 74px; padding: 23px; background: #F5F5F5; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; display: flex\">\n          <div style=\"flex: 1 1 0; align-self: stretch; justify-content: flex-start; align-items: center; display: inline-flex\">\n            <div style=\"width: 2814.61px; height: 3537.13px; position: relative\">\n              <div style=\"width: 2814.61px; height: 3537.13px; left: 0px; top: 0px; position: absolute; background: black\"></div>\n              <div style=\"width: 22.84px; height: 19.48px; left: 2067.74px; top: 1969.74px; position: absolute\">\n                <div style=\"width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute\">\n                  <div style=\"width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black\"></div>\n                  <div style=\"width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black\"></div>\n                </div>\n                <div style=\"width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; border: 0.41px black solid\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div style=\"width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid\"></div>\n        <div style=\"width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid\"></div>\n        <div style=\"width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1447px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">上架展示</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1543px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">支持员工赠送</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1639px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">计入低消</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1735px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">计算业绩</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2023px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">指定投放区域 </div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"padding-top: 21px; padding-bottom: 20px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1831px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: center; gap: 99px; display: inline-flex\">\n    <div style=\"width: 180px; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">推广</div>\n      <div style=\"align-self: stretch; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word\">开启后将在线上点单推荐分类展示</div>\n    </div>\n    <div style=\"width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1927px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 151px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">指定投放包厢类型</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 2648px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">点单数量限制</div>\n  </div>\n  <div style=\"width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 317px; left: 16px; top: 2686px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex\">\n    <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">200</div>\n  </div>\n  <div style=\"left: 20px; top: 2896px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">投放渠道</div>\n  </div>\n  <div style=\"width: 361px; height: 198px; left: 16px; top: 2934px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">点单屏</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 134px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">落单机</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 12px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">线上自助</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">移动点单</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 12px; position: absolute; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">收银机</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2286px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 199px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">满低消可点</div>\n    <div style=\"width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex\">\n      <div style=\"width: 12px; height: 12px; position: relative; background: white; border-radius: 20px\"></div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 307px; left: 16px; top: 918px; position: absolute; background: white; border-radius: 10px; overflow: hidden\">\n    <div style=\"left: 16px; top: 16px; position: absolute; justify-content: flex-start; align-items: center; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐商品</div>\n    </div>\n    <div style=\"padding-top: 4px; padding-bottom: 4px; padding-left: 4px; padding-right: 10px; left: 289px; top: 13px; position: absolute; background: black; border-radius: 25px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; display: flex\">\n        <div style=\"width: 18px; height: 18px; position: relative\">\n          <div style=\"width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: white; border-radius: 8px\"></div>\n          <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: white; border-radius: 8px\"></div>\n        </div>\n        <div style=\"color: white; font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">新增</div>\n      </div>\n    </div>\n    <div style=\"width: 361px; height: 1px; left: 0px; top: 255px; position: absolute; background: rgba(0, 0, 0, 0.05)\"></div>\n    <div style=\"left: 229px; top: 272px; position: absolute; text-align: right; color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品总价: ¥196.00</div>\n    <div style=\"width: 361px; height: 90px; left: 0px; top: 59px; position: absolute\">\n      <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n        <img style=\"width: 70px; height: 70px; border-radius: 8px\" src=\"https://via.placeholder.com/70x70\" />\n      </div>\n      <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n      <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n        <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n        <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n        <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n      </div>\n      <div style=\"width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 262px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n        <div style=\"justify-content: flex-start; align-items: center; gap: 2px; display: flex\">\n          <div style=\"width: 18px; height: 18px; position: relative\">\n            <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px\"></div>\n          </div>\n        </div>\n      </div>\n      <div style=\"width: 17px; left: 296px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">1</div>\n      <div style=\"width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 323px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n        <div style=\"justify-content: flex-start; align-items: center; gap: 2px; display: flex\">\n          <div style=\"width: 18px; height: 18px; position: relative\">\n            <div style=\"width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px\"></div>\n            <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px\"></div>\n          </div>\n        </div>\n      </div>\n      <div style=\"width: 24px; height: 24px; left: 323px; top: 10px; position: absolute; border-radius: 35px; overflow: hidden\">\n        <div style=\"width: 4px; height: 1px; left: 10px; top: 6px; position: absolute; background: #FF4747\"></div>\n        <div style=\"width: 10px; height: 1.50px; left: 7px; top: 7px; position: absolute; background: #FF4747; border-radius: 3px\"></div>\n        <div style=\"width: 8px; height: 7px; left: 8px; top: 10px; position: absolute; border-radius: 1px; border: 1.50px #FF4747 solid\"></div>\n      </div>\n    </div>\n    <div style=\"width: 361px; height: 90px; left: 0px; top: 149px; position: absolute\">\n      <div style=\"width: 70px; height: 70px; left: 14px; top: 10px; position: absolute; background: #F3F3F3; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex\">\n        <img style=\"width: 70px; height: 70px; border-radius: 7px\" src=\"https://via.placeholder.com/70x70\" />\n      </div>\n      <div style=\"left: 94px; top: 12px; position: absolute; opacity: 0.80; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">轩尼诗VSOP700ml</div>\n      <div style=\"width: 41px; height: 22px; left: 94px; top: 58px; position: absolute\">\n        <div style=\"left: 0px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">¥</div>\n        <div style=\"left: 29px; top: 9px; position: absolute; color: #FF3333; font-size: 8px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">/瓶</div>\n        <div style=\"left: 7px; top: 0px; position: absolute; color: #FF3333; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">98</div>\n      </div>\n      <div style=\"width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 264px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n        <div style=\"justify-content: flex-start; align-items: center; gap: 2px; display: flex\">\n          <div style=\"width: 18px; height: 18px; position: relative\">\n            <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px\"></div>\n          </div>\n        </div>\n      </div>\n      <div style=\"width: 17px; left: 298px; top: 58px; position: absolute; text-align: center; color: black; font-size: 15px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">1</div>\n      <div style=\"width: 24px; height: 24px; padding-left: 12px; padding-right: 12px; padding-top: 8px; padding-bottom: 8px; left: 325px; top: 56px; position: absolute; background: rgba(0, 0, 0, 0.05); border-radius: 22px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n        <div style=\"justify-content: flex-start; align-items: center; gap: 2px; display: flex\">\n          <div style=\"width: 18px; height: 18px; position: relative\">\n            <div style=\"width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: black; border-radius: 8px\"></div>\n            <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: black; border-radius: 8px\"></div>\n          </div>\n        </div>\n      </div>\n      <div style=\"width: 24px; height: 24px; left: 325px; top: 10px; position: absolute; border-radius: 35px; overflow: hidden\">\n        <div style=\"width: 4px; height: 1px; left: 10px; top: 6px; position: absolute; background: #FF4747\"></div>\n        <div style=\"width: 10px; height: 1.50px; left: 7px; top: 7px; position: absolute; background: #FF4747; border-radius: 3px\"></div>\n        <div style=\"width: 8px; height: 7px; left: 8px; top: 10px; position: absolute; border-radius: 1px; border: 1.50px #FF4747 solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 1255px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">已设置</div>\n    </div>\n    <div style=\"left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">可选组</div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 137px; left: 16px; top: 2119px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 12px; position: absolute; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 76px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域 0</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 12px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 81px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域2</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 12px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 84px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域1</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 185px; top: 73px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 81px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域3</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n  </div>\n</div>"}