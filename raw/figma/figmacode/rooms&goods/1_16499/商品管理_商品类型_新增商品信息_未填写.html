<div style="width: 393px; height: 3563px; position: relative; background: #F5F5F5">
  <div class="MiniProgramsButtons" style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white">
    <div class="Rectangle938" style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div class="Stroke" style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img class="Union" style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img class="Union" style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div class="Separator" style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="left: 157px; top: 56px; position: absolute; opacity: 0.80; text-align: center; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">商品信息</div>
  <div class="MaskGroup" style="width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
    <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
      <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
    </div>
    <div class="Rectangle3469044" style="width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3465271" style="left: 20px; top: 218px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品名称</div>
    <div class="Frame3465264" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465297" style="left: 20px; top: 2968px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品介绍</div>
  </div>
  <div class="Frame3465299" style="left: 20px; top: 3199px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品介绍</div>
  </div>
  <div class="Frame3465274" style="left: 20px; top: 548px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品现价</div>
    <div class="Frame3465264" style="width: 12px; height: 12px; position: relative">
      <div class="Group14147" style="width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute">
        <div class="Vector17" style="width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector19" style="width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
        <div class="Vector18" style="width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465281" style="left: 20px; top: 2844px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">条形码</div>
  </div>
  <div class="Frame11" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 287px; left: 16px; top: 256px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品类型</div>
  </div>
  <div class="Frame3465295" style="left: 20px; top: 2654px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">投放时段</div>
  </div>
  <div class="Frame3465298" style="width: 361px; height: 129px; padding-top: 18px; padding-bottom: 91px; padding-left: 18px; padding-right: 245px; left: 16px; top: 3040px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入商品介绍</div>
  </div>
  <div class="Frame3465275" style="width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 301px; left: 16px; top: 586px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex">
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
  </div>
  <div class="Frame3465282" style="width: 361px; padding-top: 10px; padding-bottom: 10px; padding-left: 10px; padding-right: 263px; left: 16px; top: 2882px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
    <div class="IcScan" style="width: 36px; height: 36px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
      <img class="Subtract" style="width: 16px; height: 16px" src="https://via.placeholder.com/16x16" />
      <div class="Rectangle3469226" style="width: 16px; height: 2px; background: rgba(0, 0, 0, 0.40)"></div>
    </div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请输入</div>
  </div>
  <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; left: 0px; top: 3542px; position: absolute; justify-content: center; align-items: center; display: inline-flex">
    <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
  </div>
  <div class="Frame3" style="height: 121px; left: 0px; top: 3442px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="width: 393px; height: 100px; position: relative; background: white">
      <div class="Frame4" style="width: 393px; height: 1px; left: 0px; top: 0px; position: absolute; background: rgba(0, 0, 0, 0.10)"></div>
      <div class="Frame9" style="width: 393px; left: 0px; top: 1px; position: absolute"></div>
      <div class="Frame16" style="height: 50px; padding-top: 14px; padding-bottom: 14px; padding-left: 24px; padding-right: 164px; left: 16px; top: 17px; position: absolute; background: rgba(0, 0, 0, 0.20); border-radius: 8px; overflow: hidden; justify-content: flex-start; align-items: flex-start; gap: 29px; display: inline-flex">
        <div class="Frame18" style="align-self: stretch"></div>
        <div style="text-align: center; color: white; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">保存</div>
      </div>
    </div>
    <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
  <div class="Frame3465199" style="width: 361px; height: 80px; left: 16px; top: 114px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请选择</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 21px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品类型</div>
      </div>
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">提前设置好价格，支持多渠道投放</div>
    </div>
  </div>
  <div class="Frame3465272" style="width: 361px; height: 80px; left: 16px; top: 342px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请选择</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品分类</div>
      </div>
    </div>
  </div>
  <div class="Frame3465276" style="width: 361px; height: 80px; left: 16px; top: 816px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">区域价格</div>
      </div>
    </div>
  </div>
  <div class="Frame3465273" style="width: 361px; height: 80px; left: 16px; top: 438px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">请选择</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">单位</div>
      </div>
    </div>
  </div>
  <div class="Frame3465277" style="width: 361px; height: 80px; left: 16px; top: 912px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">时段价格设置</div>
      </div>
    </div>
  </div>
  <div class="Frame3465278" style="width: 361px; height: 80px; left: 16px; top: 1008px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">买赠方案</div>
      </div>
    </div>
  </div>
  <div class="Frame3465279" style="width: 361px; height: 80px; left: 16px; top: 1200px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">推荐搭配</div>
      </div>
    </div>
  </div>
  <div class="Frame3465280" style="width: 361px; height: 80px; left: 16px; top: 1296px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">会员卡结账限制</div>
      </div>
    </div>
  </div>
  <div class="Frame3465283" style="width: 361px; height: 80px; left: 16px; top: 1392px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商品口味</div>
      </div>
    </div>
  </div>
  <div class="Frame3465284" style="width: 361px; height: 80px; left: 16px; top: 1488px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">辅料配方</div>
      </div>
    </div>
  </div>
  <div class="Frame3465296" style="width: 361px; height: 80px; left: 16px; top: 2256px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid">
    <div class="Frame3465252" style="left: 18px; top: 21px; position: absolute"></div>
    <div class="MaskGroup" style="width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
      <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
        <div class="Rectangle3469043" style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
      </div>
      <div class="Rectangle3469044" style="width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid"></div>
    </div>
    <div class="Frame3465279" style="height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">未设置</div>
    </div>
    <div class="Frame3465355" style="left: 24px; top: 29.50px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div class="Frame3465278" style="justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">消费赠券</div>
      </div>
    </div>
  </div>
  <div class="Frame3465266" style="left: 20px; top: 672px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持折扣</div>
  </div>
  <div class="Frame14" style="width: 361px; height: 76px; padding: 12px; left: 16px; top: 710px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: flex-start; gap: 9px; display: inline-flex">
    <div class="Frame3465234" style="flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商家折扣</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
    <div class="Frame3465230" style="flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">商家减免</div>
      <div class="Frame3465232" style="width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid"></div>
    </div>
  </div>
  <div class="Frame3465202" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1104px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持重复购买</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465285" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1584px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">上架展示</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465286" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2352px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">计算库存</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 23px; background: #BABABA; border-radius: 40px; overflow: hidden; justify-content: flex-start; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465287" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1680px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持员工赠送</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465288" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1776px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">计入低消</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465289" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1872px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">计算业绩</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465291" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2064px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 247px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">沽清</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465292" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2544px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 151px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">指定投放包厢类型</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 23px; background: #BABABA; border-radius: 40px; overflow: hidden; justify-content: flex-start; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465293" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2448px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 183px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">指定投放区域 </div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 23px; background: #BABABA; border-radius: 40px; overflow: hidden; justify-content: flex-start; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465294" style="height: 80px; padding-left: 24px; padding-right: 18px; left: 16px; top: 2160px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: flex-start; gap: 215px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">支持存酒</div>
    <div class="Frame3465196" style="width: 40px; align-self: stretch; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame3465290" style="padding-top: 21px; padding-bottom: 20px; padding-left: 24px; padding-right: 18px; left: 16px; top: 1968px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-end; align-items: center; gap: 99px; display: inline-flex">
    <div class="Frame3465356" style="width: 180px; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">推广</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">开启后将在线上点单推荐分类展示</div>
    </div>
    <div class="Frame3465196" style="width: 40px; height: 22px; padding-top: 5px; padding-bottom: 5px; padding-left: 23px; padding-right: 5px; background: #31BD50; border-radius: 40px; overflow: hidden; justify-content: flex-end; align-items: center; display: inline-flex">
      <div class="Frame3465197" style="width: 12px; height: 12px; position: relative; background: white; border-radius: 20px"></div>
    </div>
  </div>
  <div class="Frame15" style="width: 361px; padding-left: 14px; padding-right: 14px; padding-top: 18px; padding-bottom: 18px; left: 16px; top: 2726px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 14px; display: inline-flex">
    <div class="Frame3465249" style="flex: 1 1 0; align-self: stretch; padding-left: 42px; padding-right: 42px; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="text-align: center; color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">开始时间</div>
    </div>
    <div class="Frame3465251" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
      <div class="Rectangle3469069" style="width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)"></div>
      <div class="Rectangle3469070" style="width: 11px; height: 2px; background: rgba(0, 0, 0, 0.20)"></div>
    </div>
    <div class="Frame3465250" style="flex: 1 1 0; align-self: stretch; padding-left: 42px; padding-right: 42px; padding-top: 16px; padding-bottom: 16px; background: #F5F5F5; border-radius: 6px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">结束时间</div>
    </div>
  </div>
  <div style="width: 353px; left: 20px; top: 2676px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">提示：处于时段外时商品将自动沽清,处于时段内时非手动沽清商品将自动取消沽清</div>
  <div style="width: 353px; left: 20px; top: 2990px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">提示：处于时段外时商品将自动沽清,处于时段内时非手动沽清商品将自动取消沽清</div>
  <div class="Frame17" style="padding-top: 24px; padding-bottom: 25px; padding-left: 24px; padding-right: 24px; left: 16px; top: 3237px; position: absolute; background: white; border-radius: 10px; overflow: hidden; flex-direction: column; justify-content: center; align-items: flex-start; gap: 20px; display: inline-flex">
    <div style="color: black; font-size: 16px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">上传商品图片</div>
    <div class="Frame3465205" style="align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
      <div class="Frame3465203" style="width: 313px; justify-content: flex-start; align-items: flex-start; gap: 6px; display: inline-flex">
        <div class="Frame3465199" style="width: 74px; height: 74px; padding: 23px; background: #F5F5F5; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; display: flex">
          <div class="1" style="flex: 1 1 0; align-self: stretch; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ClipPathGroup" style="width: 2814.61px; height: 3537.13px; position: relative">
              <div class="Vector" style="width: 2814.61px; height: 3537.13px; left: 0px; top: 0px; position: absolute; background: black"></div>
              <div class="Group" style="width: 22.84px; height: 19.48px; left: 2067.74px; top: 1969.74px; position: absolute">
                <div class="ClipPathGroup" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute">
                  <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black"></div>
                  <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; background: black"></div>
                </div>
                <div class="Vector" style="width: 22.84px; height: 19.48px; left: 0px; top: 0px; position: absolute; border: 0.41px black solid"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="Frame3465200" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
        <div class="Frame3465201" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
        <div class="Frame3465202" style="width: 73.67px; height: 74px; position: relative; opacity: 0; border-radius: 4px; border: 1px black solid"></div>
      </div>
    </div>
  </div>
</div>