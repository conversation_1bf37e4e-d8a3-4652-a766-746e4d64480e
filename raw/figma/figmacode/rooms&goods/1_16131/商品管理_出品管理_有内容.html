<div class=" w-[393px] h-[852px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">出品管理</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class=" left-[20px] top-[134px] absolute text-black text-[28px] font-medium font-['PingFang SC']">出品管理</div>
  <div class="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div class="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465221 pl-1.5 pr-3 py-2 left-[311px] top-[137px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
    <div class="Frame3465223 justify-start items-center gap-0.5 flex">
      <div class="Frame3465222 w-[18px] h-[18px] relative">
        <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg"></div>
        <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg"></div>
      </div>
      <div class=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
    </div>
  </div>
  <div class="Frame11 h-40 px-5 pt-6 pb-5 left-[16px] top-[217px] absolute bg-white rounded-xl flex-col justify-end items-start gap-[72px] inline-flex">
    <div class="1 w-[135px] text-black text-base font-semibold font-['PingFang SC']">出品类型1</div>
    <div class="Frame3465285 self-stretch justify-start items-center gap-0.5 inline-flex">
      <div class=" text-right text-black/30 text-base font-medium font-['PingFang SC']">编辑</div>
      <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 relative">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="Frame3465223 h-40 px-5 pt-6 pb-5 left-[16px] top-[388px] absolute bg-white rounded-xl flex-col justify-end items-start gap-[72px] inline-flex">
    <div class="3 w-[135px] text-black text-base font-semibold font-['PingFang SC']">出品类型3</div>
    <div class="Frame3465285 self-stretch justify-start items-center gap-0.5 inline-flex">
      <div class=" text-right text-black/30 text-base font-medium font-['PingFang SC']">编辑</div>
      <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 relative">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="Frame3465222 h-40 px-5 pt-6 pb-5 left-[202px] top-[217px] absolute bg-white rounded-xl flex-col justify-end items-start gap-[72px] inline-flex">
    <div class="2 w-[135px] text-black text-base font-semibold font-['PingFang SC']">出品类型2</div>
    <div class="Frame3465285 self-stretch justify-start items-center gap-0.5 inline-flex">
      <div class=" text-right text-black/30 text-base font-medium font-['PingFang SC']">编辑</div>
      <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 relative">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="Frame3465224 h-40 px-5 pt-6 pb-5 left-[202px] top-[388px] absolute bg-white rounded-xl flex-col justify-end items-start gap-[72px] inline-flex">
    <div class="4 w-[135px] text-black text-base font-semibold font-['PingFang SC']">出品类型4</div>
    <div class="Frame3465285 self-stretch justify-start items-center gap-0.5 inline-flex">
      <div class=" text-right text-black/30 text-base font-medium font-['PingFang SC']">编辑</div>
      <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 relative">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
          <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
        </div>
      </div>
    </div>
  </div>
</div>