{"codeType": "HTML", "figmaPageCode": "<div style=\"width: 393px; height: 852px; position: relative; background: #F5F5F5\">\n  <div style=\"height: 1232px; left: 16px; top: 217px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: inline-flex\">\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品新增</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐新增</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品类型</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐类型</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 197px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品统计分类</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 197px; display: inline-flex\">\n      <div style=\"align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex\">\n        <div style=\"align-self: stretch; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品展示分类</div>\n      </div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">时段管理</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">口味管理</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">出品管理</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品绑定</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 229px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">存酒设置</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 245px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">赠品组</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n    <div style=\"height: 80px; padding-top: 28px; padding-bottom: 30px; padding-left: 24px; padding-right: 24px; background: white; border-radius: 10px; overflow: hidden; justify-content: center; align-items: flex-start; gap: 181px; display: inline-flex\">\n      <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">多商品累计买赠</div>\n      <div style=\"width: 20px; height: 20px; position: relative; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n          <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n        </div>\n        <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white\">\n    <div style=\"width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9\"></div>\n    <div style=\"width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid\"></div>\n    <img style=\"width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/18x17\" />\n    <img style=\"width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/20x6\" />\n    <div style=\"width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid\"></div>\n  </div>\n  <div style=\"left: 157px; top: 56px; position: absolute; opacity: 0.80; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">商品管理</div>\n  <div style=\"width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n    <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n      <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n    </div>\n    <div style=\"width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid\"></div>\n  </div>\n  <div style=\"width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 28.43px; height: 11.09px; background: black\"></div>\n      </div>\n    </div>\n    <div style=\"width: 66.66px; height: 11.34px; position: relative\">\n      <div style=\"width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute\">\n        <div style=\"width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid\"></div>\n        <div style=\"width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black\"></div>\n        <div style=\"width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px\"></div>\n      </div>\n      <img style=\"width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute\" src=\"https://via.placeholder.com/15x11\" />\n      <img style=\"width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute\" src=\"https://via.placeholder.com/17x11\" />\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 134px; position: absolute; color: black; font-size: 28px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商品管理</div>\n  <div style=\"height: 21px; left: 0px; top: 831px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n    <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n    </div>\n  </div>\n</div>"}