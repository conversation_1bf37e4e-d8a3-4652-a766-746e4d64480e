<div class=" w-[393px] h-[1565px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">存酒设置</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[297px] py-[18px] left-[16px] top-[158px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465337 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">20</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3465298 w-[361px] pl-[18px] pr-[305px] py-[18px] left-[16px] top-[299px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465338 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">3</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/次</div>
    </div>
  </div>
  <div class="Frame3465310 w-[361px] pl-[18px] pr-[299px] py-[18px] left-[16px] top-[1348px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465343 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">31</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3465300 w-[361px] pl-[18px] pr-[299px] py-[18px] left-[16px] top-[423px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465339 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">14</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3465302 w-[361px] pl-[18px] pr-[299px] py-[18px] left-[16px] top-[547px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465340 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">31</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3465304 w-[361px] pl-[18px] pr-[299px] py-[18px] left-[16px] top-[671px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465341 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">31</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3465307 w-[361px] pl-[18px] pr-[299px] py-[18px] left-[16px] top-[1097px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465342 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">31</div>
      <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">/天</div>
    </div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[1444px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465297 left-[20px] top-[120px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">存酒保存天数</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465301 left-[20px] top-[385px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">每次续存天数</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465303 left-[20px] top-[509px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=""><span style="text-black text-base font-medium font-['PingFang SC']">提前通知 </span><span style="text-[#ffab2e] text-base font-medium font-['PingFang SC']">客户</span><span style="text-black text-base font-medium font-['PingFang SC']"> 存酒过期天数</span></div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465299 left-[20px] top-[244px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">续存次数</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465311 left-[20px] top-[1293px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">过期多少天后不可支取(超期)</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465305 left-[20px] top-[633px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=""><span style="text-black text-base font-medium font-['PingFang SC']">提前通知 </span><span style="text-[#5855ff] text-base font-medium font-['PingFang SC']">商户 </span><span style="text-black text-base font-medium font-['PingFang SC']">存酒过期天数</span></div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465308 left-[20px] top-[1059px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">过期多少天后自动充公</div>
    <div class="Frame3465265 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="990 w-[353px] left-[20px] top-[266px] absolute text-black/40 text-xs font-normal font-['PingFang SC']"> 0~99次，默认0次，即不可续存</div>
  <div class=" w-[353px] left-[20px] top-[1315px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">过期取酒权限不可超期取酒，取酒超管权限才可超期取酒</div>
  <div class="Frame3465199 w-[361px] h-20 left-[16px] top-[757px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">存酒过期提醒(商户)</div>
    </div>
    <div class=" left-[283px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">商户</div>
  </div>
  <div class="Frame3465306 w-[361px] h-20 left-[16px] top-[853px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">存酒过期提醒(代订人)</div>
    </div>
    <div class=" left-[267px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">代订人</div>
  </div>
  <div class="Frame3465309 w-[361px] h-20 left-[16px] top-[1183px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div class=" text-black text-base font-medium font-['PingFang SC']">充公仓库</div>
      <div class="Frame3465265 w-3 h-3 relative">
        <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
    <div class="1 left-[276px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">仓库1</div>
  </div>
  <div class="Frame3465287 w-[361px] h-20 left-[16px] top-[949px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class=" left-[24px] top-[29px] absolute text-black text-base font-medium font-['PingFang SC']">自动充公</div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] left-[303px] top-[29px] absolute bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
</div>