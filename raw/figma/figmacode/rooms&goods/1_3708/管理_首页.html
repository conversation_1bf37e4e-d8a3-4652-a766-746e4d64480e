<div class=" w-[393px] h-[1720px] relative bg-white">
  <div class="Frame31 w-[393px] h-[533px] left-0 top-[90px] absolute bg-white">
    <div class="MaskGroup w-[393px] h-[453px] left-0 top-0 absolute">
      <div class="Rectangle3469063 w-[393px] h-[453px] left-0 top-0 absolute bg-gradient-to-b from-white to-white"></div>
      <img class="PhotographeBeauteModeTexturesPortraitsSpiritueuxDeCorUpscayl4xRealesrganX4plus1 w-[679px] h-[908px] left-[549px] top-[783px] absolute origin-top-left -rotate-180" src="https://via.placeholder.com/679x908" />
    </div>
    <img class="Frame28 w-[50px] h-[50px] left-[320px] top-[47px] absolute rounded-[27px] border-2 border-black/20" src="https://via.placeholder.com/50x50" />
    <div class="Frame40 h-[68px] left-[20px] top-[38px] absolute flex-col justify-start items-start gap-2 inline-flex">
      <div class="Frame38 self-stretch justify-start items-center gap-1.5 inline-flex">
        <div class=" text-black text-2xl font-semibold font-['PingFang SC']">北苑路北店</div>
        <div class="Frame30 w-5 h-5 relative"></div>
      </div>
      <div class="Frame39 px-3 py-1.5 bg-[#1ecc6e] rounded-[30px] backdrop-blur-[10px] justify-center items-center gap-2.5 inline-flex">
        <div class=" text-white text-[10px] font-semibold font-['PingFang SC']">营业中</div>
      </div>
    </div>
    <div class="Frame3465447 w-[59px] h-[60px] left-[37px] top-[452px] absolute"></div>
  </div>
  <div class="Frame41 h-[45px] left-[37px] top-[250px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">包厢管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">快捷轻松管理好包厢</div>
  </div>
  <div class="Frame45 w-[72px] h-[72px] left-[28px] top-[339px] absolute">
    <div class="Rectangle3469084 w-12 h-[34px] left-[12px] top-[14px] absolute bg-[#3d96ff] rounded border backdrop-blur-[10px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[219px] top-[250px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">商品管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">建立完善的供应链服务</div>
  </div>
  <div class="Frame45 w-[72px] h-[72px] left-[210px] top-[339px] absolute"></div>
  <div class="Frame46 w-[72px] h-[72px] left-[210px] top-[339px] absolute">
    <div class="Group14146 origin-top-left rotate-180 w-[48.29px] h-[44.67px] left-[56.29px] top-[14px] absolute">
      <div class="Ellipse710 w-[13.67px] h-[13.67px] left-[-0.62px] top-[31px] absolute origin-top-left rotate-180 bg-[#ffee99]/50 rounded-full backdrop-blur-[2px]"></div>
      <div class="Ellipse711 w-[13.67px] h-[13.67px] left-[-27.62px] top-[30.91px] absolute origin-top-left rotate-180 bg-[#ffee99]/50 rounded-full backdrop-blur-[2px]"></div>
    </div>
  </div>
  <div class=" w-[137px] left-[37px] top-[445px] absolute text-black text-lg font-semibold font-['PingFang SC']">库存管理</div>
  <div class=" w-[137px] left-[37px] top-[472px] absolute text-black/50 text-[13px] font-normal font-['PingFang SC']">随时随地处理所有进销存业务</div>
  <div class="Group3465433 w-[47px] h-[42px] left-[41px] top-[549px] absolute">
    <div class="Rectangle3469152 w-3 h-[3px] left-[24px] top-[29px] absolute bg-white rounded-[3px]"></div>
    <div class="Rectangle3469151 w-3 h-[3px] left-[24px] top-[10px] absolute bg-white rounded-[3px]"></div>
  </div>
  <div class="Frame42 h-[63px] left-[218px] top-[445px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">营销管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">多样营销活动流量裂变更轻松</div>
  </div>
  <div class="Frame56 w-[72px] h-[72px] left-[209px] top-[534px] absolute">
    <div class="Rectangle3469153 w-12 h-[35px] left-[12px] top-[16px] absolute bg-[#1ec9ff] rounded-[3px]"></div>
    <div class="Rectangle3469154 w-1 h-3.5 left-[34px] top-[26px] absolute bg-white rounded-[5px]"></div>
    <div class="Rectangle3469155 w-1 h-[9px] left-[25px] top-[31px] absolute bg-white rounded-[5px]"></div>
    <div class="Rectangle3469156 w-1 h-1.5 left-[43px] top-[34px] absolute bg-white rounded-[5px]"></div>
    <div class="Rectangle3469157 w-[26px] h-2 left-[23px] top-[49px] absolute bg-[#8ae3f7]/40 rounded-[7px] backdrop-blur-[2px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[37px] top-[640px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">优惠券</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">提高客户消费更轻松</div>
  </div>
  <div class="Frame47 w-[72px] h-[72px] left-[28px] top-[729px] absolute">
    <div class="Rectangle3469151 w-3 h-[3px] left-[36px] top-[27px] absolute bg-white rounded-[3px]"></div>
    <div class="Rectangle3469158 w-[38px] h-[30px] left-[11px] top-[16px] absolute bg-[#ff0d72] rounded"></div>
    <img class="Subtract w-[38px] h-[30px] left-[23px] top-[26px] absolute rounded-[0.40px] backdrop-blur-[3.42px]" src="https://via.placeholder.com/38x30" />
    <div class="Ellipse974 w-[5.42px] h-[5.42px] left-[34.26px] top-[34.19px] absolute origin-top-left rotate-[30deg] rounded-full border-2 border-white"></div>
    <div class="Ellipse975 w-[5.45px] h-[5.45px] left-[46.76px] top-[41.39px] absolute origin-top-left rotate-[30deg] rounded-full border-2 border-white"></div>
    <div class="Rectangle3469160 w-[2.69px] h-[13.78px] left-[43.78px] top-[34.86px] absolute origin-top-left rotate-[30deg] bg-white rounded-[11px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[37px] top-[835px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">业绩管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">让绩效考核更简单</div>
  </div>
  <div class="Frame57 w-[72px] h-[72px] left-[28px] top-[924px] absolute">
    <div class="Ellipse983 w-[38px] h-[38px] left-[17px] top-[17px] absolute bg-[#7353de] rounded-full"></div>
    <img class="Intersect w-[22px] h-[21.98px] left-[13px] top-[13.02px] absolute rounded-[1px] border backdrop-blur-[2px]" src="https://via.placeholder.com/22x22" />
    <div class="Rectangle3469199 w-[19px] h-4 left-[39px] top-[41px] absolute bg-[#cfc1ff]/40 rounded-sm backdrop-blur-[4.13px]"></div>
    <div class="Rectangle3469196 w-[3px] h-2.5 left-[47px] top-[44px] absolute bg-white rounded-[10px]"></div>
    <div class="Rectangle3469198 w-[3px] h-[5px] left-[42px] top-[49px] absolute bg-white rounded-[10px]"></div>
    <div class="Rectangle3469197 w-[3px] h-1.5 left-[52px] top-[48px] absolute bg-white rounded-[10px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[37px] top-[1030px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">线上经营</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">线上自主销售便捷成交</div>
  </div>
  <div class="Frame50 w-[72px] h-[72px] left-[28px] top-[1119px] absolute">
    <div class="Rectangle3469153 w-12 h-[35px] left-[12px] top-[16px] absolute bg-[#1ec9ff] rounded-[3px]"></div>
    <div class="Rectangle3469157 w-[26px] h-2 left-[23px] top-[49px] absolute bg-[#8ae3f7]/40 rounded-[7px] backdrop-blur-[2px]"></div>
  </div>
  <div class="Frame42 h-[63px] left-[37px] top-[1225px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">排号管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">优化流程降低等位流失率</div>
  </div>
  <div class="Frame51 w-[72px] h-[72px] left-[28px] top-[1314px] absolute">
    <img class="Union w-[29.82px] h-[33.55px] left-[31.43px] top-[19.42px] absolute" src="https://via.placeholder.com/30x34" />
    <img class="Union w-[29.82px] h-[33.55px] left-[10px] top-[19.42px] absolute" src="https://via.placeholder.com/30x34" />
    <img class="Union w-[36.44px] h-[41px] left-[16.83px] top-[16px] absolute border backdrop-blur-sm" src="https://via.placeholder.com/36x41" />
    <div class="Rectangle3469169 w-2.5 h-1.5 left-[38px] top-[43px] absolute origin-top-left rotate-90 bg-white rounded-[0.97px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[37px] top-[1420px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">平台对接服务</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">主流平台轻松对接</div>
  </div>
  <div class="Frame48 w-[72px] h-[72px] left-[688px] top-[1044px] absolute">
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute" src="https://via.placeholder.com/0x0" />
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute" src="https://via.placeholder.com/0x0" />
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute backdrop-blur-[3.42px]" src="https://via.placeholder.com/0x0" />
    <div class="Rectangle3469175 w-8 h-8 left-[9px] top-[16px] absolute bg-[#7353de] rounded"></div>
    <div class="Rectangle3469176 w-7 h-7 left-[21px] top-[28px] absolute bg-[#cebdff]/30 rounded-[3px] backdrop-blur-[3.42px]"></div>
  </div>
  <div class="Frame62 w-[72px] h-[72px] left-[28px] top-[1509px] absolute">
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute" src="https://via.placeholder.com/0x0" />
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute" src="https://via.placeholder.com/0x0" />
    <img class="Union w-[0px] h-[0px] left-0 top-0 absolute backdrop-blur-[3.42px]" src="https://via.placeholder.com/0x0" />
    <div class="Rectangle3469175 w-8 h-8 left-[16px] top-[16px] absolute bg-[#7353de] rounded"></div>
    <div class="Rectangle3469176 w-7 h-7 left-[28px] top-[28px] absolute bg-[#cebdff]/30 rounded-[3px] backdrop-blur-[3.42px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[218px] top-[640px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">员工管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">积极性高流失率低</div>
  </div>
  <div class="Frame59 w-[72px] h-[72px] left-[209px] top-[729px] absolute">
    <img class="Union w-[35.56px] h-10 left-[24.83px] top-[16px] absolute" src="https://via.placeholder.com/36x40" />
    <img class="Union w-[35.56px] h-10 left-[12.61px] top-[16px] absolute border backdrop-blur-[2px]" src="https://via.placeholder.com/36x40" />
  </div>
  <div class="Frame42 h-[45px] left-[218px] top-[835px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">门店管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">传统门店快速转型升级</div>
  </div>
  <div class="Frame54 w-[72px] h-[72px] left-[209px] top-[924px] absolute">
    <div class="Rectangle3469151 w-[13.35px] h-[3.34px] left-[29.32px] top-[32.69px] absolute bg-white rounded"></div>
  </div>
  <div class="Frame42 h-[45px] left-[218px] top-[1030px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">收银管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">高效收银安全稳定</div>
  </div>
  <div class="Frame58 w-[72px] h-[72px] left-[209px] top-[1119px] absolute">
    <div class="Rectangle3469166 w-[46.45px] h-[33.87px] left-[13.49px] top-[23.13px] absolute bg-[#ebfff6]/70 rounded border border-[#90e8a3]/40 backdrop-blur-[4.84px]"></div>
    <div class="Rectangle3469168 w-[46.45px] h-[3.87px] left-[13.49px] top-[28.94px] absolute bg-[#40d98f]"></div>
    <div class="Rectangle3469169 w-[9.68px] h-[5.81px] left-[46.39px] top-[47.32px] absolute bg-white rounded-[0.97px]"></div>
  </div>
  <div class="Frame42 h-[45px] left-[218px] top-[1225px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
    <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">会员管理</div>
    <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">智能在线客户服务系统</div>
  </div>
  <div class="Frame60 w-[72px] h-[72px] left-[209px] top-[1314px] absolute"></div>
  <div class="Frame39 w-[172px] h-[186px] left-[201px] top-[1403px] absolute bg-[#e2f1ff]/60 rounded-[20px]">
    <div class="Frame42 h-[45px] left-[17px] top-[17px] absolute flex-col justify-start items-start gap-0.5 inline-flex">
      <div class=" self-stretch text-black text-lg font-semibold font-['PingFang SC']">联系我们</div>
      <div class=" self-stretch text-black/50 text-[13px] font-normal font-['PingFang SC']">为商家提供专业的服务</div>
    </div>
    <div class="Frame61 w-[72px] h-[72px] left-[8px] top-[106px] absolute">
      <div class="Rectangle3469170 w-7 h-11 left-[13px] top-[14px] absolute bg-[#0d8aff] rounded"></div>
      <div class="Rectangle3469173 w-3.5 h-[3px] left-[38px] top-[28px] absolute bg-white rounded-[5px]"></div>
      <div class="Rectangle3469174 w-3.5 h-[3px] left-[38px] top-[35px] absolute bg-white rounded-[5px]"></div>
    </div>
  </div>
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white shadow-inner">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3 h-[91px] left-0 top-[1629px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[70px] bg-white flex-col justify-end items-start flex">
      <div class="Frame4 w-[393px] h-px relative bg-black/10"></div>
      <div class="Frame9 self-stretch justify-start items-center inline-flex">
        <div class="Frame7 h-[78px] pl-7 pr-[27.88px] pt-[5px] pb-7 justify-center items-center flex">
          <div class="Frame6 grow shrink basis-0 self-stretch flex-col justify-start items-center inline-flex">
            <div class="ProgrammingScript2StreamlineFlex w-7 h-7 relative"></div>
            <div class=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">营业简报</div>
          </div>
        </div>
        <div class="Frame8 h-[78px] pl-[27.75px] pr-[28.13px] pt-[5px] pb-7 justify-center items-center flex">
          <div class="Frame6 grow shrink basis-0 self-stretch flex-col justify-start items-center inline-flex">
            <div class="ProgrammingScript2StreamlineFlex w-7 h-7 relative">
              <div class="Rectangle3469192 w-4 h-[18px] left-[5.82px] top-[5px] absolute rounded-sm border-2 border-black"></div>
              <div class="Rectangle3469194 w-0.5 h-1 left-[11.82px] top-[12px] absolute bg-white"></div>
            </div>
            <div class=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">包厢</div>
          </div>
        </div>
        <div class="Frame9 h-[78px] pl-[27.50px] pr-[28.38px] pt-[5px] pb-7 justify-center items-center flex">
          <div class="Frame6 grow shrink basis-0 self-stretch flex-col justify-start items-center inline-flex">
            <div class="ProgrammingScript2StreamlineFlex w-7 h-7 relative">
              <div class="Rectangle3469186 w-[15px] h-4 left-[6.82px] top-[7px] absolute rounded-sm border-2 border-black"></div>
              <div class="Rectangle3469188 w-[9px] h-0.5 left-[9.82px] top-[10px] absolute bg-[#999999]"></div>
              <div class="Rectangle3469190 w-[9px] h-0.5 left-[9.82px] top-[14px] absolute bg-[#999999]"></div>
              <div class="Rectangle3469189 w-[5px] h-0.5 left-[9.82px] top-[18px] absolute bg-[#999999]"></div>
            </div>
            <div class=" text-center text-black/40 text-xs font-medium font-['PingFang SC']">订单</div>
          </div>
        </div>
        <div class="Frame10 h-[78px] pl-[28.25px] pr-[27.63px] pt-[5px] pb-7 justify-center items-center flex">
          <div class="Frame6 grow shrink basis-0 self-stretch flex-col justify-start items-center inline-flex">
            <div class="ProgrammingScript2StreamlineFlex w-7 h-7 relative">
              <div class="Rectangle3469179 w-6 h-0.5 left-[1.82px] top-[21px] absolute bg-[#5855ff] border border-[#5855ff]"></div>
              <div class="Rectangle3469180 w-0.5 h-1.5 left-[12.82px] top-[12px] absolute bg-[#5855ff]"></div>
              <div class="Rectangle3469182 w-0.5 h-[3px] left-[8.82px] top-[15px] absolute bg-[#5855ff]"></div>
              <div class="Rectangle3469181 w-0.5 h-2 left-[16.82px] top-[10px] absolute bg-[#5855ff]"></div>
            </div>
            <div class=" text-center text-[#5855ff] text-xs font-medium font-['PingFang SC']">管理</div>
          </div>
        </div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
</div>