<div class=" w-[393px] h-[852px] relative bg-white">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div class="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame11 pl-3.5 pr-[157px] py-3.5 left-[16px] top-[114px] absolute bg-white rounded-[10px] border border-black/10 justify-start items-center gap-2.5 inline-flex">
    <div class="Frame3465220 w-7 h-7 relative flex-col justify-start items-start flex">
      <img class="Union w-[17.60px] h-[17.37px]" src="https://via.placeholder.com/18x17" />
    </div>
    <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索套餐</div>
  </div>
  <div class="Frame3465228 w-[90px] h-14 p-1 left-[287px] top-[114px] absolute bg-black rounded-[10px] justify-center items-center gap-2.5 inline-flex">
    <div class="Frame3465223 justify-center items-center gap-1 flex">
      <div class="Frame3465222 w-[18px] h-[18px] relative">
        <div class="Rectangle3469066 w-0.5 h-3.5 left-[8px] top-[2px] absolute bg-white rounded-lg"></div>
        <div class="Rectangle3469067 w-0.5 h-3.5 left-[16px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg"></div>
      </div>
      <div class=" text-white text-sm font-medium font-['PingFang SC']">新增</div>
    </div>
  </div>
  <div class="Frame3465323 w-[297px] h-[658px] left-[96px] top-[194px] absolute bg-white">
    <div class="Frame3465346 w-[297px] h-[90px] left-0 top-[68px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[259px] top-[33px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
    <div class="Frame3465347 w-[297px] h-[90px] left-0 top-[158px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469059 w-[70px] h-[70px] rounded-[7px]" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[261px] top-[33px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
    <div class="Frame3465257 w-[92px] h-10 pl-1 pr-2 py-1 left-[103px] top-[272px] absolute bg-black/5 rounded-[46px] justify-center items-center gap-2.5 inline-flex">
      <div class="Frame3465223 justify-center items-center gap-1 flex">
        <div class="Frame3465223 justify-start items-center flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg"></div>
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
          <div class=" text-black text-xs font-medium font-['PingFang SC']">添加商品</div>
        </div>
      </div>
    </div>
    <div class="1 left-[14px] top-[26px] absolute text-black/40 text-xs font-medium font-['PingFang SC']">赠品组 1</div>
    <div class=" left-[235px] top-[26px] absolute text-right text-[#ff4747] text-xs font-semibold font-['PingFang SC']">删除方案</div>
  </div>
  <div class="Frame3465330 h-[21px] left-0 top-[831px] absolute flex-col justify-start items-start inline-flex">
    <div class="HomeIndicator h-[21px] px-[127px] justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465329 h-[720px] left-0 top-[194px] absolute flex-col justify-start items-start inline-flex">
    <div class="Frame3465322 h-[72px] pl-[22px] pr-[21px] bg-white justify-center items-center inline-flex">
      <div class="1 text-center text-black text-sm font-medium font-['PingFang SC']">赠品组 1</div>
    </div>
    <div class="Frame3465324 h-[72px] relative bg-neutral-100 rounded-tr-[10px]">
      <div class=" left-[34px] top-[26px] absolute text-black/0 text-sm font-medium font-['PingFang SC']">饮料</div>
      <div class="2 left-[20px] top-[26px] absolute text-center text-black text-sm font-medium font-['PingFang SC']">赠品组 2</div>
    </div>
    <div class="Frame3465325 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div class=" text-black/0 text-sm font-medium font-['PingFang SC']">果盘</div>
    </div>
    <div class="Frame3465326 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div class=" text-black/0 text-sm font-medium font-['PingFang SC']">小吃</div>
    </div>
    <div class="Frame3465327 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div class=" text-black/0 text-sm font-medium font-['PingFang SC']">其他</div>
    </div>
    <div class="Frame3465328 h-[72px] bg-neutral-100 justify-center items-center inline-flex">
      <div class=" text-black/0 text-sm font-medium font-['PingFang SC']">烧烤</div>
    </div>
    <div class="Frame3465329 h-[72px] relative bg-neutral-100"></div>
    <div class="Frame3465330 h-[72px] relative bg-neutral-100"></div>
    <div class="Frame3465333 h-[72px] relative bg-neutral-100"></div>
    <div class="Frame3465334 h-[72px] relative bg-neutral-100"></div>
  </div>
  <div class=" left-[173px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">赠品组</div>
</div>