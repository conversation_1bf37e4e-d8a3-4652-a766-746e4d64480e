{"codeType": "HTML", "figmaPageCode": "<div style=\"width: 393px; height: 1247px; position: relative; background: #F5F5F5\">\n  <div style=\"width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white\">\n    <div style=\"width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9\"></div>\n    <div style=\"width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid\"></div>\n    <img style=\"width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/18x17\" />\n    <img style=\"width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80\" src=\"https://via.placeholder.com/20x6\" />\n    <div style=\"width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid\"></div>\n  </div>\n  <div style=\"left: 157px; top: 56px; position: absolute; opacity: 0.80; text-align: center; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">时段价格</div>\n  <div style=\"width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n    <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n      <div style=\"width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n    </div>\n    <div style=\"width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid\"></div>\n  </div>\n  <div style=\"width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 28.43px; height: 11.09px; background: black\"></div>\n      </div>\n    </div>\n    <div style=\"width: 66.66px; height: 11.34px; position: relative\">\n      <div style=\"width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute\">\n        <div style=\"width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid\"></div>\n        <div style=\"width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black\"></div>\n        <div style=\"width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px\"></div>\n      </div>\n      <img style=\"width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute\" src=\"https://via.placeholder.com/15x11\" />\n      <img style=\"width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute\" src=\"https://via.placeholder.com/17x11\" />\n    </div>\n  </div>\n  <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; left: 0px; top: 1226px; position: absolute; justify-content: center; align-items: center; display: inline-flex\">\n    <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n  </div>\n  <div style=\"height: 121px; left: 0px; top: 1126px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex\">\n    <div style=\"width: 393px; height: 100px; position: relative; background: white\">\n      <div style=\"width: 393px; height: 1px; left: 0px; top: 0px; position: absolute; background: rgba(0, 0, 0, 0.10)\"></div>\n      <div style=\"width: 393px; left: 0px; top: 1px; position: absolute\"></div>\n      <div style=\"height: 50px; padding-top: 14px; padding-bottom: 14px; padding-left: 24px; padding-right: 164px; left: 16px; top: 17px; position: absolute; background: black; border-radius: 8px; overflow: hidden; justify-content: flex-start; align-items: flex-start; gap: 29px; display: inline-flex\">\n        <div style=\"align-self: stretch\"></div>\n        <div style=\"text-align: center; color: white; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">确定</div>\n      </div>\n    </div>\n    <div style=\"width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex\">\n      <div style=\"width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px\"></div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 224px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">套餐现价</div>\n    <div style=\"width: 12px; height: 12px; position: relative\">\n      <div style=\"width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute\">\n        <div style=\"width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n        <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n      </div>\n    </div>\n  </div>\n  <div style=\"width: 361px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 297px; left: 16px; top: 262px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: flex-start; align-items: center; display: inline-flex\">\n    <div style=\"align-self: stretch; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">20</div>\n      <div style=\"color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">/元</div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 80px; left: 16px; top: 114px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid\">\n    <div style=\"left: 18px; top: 21px; position: absolute\"></div>\n    <div style=\"width: 20px; height: 20px; left: 317px; top: 30px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80\">\n      <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0\">\n        <div style=\"width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; background: #D9D9D9\"></div>\n      </div>\n      <div style=\"width: 8px; height: 8px; left: 2px; top: 10px; position: absolute; transform: rotate(45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.30) solid\"></div>\n    </div>\n    <div style=\"left: 24px; top: 29px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; gap: 6px; display: flex\">\n        <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">时段</div>\n      </div>\n      <div style=\"width: 12px; height: 12px; position: relative\">\n        <div style=\"width: 9.61px; height: 10px; left: 1px; top: 1px; position: absolute\">\n          <div style=\"width: 0px; height: 10px; left: 4.80px; top: 0px; position: absolute; border: 1.50px #FF3F32 solid\"></div>\n          <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 2.23px; position: absolute; transform: rotate(60deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n          <div style=\"width: 0px; height: 11.09px; left: 9.61px; top: 7.77px; position: absolute; transform: rotate(120deg); transform-origin: 0 0; border: 1.50px #FF3F32 solid\"></div>\n        </div>\n      </div>\n    </div>\n    <div style=\"height: 22px; left: 160px; top: 29px; position: absolute; flex-direction: column; justify-content: center; align-items: flex-end; display: inline-flex\">\n      <div style=\"color: #5956FF; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">周末黄金档18:00-21:00</div>\n    </div>\n  </div>\n  <div style=\"left: 20px; top: 348px; position: absolute; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex\">\n    <div style=\"color: black; font-size: 16px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">支持折扣</div>\n  </div>\n  <div style=\"width: 361px; height: 76px; padding: 12px; left: 16px; top: 386px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px solid; justify-content: center; align-items: flex-start; gap: 9px; display: inline-flex\">\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家折扣</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n    <div style=\"flex: 1 1 0; align-self: stretch; padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 62px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家减免</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n  </div>\n  <div style=\"width: 361px; height: 594px; left: 16px; top: 492px; position: absolute; background: white; border-radius: 10px; overflow: hidden\">\n    <div style=\"left: 16px; top: 16px; position: absolute; justify-content: flex-start; align-items: center; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">区域价格</div>\n    </div>\n    <div style=\"padding-top: 4px; padding-bottom: 4px; padding-left: 4px; padding-right: 10px; left: 289px; top: 13px; position: absolute; background: black; border-radius: 25px; justify-content: center; align-items: center; gap: 10px; display: inline-flex\">\n      <div style=\"justify-content: flex-start; align-items: center; display: flex\">\n        <div style=\"width: 18px; height: 18px; position: relative\">\n          <div style=\"width: 2px; height: 10px; left: 8px; top: 4px; position: absolute; background: white; border-radius: 8px\"></div>\n          <div style=\"width: 2px; height: 10px; left: 14px; top: 8px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: white; border-radius: 8px\"></div>\n        </div>\n        <div style=\"color: white; font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">新增</div>\n      </div>\n    </div>\n    <div style=\"left: 24px; top: 77px; position: absolute; color: black; font-size: 20px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">大堂区域 1</div>\n    <div style=\"left: 24px; top: 348px; position: absolute; color: black; font-size: 20px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word\">大堂区域 1</div>\n    <div style=\"width: 361px; height: 1px; left: 0px; top: 323px; position: absolute; background: rgba(0, 0, 0, 0.05)\"></div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 24px; top: 241px; position: absolute; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 48px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家折扣</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 187px; top: 241px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 48px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家减免</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"left: 24px; top: 208px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">支持折扣</div>\n    <div style=\"width: 313px; padding-top: 18px; padding-bottom: 18px; padding-left: 18px; padding-right: 249px; left: 24px; top: 128px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex\">\n      <div style=\"color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">20</div>\n      <div style=\"color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">/元</div>\n    </div>\n    <div style=\"width: 361px; height: 1px; left: 0px; top: 52px; position: absolute; background: rgba(0, 0, 0, 0.05)\"></div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 24px; top: 512px; position: absolute; background: #5956FF; border-radius: 6px; overflow: hidden; border: 1px solid; justify-content: center; align-items: center; gap: 48px; display: inline-flex\">\n      <div style=\"color: white; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家折扣</div>\n      <div style=\"width: 16px; height: 16px; position: relative; background: white; border-radius: 2px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex\">\n        <div style=\"width: 7.90px; height: 5px; transform: rotate(-45deg); transform-origin: 0 0; border: 2px #5956FF solid\"></div>\n      </div>\n    </div>\n    <div style=\"padding-top: 16px; padding-bottom: 16px; padding-left: 16px; padding-right: 14px; left: 187px; top: 512px; position: absolute; border-radius: 6px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: center; align-items: center; gap: 48px; display: inline-flex\">\n      <div style=\"color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">商家减免</div>\n      <div style=\"width: 16px; height: 16px; position: relative; border-radius: 2px; border: 1px rgba(0, 0, 0, 0.20) solid\"></div>\n    </div>\n    <div style=\"left: 24px; top: 479px; position: absolute; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">支持折扣</div>\n    <div style=\"width: 313px; height: 56px; left: 24px; top: 399px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid\">\n      <div style=\"left: 18px; top: 18px; position: absolute\"></div>\n      <div style=\"left: 18px; top: 18px; position: absolute; color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">20</div>\n      <div style=\"left: 43px; top: 18px; position: absolute; color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word\">/元</div>\n    </div>\n    <div style=\"width: 24px; height: 24px; left: 323px; top: 338px; position: absolute; border-radius: 35px; overflow: hidden\">\n      <div style=\"width: 4px; height: 1px; left: 10px; top: 6px; position: absolute; background: #FF4747\"></div>\n      <div style=\"width: 10px; height: 1.50px; left: 7px; top: 7px; position: absolute; background: #FF4747; border-radius: 3px\"></div>\n      <div style=\"width: 8px; height: 7px; left: 8px; top: 10px; position: absolute; border-radius: 1px; border: 1.50px #FF4747 solid\"></div>\n    </div>\n    <div style=\"width: 24px; height: 24px; left: 323px; top: 67px; position: absolute; border-radius: 35px; overflow: hidden\">\n      <div style=\"width: 4px; height: 1px; left: 10px; top: 6px; position: absolute; background: #FF4747\"></div>\n      <div style=\"width: 10px; height: 1.50px; left: 7px; top: 7px; position: absolute; background: #FF4747; border-radius: 3px\"></div>\n      <div style=\"width: 8px; height: 7px; left: 8px; top: 10px; position: absolute; border-radius: 1px; border: 1.50px #FF4747 solid\"></div>\n    </div>\n  </div>\n</div>"}