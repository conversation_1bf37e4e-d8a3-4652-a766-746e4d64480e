<div class=" w-[393px] h-[1040px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[149px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">可选组信息</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[1019px] absolute justify-center items-center inline-flex">
    <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[919px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">确定</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465271 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">可选组名称</div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[276px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="1 text-black text-sm font-medium font-['PingFang SC']">限时组合 1</div>
  </div>
  <div class="Frame3465272 w-[361px] h-20 left-[16px] top-[238px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div class="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]"></div>
      </div>
    </div>
    <div class="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div class=" text-[#5855ff] text-base font-medium font-['PingFang SC']">根据方案</div>
    </div>
    <div class="Frame3465355 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div class="Frame3465278 justify-start items-center gap-1.5 flex">
        <div class=" text-black text-base font-medium font-['PingFang SC']">套餐策略</div>
      </div>
    </div>
  </div>
  <div class="Frame3465274 w-[361px] pl-[18px] pr-[334px] py-[18px] left-[16px] top-[733px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="Frame3465337 self-stretch justify-start items-center gap-2 inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">2</div>
    </div>
  </div>
  <div class="Frame3465273 left-[20px] top-[695px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">可选方案数量</div>
    <div class="Frame3465264 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame19 w-[361px] h-[307px] left-[16px] top-[348px] absolute bg-white rounded-[10px]">
    <div class="Frame3465256 left-[16px] top-[16px] absolute justify-start items-center inline-flex">
      <div class=" text-black text-sm font-medium font-['PingFang SC']">套餐商品</div>
    </div>
    <div class="Frame3465228 pl-1 pr-2.5 py-1 left-[289px] top-[13px] absolute bg-black rounded-[25px] justify-center items-center gap-2.5 inline-flex">
      <div class="Frame3465223 justify-start items-center flex">
        <div class="Frame3465222 w-[18px] h-[18px] relative">
          <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg"></div>
          <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg"></div>
        </div>
        <div class=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div class="Rectangle3469201 w-[361px] h-px left-0 top-[255px] absolute bg-black/5"></div>
    <div class="19600 left-[229px] top-[272px] absolute text-right text-black text-sm font-medium font-['PingFang SC']">商品总价: ¥196.00</div>
    <div class="Frame3465346 w-[361px] h-[90px] left-0 top-[59px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">1</div>
      <div class="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg"></div>
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
    <div class="Frame3465347 w-[361px] h-[90px] left-0 top-[149px] absolute">
      <div class="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img class="Rectangle3469059 w-[70px] h-[70px] rounded-[7px]" src="https://via.placeholder.com/70x70" />
      </div>
      <div class="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-[15px] font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div class="Group3465431 w-[41px] h-[22px] left-[94px] top-[58px] absolute">
        <div class=" left-0 top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">¥</div>
        <div class=" left-[29px] top-[9px] absolute text-[#ff3333] text-[8px] font-semibold font-['PingFang SC']">/瓶</div>
        <div class=" left-[7px] top-0 absolute text-[#ff3333] text-base font-semibold font-['PingFang SC']">98</div>
      </div>
      <div class="Frame3465353 w-6 h-6 px-3 py-2 left-[264px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class=" w-[17px] left-[298px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">1</div>
      <div class="Frame3465221 w-6 h-6 px-3 py-2 left-[325px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div class="Frame3465223 justify-start items-center gap-0.5 flex">
          <div class="Frame3465222 w-[18px] h-[18px] relative">
            <div class="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg"></div>
            <div class="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg"></div>
          </div>
        </div>
      </div>
      <div class="Frame3465262 w-6 h-6 left-[325px] top-[10px] absolute rounded-[35px]">
        <div class="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]"></div>
      </div>
    </div>
  </div>
  <div class="Frame3465255 w-[361px] py-3.5 left-[16px] top-[829px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div class=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
</div>