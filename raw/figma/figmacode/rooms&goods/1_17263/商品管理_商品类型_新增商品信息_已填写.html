<div className=" w-[393px] h-[4138px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[157px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">商品信息</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3465271 left-[20px] top-[218px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">商品名称</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465297 left-[20px] top-[3453px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">商品介绍</div>
  </div>
  <div className="Frame3465274 left-[20px] top-[548px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">商品现价</div>
    <div className="Frame3465264 w-3 h-3 relative">
      <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div className="Frame3465281 left-[20px] top-[3329px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">条形码</div>
  </div>
  <div className="Frame11 pl-[18px] pr-[216px] py-[18px] left-[16px] top-[256px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className="28l text-black text-sm font-medium font-['PingFang SC']">微醺白葡萄果酒2.8L</div>
  </div>
  <div className="Frame3465295 left-[20px] top-[3139px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">投放时段</div>
  </div>
  <div className="Frame3465298 w-[361px] h-[129px] p-[18px] left-[16px] top-[3525px] absolute bg-white rounded-[10px] border justify-center items-center inline-flex">
    <div className=" w-[325px] h-[93px] text-black text-sm font-medium font-['PingFang SC']">商品介绍文案</div>
  </div>
  <div className="Frame3465275 w-[361px] pl-[18px] pr-[326px] py-[18px] left-[16px] top-[586px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">22</div>
  </div>
  <div className="Frame3465282 w-[361px] pl-2.5 pr-[263px] py-2.5 left-[16px] top-[3367px] absolute bg-white rounded-[10px] border justify-start items-center gap-2.5 inline-flex">
    <div className="IcScan w-9 h-9 relative flex-col justify-start items-start flex">
      <img className="Subtract w-4 h-4" src="https://via.placeholder.com/16x16" />
      <div className="Rectangle3469226 w-4 h-0.5 bg-black/40" />
    </div>
    <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入</div>
  </div>
  <div className="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[4117px] absolute justify-center items-center inline-flex">
    <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
  </div>
  <div className="Frame3 h-[121px] left-0 top-[4017px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[114px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">普通商品</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[21px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">商品类型</div>
      </div>
      <div className=" text-black/40 text-xs font-normal font-['PingFang SC']">提前设置好价格，支持多渠道投放</div>
    </div>
  </div>
  <div className="Frame3465272 w-[361px] h-20 left-[16px] top-[342px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">酒水</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">商品分类</div>
      </div>
    </div>
  </div>
  <div className="Frame3465276 w-[361px] h-20 left-[16px] top-[816px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">区域价格</div>
      </div>
    </div>
  </div>
  <div className="Frame3465273 w-[361px] h-20 left-[16px] top-[438px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">瓶</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">单位</div>
      </div>
    </div>
  </div>
  <div className="Frame3465277 w-[361px] h-20 left-[16px] top-[912px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">时段价格设置</div>
      </div>
    </div>
  </div>
  <div className="Frame3465278 w-[361px] h-20 left-[16px] top-[1008px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">买赠方案</div>
      </div>
    </div>
  </div>
  <div className="Frame3465279 w-[361px] h-20 left-[16px] top-[1200px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">推荐搭配</div>
      </div>
    </div>
  </div>
  <div className="Frame3465280 w-[361px] h-20 left-[16px] top-[1296px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">会员卡结账限制</div>
      </div>
    </div>
  </div>
  <div className="Frame3465283 w-[361px] h-20 left-[16px] top-[1392px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-[#5855ff] text-base font-medium font-['PingFang SC']">已设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">商品口味</div>
      </div>
    </div>
  </div>
  <div className="Frame3465284 w-[361px] h-20 left-[16px] top-[1488px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-black/40 text-base font-medium font-['PingFang SC']">未设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">辅料配方</div>
      </div>
    </div>
  </div>
  <div className="Frame3465296 w-[361px] h-20 left-[16px] top-[2256px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="Frame3465279 h-[22px] left-[160px] top-[29px] absolute flex-col justify-center items-end inline-flex">
      <div className=" text-black/40 text-base font-medium font-['PingFang SC']">未设置</div>
    </div>
    <div className="Frame3465355 left-[24px] top-[29.50px] absolute flex-col justify-start items-start inline-flex">
      <div className="Frame3465278 justify-start items-center gap-1.5 inline-flex">
        <div className=" text-black text-base font-medium font-['PingFang SC']">消费赠券</div>
      </div>
    </div>
  </div>
  <div className="Frame3465266 left-[20px] top-[672px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">支持折扣</div>
  </div>
  <div className="Frame14 w-[361px] h-[76px] p-3 left-[16px] top-[710px] absolute bg-white rounded-[10px] border justify-center items-start gap-[9px] inline-flex">
    <div className="Frame3465229 grow shrink basis-0 self-stretch pl-4 pr-3.5 py-4 bg-[#5855ff] rounded-md border justify-center items-center gap-[62px] inline-flex">
      <div className=" text-white text-sm font-medium font-['PingFang SC']">商家折扣</div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465230 grow shrink basis-0 self-stretch pl-4 pr-3.5 py-4 rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">商家减免</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
  </div>
  <div className="Frame3465202 h-20 pl-6 pr-[18px] left-[16px] top-[1104px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">支持重复购买</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465285 h-20 pl-6 pr-[18px] left-[16px] top-[1584px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">上架展示</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465286 h-20 pl-6 pr-[18px] left-[16px] top-[2352px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">计算库存</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465287 h-20 pl-6 pr-[18px] left-[16px] top-[1680px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">支持员工赠送</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465288 h-20 pl-6 pr-[18px] left-[16px] top-[1776px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">计入低消</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465289 h-20 pl-6 pr-[18px] left-[16px] top-[1872px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">计算业绩</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465291 h-20 pl-6 pr-[18px] left-[16px] top-[2064px] absolute bg-white rounded-[10px] border justify-end items-start gap-[247px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">沽清</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465292 h-20 pl-6 pr-[18px] left-[16px] top-[2711px] absolute bg-white rounded-[10px] border justify-end items-start gap-[151px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">指定投放包厢类型</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465293 h-20 pl-6 pr-[18px] left-[16px] top-[2448px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">指定投放区域 </div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465294 h-20 pl-6 pr-[18px] left-[16px] top-[2160px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">支持存酒</div>
    <div className="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame3465290 pl-6 pr-[18px] pt-[21px] pb-5 left-[16px] top-[1968px] absolute bg-white rounded-[10px] border justify-end items-center gap-[99px] inline-flex">
    <div className="Frame3465356 w-[180px] self-stretch flex-col justify-start items-start inline-flex">
      <div className=" self-stretch text-black text-base font-medium font-['PingFang SC']">推广</div>
      <div className=" self-stretch text-black/40 text-xs font-normal font-['PingFang SC']">开启后将在线上点单推荐分类展示</div>
    </div>
    <div className="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div className="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]" />
    </div>
  </div>
  <div className="Frame15 w-[361px] px-3.5 py-[18px] left-[16px] top-[3211px] absolute bg-white rounded-[10px] border justify-center items-center gap-3.5 inline-flex">
    <div className="Frame3465249 grow shrink basis-0 self-stretch px-[42px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-center text-black/40 text-sm font-medium font-['PingFang SC']">开始时间</div>
    </div>
    <div className="Frame3465251 self-stretch justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465250 grow shrink basis-0 self-stretch px-[42px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">结束时间</div>
    </div>
  </div>
  <div className=" w-[353px] left-[20px] top-[3161px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">提示：处于时段外时商品将自动沽清,处于时段内时非手动沽清商品将自动取消沽清</div>
  <div className=" w-[353px] left-[20px] top-[3475px] absolute text-black/40 text-xs font-normal font-['PingFang SC']">提示：处于时段外时商品将自动沽清,处于时段内时非手动沽清商品将自动取消沽清</div>
  <div className="Frame13 h-[302px] left-[16px] top-[2807px] absolute flex-col justify-start items-start gap-3 inline-flex">
    <div className="Frame11 h-[302px] relative bg-white rounded-[10px]">
      <div className="Frame3465229 h-[86px] p-2 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
        <div className="36 w-[90px] text-white text-sm font-medium font-['PingFang SC']">小包箱<br/>(3-6人)</div>
      </div>
      <div className="Frame3465230 h-[86px] p-2 left-[128px] top-[12px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="510 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">中包厢<br/>(5-10人)</div>
      </div>
      <div className="Frame3465233 h-[86px] p-2 left-[128px] top-[108px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="510 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">中包厢<br/>(5-10人)</div>
      </div>
      <div className="Frame3465236 h-[86px] p-2 left-[128px] top-[204px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="510 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">中包厢<br/>(5-10人)</div>
      </div>
      <div className="Frame3465235 h-[86px] p-2 left-[12px] top-[108px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="510 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">中包厢<br/>(5-10人)</div>
      </div>
      <div className="Frame3465237 h-[86px] p-2 left-[12px] top-[204px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="510 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">中包厢<br/>(5-10人)</div>
      </div>
      <div className="Frame3465231 h-[86px] p-2 left-[243px] top-[12px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="612 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">豪华中包箱<br/>(6-12人)</div>
      </div>
      <div className="Frame3465234 h-[86px] p-2 left-[243px] top-[108px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="612 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">豪华中包箱<br/>(6-12人)</div>
      </div>
      <div className="Frame3465238 h-[86px] p-2 left-[243px] top-[204px] absolute rounded-md border border-black/10 flex-col justify-center items-end gap-3.5 inline-flex">
        <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm border border-black/20" />
        <div className="612 w-[90px] text-black/40 text-sm font-medium font-['PingFang SC']">豪华中包箱<br/>(6-12人)</div>
      </div>
    </div>
  </div>
  <div className="Frame14 w-[361px] h-[137px] left-[16px] top-[2544px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465233 pl-4 pr-3.5 py-4 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[76px] inline-flex">
      <div className="0 text-white text-sm font-medium font-['PingFang SC']">区域 0</div>
      <div className="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex" />
    </div>
    <div className="Frame3465234 pl-4 pr-3.5 py-4 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[81px] inline-flex">
      <div className="2 text-black/40 text-sm font-medium font-['PingFang SC']">区域2</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465235 pl-4 pr-3.5 py-4 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-[84px] inline-flex">
      <div className="1 text-black/40 text-sm font-medium font-['PingFang SC']">区域1</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
    <div className="Frame3465236 pl-4 pr-3.5 py-4 left-[185px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[81px] inline-flex">
      <div className="3 text-black/40 text-sm font-medium font-['PingFang SC']">区域3</div>
      <div className="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20" />
    </div>
  </div>
  <div className="Frame3465255 w-[361px] py-3.5 left-[16px] top-[3927px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div className=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
  <div className="Frame3465299 left-[20px] top-[3684px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">商品介绍</div>
  </div>
  <div className="Frame17 px-6 pt-6 pb-[25px] left-[16px] top-[3722px] absolute bg-white rounded-[10px] flex-col justify-center items-start gap-5 inline-flex">
    <div className=" text-black text-base font-normal font-['PingFang SC']">上传商品图片</div>
    <div className="Frame3465205 self-stretch flex-col justify-start items-start gap-1.5 inline-flex">
      <div className="Frame3465203 w-[313px] justify-start items-start gap-1.5 inline-flex">
        <div className="Frame3465199 w-[74px] h-[74px] p-[23px] bg-neutral-100 rounded-md border justify-center items-center flex">
          <div className="1 grow shrink basis-0 self-stretch justify-start items-center inline-flex">
            <div className="ClipPathGroup w-[2814.61px] h-[3537.13px] relative">
              <div className="Group w-[22.84px] h-[19.48px] left-[2067.74px] top-[1969.74px] absolute">
                <div className="ClipPathGroup w-[22.84px] h-[19.48px] left-0 top-0 absolute">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="Frame3465200 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465201 h-[74px] relative opacity-0 rounded border border-black" />
        <div className="Frame3465202 h-[74px] relative opacity-0 rounded border border-black" />
      </div>
    </div>
  </div>
</div>