<div class=" w-[393px] h-[2022px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[141px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">新增商品类型</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3465271 left-[20px] top-[114px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">类型名称</div>
    <div class="Frame3465264 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[315px] py-[18px] left-[16px] top-[152px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class=" text-black text-sm font-medium font-['PingFang SC']">酒水</div>
  </div>
  <div class="Frame3465266 left-[20px] top-[238px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">投放渠道</div>
  </div>
  <div class="Frame14 w-[361px] h-[198px] left-[16px] top-[276px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465229 pl-4 pr-3.5 py-4 left-[12px] top-[12px] absolute bg-[#5855ff] rounded-md border justify-center items-center gap-[76px] inline-flex">
      <div class=" text-white text-sm font-medium font-['PingFang SC']">收银机</div>
      <div class="Frame3465232 w-4 h-4 relative bg-white rounded-sm flex-col justify-start items-start flex"></div>
    </div>
    <div class="Frame3465231 pl-4 pr-3.5 py-4 left-[12px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[76px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">点单屏</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465233 pl-4 pr-3.5 py-4 left-[12px] top-[134px] absolute rounded-md border border-black/10 justify-center items-center gap-[76px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">落单机</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465230 pl-4 pr-3.5 py-4 left-[185px] top-[12px] absolute rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">线上自助</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
    <div class="Frame3465232 pl-4 pr-3.5 py-4 left-[185px] top-[73px] absolute rounded-md border border-black/10 justify-center items-center gap-[62px] inline-flex">
      <div class=" text-black/40 text-sm font-medium font-['PingFang SC']">移动点单</div>
      <div class="Frame3465232 w-4 h-4 relative rounded-sm border border-black/20"></div>
    </div>
  </div>
  <div class="Frame3465202 h-20 pl-6 pr-[18px] left-[16px] top-[504px] absolute bg-white rounded-[10px] border justify-end items-start gap-[215px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">支持积分</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465272 w-[361px] h-20 left-[16px] top-[600px] absolute bg-white rounded-[10px] border">
    <div class=" left-[24px] top-[29px] absolute text-black text-base font-medium font-['PingFang SC']">计入酒水分析</div>
    <div class="Frame3465335 w-5 h-5 left-[126px] top-[30px] absolute">
      <div class="Ellipse984 w-[18px] h-[18px] left-[1px] top-[1px] absolute bg-[#bababa] rounded-full"></div>
      <div class="Rectangle3469222 w-0.5 h-0.5 left-[9px] top-[5px] absolute bg-white rounded-[0.80px]"></div>
    </div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] left-[303px] top-[29px] absolute bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465273 h-20 pl-6 pr-[18px] left-[16px] top-[696px] absolute bg-white rounded-[10px] border justify-end items-start gap-[183px] inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">启用后厨监控</div>
    <div class="Frame3465196 w-10 self-stretch pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465274 left-[20px] top-[806px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">送达超时时间（分钟）</div>
  </div>
  <div class="Frame3465275 w-[361px] pl-[18px] pr-[326px] py-[18px] left-[16px] top-[844px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class=" text-black text-sm font-medium font-['PingFang SC']">20</div>
  </div>
  <div class="Frame3465276 w-[361px] h-20 left-[16px] top-[930px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class=" left-[24px] top-[29px] absolute text-black text-base font-medium font-['PingFang SC']">存酒配置自定义</div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] left-[303px] top-[29px] absolute bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="HomeIndicator w-[393px] h-[21px] px-[127px] left-0 top-[2001px] absolute justify-center items-center inline-flex">
    <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[1901px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465277 left-[20px] top-[1040px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">存酒保存天数</div>
  </div>
  <div class="Frame3465281 left-[20px] top-[1288px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">每次续存天数</div>
  </div>
  <div class="Frame3465285 left-[20px] top-[1536px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=""><span style="text-black text-base font-medium font-['PingFang SC']">提前通知 </span><span style="text-[#5855ff] text-base font-medium font-['PingFang SC']">商户 </span><span style="text-black text-base font-medium font-['PingFang SC']">存酒过期天数</span></div>
  </div>
  <div class=" left-[20px] top-[1412px] absolute"><span style="text-black text-base font-medium font-['PingFang SC']">提前通知 </span><span style="text-[#ffab2e] text-base font-medium font-['PingFang SC']">客户 </span><span style="text-black text-base font-medium font-['PingFang SC']">存酒过期天数</span></div>
  <div class="Frame3465278 w-[361px] pl-[18px] pr-64 py-[18px] left-[16px] top-[1078px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="1999 text-black/20 text-sm font-medium font-['PingFang SC']">请输入 1-999</div>
  </div>
  <div class="Frame3465284 w-[361px] pl-[18px] pr-[265px] py-[18px] left-[16px] top-[1450px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="199 text-black/20 text-sm font-medium font-['PingFang SC']">请输入 1-99</div>
  </div>
  <div class="Frame3465282 w-[361px] pl-[18px] pr-[262px] py-[18px] left-[16px] top-[1326px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="099 text-black/20 text-sm font-medium font-['PingFang SC']">请输入 0-99</div>
  </div>
  <div class="Frame3465279 left-[20px] top-[1164px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class="0990 text-black text-base font-medium font-['PingFang SC']">续存次数(0~99次，默认0次，即不可续存)</div>
  </div>
  <div class="Frame3465286 w-[361px] pl-[18px] pr-[265px] py-[18px] left-[16px] top-[1574px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="199 text-black/20 text-sm font-medium font-['PingFang SC']">请输入 1-99</div>
  </div>
  <div class="Frame3465280 w-[361px] pl-[18px] pr-[262px] py-[18px] left-[16px] top-[1202px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="099 text-black/20 text-sm font-medium font-['PingFang SC']">请输入 0-99</div>
  </div>
  <div class="Frame3465255 w-[361px] py-3.5 left-[16px] top-[1811px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div class=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
  <div class="Frame3465287 w-[361px] h-20 left-[16px] top-[1691px] absolute bg-white rounded-[10px] border">
    <div class="Frame3465252 left-[18px] top-[21px] absolute"></div>
    <div class=" left-[24px] top-[29px] absolute text-black text-base font-medium font-['PingFang SC']">是否启用</div>
    <div class="Frame3465196 w-10 h-[22px] pl-[5px] pr-[23px] py-[5px] left-[303px] top-[29px] absolute bg-[#bababa] rounded-[40px] justify-start items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Rectangle3469205 w-[353px] h-px left-[20px] top-[1660px] absolute bg-black/5"></div>
</div>