<div className=" w-[393px] h-[2891px] relative bg-neutral-100">
  <div className="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div className="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black" />
    <img className="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img className="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div className=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">新增出库</div>
  <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div className="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div className="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div className="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex" />
    </div>
    <div className="RightSide w-[66.66px] h-[11.34px] relative">
      <div className="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img className="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img className="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div className="Frame3 h-[121px] left-0 top-[2770px] absolute flex-col justify-start items-start inline-flex">
    <div className=" h-[100px] relative bg-white">
      <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
      <div className="Frame9 w-[393px] left-0 top-[1px] absolute" />
      <div className="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div className="Frame18 self-stretch" />
        <div className=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div className="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div className="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]" />
    </div>
  </div>
  <div className="Frame3465199 w-[361px] h-20 left-[16px] top-[114px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="1 left-[271px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">仓库 1</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">出库仓库</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465200 w-[361px] h-20 left-[16px] top-[210px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">采购</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">出库类型</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465201 w-[361px] h-20 left-[16px] top-[306px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className=" left-[283px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">桉树</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">经手人</div>
      <div className="Frame3465264 w-3 h-3 relative">
        <div className="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
        </div>
      </div>
    </div>
  </div>
  <div className="Frame3465296 w-[361px] h-20 left-[16px] top-[572px] absolute bg-white rounded-[10px] border">
    <div className="Frame3465252 left-[18px] top-[21px] absolute" />
    <div className="MaskGroup origin-top-left rotate-90 opacity-80 w-5 h-5 left-[317px] top-[30px] absolute">
      <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute origin-top-left rotate-90">
        <div className="Rectangle3469043 w-5 h-5 left-0 top-0 absolute bg-[#d9d9d9]" />
      </div>
    </div>
    <div className="1 left-[255px] top-[29px] absolute text-right text-[#5855ff] text-base font-medium font-['PingFang SC']">供应商 1</div>
    <div className="Frame3465278 left-[24px] top-[29px] absolute justify-start items-center gap-1.5 inline-flex">
      <div className=" text-black text-base font-medium font-['PingFang SC']">供应商</div>
    </div>
  </div>
  <div className="Frame3465295 left-[20px] top-[416px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">出库时间</div>
  </div>
  <div className="Frame15 w-[361px] px-3.5 py-[18px] left-[16px] top-[454px] absolute bg-white rounded-[10px] border justify-center items-center gap-[20.50px] inline-flex">
    <div className="Frame3465249 grow shrink basis-0 self-stretch px-[29px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0913 text-center text-black text-sm font-medium font-['PingFang SC']">2024-09-13</div>
    </div>
    <div className="Frame3465251 self-stretch justify-start items-center gap-0.5 inline-flex" />
    <div className="Frame3465250 grow shrink basis-0 self-stretch px-[45px] py-4 bg-neutral-100 rounded-md justify-center items-center inline-flex">
      <div className="0010 text-black text-sm font-medium font-['PingFang SC']">11:00:10</div>
    </div>
  </div>
  <div className=" left-[20px] top-[822px] absolute text-black text-base font-medium font-['PingFang SC']">商品</div>
  <div className=" left-[20px] top-[1697px] absolute text-black text-base font-medium font-['PingFang SC']">批量添加</div>
  <div className="Frame19 w-[361px] h-[807px] left-[16px] top-[860px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 pl-1 pr-2.5 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className="Frame11 w-[253px] h-10 pl-2.5 pr-[159px] py-2.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-start items-start gap-2 inline-flex">
      <div className="Frame3465220 w-5 h-5 relative flex-col justify-start items-start flex">
        <img className="Union w-[12.57px] h-[12.41px]" src="https://via.placeholder.com/13x12" />
      </div>
      <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索商品</div>
    </div>
    <div className="Frame3465346 w-[361px] h-[354px] left-0 top-[80px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className=" left-[14px] top-[118px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">商品滚动成本：</div>
      <div className="Frame3465366 w-[72px] py-2 left-[243px] top-[110px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[118px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库单价：</div>
      <div className="Frame3465367 w-[72px] py-2 left-[243px] top-[159px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[167px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[216px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库小计：</div>
      <div className="Frame3465368 w-[72px] py-2 left-[243px] top-[208px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[216px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="12 left-[94px] top-[56px] absolute"><span style="text-black/40 text-base font-semibold font-['PingFang SC']">库存 </span><span style="text-[#5855ff] text-base font-semibold font-['PingFang SC']">12</span></div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">2</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
      <div className="Frame3465251 left-[168px] top-[296px] absolute justify-start items-center gap-0.5 inline-flex" />
      <div className="Frame3465249 pl-[31px] pr-[30px] py-4 left-[14px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className="0911 text-center text-black text-sm font-medium font-['PingFang SC']">2024-09-11</div>
      </div>
      <div className="Frame3465250 pl-[31px] pr-[30px] py-4 left-[207px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className="0911 text-black text-sm font-medium font-['PingFang SC']">2025-09-11</div>
      </div>
      <div className="Rectangle3469238 w-[333px] h-px left-[14px] top-[353px] absolute bg-black/5" />
    </div>
    <div className="Frame3465347 w-[361px] h-[354px] left-0 top-[454px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className=" left-[14px] top-[118px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">商品滚动成本：</div>
      <div className="Frame3465366 px-[18px] py-2 left-[243px] top-[110px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[118px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库单价：</div>
      <div className="Frame3465367 px-[18px] py-2 left-[243px] top-[159px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[167px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[216px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库小计：</div>
      <div className="Frame3465368 px-[18px] py-2 left-[243px] top-[208px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[216px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="12 left-[94px] top-[56px] absolute"><span style="text-black/40 text-base font-semibold font-['PingFang SC']">库存 </span><span style="text-[#5855ff] text-base font-semibold font-['PingFang SC']">12</span></div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">0</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
      <div className="Frame3465251 left-[168px] top-[296px] absolute justify-start items-center gap-0.5 inline-flex" />
      <div className="Frame3465249 px-[42px] py-4 left-[14px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className=" text-center text-black/40 text-sm font-medium font-['PingFang SC']">开始时间</div>
      </div>
      <div className="Frame3465250 px-[42px] py-4 left-[207px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">结束时间</div>
      </div>
    </div>
  </div>
  <div className="Frame3465366 w-[361px] h-[807px] left-[16px] top-[1735px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 pl-1 pr-2.5 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className="Frame3465222 w-[18px] h-[18px] relative">
          <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-white rounded-lg" />
          <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-white rounded-lg" />
        </div>
        <div className=" text-white text-xs font-medium font-['PingFang SC']">新增</div>
      </div>
    </div>
    <div className="Frame11 w-[253px] h-10 pl-2.5 pr-[159px] py-2.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-start items-start gap-2 inline-flex">
      <div className="Frame3465220 w-5 h-5 relative flex-col justify-start items-start flex">
        <img className="Union w-[12.57px] h-[12.41px]" src="https://via.placeholder.com/13x12" />
      </div>
      <div className=" text-black/20 text-sm font-medium font-['PingFang SC']">搜索商品</div>
    </div>
    <div className="Frame3465346 w-[361px] h-[354px] left-0 top-[80px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className=" left-[14px] top-[118px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">商品滚动成本：</div>
      <div className="Frame3465366 w-[72px] py-2 left-[243px] top-[110px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[118px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库单价：</div>
      <div className="Frame3465367 w-[72px] py-2 left-[243px] top-[159px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[167px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[216px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库小计：</div>
      <div className="Frame3465368 w-[72px] py-2 left-[243px] top-[208px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-center text-black text-xs font-semibold font-['PingFang SC']">20</div>
      </div>
      <div className=" left-[327px] top-[216px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="12 left-[94px] top-[56px] absolute"><span style="text-black/40 text-base font-semibold font-['PingFang SC']">库存 </span><span style="text-[#5855ff] text-base font-semibold font-['PingFang SC']">12</span></div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">2</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
      <div className="Frame3465251 left-[168px] top-[296px] absolute justify-start items-center gap-0.5 inline-flex" />
      <div className="Frame3465249 pl-[31px] pr-[30px] py-4 left-[14px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className="0911 text-center text-black text-sm font-medium font-['PingFang SC']">2024-09-11</div>
      </div>
      <div className="Frame3465250 pl-[31px] pr-[30px] py-4 left-[207px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className="0911 text-black text-sm font-medium font-['PingFang SC']">2025-09-11</div>
      </div>
      <div className="Rectangle3469238 w-[333px] h-px left-[14px] top-[353px] absolute bg-black/5" />
    </div>
    <div className="Frame3465347 w-[361px] h-[354px] left-0 top-[454px] absolute">
      <div className="Frame3465344 w-[70px] h-[70px] left-[14px] top-[10px] absolute bg-[#f2f2f2] rounded-md justify-center items-center inline-flex">
        <img className="Rectangle3469060 w-[70px] h-[70px] rounded-lg" src="https://via.placeholder.com/70x70" />
      </div>
      <div className="Vsop700ml left-[94px] top-[12px] absolute opacity-80 text-black text-base font-semibold font-['PingFang SC']">轩尼诗VSOP700ml</div>
      <div className=" left-[14px] top-[118px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">商品滚动成本：</div>
      <div className="Frame3465366 px-[18px] py-2 left-[243px] top-[110px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[118px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[167px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库单价：</div>
      <div className="Frame3465367 px-[18px] py-2 left-[243px] top-[159px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[167px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className=" left-[14px] top-[216px] absolute opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">入库小计：</div>
      <div className="Frame3465368 px-[18px] py-2 left-[243px] top-[208px] absolute bg-neutral-100 rounded justify-center items-center inline-flex">
        <div className=" opacity-80 text-black/40 text-xs font-semibold font-['PingFang SC']">请输入</div>
      </div>
      <div className=" left-[327px] top-[216px] absolute opacity-80 text-black/20 text-xs font-semibold font-['PingFang SC']">/元</div>
      <div className="Frame3465262 w-6 h-6 left-[323px] top-[10px] absolute rounded-[35px]">
        <div className="Rectangle3469229 w-2.5 h-[1.50px] left-[7px] top-[7px] absolute bg-[#ff4747] rounded-[3px]" />
      </div>
      <div className="12 left-[94px] top-[56px] absolute"><span style="text-black/40 text-base font-semibold font-['PingFang SC']">库存 </span><span style="text-[#5855ff] text-base font-semibold font-['PingFang SC']">12</span></div>
      <div className="Frame3465353 w-6 h-6 px-3 py-2 left-[262px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black/20 rounded-lg" />
          </div>
        </div>
      </div>
      <div className=" w-[17px] left-[296px] top-[58px] absolute text-center text-black text-[15px] font-semibold font-['PingFang SC']">0</div>
      <div className="Frame3465221 w-6 h-6 px-3 py-2 left-[323px] top-[56px] absolute bg-black/5 rounded-[22px] justify-center items-center gap-2.5 inline-flex">
        <div className="Frame3465223 justify-start items-center gap-0.5 flex">
          <div className="Frame3465222 w-[18px] h-[18px] relative">
            <div className="Rectangle3469066 w-0.5 h-2.5 left-[8px] top-[4px] absolute bg-black rounded-lg" />
            <div className="Rectangle3469067 w-0.5 h-2.5 left-[14px] top-[8px] absolute origin-top-left rotate-90 bg-black rounded-lg" />
          </div>
        </div>
      </div>
      <div className="Frame3465251 left-[168px] top-[296px] absolute justify-start items-center gap-0.5 inline-flex" />
      <div className="Frame3465249 px-[42px] py-4 left-[14px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className=" text-center text-black/40 text-sm font-medium font-['PingFang SC']">开始时间</div>
      </div>
      <div className="Frame3465250 px-[42px] py-4 left-[207px] top-[271px] absolute bg-neutral-100 rounded-md justify-center items-center inline-flex">
        <div className=" text-black/40 text-sm font-medium font-['PingFang SC']">结束时间</div>
      </div>
    </div>
  </div>
  <div className="Frame3465365 w-[393px] h-16 left-0 top-[2706px] absolute bg-white">
    <div className="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/5" />
    <div className=" left-[20px] top-[36px] absolute text-black text-[11px] font-medium font-['PingFang SC']">金额合计</div>
    <div className="4 left-[20px] top-[18px] absolute"><span style="text-black/40 text-[11px] font-semibold font-['PingFang SC']">共 </span><span style="text-[#5855ff] text-[11px] font-semibold font-['PingFang SC']">4</span><span style="text-black/40 text-[11px] font-semibold font-['PingFang SC']"> 件</span></div>
    <div className=" left-[73px] top-[27px] absolute text-black text-xl font-medium font-['PingFang SC']">120</div>
    <div className="00 left-[108px] top-[37px] absolute text-black/40 text-[11px] font-medium font-['PingFang SC']">.00</div>
    <div className=" left-[65px] top-[36px] absolute text-center text-black text-[11px] font-medium font-['PingFang SC']">：</div>
  </div>
  <div className=" left-[20px] top-[682px] absolute text-black text-base font-medium font-['PingFang SC']">入库单号</div>
  <div className="Frame3465367 w-[361px] h-[72px] left-[16px] top-[720px] absolute bg-white rounded-[10px]">
    <div className="Frame3465256 left-[16px] top-[17px] absolute" />
    <div className="Frame3465228 w-[70px] h-10 py-1 left-[275px] top-[16px] absolute bg-black rounded-md justify-center items-center gap-2.5 inline-flex">
      <div className="Frame3465223 justify-start items-center flex">
        <div className=" text-white text-xs font-medium font-['PingFang SC']">选择</div>
      </div>
    </div>
    <div className="Frame11 pl-2.5 pr-[25px] py-2.5 left-[16px] top-[16px] absolute bg-white rounded-md border border-black/10 justify-start items-center inline-flex">
      <div className="C24090611010183970200000004 text-black text-sm font-medium font-['PingFang SC']">C24090611010183970200000004</div>
    </div>
  </div>
  <div className="Frame11 w-[361px] pl-[18px] pr-[287px] py-[18px] left-[16px] top-[2610px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div className=" text-black text-sm font-medium font-['PingFang SC']">入库备注</div>
  </div>
  <div className="Frame3465297 left-[20px] top-[2572px] absolute justify-start items-center gap-1.5 inline-flex">
    <div className=" text-black text-base font-medium font-['PingFang SC']">备注</div>
  </div>
</div>