<div class=" w-[393px] h-[868px] relative bg-neutral-100">
  <div class="MiniProgramsButtons w-[393px] h-[46px] left-0 top-[44px] absolute bg-white">
    <div class="Stroke w-[91.18px] h-8 left-[295.54px] top-[6px] absolute opacity-10 bg-white rounded-2xl border border-black"></div>
    <img class="Union w-[18.34px] h-[17.50px] left-[354.75px] top-[13.25px] absolute opacity-80" src="https://via.placeholder.com/18x17" />
    <img class="Union w-[19.91px] h-[6.50px] left-[309.68px] top-[18.75px] absolute opacity-80" src="https://via.placeholder.com/20x6" />
  </div>
  <div class=" left-[165px] top-[56px] absolute opacity-80 text-center text-black text-base font-semibold font-['PingFang SC']">仓库新增</div>
  <div class="MaskGroup origin-top-left rotate-90 opacity-80 w-[26px] h-[26px] left-[38px] top-[54px] absolute">
    <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute origin-top-left rotate-90">
      <div class="Rectangle3469043 w-[26px] h-[26px] left-0 top-0 absolute bg-[#d9d9d9]"></div>
    </div>
  </div>
  <div class=" w-[393px] h-11 pl-[21px] pr-[14.67px] pt-3 pb-[11px] left-0 top-0 absolute bg-white justify-end items-center gap-[236.67px] inline-flex">
    <div class="TimeLightBase grow shrink basis-0 self-stretch justify-center items-center inline-flex">
      <div class="TimeLightBase w-[54px] h-[21px] relative rounded-[32px] flex-col justify-start items-start flex"></div>
    </div>
    <div class="RightSide w-[66.66px] h-[11.34px] relative">
      <div class="Battery w-[24.33px] h-[11.33px] left-[42.33px] top-0 absolute">
      </div>
      <img class="Wifi w-[15.27px] h-[10.97px] left-[22.03px] top-0 absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal w-[17px] h-[10.67px] left-0 top-[0.34px] absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame11 w-[361px] pl-[18px] pr-[304px] py-[18px] left-[16px] top-[158px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class="1 text-black text-sm font-medium font-['PingFang SC']">仓库 1</div>
  </div>
  <div class="Frame3 h-[121px] left-0 top-[747px] absolute flex-col justify-start items-start inline-flex">
    <div class=" h-[100px] relative bg-white">
      <div class="Frame4 w-[393px] h-px left-0 top-0 absolute bg-black/10"></div>
      <div class="Frame9 w-[393px] left-0 top-[1px] absolute"></div>
      <div class="Frame16 h-[50px] pl-6 pr-[164px] py-3.5 left-[16px] top-[17px] absolute bg-black rounded-lg justify-start items-start gap-[29px] inline-flex">
        <div class="Frame18 self-stretch"></div>
        <div class=" text-center text-white text-base font-medium font-['PingFang SC']">保存</div>
      </div>
    </div>
    <div class="HomeIndicator h-[21px] px-[127px] bg-white justify-center items-center inline-flex">
      <div class="HomeIndicator w-[139px] h-[5px] origin-top-left rotate-180 bg-black rounded-[100px]"></div>
    </div>
  </div>
  <div class="Frame3465290 left-[20px] top-[120px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">仓库名称</div>
    <div class="Frame3465264 w-3 h-3 relative">
      <div class="Group14147 w-[9.61px] h-2.5 left-[1px] top-[1px] absolute">
      </div>
    </div>
  </div>
  <div class="Frame3465202 pl-6 pr-[18px] pt-[21px] pb-5 left-[16px] top-[244px] absolute bg-white rounded-[10px] border justify-end items-center gap-[99px] inline-flex">
    <div class="Frame3465362 w-[180px] self-stretch flex-col justify-start items-start inline-flex">
      <div class=" self-stretch text-black text-base font-medium font-['PingFang SC']">设置为默认存酒仓库</div>
      <div class=" self-stretch text-black/40 text-xs font-normal font-['PingFang SC']">默认存酒仓库只能配置一个</div>
    </div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465273 pl-6 pr-[18px] pt-[21px] pb-5 left-[16px] top-[340px] absolute bg-white rounded-[10px] border justify-end items-center gap-[15px] inline-flex">
    <div class="Frame3465363 w-[264px] self-stretch flex-col justify-start items-start inline-flex">
      <div class=" self-stretch text-black text-base font-medium font-['PingFang SC']">存酒仓库不可选</div>
      <div class=" text-black/40 text-xs font-normal font-['PingFang SC']">打开后存酒仅能存到该仓库，不包含非整瓶存酒</div>
    </div>
    <div class="Frame3465196 w-10 h-[22px] pl-[23px] pr-[5px] py-[5px] bg-[#31bd50] rounded-[40px] justify-end items-center inline-flex">
      <div class="Frame3465197 w-3 h-3 relative bg-white rounded-[20px]"></div>
    </div>
  </div>
  <div class="Frame3465297 left-[20px] top-[450px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">备注</div>
  </div>
  <div class="Frame3465298 w-[361px] h-[129px] pl-[18px] pr-[245px] pt-[18px] pb-[91px] left-[16px] top-[488px] absolute bg-white rounded-[10px] border justify-start items-center inline-flex">
    <div class=" text-black/20 text-sm font-medium font-['PingFang SC']">请输入套餐介绍</div>
  </div>
  <div class="Frame3465299 left-[20px] top-[450px] absolute justify-start items-center gap-1.5 inline-flex">
    <div class=" text-black text-base font-medium font-['PingFang SC']">备注</div>
  </div>
  <div class="Frame3465255 w-[361px] py-3.5 left-[16px] top-[657px] absolute rounded-lg border-2 border-[#f18080] justify-center items-center inline-flex">
    <div class=" text-center text-[#f84d4d] text-base font-medium font-['PingFang SC']">删除</div>
  </div>
</div>