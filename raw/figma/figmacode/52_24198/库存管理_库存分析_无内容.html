<div style="width: 393px; height: 852px; position: relative; background: white">
  <div class="MiniProgramsButtons" style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white">
    <div class="Rectangle938" style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div class="Stroke" style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img class="Union" style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img class="Union" style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div class="Separator" style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="left: 165px; top: 56px; position: absolute; opacity: 0.80; color: black; font-size: 16px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">库存分析</div>
  <div class="MaskGroup" style="width: 26px; height: 26px; left: 38px; top: 54px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; opacity: 0.80">
    <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; transform: rotate(90deg); transform-origin: 0 0">
      <div class="Rectangle3469043" style="width: 26px; height: 26px; left: 0px; top: 0px; position: absolute; background: #D9D9D9"></div>
    </div>
    <div class="Rectangle3469044" style="width: 10.40px; height: 10.40px; left: -2.60px; top: 13px; position: absolute; transform: rotate(135deg); transform-origin: 0 0; border: 2px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3" style="height: 21px; left: 0px; top: 823px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; justify-content: center; align-items: center; display: inline-flex">
      <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
  <div class="Frame11" style="padding-top: 8px; padding-bottom: 8px; padding-left: 8px; padding-right: 209px; left: 20px; top: 172px; position: absolute; background: white; border-radius: 10px; overflow: hidden; border: 1px rgba(0, 0, 0, 0.10) solid; justify-content: flex-start; align-items: center; gap: 10px; display: inline-flex">
    <div class="Frame3465220" style="width: 28px; height: 28px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
      <img class="Union" style="width: 17.60px; height: 17.37px" src="https://via.placeholder.com/18x17" />
    </div>
    <div style="color: rgba(0, 0, 0, 0.20); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">输入关键词搜索</div>
  </div>
  <div class="Frame3465329" style="height: 864px; left: 0px; top: 240px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div class="Frame3465322" style="width: 96px; height: 72px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: black; font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">酒水</div>
    </div>
    <div class="Frame3465324" style="width: 96px; height: 72px; background: #F5F5F5; border-top-left-radius: 10px; overflow: hidden; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">饮料</div>
    </div>
    <div class="Frame3465325" style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">果盘</div>
    </div>
    <div class="Frame3465326" style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">小吃</div>
    </div>
    <div class="Frame3465327" style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">其他</div>
    </div>
    <div class="Frame3465328" style="width: 96px; height: 72px; background: #F5F5F5; justify-content: center; align-items: center; display: inline-flex">
      <div style="color: rgba(0, 0, 0, 0.40); font-size: 14px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">烧烤</div>
    </div>
    <div class="Frame3465329" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div class="Frame3465330" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div class="Frame3465331" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div class="Frame3465332" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div class="Frame3465333" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
    <div class="Frame3465334" style="width: 96px; height: 72px; position: relative; background: #F5F5F5"></div>
  </div>
  <div class="Frame3465323" style="width: 297px; height: 612px; padding-top: 170px; padding-bottom: 319px; padding-left: 47px; padding-right: 46px; left: 96px; top: 240px; position: absolute; background: white; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
    <div class="Frame3465288" style="align-self: stretch; height: 123px; flex-direction: column; justify-content: flex-start; align-items: center; gap: 16px; display: inline-flex">
      <div class="ImgNull" style="width: 90px; height: 90px; padding: 13px; justify-content: center; align-items: center; display: inline-flex">
        <div class="Group14152" style="width: 64px; height: 64px; position: relative">
          <div class="Rectangle3469216" style="width: 54px; height: 54px; left: 10px; top: 10px; position: absolute; background: #EEEEEE; border-radius: 10px"></div>
          <img class="Subtract" style="width: 54px; height: 54px; left: 0px; top: 0px; position: absolute; border: 1px rgba(178, 183, 203, 0.12) solid; backdrop-filter: blur(10px)" src="https://via.placeholder.com/54x54" />
          <div class="Rectangle3469214" style="width: 6px; height: 18px; left: 24px; top: 12px; position: absolute; background: #B9BDCB; border-radius: 14px"></div>
          <div class="Rectangle3469215" style="width: 6px; height: 6px; left: 24px; top: 34px; position: absolute; background: #B9BDCB; border-radius: 14px"></div>
        </div>
      </div>
      <div style="text-align: center; color: rgba(0, 0, 0, 0.20); font-size: 12px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">暂无内容</div>
    </div>
  </div>
  <div class="Frame40" style="height: 34px; left: 20px; top: 114px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
    <div class="Frame38" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
      <div style="color: black; font-size: 24px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">全部库存</div>
      <div class="Frame30" style="width: 20px; height: 20px; position: relative">
        <div class="Rectangle3469062" style="width: 8px; height: 8px; left: 4.34px; top: 7px; position: absolute; transform: rotate(-45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.50) solid"></div>
      </div>
    </div>
  </div>
</div>