<div style="width: 393px; height: 852px; position: relative; background: white">
  <div class="Frame31" style="width: 393px; height: 533px; left: 0px; top: 90px; position: absolute; background: white">
    <div class="MaskGroup" style="width: 393px; height: 453px; left: 0px; top: 0px; position: absolute">
      <div class="Rectangle3469063" style="width: 393px; height: 453px; left: 0px; top: 0px; position: absolute; background: linear-gradient(180deg, white 0%, rgba(255, 255, 255, 0) 100%)"></div>
      <img class="PhotographeBeauteModeTexturesPortraitsSpiritueuxDeCorUpscayl4xRealesrganX4plus1" style="width: 679px; height: 908px; left: 549px; top: 783px; position: absolute; transform: rotate(-180deg); transform-origin: 0 0" src="https://via.placeholder.com/679x908" />
    </div>
    <img class="Frame28" style="width: 50px; height: 50px; left: 320px; top: 47px; position: absolute; border-radius: 27px; border: 3px rgba(0, 0, 0, 0.10) solid" src="https://via.placeholder.com/50x50" />
    <div class="Frame40" style="height: 68px; left: 20px; top: 38px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
      <div class="Frame38" style="align-self: stretch; justify-content: flex-start; align-items: center; gap: 6px; display: inline-flex">
        <div style="color: black; font-size: 24px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">北苑路北店</div>
        <div class="Frame30" style="width: 20px; height: 20px; position: relative">
          <div class="Rectangle3469062" style="width: 8px; height: 8px; left: 4.34px; top: 7px; position: absolute; transform: rotate(-45deg); transform-origin: 0 0; border: 2px rgba(0, 0, 0, 0.50) solid"></div>
        </div>
      </div>
      <div class="Frame39" style="padding-left: 12px; padding-right: 12px; padding-top: 6px; padding-bottom: 6px; background: #36D981; border-radius: 30px; backdrop-filter: blur(10px); justify-content: center; align-items: center; gap: 10px; display: inline-flex">
        <div style="color: white; font-size: 10px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">营业中</div>
      </div>
    </div>
  </div>
  <div class="Frame19" style="width: 171px; height: 186px; left: 20px; top: 233px; position: absolute; background: rgba(226.46, 241.32, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame41" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">包厢管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">快捷轻松管理好包厢</div>
    </div>
    <div class="Frame45" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469084" style="width: 48px; height: 34px; left: 12px; top: 14px; position: absolute; background: #3E97FF; border-radius: 4px; border: 0.80px solid; backdrop-filter: blur(10px)"></div>
      <div class="Ellipse971" style="width: 13.30px; height: 13.27px; left: 36px; top: 21px; position: absolute; transform: rotate(44.29deg); transform-origin: 0 0; border: 3px white solid"></div>
      <div class="Rectangle3469144" style="width: 3px; height: 8px; left: 34.50px; top: 22px; position: absolute; background: white; border-radius: 4px; border: 1px solid"></div>
      <div class="Polygon3" style="width: 26px; height: 15px; left: 23px; top: 41px; position: absolute; background: rgba(152.79, 218.09, 254.82, 0.40); border-radius: 2px; backdrop-filter: blur(3.42px)"></div>
    </div>
  </div>
  <div class="Frame24" style="width: 171px; height: 186px; left: 202px; top: 233px; position: absolute; background: rgba(255, 248, 222, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">商品管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">建立完善的供应链服务</div>
    </div>
    <div class="Frame45" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute"></div>
    <div class="Frame46" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Group14146" style="width: 48.29px; height: 44.67px; left: 56.29px; top: 14px; position: absolute; transform: rotate(180deg); transform-origin: 0 0">
        <div class="Union" style="width: 48.29px; height: 35.33px; left: 0px; top: 0px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, #FFD913 0%, #FFD913 100%); border: 1.14px solid; backdrop-filter: blur(4.56px)"></div>
        <div class="Vector6" style="width: 0.57px; height: 6.84px; left: -25.50px; top: 18.23px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; border: 4px white solid"></div>
        <div class="Vector7" style="width: 0.57px; height: 6.84px; left: -16.39px; top: 18.23px; position: absolute; border: 4px white solid"></div>
        <div class="Ellipse710" style="width: 13.67px; height: 13.67px; left: -0.62px; top: 31px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 238.62, 153.30, 0.50); border-radius: 9999px; backdrop-filter: blur(2px)"></div>
        <div class="Ellipse711" style="width: 13.67px; height: 13.67px; left: -27.62px; top: 30.91px; position: absolute; transform: rotate(180deg); transform-origin: 0 0; background: rgba(255, 238.62, 153.30, 0.50); border-radius: 9999px; backdrop-filter: blur(2px)"></div>
      </div>
    </div>
  </div>
  <div class="Frame21" style="width: 172px; height: 186px; left: 20px; top: 428px; position: absolute; background: rgba(243.42, 239.96, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame41" style="height: 63px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">库存管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">随时随地处理所有进销存业务</div>
    </div>
    <div class="Frame48" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469147" style="width: 39px; height: 42px; left: 13px; top: 15px; position: absolute; background: #7353DE; border-radius: 4px"></div>
      <div class="Rectangle3469148" style="width: 34px; height: 17px; left: 26px; top: 18px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, rgba(207.72, 193.53, 255, 0.40) 0%, rgba(207.72, 193.53, 255, 0.40) 100%); border-radius: 2px; backdrop-filter: blur(3.42px)"></div>
      <div class="Rectangle3469149" style="width: 34px; height: 17px; left: 26px; top: 37px; position: absolute; background: rgba(207.72, 193.53, 255, 0.40); border-radius: 2px; backdrop-filter: blur(3.42px)"></div>
      <div class="Rectangle3469152" style="width: 12px; height: 3px; left: 37px; top: 44px; position: absolute; background: white; border-radius: 3px"></div>
      <div class="Rectangle3469151" style="width: 12px; height: 3px; left: 37px; top: 25px; position: absolute; background: white; border-radius: 3px"></div>
    </div>
  </div>
  <div class="Frame25" style="width: 172px; height: 186px; left: 201px; top: 428px; position: absolute; background: rgba(222, 247.08, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 63px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">营销管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">多样营销活动流量裂变更轻松</div>
    </div>
    <div class="Frame56" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469153" style="width: 48px; height: 35px; left: 12px; top: 16px; position: absolute; background: #1EC9FF; border-radius: 3px"></div>
      <div class="Rectangle3469154" style="width: 4px; height: 14px; left: 34px; top: 26px; position: absolute; background: white; border-radius: 5px"></div>
      <div class="Rectangle3469155" style="width: 4px; height: 9px; left: 25px; top: 31px; position: absolute; background: white; border-radius: 5px"></div>
      <div class="Rectangle3469156" style="width: 4px; height: 6px; left: 43px; top: 34px; position: absolute; background: white; border-radius: 5px"></div>
      <div class="Rectangle3469157" style="width: 26px; height: 8px; left: 23px; top: 49px; position: absolute; background: rgba(138.03, 227.47, 247.10, 0.40); border-radius: 7px; backdrop-filter: blur(2px)"></div>
    </div>
  </div>
  <div class="Frame23" style="width: 172px; height: 186px; left: 20px; top: 623px; position: absolute; background: rgba(255, 228.91, 239.87, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">优惠券</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">提高客户消费更轻松</div>
    </div>
    <div class="Frame47" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469151" style="width: 12px; height: 3px; left: 36px; top: 27px; position: absolute; background: white; border-radius: 3px"></div>
      <div class="Rectangle3469158" style="width: 38px; height: 30px; left: 11px; top: 16px; position: absolute; background: #FF0D73; border-radius: 4px"></div>
      <img class="Subtract" style="width: 38px; height: 30px; left: 23px; top: 26px; position: absolute; border-radius: 0.40px; backdrop-filter: blur(3.42px)" src="https://via.placeholder.com/38x30" />
      <div class="Ellipse974" style="width: 5.42px; height: 5.42px; left: 34.26px; top: 34.19px; position: absolute; transform: rotate(30deg); transform-origin: 0 0; border-radius: 9999px; border: 2px white solid"></div>
      <div class="Ellipse975" style="width: 5.45px; height: 5.45px; left: 46.76px; top: 41.39px; position: absolute; transform: rotate(30deg); transform-origin: 0 0; border-radius: 9999px; border: 2px white solid"></div>
      <div class="Rectangle3469160" style="width: 2.69px; height: 13.78px; left: 43.78px; top: 34.86px; position: absolute; transform: rotate(30deg); transform-origin: 0 0; background: white; border-radius: 11px"></div>
    </div>
  </div>
  <div class="Frame32" style="width: 172px; height: 186px; left: 20px; top: 818px; position: absolute; background: rgba(243.42, 239.96, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">业绩管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">让绩效考核更简单</div>
    </div>
    <div class="Frame57" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Ellipse983" style="width: 38px; height: 38px; left: 17px; top: 17px; position: absolute; background: #7353DE; border-radius: 9999px"></div>
      <img class="Intersect" style="width: 22px; height: 21.98px; left: 13px; top: 13.02px; position: absolute; border-radius: 1px; border: 1.21px solid; backdrop-filter: blur(2px)" src="https://via.placeholder.com/22x22" />
      <div class="Rectangle3469199" style="width: 19px; height: 16px; left: 39px; top: 41px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, rgba(207.72, 193.53, 255, 0.40) 0%, rgba(207.72, 193.53, 255, 0.40) 100%); border-radius: 2px; backdrop-filter: blur(4.13px)"></div>
      <div class="Rectangle3469196" style="width: 3px; height: 10px; left: 47px; top: 44px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, white 0%, white 100%); border-radius: 10px"></div>
      <div class="Rectangle3469198" style="width: 3px; height: 5px; left: 42px; top: 49px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, white 0%, white 100%); border-radius: 10px"></div>
      <div class="Rectangle3469197" style="width: 3px; height: 6px; left: 52px; top: 48px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, white 0%, white 100%); border-radius: 10px"></div>
    </div>
  </div>
  <div class="Frame34" style="width: 172px; height: 186px; left: 20px; top: 1013px; position: absolute; background: rgba(219.80, 246.55, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">线上经营</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">线上自主销售便捷成交</div>
    </div>
    <div class="Frame50" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469153" style="width: 48px; height: 35px; left: 12px; top: 16px; position: absolute; background: #1EC9FF; border-radius: 3px"></div>
      <div class="Rectangle3469157" style="width: 26px; height: 8px; left: 23px; top: 49px; position: absolute; background: rgba(138.03, 227.47, 247.10, 0.40); border-radius: 7px; backdrop-filter: blur(2px)"></div>
      <div class="Vector10" style="width: 18px; height: 6px; left: 27px; top: 37px; position: absolute; border: 4px white solid"></div>
    </div>
  </div>
  <div class="Frame36" style="width: 172px; height: 186px; left: 20px; top: 1208px; position: absolute; background: rgba(226.46, 241.32, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 63px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">排号管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">优化流程降低等位流失率</div>
    </div>
    <div class="Frame51" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <img class="Union" style="width: 29.82px; height: 33.55px; left: 31.43px; top: 19.42px; position: absolute" src="https://via.placeholder.com/30x34" />
      <img class="Union" style="width: 29.82px; height: 33.55px; left: 10px; top: 19.42px; position: absolute" src="https://via.placeholder.com/30x34" />
      <img class="Union" style="width: 36.44px; height: 41px; left: 16.83px; top: 16px; position: absolute; border: 1px solid; backdrop-filter: blur(4px)" src="https://via.placeholder.com/36x41" />
      <div class="Rectangle3469169" style="width: 10px; height: 6px; left: 38px; top: 43px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; background: white; border-radius: 0.97px"></div>
    </div>
  </div>
  <div class="Frame38" style="width: 172px; height: 186px; left: 20px; top: 1403px; position: absolute; background: rgba(243.42, 239.96, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">平台对接服务</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">主流平台轻松对接</div>
    </div>
    <div class="Frame48" style="width: 72px; height: 72px; left: 668px; top: -359px; position: absolute">
      <div class="Rectangle3469175" style="width: 32px; height: 32px; left: 9px; top: 16px; position: absolute; background: #7353DE; border-radius: 4px"></div>
      <div class="Rectangle3469176" style="width: 28px; height: 28px; left: 21px; top: 28px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, rgba(206.28, 189.17, 255, 0.30) 0%, rgba(206.28, 189.17, 255, 0.30) 100%); border-radius: 3px; backdrop-filter: blur(3.42px)"></div>
      <div class="Rectangle3469177" style="width: 8px; height: 8px; left: 30px; top: 37px; position: absolute; border-radius: 1px; border: 3px white solid"></div>
      <div class="Vector12" style="width: 9.50px; height: 9.50px; left: 30px; top: 37px; position: absolute; border: 3px white solid"></div>
    </div>
    <div class="Frame62" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469175" style="width: 32px; height: 32px; left: 16px; top: 16px; position: absolute; background: #7353DE; border-radius: 4px"></div>
      <div class="Rectangle3469176" style="width: 28px; height: 28px; left: 28px; top: 28px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, rgba(206.28, 189.17, 255, 0.30) 0%, rgba(206.28, 189.17, 255, 0.30) 100%); border-radius: 3px; backdrop-filter: blur(3.42px)"></div>
      <div class="Rectangle3469177" style="width: 8px; height: 8px; left: 37px; top: 37px; position: absolute; border-radius: 1px; border: 3px white solid"></div>
      <div class="Vector12" style="width: 9.50px; height: 9.50px; left: 37px; top: 37px; position: absolute; border: 3px white solid"></div>
    </div>
  </div>
  <div class="Frame26" style="width: 172px; height: 186px; left: 201px; top: 623px; position: absolute; background: rgba(255, 247.70, 221.84, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">员工管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">积极性高流失率低</div>
    </div>
    <div class="Frame59" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <img class="Union" style="width: 35.56px; height: 40px; left: 24.83px; top: 16px; position: absolute" src="https://via.placeholder.com/36x40" />
      <img class="Union" style="width: 35.56px; height: 40px; left: 12.61px; top: 16px; position: absolute; border: 1.11px solid; backdrop-filter: blur(2px)" src="https://via.placeholder.com/36x40" />
    </div>
  </div>
  <div class="Frame33" style="width: 172px; height: 186px; left: 201px; top: 818px; position: absolute; background: rgba(255, 228.91, 239.87, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">门店管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">传统门店快速转型升级</div>
    </div>
    <div class="Frame54" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469164" style="width: 33.84px; height: 30.62px; left: 19.08px; top: 25.86px; position: absolute; background: #FF0D73; border-radius: 4.45px"></div>
      <div class="Rectangle3469165" style="width: 46.73px; height: 24.17px; left: 12.63px; top: 16px; position: absolute; background: linear-gradient(0deg,  0%,  100%), linear-gradient(0deg, rgba(255, 176.52, 176.52, 0.64) 0%, rgba(255, 176.52, 176.52, 0.64) 100%); backdrop-filter: blur(3.80px)"></div>
      <div class="Rectangle3469151" style="width: 13.35px; height: 3.34px; left: 29.32px; top: 32.69px; position: absolute; background: white; border-radius: 3.34px"></div>
    </div>
  </div>
  <div class="Frame35" style="width: 172px; height: 186px; left: 201px; top: 1013px; position: absolute; background: rgba(227, 253, 241, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">收银管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">高效收银安全稳定</div>
    </div>
    <div class="Frame58" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469167" style="width: 40.56px; height: 42px; left: 19.38px; top: 15px; position: absolute; background: #26DC85"></div>
      <div class="Rectangle3469166" style="width: 46.45px; height: 33.87px; left: 13.49px; top: 23.13px; position: absolute; background: rgba(235.55, 255, 246.83, 0.70); border-radius: 4.70px; border: 1.18px rgba(144.20, 232.48, 163.62, 0.40) solid; backdrop-filter: blur(4.84px)"></div>
      <div class="Rectangle3469168" style="width: 46.45px; height: 3.87px; left: 13.49px; top: 28.94px; position: absolute; background: #40D990"></div>
      <div class="Rectangle3469169" style="width: 9.68px; height: 5.81px; left: 46.39px; top: 47.32px; position: absolute; background: white; border-radius: 0.97px"></div>
    </div>
  </div>
  <div class="Frame37" style="width: 172px; height: 186px; left: 201px; top: 1208px; position: absolute; background: rgba(255, 248, 222, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">会员管理</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">智能在线客户服务系统</div>
    </div>
    <div class="Frame60" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3467561" style="width: 39.83px; height: 36.77px; left: 20.17px; top: 20.23px; position: absolute; background: #F6D630; border: 1.02px solid"></div>
      <div class="Rectangle3467562" style="width: 44.94px; height: 41.87px; left: 12px; top: 15.13px; position: absolute; background: rgba(255, 238.62, 153.30, 0.50); border: 1px rgba(255, 241.76, 173.10, 0.30) solid; backdrop-filter: blur(2px)"></div>
      <div class="Polygon4" style="width: 16.19px; height: 12.26px; left: 26.37px; top: 39.64px; position: absolute; background: white"></div>
    </div>
  </div>
  <div class="Frame39" style="width: 172px; height: 186px; left: 201px; top: 1403px; position: absolute; background: rgba(226.46, 241.32, 255, 0.60); border-radius: 20px; overflow: hidden; backdrop-filter: blur(8px)">
    <div class="Frame42" style="height: 45px; left: 17px; top: 17px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2px; display: inline-flex">
      <div style="align-self: stretch; color: black; font-size: 18px; font-family: PingFang SC; font-weight: 600; word-wrap: break-word">联系我们</div>
      <div style="align-self: stretch; color: rgba(0, 0, 0, 0.50); font-size: 13px; font-family: PingFang SC; font-weight: 400; word-wrap: break-word">为商家提供专业的服务</div>
    </div>
    <div class="Frame61" style="width: 72px; height: 72px; left: 8px; top: 106px; position: absolute">
      <div class="Rectangle3469170" style="width: 28px; height: 44px; left: 13px; top: 14px; position: absolute; background: #0D8BFF; border-radius: 4px"></div>
      <div class="Rectangle3469171" style="width: 16px; height: 3px; left: 19px; top: 15px; position: absolute; background: white"></div>
      <div class="Rectangle3469172" style="width: 28px; height: 27px; left: 31px; top: 23px; position: absolute; background: rgba(152.79, 218.09, 254.82, 0.40); border: 1px solid; backdrop-filter: blur(2px)"></div>
      <div class="Rectangle3469173" style="width: 14px; height: 3px; left: 38px; top: 28px; position: absolute; background: white; border-radius: 5px"></div>
      <div class="Rectangle3469174" style="width: 14px; height: 3px; left: 38px; top: 35px; position: absolute; background: white; border-radius: 5px"></div>
    </div>
  </div>
  <div class="MiniProgramsButtons" style="width: 393px; height: 46px; left: 0px; top: 44px; position: absolute; background: white; box-shadow: 0px -1px 0px rgba(0, 0, 0, 0.06) inset">
    <div class="Rectangle938" style="width: 393px; height: 46px; left: 0px; top: 0px; position: absolute; opacity: 0; background: #D9D9D9"></div>
    <div class="Stroke" style="width: 91.18px; height: 32px; left: 295.54px; top: 6px; position: absolute; opacity: 0.08; background: white; border-radius: 16px; border: 0.50px black solid"></div>
    <img class="Union" style="width: 18.34px; height: 17.50px; left: 354.75px; top: 13.25px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/18x17" />
    <img class="Union" style="width: 19.91px; height: 6.50px; left: 309.68px; top: 18.75px; position: absolute; opacity: 0.80" src="https://via.placeholder.com/20x6" />
    <div class="Separator" style="width: 0px; height: 18.70px; left: 341.12px; top: 12.65px; position: absolute; opacity: 0.20; background: black; border: 0.35px black solid"></div>
  </div>
  <div style="width: 393px; height: 44px; padding-top: 12px; padding-bottom: 11px; padding-left: 21px; padding-right: 14.67px; left: 0px; top: 0px; position: absolute; background: white; justify-content: flex-end; align-items: center; gap: 236.67px; display: inline-flex">
    <div class="TimeLightBase" style="flex: 1 1 0; align-self: stretch; justify-content: center; align-items: center; display: inline-flex">
      <div class="TimeLightBase" style="width: 54px; height: 21px; position: relative; border-radius: 32px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
        <div class="41" style="width: 28.43px; height: 11.09px; background: black"></div>
      </div>
    </div>
    <div class="RightSide" style="width: 66.66px; height: 11.34px; position: relative">
      <div class="Battery" style="width: 24.33px; height: 11.33px; left: 42.33px; top: 0px; position: absolute">
        <div class="Rectangle" style="width: 22px; height: 11.33px; left: 0px; top: 0px; position: absolute; opacity: 0.35; border-radius: 2.67px; border: 1px black solid"></div>
        <div class="CombinedShape" style="width: 1.33px; height: 4px; left: 23px; top: 3.67px; position: absolute; opacity: 0.40; background: black"></div>
        <div class="Rectangle" style="width: 18px; height: 7.33px; left: 2px; top: 2px; position: absolute; background: black; border-radius: 1.33px"></div>
      </div>
      <img class="Wifi" style="width: 15.27px; height: 10.97px; left: 22.03px; top: 0px; position: absolute" src="https://via.placeholder.com/15x11" />
      <img class="MobileSignal" style="width: 17px; height: 10.67px; left: 0px; top: 0.34px; position: absolute" src="https://via.placeholder.com/17x11" />
    </div>
  </div>
  <div class="Frame3" style="height: 91px; left: 0px; top: 761px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div style="height: 70px; background: white; flex-direction: column; justify-content: flex-end; align-items: flex-start; display: flex">
      <div class="Frame4" style="width: 393px; height: 1px; position: relative; background: rgba(0, 0, 0, 0.10)"></div>
      <div class="Frame9" style="align-self: stretch; justify-content: flex-start; align-items: center; display: inline-flex">
        <div class="Frame7" style="height: 78px; padding-top: 5px; padding-bottom: 28px; padding-left: 28px; padding-right: 27.88px; justify-content: center; align-items: center; display: flex">
          <div class="Frame6" style="flex: 1 1 0; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ProgrammingScript2StreamlineFlex" style="width: 28px; height: 28px; position: relative">
              <div class="Rectangle3469185" style="width: 13px; height: 4.50px; left: 3.82px; top: 17.50px; position: absolute; border: 2px black solid"></div>
              <div class="Vector13" style="width: 16.50px; height: 16.50px; left: 7.32px; top: 5.50px; position: absolute; border: 2px black solid"></div>
              <div class="Vector14" style="width: 13.50px; height: 12.50px; left: 7.82px; top: 5.50px; position: absolute; border: 2px black solid"></div>
              <div class="Vector15" style="width: 5.50px; height: 0px; left: 10.32px; top: 9.50px; position: absolute; border: 2px #999999 solid"></div>
              <div class="Vector16" style="width: 5.50px; height: 0px; left: 10.32px; top: 14px; position: absolute; border: 2px #999999 solid"></div>
            </div>
            <div style="text-align: center; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">营业简报</div>
          </div>
        </div>
        <div class="Frame8" style="height: 78px; padding-top: 5px; padding-bottom: 28px; padding-left: 27.75px; padding-right: 28.13px; justify-content: center; align-items: center; display: flex">
          <div class="Frame6" style="flex: 1 1 0; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ProgrammingScript2StreamlineFlex" style="width: 28px; height: 28px; position: relative">
              <div class="Rectangle3469192" style="width: 16px; height: 18px; left: 5.82px; top: 5px; position: absolute; border-radius: 2px; border: 2px black solid"></div>
              <div class="Rectangle3469193" style="width: 8.50px; height: 16px; left: 6.82px; top: 6px; position: absolute; background: #999999; border-radius: 1px"></div>
              <div class="Rectangle3469194" style="width: 2px; height: 4px; left: 11.82px; top: 12px; position: absolute; background: white"></div>
            </div>
            <div style="text-align: center; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">包厢</div>
          </div>
        </div>
        <div class="Frame9" style="height: 78px; padding-top: 5px; padding-bottom: 28px; padding-left: 27.50px; padding-right: 28.38px; justify-content: center; align-items: center; display: flex">
          <div class="Frame6" style="flex: 1 1 0; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ProgrammingScript2StreamlineFlex" style="width: 28px; height: 28px; position: relative">
              <div class="Rectangle3469186" style="width: 15px; height: 16px; left: 6.82px; top: 7px; position: absolute; border-radius: 2px; border: 2px black solid"></div>
              <div class="Rectangle3469188" style="width: 9px; height: 2px; left: 9.82px; top: 10px; position: absolute; background: #999999"></div>
              <div class="Rectangle3469190" style="width: 9px; height: 2px; left: 9.82px; top: 14px; position: absolute; background: #999999"></div>
              <div class="Rectangle3469189" style="width: 5px; height: 2px; left: 9.82px; top: 18px; position: absolute; background: #999999"></div>
              <div class="Rectangle3469191" style="width: 11px; height: 1.50px; left: 8.82px; top: 4.50px; position: absolute; background: #999999"></div>
            </div>
            <div style="text-align: center; color: rgba(0, 0, 0, 0.40); font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">订单</div>
          </div>
        </div>
        <div class="Frame10" style="height: 78px; padding-top: 5px; padding-bottom: 28px; padding-left: 28.25px; padding-right: 27.63px; justify-content: center; align-items: center; display: flex">
          <div class="Frame6" style="flex: 1 1 0; align-self: stretch; flex-direction: column; justify-content: flex-start; align-items: center; display: inline-flex">
            <div class="ProgrammingScript2StreamlineFlex" style="width: 28px; height: 28px; position: relative">
              <div class="Rectangle3469178" style="width: 18px; height: 14px; left: 4.82px; top: 5px; position: absolute; background: #5956FF; border: 2px #5956FF solid"></div>
              <div class="Rectangle3469179" style="width: 24px; height: 2px; left: 1.82px; top: 21px; position: absolute; background: #5956FF; border: 1px #5956FF solid"></div>
              <div class="Rectangle3469180" style="width: 2px; height: 6px; left: 12.82px; top: 12px; position: absolute; background: #5956FF"></div>
              <div class="Rectangle3469182" style="width: 2px; height: 3px; left: 8.82px; top: 15px; position: absolute; background: #5956FF"></div>
              <div class="Rectangle3469181" style="width: 2px; height: 8px; left: 16.82px; top: 10px; position: absolute; background: #5956FF"></div>
            </div>
            <div style="text-align: center; color: #5956FF; font-size: 12px; font-family: PingFang SC; font-weight: 500; word-wrap: break-word">管理</div>
          </div>
        </div>
      </div>
    </div>
    <div class="HomeIndicator" style="width: 393px; height: 21px; padding-left: 127px; padding-right: 127px; background: white; justify-content: center; align-items: center; display: inline-flex">
      <div class="HomeIndicator" style="width: 139px; height: 5px; transform: rotate(180deg); transform-origin: 0 0; background: black; border-radius: 100px"></div>
    </div>
  </div>
</div>