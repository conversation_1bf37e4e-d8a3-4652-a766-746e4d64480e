import json
import os
import re
from pypinyin import pinyin, Style

def read_json_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return None
    except json.JSONDecodeError:
        print(f"JSON解析错误: {file_path}")
        return None

def map_to_vant_component(figma_element):
    component_map = {
        "RECTANGLE": "van-button",
        "TEXT": "van-field",
        "FRAME": "view",
        "GROUP": "view",
        # 可以根据需要添加更多映射
    }
    return component_map.get(figma_element["type"], "view")

def sanitize_class_name(name):
    # 将中文转换为拼音
    pinyin_list = pinyin(name, style=Style.NORMAL)
    pinyin_name = ''.join([item[0] for item in pinyin_list])
    # 移除非字母数字的字符，将空格替换为连字符
    return re.sub(r'[^\w\s-]', '', pinyin_name).strip().lower()
    return re.sub(r'[-\s]+', '-', name).lower()

def generate_template(element, indent=0):
    component = map_to_vant_component(element)
    template = "  " * indent + f"<{component}"
    
    if "name" in element:
        sanitized_name = sanitize_class_name(element["name"])
        template += f' class="{sanitized_name}"'
        # 添加一个唯一的data属性
        template += f' data-figma-name="{element["name"]}"'
    
    if element.get("children"):
        template += ">\n"
        for child in element["children"]:
            template += generate_template(child, indent + 1)
        template += "  " * indent + f"</{component}>\n"
    else:
        template += " />\n"
    
    return template

def camel_to_kebab(string):
    return re.sub(r'(?<!^)(?=[A-Z])', '-', string).lower()

def convert_figma_style_to_css(prop, value):
    # 将驼峰命名转换为kebab-case
    prop = camel_to_kebab(prop)
    
    # 处理特殊的属性名
    prop_map = {
        'font-family': 'font-family',
        'font-post-script-name': None,  # 忽略这个属性
        'font-weight': 'font-weight',
        'text-auto-resize': None,  # 忽略这个属性
        'font-size': 'font-size',
        'text-align-horizontal': 'text-align',
        'text-align-vertical': 'vertical-align',
        'letter-spacing': 'letter-spacing',
        'line-height-px': 'line-height',
        'line-height-percent': None,  # 忽略这个属性
        'line-height-unit': None,  # 忽略这个属性
    }
    
    prop = prop_map.get(prop, prop)
    
    if prop is None:
        return None
    
    # 处理特殊的值
    if prop == 'font-size' or prop == 'line-height':
        value = f'{value}px'
    elif prop == 'font-weight':
        value = int(value)
    elif prop == 'vertical-align':
        value_map = {
            'TOP': 'top',
            'CENTER': 'middle',
            'BOTTOM': 'bottom'
        }
        value = value_map.get(value, value)
    elif prop == 'text-align':
        value = value.lower()
    
    return f'{prop}: {value};'

def generate_style(element):
    style = ""
    if "style" in element:
        sanitized_name = sanitize_class_name(element["name"])
        selector = f".{sanitized_name}"
        style += f"{selector} {{\n"
        for prop, value in element["style"].items():
            css_rule = convert_figma_style_to_css(prop, value)
            if css_rule:
                style += f"  {css_rule}\n"
            else:
                print(f"未处理的Figma样式属性: {prop}: {value}")
        style += "}\n"
    
    if element.get("children"):
        for child in element["children"]:
            style += generate_style(child)
    
    return style

def generate_script():
    return """<script setup>
import { ref } from 'vue';
// 这里可以添加组件的逻辑
</script>
"""

def generate_vue_component(figma_data):
    if not figma_data:
        return None
    
    template = "<template>\n"
    template += generate_template(figma_data["document"])
    template += "</template>\n\n"
    
    script = generate_script()
    
    style = "<style scoped>\n"
    style += generate_style(figma_data["document"])
    style += "</style>"
    
    return template + script + style

def process_figma_file(file_path):
    figma_data = read_json_file(file_path)
    if figma_data:
        vue_component = generate_vue_component(figma_data)
        if vue_component:
            # 生成Vue文件路径
            vue_file_path = os.path.splitext(file_path)[0] + '.vue'
            with open(vue_file_path, 'w', encoding='utf-8') as vue_file:
                vue_file.write(vue_component)
            print(f"Vue组件已生成: {vue_file_path}")
        else:
            print("无法生成Vue组件,因为生成的内容为空。")
    else:
        print("无法生成Vue组件,因为Figma数据无效。")

# 使用示例
if __name__ == "__main__":
    file_path = "raw/figma/pages/92_2778.json"
    process_figma_file(file_path)
