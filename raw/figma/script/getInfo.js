const fetch = require('node-fetch');
const fs = require('fs').promises;
const path = require('path');

async function getFigmaPageJson(fileKey, nodeId) {
  const FIGMA_ACCESS_TOKEN = '*********************************************';
  const API_BASE_URL = 'https://api.figma.com/v1';

  try {
    const response = await fetch(`${API_BASE_URL}/files/${fileKey}/nodes?ids=${nodeId}`, {
      method: 'GET',
      headers: {
        'X-Figma-Token': FIGMA_ACCESS_TOKEN
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.nodes[nodeId];
  } catch (error) {
    console.error('获取Figma页面JSON时出错:', error);
    throw error;
  }
}

async function saveJsonToFile(data, nodeId) {
  const fileName = `${nodeId.replace(':', '_')}.json`;
  const outputDir = path.join(__dirname, '..', 'pages');
  const filePath = path.join(outputDir, fileName);
  
  try {
    // 确保输出目录存在
    await fs.mkdir(outputDir, { recursive: true });
    
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    console.log(`JSON数据已保存到文件: ${filePath}`);
  } catch (error) {
    console.error('保存JSON数据到文件时出错:', error);
  }
}

// 使用示例
const fileKey = '5wpWFZLEtGoFSuCYMmCQDx';
const nodeId = '92:9366';

getFigmaPageJson(fileKey, nodeId)
  .then(pageJson => {
    console.log('获取到的页面JSON:', pageJson);
    return saveJsonToFile(pageJson, nodeId);
  })
  .catch(error => {
    console.error('获取或保存页面JSON失败:', error);
  });
