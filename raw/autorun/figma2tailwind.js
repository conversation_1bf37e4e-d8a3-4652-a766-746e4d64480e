import express from 'express';
import cors from 'cors';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const app = express();
const port = 4001;

app.use(cors());
app.use(express.json({ limit: '50mb' }));

app.post('/save', async (req, res) => {
  try {
    const {
      figmaSystemId,
      figmaSystemName,
      figmaPageId,
      figmaPageName,
      figmaPageCode,
      figmaPagePngBase64,
      ...otherData
    } = req.body;

    console.log('figmaPageId', figmaPageId);
    console.log('figmaPageName', figmaPageName);
    // console.log('figmaPageCode', figmaPageCode);
    // console.log('figmaPagePngBase64', figmaPagePngBase64);

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const baseDir = path.join(__dirname, '../figma/', 'employee_management');
    const safePageId = figmaPageId.replace(/:/g, '_');

    const pageDir = path.join(baseDir, safePageId);
    await fs.mkdir(pageDir, { recursive: true });

    // 替换 figmaPageId 中的 ':' 为 '_'

    // 保存 JSON 文件
    const jsonFileName = `${figmaPageName}.html`;
    const jsonFilePath = path.join(pageDir, jsonFileName);
    // const jsonContent = JSON.stringify({ ...otherData, figmaPageCode }, null, 2);
    await fs.writeFile(jsonFilePath, figmaPageCode);

    // 保存 PNG 文件
    if (figmaPagePngBase64) {
      console.log('保存PNG文件');
      const pngFileName = `${figmaPageName}.png`;
      const pngFilePath = path.join(pageDir, pngFileName);
      const pngBuffer = Buffer.from(figmaPagePngBase64, 'base64');
      await fs.writeFile(pngFilePath, pngBuffer);
    }

    console.log('文件已保存:', jsonFilePath);
    res.status(200).send('文件保存成功');
  } catch (error) {
    console.error('保存文件时出错:', error);
    res.status(500).send('保存文件失败');
  }
});

app.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
});