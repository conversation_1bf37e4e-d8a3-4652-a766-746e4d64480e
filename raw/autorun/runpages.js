import fetch from 'node-fetch';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const localServer = 'http://10.225.78.105:52520/push-code';

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function readApiDeclares() {
    const declaresPath = path.join(__dirname, '..', 'project', 'api_declares.json');
    const declaresData = await fs.readFile(declaresPath, 'utf8');
    return JSON.parse(declaresData);
}

async function callAPI(page, apiDeclares, figma_code) {
    const url = 'https://mone.test.mi.com/open-apis/ai-plugin-new/feature/router/flow/query';

    const headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json'
    };

    const data = {
        userName: 'dingliang',
        flowId: '60288',
        input: {
            pages: page,
            api_declares: apiDeclares,
            figma_code: figma_code,
            ipserver: localServer
        }
    };

    console.log('当前处理的页面:', page);
    //   console.log('请求头:', JSON.stringify(headers, null, 2));
    //   console.log('请求数据:', JSON.stringify(data, null, 2));

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP error: ${response}`);
        }

        const responseData = await response.json();
        console.log('API 调用结果:', responseData);

        if (responseData.code === 0 && responseData.data) {
            const parsedData = JSON.parse(responseData.data);
            if (parsedData.result) {
                console.log('API 调用成功，包含 result 字段');
                return true;
            }
        }

        console.log('API 调用未返回预期结果');
        return false;
    } catch (error) {
        console.error('API 调用出错:', error);
        return false;
    }
}

async function getFigmaCode(figmaCode) {
    try {
        const figmaCodePath = path.join(__dirname, '..', 'figma', 'inventory_management', figmaCode);
        const files = await fs.readdir(figmaCodePath);

        // 过滤出所有的 HTML 文件
        const htmlFiles = files.filter(file => file.endsWith('.html'));

        if (htmlFiles.length > 0) {
            // 获取第一个 HTML 文件的路径
            const filePath = path.join(figmaCodePath, htmlFiles[0]);
            // 读取文件内容
            const fileContent = await fs.readFile(filePath, 'utf8');
            return fileContent;
        }

        console.log('未找到 HTML 文件');
        return null;
    } catch (error) {
        console.error('获取 figma_code 失败:', error);
        return null;
    }

}

async function createOrUpdatePage(page) {
    const { model, pagename, name } = page;
    if (model && pagename) {
        const filePath = path.join(__dirname, '..', '..', 'src', 'pages', model, `${pagename}.vue`);
        const dirPath = path.dirname(filePath);

        // 确保目录存在
        await fs.mkdir(dirPath, { recursive: true });
        // 创建或覆盖页面内容
        const pageContent = `
        <!--
        页面信息:
        ${Object.entries(page).map(([key, value]) => `${key}: ${value}`).join('\n        ')}
        -->
        <template>
                <view class="page">
                    <text>${name}</text>
                </view>
                </template>

                <script setup>
                import { ref } from 'vue'
                </script>

                <style scoped>
                .page {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                }
                </style>
                `;
        await fs.writeFile(filePath, pageContent);
        console.log(`页面文件已创建/更新: ${filePath}`);

        // 更新 pages.json
        const pagesJsonPath = path.join(__dirname, '..', '..', 'src', 'pages.json');
        let pagesJson = JSON.parse(await fs.readFile(pagesJsonPath, 'utf8'));

        const newRoute = {
            path: `pages/${model}/${pagename}`,
            style: {
                navigationBarTitleText: name
            }
        };

        // 检查是否已存在相同路径的页面
        const existingIndex = pagesJson.pages.findIndex(route => route.path === newRoute.path);
        if (existingIndex !== -1) {
            pagesJson.pages[existingIndex] = newRoute;
        } else {
            pagesJson.pages.push(newRoute);
        }

        await fs.writeFile(pagesJsonPath, JSON.stringify(pagesJson, null, 2));
        console.log(`pages.json 已更新`);
    }
}

async function processPages() {
    console.log('开始处理页面:',path);
    const jsonPath = path.join(__dirname, '..', 'project', 'inventory_management.json');
    // const jsonPath = path.join(__dirname, '..', 'project', 'allpage_new.json');
    const jsonData = await fs.readFile(jsonPath, 'utf8');
    const pages = JSON.parse(jsonData);

    const apiDeclares = await readApiDeclares();

    for (let i = 0; i < pages.length; i++) {
        let page = pages[i];
        if (page.processed === true) {
            console.log(`跳过已成功处理的页面: ${page.name}`);
            continue;
        }

        console.log(`处理页面: ${page.name}`);
        // 创建或更新页面文件并更新 pages.json
        await createOrUpdatePage(page);
        const figma_code = await getFigmaCode(page.figma_nodeid);
        if(figma_code){
            const success = await callAPI(page, apiDeclares, figma_code);

            if (success) {
                page.processed = true;
                console.log(`页面 ${page.name} 处理成功并已标记`);
            } else {
                page.processed = "failed";
                console.log(`页面 ${page.name} 处理失败，已标记为失败`);
            }
    
            await fs.writeFile(jsonPath, JSON.stringify(pages, null, 2));
            await sleep(3000);
        }
        
    }

    console.log('所有页面处理完成');
}

processPages().catch(console.error);

export { processPages };
