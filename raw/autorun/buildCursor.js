const fetch = require('node-fetch');
const fs = require('fs').promises;
const path = require('path');

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function getFigmaCode(figmaCode) {
    try {
        const figmaCodePath = path.join(__dirname, '..', 'figma', 'figmacode', figmaCode);
        console.log(figmaCodePath);
        const files = await fs.readdir(figmaCodePath);

        for (let file of files) {
            if (file.endsWith('.html')) {
                const filePath = path.join(figmaCodePath, file);
                const fileContent = await fs.readFile(filePath, 'utf8');
                return fileContent;
            }
        }

        return null;
    } catch (error) {
        console.error('获取figma_code失败:', error);
        return null;
    }
}

async function createOrUpdatePage(page) {
    const { model, pagename, name } = page;
    if (model && pagename) {
        const filePath = path.join(__dirname, '..', '..', 'src', 'pages', model, `${pagename}.vue`);
        const dirPath = path.dirname(filePath);

        // 确保目录存在
        await fs.mkdir(dirPath, { recursive: true });
        // 创建或覆盖页面内容
        const pageContent = `
        <!--
        页面信息:
        ${Object.entries(page).map(([key, value]) => {
            if (typeof value === 'object' && value !== null) {
                return `${key}:\n${JSON.stringify(value, null, 2).split('\n').map(line => `        ${line}`).join('\n')}`;
            } else {
                return `${key}: ${value}`;
            }
        }).join('\n        ')}
        -->
        <template>
                <view class="page">
                    <text>${name}</text>
                </view>
                </template>

                <script setup>
                import { ref } from 'vue'
                </script>

                <style scoped>
                .page {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                }
                </style>
                `;
        await fs.writeFile(filePath, pageContent);
        console.log(`页面文件已创建/更新: ${filePath}`);

        // 更新 pages.json
        const pagesJsonPath = path.join(__dirname, '..', '..', 'src', 'pages.json');
        let pagesJson = JSON.parse(await fs.readFile(pagesJsonPath, 'utf8'));

        const newRoute = {
            path: `pages/${model}/${pagename}`,
            style: {
                navigationBarTitleText: name
            }
        };

        // 检查是否已存在相同路径的页面
        const existingIndex = pagesJson.pages.findIndex(route => route.path === newRoute.path);
        if (existingIndex !== -1) {
            pagesJson.pages[existingIndex] = newRoute;
        } else {
            pagesJson.pages.push(newRoute);
        }

        await fs.writeFile(pagesJsonPath, JSON.stringify(pagesJson, null, 2));
        console.log(`pages.json 已更新`);
    }
}

async function processPage(page) {
    const jsonPath = path.join(__dirname, '..', 'project', 'page_one.json');
    // const jsonPath = path.join(__dirname, '..', 'project', 'allpage_new.json');
    const jsonData = await fs.readFile(jsonPath, 'utf8');
    const pages = JSON.parse(jsonData);

    const apiDeclares = await readApiDeclares();

    for (let i = 0; i < pages.length; i++) {
        let page = pages[i];
        if (page.processed === true) {
            console.log(`跳过已成功处理的页面: ${page.name}`);
            continue;
        }

        console.log(`处理页面: ${page.name}`);
        // 创建或更新页面文件并更新 pages.json
        await createOrUpdatePage(page);

        await fs.writeFile(jsonPath, JSON.stringify(pages, null, 2));
        await sleep(3000);
    }

    console.log('所有页面处理完成');
}

processPages().catch(console.error);
