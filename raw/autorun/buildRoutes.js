import fs from 'fs';
import path from 'path';

// 获取当前目录名
const __dirname = path.dirname(new URL(import.meta.url).pathname);

// 读取并清理 JSON 文件中的注释
function readJSONFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    // 移除单行注释
    content = content.replace(/\/\/.*$/gm, '');
    // 移除多行注释
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');
    // 移除多余的逗号
    content = content.replace(/,(\s*[}\]])/g, '$1');
    return JSON.parse(content);
  } catch (error) {
    console.error('解析 JSON 文件时出错:', error);
    throw error;
  }
}

// 读取 pages_store.json 文件
const allpageData = readJSONFile(path.join(__dirname, '..', 'project', 'pages_6.员工管理.json'));

// 创建路由数组
const routes = allpageData.map(page => {
  const { model, pagename, name } = page;
  if (model && pagename) {
    return {
      path: `pages/${model}/${pagename}`,
      style: {
        navigationBarTitleText: name
      }
    };
  }
}).filter(Boolean); // 过滤掉可能的 undefined 值

// 读取现有的 pages.json 文件
const pagesJsonPath = path.join(__dirname, '..', '..', 'src', 'pages.json');
let pagesJson = readJSONFile(pagesJsonPath);

// 更新或增加 pages 数组，保留已有字段
routes.forEach(route => {
  const existingPage = pagesJson.pages.find(page => page.path === route.path);
  if (!existingPage) {
    pagesJson.pages.push(route); // 如果不存在，则添加新路由
  } else {
    // 如果需要更新，可以在这里更新 existingPage 的字段
    // 例如：existingPage.style = route.style; // 更新样式
  }
});

// 将更新后的内容写回 pages.json 文件
fs.writeFileSync(pagesJsonPath, JSON.stringify(pagesJson, null, 2));

// 创建空白页面文件
const blankPageContent = `<template>
  <view class="page">
    <text>开发中...</text>
  </view>
</template>

<script setup>
import { ref } from 'vue'
</script>

<style scoped>
.page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
`;

routes.forEach(route => {
  const filePath = path.join(__dirname, '..', '..', 'src', route.path + '.vue');
  if (!fs.existsSync(filePath)) {
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    fs.writeFileSync(filePath, blankPageContent);
    console.log(`创建空白页面: ${filePath}`);
  }
});

console.log('路由表已生成并更新到 pages.json 文件，缺失的页面文件已创建');