我的目标是将html转换为uniapp、ant-weapp组件库，支持跳转链接、支持函数调用的vue代码。
为了完成我的目标，我需要将任务分拆到4个步骤
- 步骤1：你通过我输入的prompt+html+vue，生成vue代码，文件路径为： src/pages/goods_management/goods_management.vue 
    你要用到的prompt：@HTML2Vue.md ,
    HTML代码： @管理_商品管理.html 
    图片 @管理_商品管理.png  
- 步骤2：
    在步骤3生成的 src/pages/goods_management/goods_management.vue 的基础上，提取出vant-weapp组件，尽量使用组件，并严格保持页面样式的一致性。严格的vant-weapp组件写法，不要偷懒！我给出一些官方用例帮助你更好的理解组件写法：
    可用的vant-weapp组件列表:
    van-button, van-cell, van-icon, van-image, van-nav-bar, van-search, van-tabbar, van-tabbar-item, van-tabs, van-tab, van-popup, van-picker, van-datetime-picker, van-action-sheet, van-uploader, van-field, van-checkbox, van-checkbox-group, van-radio, van-radio-group, van-switch, van-stepper, van-slider, van-rate, van-progress, van-loading, van-notice-bar, van-swipe-cell, van-grid, van-grid-item, van-card, van-tag, van-col, van-row, van-collapse, van-collapse-item, van-submit-bar, van-goods-action, van-goods-action-icon, van-goods-action-button, van-skeleton, van-empty, van-divider, van-circle, van-count-down, van-sticky, van-dropdown-menu, van-dropdown-item, van-overlay, van-share-sheet, van-cascader, van-area, van-tree-select, van-index-bar, van-index-anchor
    ```
        <van-cell-group>
            <van-field value="{{ sms }}" center clearable label="短信验证码" placeholder="请输入短信验证码" border="{{ false }}" use-button-slot>
                <van-button slot="button" size="small" type="primary">
                发送验证码
                </van-button>
            </van-field>
        </van-cell-group>
    ```
    ---
    ```
        <van-cell-group>
        <van-cell title="价格信息" use-label-slot>
            <template #label>
            <van-grid :column-num="2" :gutter="10">
                <van-grid-item
                v-for="(room, index) in roomTypes"
                :key="index"
                :icon="room.icon"
                :text="room.text"
                @click="navigateToCreateRoomPrice(room.type)"/>
            </van-grid>
            </template>
        </van-cell>
        </van-cell-group>
    ```
    ---
    ```
        <van-cell-group id="menuList" inset custom-style="background-color: #fff; border-radius: 8px;">
        <van-cell id="menuItem1" is-link title="包厢新增" custom-style="font-size: 16px; color: #333; padding: 16px;" @click="onClickMenuItem('pages/room_management/add_room')"></van-cell>
        <van-divider custom-style="margin: 0 16px;"></van-divider>
        </van-cell-group>
    ```
    ---
    ```
        <van-cell-group inset title="启用包间类型">
            <van-field label="启用包间类型" readonly :border="false" />
            <van-checkbox-group id="checkbox-group-1" direction="horizontal" v-model="selectedRoomTypes">
                <van-cell v-for="(room, index) in roomTypes" :key="index" :title="room" clickable @click="toggleRoomType(room)">
                <template #right-icon>
                    <van-checkbox :name="room" shape="square" :checked-color="index === 0 ? '#5f6fee' : ''" @click.stop />
                </template>
                </van-cell>
            </van-checkbox-group>
        </van-cell-group>
        ```

    请记住:你的响应应该只包含完整的修改后的代码,准备直接替换原始文件。不要包含任何额外的解释或注释,除非是在代码内部的必要注释。

- 步骤3：在步骤2生成 src/pages/goods_management/goods_management.vue 的基础上，保持现有的样式不变,专注于添加交互功能:
   a. 为适当的元素添加点击事件。
   b. 实现页面跳转功能,使用project_routes中提供的路由信息。
   c. 集成API调用,使用api_declares中定义的函数进行数据提交或获取。
   d. 使用utils/navigation.js中的navigateTo方法进行页面跳转，不要偷懒！
   这个页面的下一级路由包括： [
      "goods_management/add_goods",
      "goods_management/add_package",
      "goods_management/goods_category",
      "goods_management/goods_statistics_category",
      "goods_management/goods_display_category",
      "goods_management/time_period_management",
      "goods_management/flavor_management",
      "goods_management/production_management",
      "goods_management/goods_binding",
      "goods_management/wine_storage_settings",
      "goods_management/gift_group",
      "goods_management/multi_goods_cumulative_promotion"
    ]
