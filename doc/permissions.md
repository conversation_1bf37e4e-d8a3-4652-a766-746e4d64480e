# 权限配置说明文档

## 概述
本文档描述了系统的权限配置数据结构及其字段说明。权限配置采用动态方式，由服务端下发配置数据。

## 权限组件类型定义
系统中定义了以下几种基础权限组件类型：

```json
{
  "components": {
    "operation": {
      "id": "string",
      "name": "string",
      "operable": "boolean",
      "visible": "boolean"
    },
    "switch": {
      "id": "string",
      "name": "string",
      "value": "boolean"
    },
    "input": {
      "id": "string",
      "name": "string",
      "value": "string"
    }
  }
}
```

## UI组件映射
权限组件类型与UI组件的映射关系：

### 权限组件类型 -> UI组件类型
| 权限组件类型 | UI组件类型 | 说明 |
|------------|------------|------|
| operation | name-desc-config | 操作与可见性控制组件 |
| switch | name-desc | 开关选择组件 |
| input | value-group | 数值输入组件 |

### UI组件类型映射定义
```json
{
  "uiMapping": {
    "name-desc-config": {
      "componentType": "operation",
      "description": "操作与可见性控制组件"
    },
    "name-desc": {
      "componentType": "switch",
      "description": "开关选择组件"
    },
    "value-group": {
      "componentType": "input",
      "description": "数值输入组件"
    }
  }
}
```

## 公共字段
所有权限配置类型都包含以下公共字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 权限唯一标识符 |
| name | string | 权限名称 |
| type | string | 权限组件类型 |

## 权限配置类型

### 1. operation
操作与可见性配置类型

| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 固定值："operation" |
| operable | boolean | 是否可操作 |
| visible | boolean | 是否可见 |

### 2. switch
开关类型配置

| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 固定值："switch" |
| value | boolean | 是否启用该权限 |

### 3. input
数值类型配置

| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 固定值："input" |
| value | string | 输入值 |

## 示例
```json
{
  "permissions": {
    "open": {
      "id": "001",
      "name": "开台",
      "type": "operation",
      "operable": false,
      "visible": true
    },
    "export": {
      "id": "002",
      "name": "数据导出",
      "type": "switch",
      "value": true
    },
    "approvalLimit": {
      "id": "003",
      "name": "审批额度",
      "type": "input",
      "value": "10000"
    }
  }
}
```

## 使用说明
1. 服务端按照权限组件类型定义下发权限配置
2. 前端根据UI组件映射关系渲染对应的控件
3. 权限变更时需要同步更新服务端配置
4. UI组件可以根据实际需求进行调整，但需要保持与权限组件类型的映射关系 