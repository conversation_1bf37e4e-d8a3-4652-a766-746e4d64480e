# 微信小程序登录流程说明

## 登录流程概述

登录流程主要分为以下几个步骤：
1. 检查路由参数
2. 检查本地 token
3. 微信登录获取 code
4. 根据场景判断是否需要手机号
5. 用户注册/登录

## 路由参数说明

### 场景参数
- `sa`: 场景ID，用于判断是否需要获取手机号
- 当 `sa` 参数存在时，表示需要手机号验证
- 当 `sa` 参数不存在时, 可跳过手机号验证

## 详细流程说明

### 1. 路由参数检查
- 获取页面路由参数
- 检查是否包含 `sa` 参数
- 根据 `sa` 参数决定后续流程

### 2. Token 检查
- 检查本地是否存在有效 token
- 如果有 token，直接携带 token 请求 `/login` 接口
- 如果没有 token 或 token 失效，进入微信登录流程

### 3. 微信登录
- 调用 `wx.login()` 获取 code
- 将 code 发送到服务端 `/login` 接口
- 服务端通过 code 获取 unionId

### 4. 手机号处理
- 检查是否需要手机号（根据 sa 参数）
- 如需要手机号：
  - 检查该 unionId 是否已绑定手机号
  - 如未绑定，调用 `wx.getPhoneNumber` 获取手机号
  - 将 code 和 unionId 发送到 `/api/getPhoneNumber` 接口
- 如不需要手机号：
  - 直接进入注册/登录流程

### 5. 注册/登录
- 调用 `/loginStr` 接口完成注册/登录
- 保存返回的 token 到本地存储

## 流程图

```mermaid
flowchart TD
    Start[开始] --> A{检查路由参数}
    A --> B{检查本地token}
    
    B -->|有token| C[携带token请求/login]
    B -->|无token| D[wx.login获取code]
    
    D --> E[发送code到服务端]
    E --> F[获取unionId]
    
    F --> G{是否有sa参数}
    G -->|有sa| H{检查是否有手机号}
    G -->|无sa| J[调用/loginStr]
    
    H -->|有| J
    H -->|无| I[获取手机号]
    I --> I2[调用/api/getPhoneNumber]
    I2 --> J
    
    J --> K[保存token]
    K --> L[登录成功]
    
    C -->|验证成功| L
    C -->|验证失败| D
```

## 接口说明

### 1. 登录接口
```typescript
interface LoginRequest {
    token?: string;
    code?: string;
    sa?: string;  // 场景ID参数
}

interface LoginResponse {
    token: string;
    unionId: string;
    hasPhone: boolean;
    needPhone: boolean;  // 是否需要手机号
}
```

### 2. 获取手机号接口
```typescript
interface GetPhoneRequest {
    code: string;
    unionId: string;
    sa: string;  // 场景ID参数
}

interface GetPhoneResponse {
    phoneNumber: string;
    success: boolean;
}
```

### 3. 注册登录接口
```typescript
interface LoginStrRequest {
    unionId: string;
    phoneNumber?: string;  // 可选参数
    sa?: string;  // 场景ID参数
}

interface LoginStrResponse {
    token: string;
    userInfo: UserInfo;
}
```

## 注意事项

1. 场景判断：需要根据 `sa` 参数判断是否需要手机号验证
2. token 失效处理：当 token 失效时，需要重新走完整个登录流程
3. 手机号获取失败：需要提供重试机制
4. 数据安全：所有敏感信息传输需要使用 HTTPS
5. 错误处理：各步骤都需要合适的错误提示和异常处理
6. 参数传递：确保 `sa` 参数在整个流程中正确传递

## 示例代码

```typescript
// 获取路由参数
const query = wx.getLaunchOptionsSync().query;
const sa = query.sa;

// 判断是否需要手机号
const needPhone = !!sa;

// 登录流程
async function login() {
  try {
    const token = wx.getStorageSync('token');
    if (token) {
      const loginResult = await loginWithToken(token, sa);
      if (loginResult.success) return;
    }
    
    const code = await wx.login();
    const loginRes = await loginWithCode(code, sa);
    
    if (needPhone && !loginRes.hasPhone) {
      const phoneRes = await getPhoneNumber(loginRes.unionId, sa);
      // 处理手机号逻辑
    }
    
    const finalLogin = await loginStr({
      unionId: loginRes.unionId,
      phoneNumber: phoneRes?.phoneNumber,
      sa
    });
    
    wx.setStorageSync('token', finalLogin.token);
  } catch (error) {
    // 错误处理
  }
}
```