# VIPER-VM 架构说明文档

## 1. 架构概述

VIPER-VM (VIPER with Vue and Model Management) 是一个基于VIPER架构模式，专为Vue3应用设计的现代化前端架构。该架构整合了Vue3的组合式API、响应式系统以及完善的模型管理机制，旨在提供一个清晰、可维护且高度可测试的应用架构方案。

## 2. 目录结构

```
src/
├── pages/                              # 业务页面模块
│   └── [business_module1]/                 # 业务模块
│       ├── [page_name1]/                       # 页面1功能
│       │   ├── index.vue                           # View层
│       │   ├── form.ts                             # UI表单模型
│       │   ├── presenter.ts                        # Presenter层
│       │   ├── converter.ts                        # ViewModel转换层
│       │   └── interactor.ts                       # 业务交互层
│       ├── [page_name2]/                       # 页面2功能
│       ├── api/                                # api接口
│       │   ├── api1.ts                             # 模块api接口1
│       │   ├── api2.ts                             # 模块api接口2
│       │   └── index.ts                            # 统一导出
│       ├── entity/                             # 实体模型
│       │   ├── model1.ts                           # 模块内实体1
│       │   ├── model2.ts                           # 模块内实体2
│       │   └── index.ts                            # 统一导出
│       └── store/                              # store状态管理
│           ├── store1.ts                           # 状态管理1
│           ├── store2.ts                           # 状态管理2
│           └── index.ts                            # 统一导出
├── router/                                 # 路由管理
│   ├── index.ts                            # 路由主文件
│   ├── helper.ts                           # 路由工具方法
│   ├── guards.ts                           # 路由守卫
│   ├── types.ts                            # 类型定义
│   └── modules/                            # 路由模块配置
│       ├── module1.routes.ts                   # 模块1路由配置
│       ├── module2.routes.ts                   # 模块2路由配置
│       └── index.ts                            # 统一导出
├── components/                             # easycom共享组件
├── api                                     # 通用api接口
│   ├── api1.ts                                 # 模块api接口1
│   ├── api2.ts                                 # 模块api接口2
│   └── index.ts                                # 统一导出
├── data/                                   # 数据管理模块
│   │── biz1DataManager.ts                      # 业务数据管理1
│   └── biz2DataManager.ts                      # 业务数据管理2
├── entity/                                 # 通用实体模型
│   ├── model1.ts                               # 通用实体1
│   ├── model2.ts                               # 通用实体2
│   └── index.ts                                # 统一导出
├── store/                                  # 全局状态
│   ├── store1.ts                               # 状态管理1
│   ├── store2.ts                               # 状态管理1
│   └── index.ts                                # 统一导出
├── utils/                                  # 工具集
│   ├── utils.ts                                # 工具
│   └── navigation.ts                           # 导航封装
└── persistence/                            # 持久化配置
```

## 3. 核心模块说明

### 3.1 业务页面模块 (pages)
每个页面模块包含完整的VIPER-VM架构实现：
- `index.vue`: 视图层，负责UI渲染和用户交互
- `form.ts`: UI表单模型，定义页面表单数据结构
- `presenter.ts`: 主持层，管理组件状态和业务流程
- `converter.ts`: 视图模型转换，处理数据转换
- `interactor.ts`: 业务交互层，实现业务逻辑

### 3.2 共享组件 (components)
- 基于easycom规范的可复用组件
- 遵循自动导入规则
- 专注于UI复用，不包含业务逻辑

### 3.3 API接口 (api)
- 按业务模块组织API接口
- 统一的接口定义和导出
- 支持TypeScript类型定义

### 3.4 数据管理 (data)
- 实现DataManager模式
- 处理数据的CRUD操作
- 管理缓存策略
- 协调本地存储和网络请求

### 3.5 实体模型 (entity)
- 定义核心数据模型
- 按业务模块组织
- 包含通用类型定义
- 支持模型关系映射

### 3.6 全局状态 (store)
- 基于Pinia的状态管理
- 模块化的状态组织
- 支持持久化配置
- 统一的状态访问接口

### 3.7 工具集 (utils)
- 通用工具函数
- 导航管理封装
- 其他辅助功能

### 3.8 持久化配置 (persistence)
- 状态持久化策略
- 本地存储配置
- 缓存策略定义

## 4. 开发规范

### 4.1 命名规范
- 文件夹名：小写字母，单词间用下划线分隔
- vue文件：小写字母，单词间用下划线分隔
- 其他文件：camelCase
- 类名：PascalCase
- 变量/函数：camelCase

### 4.2 模块规范
- 每个业务模块独立维护
- 统一的导出接口
- 明确的依赖关系
- 完整的类型定义

### 4.3 代码组织
- 相关代码放在一起
- 职责单一
- 高内聚，低耦合
- 避免循环依赖

## 5. 数据流

### 5.1 视图数据流
View <-> Presenter <-> ViewModelConverter <-> Entity

### 5.2 业务数据流
Presenter -> Interactor -> DataManager -> API/Store

### 5.3 状态管理
- vue页面状态：Presenter管理
- 全局状态：Store管理
- 持久化：Persistence配置

## 6. 最佳实践

### 6.1 组件开发
- 使用组合式API
- 保持组件纯粹性
- 适当的粒度划分
- 充分利用TypeScript

### 6.2 状态管理
- 合理使用响应式
- 避免状态混乱
- 清晰的数据流向
- 适当的缓存策略

### 6.3 性能优化
- 按需加载
- 合理的缓存
- 避免不必要的计算
- 优化打包体积

## 7. 测试策略

### 7.1 单元测试
- Presenter层逻辑
- Interactor业务规则
- Converter转换逻辑
- Utils工具函数

### 7.2 集成测试
- 页面交互流程
- 数据流程验证
- API调用集成
- 状态管理集成

## 8. 部署和构建

### 8.1 开发环境
- 开发服务器配置
- 代理设置
- 热更新支持

### 8.2 生产环境
- 构建优化
- 代码分割
- 缓存策略
- 错误处理

## 9. 维护和更新

### 9.1 代码维护
- 定期代码审查
- 性能监控
- 依赖更新
- 文档更新

### 9.2 版本控制
- 语义化版本
- 变更日志
- 升级指南

## 10. 工具支持

### 10.1 开发工具
- VS Code配置
- ESLint规则
- TypeScript配置
- Git规范

### 10.2 调试工具
- Vue DevTools
- 网络调试
- 性能分析

## 11. 注意事项

- 遵循单一职责原则
- 保持代码简洁清晰
- 注重代码复用
- 重视类型安全
- 保持文档更新