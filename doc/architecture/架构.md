
```txt
view
    - form data

presenter 
    - converter
        - convert(view form data  <--> models)
        - data binding
        - computed
    - interactor(包含应用程序中用例描述的业务逻辑。Interactor负责从模型层获取数据[使用网络或本地数据库],其实现完全独立于用户界面。)
        - data loader
        - model cache(memory or pinia)
        - business logic
    - router
    - user event
    - state listener

model
    - model1
    - model2
    - model3
```


```
架构名称：VIPER-VM (VIPER with Vue and Model Management)
View（视图层）
    - Template (UI结构)
        - databinding: @Entity.UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter

Presenter（主持层）
    - StateManager (状态管理)
        - reactive data（响应式数据）
        - computed properties（计算属性）
    - EventHandlers (事件处理)
    - DataManagement（数据管理）
        - @ViewModelConverter
        - @Interactor
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (视图-模型转换层)
    - ConversionLogic(转换逻辑)
    - @Entity

Interactor（业务交互层）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（路由层）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调

Entity（实体层）
    - DataModels (数据模型)
    - UIForms (UI表单模型, 单独文件)
    - ModelRelationships (模型关系)

Store (全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```



```
架构名称：VIPER-VM (VIPER with Vue and Model Management)
View（视图层，一个文件）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @UIForms (UI表单模型, 单独文件)

UIForms (UI表单模型, 一个View对应一个文件)

Presenter（主持层，一个文件）
    - StateManager (状态管理)
        - reactive data（响应式数据）
        - computed properties（计算属性）
    - EventHandlers (事件处理)
    - DataManagement（数据管理）
        - @ViewModelConverter
        - @Interactor
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (视图-模型转换层，一个文件)
    - ConversionLogic(转换逻辑)
    - @Entity
    - @View.UIForms

Interactor（业务交互层，一个文件）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层，一个文件）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（路由层-页面切换）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调
    - 切换Component

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)

Store (全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```


```
src/
├── pages/                               # 业务页面模块
│   └── [business_module1]/              # 业务模块
│       └── [page_name]/                 # 页面功能
│           ├── index.vue                # View层
│           ├── form.ts                  # UI表单模型
│           ├── presenter.ts             # Presenter层
│           ├── converter.ts             # ViewModel转换层
│           └── interactor.ts            # 业务交互层
├── components/                          # easycom共享组件
├── api                                  # api接口
│   └── [business_module]/               # 业务模块
│       ├── api1.ts                      # 模块api接口1
│       ├── api2.ts                      # 模块api接口2
│       └── index.ts                     # 统一导出
├── data/                                # 数据管理模块
│   │── biz1DataManager.ts
│   └── biz2DataManager.ts
├── entity/                              # 实体模型
│   │── [business_module1]/               # 业务模块
│   │   ├── model1.ts                    # 模块内实体1
│   │   ├── model2.ts                    # 模块内实体2
│   │   └── index.ts                     # 统一导出
│   └── common/                          # 通用类型
│       ├── model1.ts                    # 通用实体1
│       ├── model2.ts                    # 通用实体2
│       └── index.ts                     # 统一导出
├── store/                               # 全局状态
│   ├── [business_module1]/              # 状态模块1
│   │   ├── store1.ts
│   │   ├── store2.ts
│   │   └── index.ts                     # 统一导出
│   └── index.ts                         # 统一导出
├── utils/                               # 工具集
│   ├── utils.ts                         # 工具
│   ├── navigation.ts                    # 导航封装
│   └── request.ts                       # 网络请求封装
└── persistence/                         # 持久化配置
```


```mermaid
graph TD
    subgraph View
        A[Template]
        B[ComponentLogic]
        C[UIForms]
        style A fill:#ffcccc,stroke:#ff0000
        style B fill:#ffcccc,stroke:#ff0000
        style C fill:#ffcccc,stroke:#ff0000
        D[Presenter]
        style D fill:#ccffcc,stroke:#00ff00
    end

    subgraph Presenter
        E[StateManager]
        F[EventHandlers]
        G[DataManagement]
        style E fill:#ffcccc,stroke:#ff0000
        style F fill:#ffcccc,stroke:#ff0000
        style G fill:#ffcccc,stroke:#ff0000
        H[ViewModelConverter]
        I[Interactor]
        J[Router]
        K[Store]
        style H fill:#ccffcc,stroke:#00ff00
        style I fill:#ccffcc,stroke:#00ff00
        style J fill:#ccffcc,stroke:#00ff00
        style K fill:#ccffcc,stroke:#00ff00
    end

    subgraph ViewModelConverter
        L[ConversionLogic]
        style L fill:#ffcccc,stroke:#ff0000
        M[Entity]
        N[View.UIForms]
        style M fill:#ccffcc,stroke:#00ff00
        style N fill:#ccffcc,stroke:#00ff00
    end

    subgraph Interactor
        O[UseCases]
        P[BusinessLogic]
        style O fill:#ffcccc,stroke:#ff0000
        style P fill:#ffcccc,stroke:#ff0000
        Q[DataManager]
        R[Entity.DataModels]
        style Q fill:#ccffcc,stroke:#00ff00
        style R fill:#ccffcc,stroke:#00ff00
    end

    subgraph DataManager
        S[APIService]
        T[LocalStorage]
        U[CachingStrategy]
        style S fill:#ffcccc,stroke:#ff0000
        style T fill:#ffcccc,stroke:#ff0000
        style U fill:#ffcccc,stroke:#ff0000
        V[Store.PersistenceManager]
        W[Entity.DataModels]
        style V fill:#ccffcc,stroke:#00ff00
        style W fill:#ccffcc,stroke:#00ff00
    end

    subgraph Router
        X[NavigationLogic]
        style X fill:#ffcccc,stroke:#ff0000
    end

    subgraph Entity
        Y[DataModels]
        Z[ModelRelationships]
        style Y fill:#ffcccc,stroke:#ff0000
        style Z fill:#ffcccc,stroke:#ff0000
    end

    subgraph Store
        AA[GlobalState]
        BB[Actions]
        CC[Mutations]
        DD[PersistenceManager]
        style AA fill:#ffcccc,stroke:#ff0000
        style BB fill:#ffcccc,stroke:#ff0000
        style CC fill:#ffcccc,stroke:#ff0000
        style DD fill:#ffcccc,stroke:#ff0000
    end

    View --> Presenter
    Presenter --> ViewModelConverter
    Presenter --> Interactor
    Presenter --> Router
    Presenter --> Store
    ViewModelConverter --> Entity
    Interactor --> DataManager
    Interactor --> Entity
    DataManager --> Store
    DataManager --> Entity
    Router --> Store
```