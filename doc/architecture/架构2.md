## 架构示意说明
```
架构名称：VIPER-VM (VIPER with Vue and Model Management)
View（视图层，一个文件）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @UIForms (UI表单模型, 单独文件)

UIForms (UI表单模型, 一个View对应一个文件)

Presenter（主持层，一个文件）
    - StateManager (状态管理)
        - reactive data（响应式数据）
        - computed properties（计算属性）
    - EventHandlers (事件处理)
    - DataManagement（数据管理）
        - @ViewModelConverter
        - @Interactor
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (视图-模型转换层，一个文件)
    - ConversionLogic(转换逻辑)
    - @Entity
    - @View.UIForms

Interactor（业务交互层，一个文件）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层，一个文件）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（路由层-页面切换）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调
    - 切换Component

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)

Store (全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```

## 核心架构层级

| 架构层 | 职责 | 依赖关系 | 文件位置 |
|--------|------|----------|-----------|
| **View** | UI展示层 | @Presenter<br>@UIForms | `*.vue` |
| | • Template: UI结构<br>• ComponentLogic: 组件逻辑<br>• UIForms: UI表单模型 | | |
| **UIForms** | UI表单数据模型 | - | `form.ts` |
| | • 一个View对应一个UIForms文件 | | |
| **Presenter** | 主持层 | @ViewModelConverter<br>@Interactor<br>@Router<br>@Store | `presenter.ts` |
| | • StateManager: 响应式数据管理<br>• EventHandlers: 事件处理<br>• DataManagement: 数据管理<br>• RouterManager: 路由管理 | | |
| **ViewModelConverter** | 视图模型转换层 | @Entity<br>@View.UIForms | `converter.ts` |
| | • ConversionLogic: 转换逻辑 | | |
| **Interactor** | 业务交互层 | @DataManager<br>@Entity.DataModels | `interactor.ts` |
| | • UseCases: 业务用例<br>• BusinessLogic: 业务逻辑 | | |
| **DataManager** | 数据管理层 | @Store.PersistenceManager<br>@Entity.DataModels | `*DataManager.ts` |
| | • APIService: API服务<br>• LocalStorage: 本地存储<br>• CachingStrategy: 缓存策略 | | |
| **Router** | 路由管理层 | @Store | `router/*.ts` |
| | • NavigationLogic: 导航逻辑<br>• Component切换 | | |
| **Entity** | 实体层 | - | `entity/*.ts` |
| | • DataModels: 数据模型<br>• ModelRelationships: 模型关系 | | |
| **Store** | 全局状态管理 | - | `store/*.ts` |
| | • GlobalState: 全局状态<br>• Actions: 动作<br>• Mutations: 变更<br>• PersistenceManager: 持久化管理 | | |


## 数据流向

### 数据流向图

```mermaid
graph TD
    %% 视图层交互
    View <--> |UI事件/状态更新| Presenter
    
    %% Presenter层交互
    Presenter <--> |数据转换| ViewModelConverter
    Presenter <--> |业务操作| Interactor
    Presenter <--> |路由控制| Router
    Presenter <--> |状态管理| Store
    
    %% 数据层交互
    ViewModelConverter <--> |模型转换| Entity
    Interactor <--> |数据操作| DataManager
    Interactor <--> |模型访问| Entity
    
    %% 存储层交互
    DataManager <--> |状态同步| Store
    DataManager <--> |数据模型| Entity
    Router <--> |状态访问| Store
```

### 数据流向说明

1. **View层数据流**:
   - View → Presenter: 发送用户操作事件
   - Presenter → View: 返回更新后的UI状态
   
2. **Presenter层数据流**:
   - Presenter → ViewModelConverter: 请求数据转换
   - ViewModelConverter → Presenter: 返回转换后的视图数据
   - Presenter → Interactor: 请求业务操作
   - Interactor → Presenter: 返回业务处理结果
   - Presenter → Router: 请求路由跳转
   - Router → Presenter: 返回路由结果
   - Presenter ↔ Store: 双向数据交互

3. **数据管理流**:
   - Interactor → DataManager: 请求数据操作
   - DataManager → Interactor: 返回数据结果
   - DataManager ↔ Store: 状态同步
   - DataManager ↔ Entity: 数据模型交互

4. **实体层数据流**:
   - ViewModelConverter ↔ Entity: 模型数据转换
   - Entity ↔ DataManager: 数据模型交互


## 目录结构
```
src/
├── pages/                              # 业务页面模块
│   └── [business_module1]/                 # 业务模块
│       ├── [page_name1]/                       # 页面1功能
│       │   ├── index.vue                           # View层
│       │   ├── form.ts                             # UI表单模型
│       │   ├── presenter.ts                        # Presenter层
│       │   ├── converter.ts                        # ViewModel转换层
│       │   └── interactor.ts                       # 业务交互层
│       ├── [page_name2]/                       # 页面2功能
│       ├── api/                                # api接口
│       │   ├── api1.ts                             # 模块api接口1
│       │   ├── api2.ts                             # 模块api接口2
│       │   └── index.ts                            # 统一导出
│       ├── entity/                             # 实体模型
│       │   ├── model1.ts                           # 模块内实体1
│       │   ├── model2.ts                           # 模块内实体2
│       │   └── index.ts                            # 统一导出
│       └── store/                              # store状态管理
│           ├── store1.ts                           # 状态管理1
│           ├── store2.ts                           # 状态管理2
│           └── index.ts                            # 统一导出
├── router/                                 # 路由管理
│   ├── index.ts                            # 路由主文件
│   ├── helper.ts                           # 路由工具方法
│   ├── guards.ts                           # 路由守卫
│   ├── types.ts                            # 类型定义
│   └── modules/                            # 路由模块配置
│       ├── module1.routes.ts                   # 模块1路由配置
│       ├── module2.routes.ts                   # 模块2路由配置
│       └── index.ts                            # 统一导出
├── components/                             # easycom共享组件
├── api                                     # 通用api接口
│   ├── api1.ts                                 # 模块api接口1
│   ├── api2.ts                                 # 模块api接口2
│   └── index.ts                                # 统一导出
├── data/                                   # 数据管理模块
│   │── biz1DataManager.ts                      # 业务数据管理1
│   └── biz2DataManager.ts                      # 业务数据管理2
├── entity/                                 # 通用实体模型
│   ├── model1.ts                               # 通用实体1
│   ├── model2.ts                               # 通用实体2
│   └── index.ts                                # 统一导出
├── store/                                  # 全局状态
│   ├── store1.ts                               # 状态管理1
│   ├── store2.ts                               # 状态管理1
│   └── index.ts                                # 统一导出
├── utils/                                  # 工具集
│   ├── utils.ts                                # 工具
│   └── navigation.ts                           # 导航封装
└── persistence/                            # 持久化配置
```

## 详细模块结构图
```mermaid
graph TD
    subgraph View
        A[Template]
        B[ComponentLogic]
        C[UIForms]
        style A fill:#ffcccc,stroke:#ff0000
        style B fill:#ffcccc,stroke:#ff0000
        style C fill:#ffcccc,stroke:#ff0000
        D[Presenter]
        style D fill:#ccffcc,stroke:#00ff00
    end

    subgraph Presenter
        E[StateManager]
        F[EventHandlers]
        G[DataManagement]
        style E fill:#ffcccc,stroke:#ff0000
        style F fill:#ffcccc,stroke:#ff0000
        style G fill:#ffcccc,stroke:#ff0000
        H[ViewModelConverter]
        I[Interactor]
        J[Router]
        K[Store]
        style H fill:#ccffcc,stroke:#00ff00
        style I fill:#ccffcc,stroke:#00ff00
        style J fill:#ccffcc,stroke:#00ff00
        style K fill:#ccffcc,stroke:#00ff00
    end

    subgraph ViewModelConverter
        L[ConversionLogic]
        style L fill:#ffcccc,stroke:#ff0000
        M[Entity]
        N[View.UIForms]
        style M fill:#ccffcc,stroke:#00ff00
        style N fill:#ccffcc,stroke:#00ff00
    end

    subgraph Interactor
        O[UseCases]
        P[BusinessLogic]
        style O fill:#ffcccc,stroke:#ff0000
        style P fill:#ffcccc,stroke:#ff0000
        Q[DataManager]
        R[Entity.DataModels]
        style Q fill:#ccffcc,stroke:#00ff00
        style R fill:#ccffcc,stroke:#00ff00
    end

    subgraph DataManager
        S[APIService]
        T[LocalStorage]
        U[CachingStrategy]
        style S fill:#ffcccc,stroke:#ff0000
        style T fill:#ffcccc,stroke:#ff0000
        style U fill:#ffcccc,stroke:#ff0000
        V[Store.PersistenceManager]
        W[Entity.DataModels]
        style V fill:#ccffcc,stroke:#00ff00
        style W fill:#ccffcc,stroke:#00ff00
    end

    subgraph Router
        X[NavigationLogic]
        style X fill:#ffcccc,stroke:#ff0000
    end

    subgraph Entity
        Y[DataModels]
        Z[ModelRelationships]
        style Y fill:#ffcccc,stroke:#ff0000
        style Z fill:#ffcccc,stroke:#ff0000
    end

    subgraph Store
        AA[GlobalState]
        BB[Actions]
        CC[Mutations]
        DD[PersistenceManager]
        style AA fill:#ffcccc,stroke:#ff0000
        style BB fill:#ffcccc,stroke:#ff0000
        style CC fill:#ffcccc,stroke:#ff0000
        style DD fill:#ffcccc,stroke:#ff0000
    end

    View --> Presenter
    Presenter --> ViewModelConverter
    Presenter --> Interactor
    Presenter --> Router
    Presenter --> Store
    ViewModelConverter --> Entity
    Interactor --> DataManager
    Interactor --> Entity
    DataManager --> Store
    DataManager --> Entity
    Router --> Store
```





