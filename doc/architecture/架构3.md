## 架构示意说明
```
架构名称：VIPER-VC (VIPER with Vue and Convert)
View（视图层，一个文件）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @IViewModel (UI表单模型, 单独文件)

IViewModel (视图层，IViewModel模块, 一个View对应一个文件, 状态和actions定义)
    - UI states
    - UI actions

Presenter（协调层，一个View对应一个文件，继承IViewModel）
    - @IViewModel(UI actions的实现，UI states数据的处理)
        - StateManager (状态管理)
            - reactive data（响应式数据）
            - computed properties（计算属性）
        - EventHandlers (事件处理)
    - DataConvert（数据转换）
        - @ViewModelConverter
        - @Interactor
        - @View.IViewModel
        - @Entity
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (协调层，视图-模型转换模块，一个View对应一个文件)
    - ConversionLogic(转换逻辑)
    - @Entity
    - @View.IViewModel

Interactor（业务交互层，一个View对应一个文件）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（协调层，路由模块-页面切换）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调
    - 切换Component

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)

Store (基础组件，全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```

## 核心架构层级

| 架构层 | 子层级 | 职责 | 依赖关系 | 文件位置 |
|--------|--------|------|----------|-----------|
| **View层** | **View** | UI展示层 | @Presenter<br>@IViewModel | `*.vue` |
| | | • Template: UI结构<br>• ComponentLogic: 组件逻辑<br>• @IViewModel: UI状态和行为定义(接口) | | |
| | **IViewModel** | UI状态和行为定义 | - | `viewModel.ts` |
| | | • UI states: UI状态定义<br>• UI actions: UI动作定义<br>• 一个View对应一个IViewModel文件 | | |
| **Presenter层** | **Presenter** | 协调层（继承IViewModel） | @IViewModel<br>@ViewModelConverter<br>@Interactor<br>@Router<br>@Store | `presenter.ts` |
| | | • @IViewModel(UI actions的实现，UI states数据的处理)<br>• StateManager: 响应式数据管理<br>• EventHandlers: 事件处理<br>• DataConvert: 数据转换<br>• RouterManager: 路由管理 | | |
| | **ViewModelConverter** | 视图-模型转换层 | @Entity<br>@View.IViewModel | `converter.ts` |
| | | • ConversionLogic: 转换逻辑 | | |
| | **Router** | 路由管理层 | @Store | `router/*.ts` |
| | | • NavigationLogic: 导航逻辑<br>• Component切换 | | |
| | **Store** | 全局状态管理 | - | `store/*.ts` |
| | | • GlobalState: 全局状态<br>• PersistenceManager: 状态持久化管理 | | |
| **Interactor层** | **Interactor** | 业务交互层 | @DataManager<br>@Entity.DataModels | `interactor.ts` |
| | | • UseCases: 业务用例<br>• BusinessLogic: 业务逻辑 | | |
| | **DataManager** | 数据管理层 | @Store.PersistenceManager<br>@Entity.DataModels | `*DataManager.ts` |
| | | • APIService: API服务<br>• LocalStorage: 本地存储<br>• CachingStrategy: 缓存策略 | | |
| **Entity层** | **Entity** | 实体层 | - | `entity/*.ts` |
| | | • DataModels: 数据模型<br>• ModelRelationships: 模型关系 | |


## 数据流向

### 数据流向图

```mermaid
graph TD
    %% 视图层交互
    View <--> |UI事件/状态更新| Presenter
    
    %% Presenter层交互
    Presenter <--> |数据转换| ViewModelConverter
    Presenter <--> |业务操作| Interactor
    Presenter <--> |路由控制| Router
    Presenter <--> |状态管理| Store
    
    %% 数据层交互
    ViewModelConverter <--> |模型转换| Entity
    Interactor <--> |数据操作| DataManager
    Interactor <--> |模型访问| Entity
    
    %% 存储层交互
    DataManager <--> |状态同步| Store
    DataManager <--> |数据模型| Entity
    Router <--> |状态访问| Store
```

### 数据流向说明

1. **View层数据流**:
   - View → Presenter: 发送用户操作事件
   - Presenter → View: 返回更新后的UI状态
   - View ↔ IViewModel: 通过UI states进行数据绑定
   
2. **Presenter层数据流**:
   - Presenter → IViewModel: 实现UI actions，处理UI states
   - Presenter → ViewModelConverter: 请求数据转换
   - ViewModelConverter → Presenter: 返回转换后的视图数据
   - Presenter → Interactor: 请求业务操作
   - Interactor → Presenter: 返回业务处理结果
   - Presenter → Router: 请求路由跳转
   - Router → Presenter: 返回路由结果
   - Presenter ↔ Store: 双向数据交互

3. **数据管理流**:
   - Interactor → DataManager: 请求数据操作
   - DataManager → Interactor: 返回数据结果
   - DataManager ↔ Store: 状态同步
   - DataManager ↔ Entity: 数据模型交互

4. **实体层数据流**:
   - ViewModelConverter ↔ Entity: 模型数据转换
   - Entity ↔ DataManager: 数据模型交互


## 目录结构
```
src/
├── pages/                              # 业务页面模块
│   └── [business_module1]/                 # 业务模块
│       ├── [page_name1]/                       # 页面1功能
│       │   ├── index.vue                           # View层
│       │   ├── viewmodel.ts                        # UI状态和行为定义(接口)
│       │   ├── presenter.ts                        # Presenter层
│       │   ├── converter.ts                        # ViewModel转换层
│       │   └── interactor.ts                       # 业务交互层
│       ├── [page_name2]/                       # 页面2功能
│       ├── api/                                # api接口
│       │   ├── api1.ts                             # 模块api接口1
│       │   ├── api2.ts                             # 模块api接口2
│       │   └── index.ts                            # 统一导出
│       ├── entity/                             # 实体模型
│       │   ├── model1.ts                           # 模块内实体1
│       │   ├── model2.ts                           # 模块内实体2
│       │   └── index.ts                            # 统一导出
│       └── store/                              # store状态管理
│           ├── store1.ts                           # 状态管理1
│           ├── store2.ts                           # 状态管理2
│           └── index.ts                            # 统一导出
├── router/                                 # 路由管理
│   ├── index.ts                            # 路由主文件
│   ├── helper.ts                           # 路由工具方法
│   ├── guards.ts                           # 路由守卫
│   ├── types.ts                            # 类型定义
│   └── modules/                            # 路由模块配置
│       ├── module1.routes.ts                   # 模块1路由配置
│       ├── module2.routes.ts                   # 模块2路由配置
│       └── index.ts                            # 统一导出
├── components/                             # easycom共享组件
├── api                                     # 通用api接口
│   ├── api1.ts                                 # 模块api接口1
│   ├── api2.ts                                 # 模块api接口2
│   └── index.ts                                # 统一导出
├── data/                                   # 数据管理模块
│   │── biz1DataManager.ts                      # 业务数据管理1
│   └── biz2DataManager.ts                      # 业务数据管理2
├── entity/                                 # 通用实体模型
│   ├── model1.ts                               # 通用实体1
│   ├── model2.ts                               # 通用实体2
│   └── index.ts                                # 统一导出
├── store/                                  # 全局状态
│   ├── store1.ts                               # 状态管理1
│   ├── store2.ts                               # 状态管理2
│   └── index.ts                                # 统一导出
├── utils/                                  # 工具集
│   ├── utils.ts                                # 工具
│   └── navigation.ts                           # 导航封装
└── persistence/                            # 持久化配置
```

## 详细模块结构图
```mermaid
graph TD
    subgraph View
        A[Template]
        B[ComponentLogic]
        C[IViewModel]
        style A fill:#ffcccc,stroke:#ff0000
        style B fill:#ffcccc,stroke:#ff0000
        style C fill:#ffcccc,stroke:#ff0000
        D[Presenter]
        style D fill:#ccffcc,stroke:#00ff00
    end

    subgraph Presenter
        E[StateManager]
        F[EventHandlers]
        G[DataConvert]
        style E fill:#ffcccc,stroke:#ff0000
        style F fill:#ffcccc,stroke:#ff0000
        style G fill:#ffcccc,stroke:#ff0000
        H[ViewModelConverter]
        I[Interactor]
        J[Router]
        K[Store]
        style H fill:#ccffcc,stroke:#00ff00
        style I fill:#ccffcc,stroke:#00ff00
        style J fill:#ccffcc,stroke:#00ff00
        style K fill:#ccffcc,stroke:#00ff00
    end

    subgraph ViewModelConverter
        L[ConversionLogic]
        style L fill:#ffcccc,stroke:#ff0000
        M[Entity]
        N[View.IViewModel]
        style M fill:#ccffcc,stroke:#00ff00
        style N fill:#ccffcc,stroke:#00ff00
    end

    subgraph Interactor
        O[UseCases]
        P[BusinessLogic]
        style O fill:#ffcccc,stroke:#ff0000
        style P fill:#ffcccc,stroke:#ff0000
        Q[DataManager]
        R[Entity.DataModels]
        style Q fill:#ccffcc,stroke:#00ff00
        style R fill:#ccffcc,stroke:#00ff00
    end

    subgraph DataManager
        S[APIService]
        T[LocalStorage]
        U[CachingStrategy]
        style S fill:#ffcccc,stroke:#ff0000
        style T fill:#ffcccc,stroke:#ff0000
        style U fill:#ffcccc,stroke:#ff0000
        V[Store.PersistenceManager]
        W[Entity.DataModels]
        style V fill:#ccffcc,stroke:#00ff00
        style W fill:#ccffcc,stroke:#00ff00
    end

    subgraph Router
        X[NavigationLogic]
        style X fill:#ffcccc,stroke:#ff0000
    end

    subgraph Entity
        Y[DataModels]
        Z[ModelRelationships]
        style Y fill:#ffcccc,stroke:#ff0000
        style Z fill:#ffcccc,stroke:#ff0000
    end

    subgraph Store
        AA[GlobalState]
        BB[Actions]
        CC[Mutations]
        DD[PersistenceManager]
        style AA fill:#ffcccc,stroke:#ff0000
        style BB fill:#ffcccc,stroke:#ff0000
        style CC fill:#ffcccc,stroke:#ff0000
        style DD fill:#ffcccc,stroke:#ff0000
    end

    View --> Presenter
    Presenter --> ViewModelConverter
    Presenter --> Interactor
    Presenter --> Router
    Presenter --> Store
    ViewModelConverter --> Entity
    Interactor --> DataManager
    Interactor --> Entity
    DataManager --> Store
    DataManager --> Entity
    Router --> Store
```


## 分层结构图
```mermaid
graph TD
    %% 定义大层级样式
    classDef majorLayer fill:#e6f3ff,stroke:#3182bd,stroke-width:2px
    classDef subLayer fill:#fff7dc,stroke:#fd8d3c,stroke-width:1px
    classDef implementation fill:#f7fcf5,stroke:#74c476,stroke-width:1px

    %% View层
    subgraph ViewLayer["View Layer"]
        View["View<br>(*.vue)"]
        IViewModel["IViewModel<br>(UI states & actions)"]
        
        View --> IViewModel
    end
    class ViewLayer majorLayer

    %% Presenter层
    subgraph PresenterLayer["Presenter Layer"]
        Presenter["Presenter<br>(继承IViewModel)"]
        VMConverter["ViewModelConverter"]
        Router["Router"]
        Store["Store"]
        
        Presenter --> VMConverter
        Presenter --> Router
        Presenter --> Store
    end
    class PresenterLayer majorLayer

    %% Interactor层
    subgraph InteractorLayer["Interactor Layer"]
        Interactor["Interactor"]
        DataManager["DataManager"]
        
        Interactor --> DataManager
    end
    class InteractorLayer majorLayer

    %% Entity层
    subgraph EntityLayer["Entity Layer"]
        Entity["Entity<br>(DataModels & Relationships)"]
    end
    class EntityLayer majorLayer

    %% 层级间的关系
    ViewLayer --> PresenterLayer
    PresenterLayer --> InteractorLayer
    PresenterLayer --> EntityLayer
    InteractorLayer --> EntityLayer

    %% 为子层级应用样式
    class View,IViewModel subLayer
    class Presenter,VMConverter,Router,Store subLayer
    class Interactor,DataManager subLayer
    class Entity subLayer
```



## 示例
假设是一个简单的计数器页面，包含计数值和名称两个状态，以及增加计数和更新名称两个动作。

### 1. viewmodel.ts
```typescript:pages/counter/viewmodel.ts
// UI状态接口
export interface ICounterState {
  count: number
  name: string
}

// UI计算属性接口
export interface ICounterComputed {
  // 显示文本
  displayText: ComputedRef<string>
  // 是否为偶数
  isEven: ComputedRef<boolean>
  // 计数的平方
  countSquared: ComputedRef<number>
}

// UI动作接口
export interface ICounterActions {
  increment(): void
  updateName(newName: string): void
}

// 总的ViewModel接口
export interface ICounterViewModel {
  state: ICounterState
  computed: ICounterComputed  // 新增computed
  actions: ICounterActions
}
```

### 2. presenter.ts
```typescript:pages/counter/presenter.ts
import { ref, reactive, computed, readonly } from 'vue'
import type { ICounterViewModel, ICounterState, ICounterComputed, ICounterActions } from './viewmodel'

export class CounterPresenter implements ICounterViewModel {
  // 内部状态 - private
  private _state = reactive({
    count: 0,
    name: '计数器'
  })

  // 计算属性
  public computed: ICounterComputed = {
    // 组合name和count的显示文本
    displayText: computed(() => {
      return `${this._state.name}: ${this._state.count}`
    }),
    // 判断是否为偶数
    isEven: computed(() => {
      return this._state.count % 2 === 0
    }),
    // 计算平方值
    countSquared: computed(() => {
      return this._state.count * this._state.count
    })
  }

  // 暴露只读状态
  public get state(): ICounterState {
    return readonly(this._state)
  }

  // 动作实现
  public actions: ICounterActions = {
    increment: () => {
      this._state.count++
    },
    updateName: (newName: string) => {
      this._state.name = newName
    }
  }
}

// 导出组合式函数
export function useCounter(): ICounterViewModel {
  return new CounterPresenter()
}
```

### 3. index.vue
```vue:pages/counter/index.vue
<template>
  <view class="counter">
    <!-- 使用计算属性 -->
    <text>{{ vm.computed.displayText }}</text>
    
    <!-- 条件渲染 -->
    <text v-if="vm.computed.isEven">当前是偶数</text>
    <text v-else>当前是奇数</text>
    
    <!-- 显示平方值 -->
    <text>平方值: {{ vm.computed.countSquared }}</text>
    
    <button type="primary" @tap="vm.actions.increment">增加</button>
    <input 
      type="text"
      :value="vm.state.name"
      @input="e => vm.actions.updateName(e.detail.value)" 
    />
  </view>
</template>

<script setup lang="ts">
import { useCounter } from './presenter'
import type { ICounterViewModel } from './viewmodel'

const vm: ICounterViewModel = useCounter()
</script>
```

### 说明：
VIPER-VM架构的基本实现方式：
- 清晰的职责分离
- 类型安全的接口定义
- 简洁的视图层
- 统一的状态和动作管理

实际项目中，可以根据需要添加更多功能：
- 在Presenter中添加与Interactor的交互
- 添加ViewModelConverter处理数据转换
- 集成Router处理导航
- 使用Store管理全局状态
