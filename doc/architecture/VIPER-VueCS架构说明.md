# VIPER-VueCS 架构说明 (VIPER with Vue, Convert, and State)

## 架构示意说明

```
架构名称：VIPER-VueCS (VIPER with Vue, Convert, and State)

View（视图层，一个文件，index.vue）
    - Template (UI结构)
        - databinding: @IViewModel (使用 .value 访问 ComputedRef/Ref)
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @IViewModel (UI状态和行为接口, 单独文件)

IViewModel (视图层，接口定义，viewmodel.ts)
    - UI states (类型为 ComputedRef/WritableComputedRef/Ref)
    - UI computed properties (类型为 ComputedRef)
    - UI actions (方法签名)

Presenter（协调层，实现IViewModel，presenter.ts）
    - @IViewModel (实现 Actions, 通过 computed/ref 实现 States 和 Computed Properties)
        - 从 State 层派生 ViewModel
        - 通过 Actions 处理 UI 事件
    - @State (交互，读取状态，调用更新方法)
    - @Converter (调用，进行 State <-> Entity/DTO 数据格式转换)
    - @Interactor (调用，执行业务用例)
    - @Router (调用，进行导航)
    - LifecycleManager (Vue/UniApp 生命周期管理)

Converter (转换层，converter.ts)
    - ConversionLogic (转换逻辑)
        - State <-> Entity/DTO 格式转换 (例如: toSaveData, toEntity, toStateFormat)
    - Dependencies: @State.PageState, @Entity.DataModels (或 API DTOs)

State (状态层，页面权威状态，state.ts)
    - PageState (响应式状态对象，如 reactive/ref)
    - StateUpdateMethods (修改状态的唯一入口方法)
    - Dependencies: (可能 @Entity 用于结构定义)

Interactor（业务交互层，interactor.ts）
    - UseCases (业务用例实现)
    - BusinessLogic (核心业务逻辑)
    - DataAccess (API 调用, 本地存储等)
    - Dependencies: @Entity.DataModels, @API

Router（路由层，router/*.ts）
    - NavigationLogic (导航逻辑)
    - DataPassing (页面间数据传递)

Entity（实体层，全局共享，entity/*.ts）
    - DataModels (纯粹的业务领域模型)
    - ModelRelationships (模型间关系)

API (通用API接口，api/*.ts)
    - API Function Definitions
```

## 核心架构层级

| 架构层          | 子层级                | 职责                                                                 | 依赖关系                                                              | 文件位置      |
| --------------- | --------------------- | -------------------------------------------------------------------- | --------------------------------------------------------------------- | ------------- |
| **View层**      | **View**              | UI展示层，触发用户事件                                                  | @Presenter<br>@IViewModel                                             | `*.vue`       |
|                 |                       | • Template: UI结构, 绑定ViewModel (注意`.value`使用)<br>• ComponentLogic: 引入Presenter    |                                                                       |               |
|                 | **IViewModel**        | 定义视图契约 (状态、计算属性、动作接口)                                     | -                                                                     | `viewmodel.ts`|
|                 |                       | • **UI states**: 视图直接绑定的基础状态接口 (类型通常为 Ref/ComputedRef/WritableComputedRef)。代表页面的“原始”或基础 UI 状态片段（如输入框的值、加载标志）。<br>• **UI computed properties**: 派生状态接口 (类型通常为 ComputedRef)。代表经过处理、格式化或逻辑判断后的展示性/逻辑性状态（如格式化文本、禁用状态）。<br>• **UI actions**: 动作方法签名。<br> *注：State 与 Computed 的语义区分有助于理解接口意图，即使两者在Presenter中都可能用 `computed` 实现。* |                                                                       |               |
| **Presenter层** | **Presenter**         | **核心协调者** (实现IViewModel)                                          | @IViewModel<br>@State<br>@Converter<br>@Interactor<br>@Router        | `presenter.ts`|
|                 |                       | • 实现 Actions<br>• **从 State 派生 ViewModel** (通过 `computed`/`ref` 实现 IViewModel 的 state & computed properties，建立响应式链接)<br>• 调用 State 更新方法<br>• 调用 Converter 进行**跨层数据格式转换**<br>• 调用 Interactor, Router<br>• 管理生命周期 |                                                                       |               |
|                 | **Converter**         | **数据格式转换器 (跨层/边界)**                                        | @State.PageState<br>@Entity.DataModels (或 API DTOs)                 | `converter.ts`|
|                 |                       | • 负责 **State 层** 与 **外部 (Interactor/API/Entity)** 之间的数据结构和格式转换。<br> • 例如: `State -> Entity/DTO` (用于保存), `FetchedData/Entity -> StateUpdateFormat` (用于加载)。<br> • **不负责** State -> ViewModel 的响应式映射（此为 Presenter 职责）。<br> • 确保格式一致性，封装转换逻辑。 |                                                                       |               |
|                 | **Router**            | 路由管理层                                                               | -                                                                     | `router/*.ts` |
|                 |                       | • 导航逻辑, 数据传递                                                      |                                                                       |               |
| **State层**     | **State**             | **页面状态管理中心** (单一数据源)                                       | (可能 @Entity 用于类型定义)                                             | `state.ts`    |
|                 |                       | • 定义并持有权威的响应式页面状态 (`reactive`或`ref`)。<br>• 提供修改状态的**唯一**方法入口。 |                                                                       |               |
| **Interactor层**| **Interactor**        | **业务逻辑执行者**                                                     | @Entity.DataModels<br>@API                                            | `interactor.ts`|
|                 |                       | • 实现具体业务用例。<br>• 封装 API 调用或本地存储等数据访问。             |                                                                       |               |
| **Entity层**    | **Entity**            | **领域模型定义** (全局)                                                 | -                                                                     | `entity/*.ts` |
|                 |                       | • 定义纯粹的业务数据结构。                                                  |                                                                       |               |
| **API层**       | **API**               | **接口定义** (全局)                                                    | -                                                                     | `api/*.ts`    |
|                 |                       | • 定义与后端或其他服务的接口函数。                                           |                                                                       |               |

## 数据流向

### 数据流向图

```mermaid
graph TD
    subgraph ViewLayer ["View Layer"]
        View["View (*.vue)"] -- Triggers Actions --> Presenter
        View -- Binds to --> IViewModel["IViewModel (Interface)"]
    end

    subgraph PresenterLayer ["Presenter Layer"]
        Presenter["Presenter (*.presenter.ts)"] -- Implements --> IViewModel
        Presenter -- Calls --> StateUpdate["State Update Methods"]
        Presenter -- Reads --> StateData["State (Readonly Data)"]
        Presenter -- Calls --> Converter["Converter (*.converter.ts)"]
        Presenter -- Calls --> Interactor["Interactor (*.interactor.ts)"]
        Presenter -- Calls --> Router["Router (*.router.ts)"]
    end
    
    subgraph StateLayer ["State Layer"]
        StateModule["State (*.state.ts)"]
        StateModule -- Contains --> StateData
        StateModule -- Contains --> StateUpdate
    end

    subgraph InteractorLayer ["Interactor Layer"]
        Interactor -- Calls --> API["API (*.api.ts)"]
        Interactor -- Uses --> Entity["Entity (*.entity.ts)"]
    end

    subgraph ConversionLayer ["Converter Layer"]
         Converter -- Uses --> StateData
         Converter -- Uses --> Entity
    end

    subgraph EntityLayer ["Entity Layer"]
        Entity
    end
    
    subgraph APILayer ["API Layer"]
        API
    end
    
    %% Dependencies
    PresenterLayer --> StateLayer
    PresenterLayer --> InteractorLayer
    PresenterLayer --> ConversionLayer
    PresenterLayer --> EntityLayer  // Via Converter/Interactor
    PresenterLayer --> APILayer     // Via Interactor
    
    InteractorLayer --> EntityLayer
    InteractorLayer --> APILayer

    StateLayer --> EntityLayer // Optional: For types

    ConversionLayer --> StateLayer
    ConversionLayer --> EntityLayer


    %% Data Flow for Update Example
    style View fill:#e6f3ff
    style Presenter fill:#e6f3ff
    style StateUpdate fill:#fff7dc
    style StateData fill:#fff7dc
    style IViewModel fill:#e6f3ff

    View -- 1. User Interaction --> Actions["ViewModel.actions.method()"]
    Actions -- 2. Call --> Presenter
    Presenter -- "3. Logic, calls StateUpdate()" --> StateUpdate
    StateUpdate -- 4. Mutates --> StateData
    StateData -- "5. Reactivity" --> Presenter
    Presenter -- "6. Updates ViewModel (Refs/ComputedRefs)" --> IViewModel
    IViewModel -- "7. Updates View" --> View
```

### 数据流向说明 (以用户更新名称为例)

1.  **View -> Presenter:** 用户在输入框输入，`v-model` 绑定到 `vm.state.name.value`。当值变化时，`vm.state.name` (通常是 `WritableComputedRef`) 的 `set` 方法被调用，该方法内部会调用 `Presenter` 中对应的 `Action` (如 `actions.updateName(newValue)`)。
2.  **Presenter -> State:** `Presenter` 的 `updateName` Action 被执行。它接收新值，并调用 `State` 层提供的**专用更新方法** (如 `pageState.setName(newValue)`)。
3.  **State Internal:** `State` 层的 `setName` 方法执行内部逻辑（如校验），并**更新其内部的响应式 `state` 对象**的 `userName` 属性。
4.  **State -> Presenter (Reactivity):** 由于 `State` 的 `state` 对象是响应式的，其 `userName` 的变化会被 Vue 检测到。`Presenter` 中**依赖** `pageState.state.userName` 的 `computed` 属性（例如 `vm.state.name` 的 `get` 部分和 `vm.computed.displayText`）会自动重新计算。
5.  **Presenter -> ViewModel (Implicit Update):** `computed` 属性重新计算后，`IViewModel` 接口暴露给视图的 `state.name` 和 `computed.displayText` 属性的值**自动更新**。
6.  **ViewModel -> View (Reactivity):** Vue 的响应式系统检测到 `ViewModel` 属性的变化，自动更新 `View` 中绑定了这些属性的部分（如输入框的值、`displayText` 的显示）。

**核心思想：** 数据更新流程是**单向**且由 **Action 发起**的。通过修改权威的 **`State` 层**数据，再利用**响应式系统**驱动 `ViewModel` 和 `View` 的更新，确保数据流清晰可控。

## 目录结构

```
src/
├── pages/                              # 业务页面模块
│   └── [business_module1]/                 # 业务模块
│       ├── [page_name1]/                       # 页面1功能
│       │   ├── index.vue                           # View层
│       │   ├── viewmodel.ts                        # ViewModel接口定义
│       │   ├── presenter.ts                        # Presenter层实现
│       │   ├── state.ts                            # State层 (页面状态管理)
│       │   ├── converter.ts                        # Converter层 (数据转换)
│       │   └── interactor.ts                       # Interactor层 (业务逻辑)
│       └── [page_name2]/                       # 页面2功能
├── router/                                 # 路由管理
├── api/                                    # 通用api接口
│   └── ...
├── entity/                                 # 通用实体模型
│   └── ...
└── utils/                                  # 工具集
    └── ...
```

## 文件职责说明 (File Responsibilities)

*   **`index.vue` (View):**
    *   负责 UI 的结构 (HTML/Template) 和样式 (CSS)。
    *   通过 `useXxxPresenter()` 获取 `IViewModel` 实例。
    *   将视图元素绑定到 `IViewModel` 的 `state` 和 `computed` 属性（**严格遵守 `.value` 使用规则**）。
    *   将用户交互事件（如点击、输入）绑定到 `IViewModel` 的 `actions` 方法。
    *   **严禁**包含任何业务逻辑、状态管理或直接 API 调用。
*   **`viewmodel.ts` (IViewModel Interface):**
    *   定义视图层与 `Presenter` 之间的**契约（接口）**。
    *   定义 `state` 接口：视图需要直接绑定的基础数据（类型通常为 Ref, ComputedRef, WritableComputedRef）。
    *   定义 `computed` 接口：基于 `state` 或底层状态派生出的计算属性（类型通常为 ComputedRef）。
    *   定义 `actions` 接口：视图可以触发的操作方法签名。
    *   **区分 `state` (基础) 和 `computed` (派生) 的语义**。
    *   **不包含**任何实现逻辑。
*   **`presenter.ts` (Presenter):**
    *   实现 `IViewModel` 接口。
    *   作为**核心协调者**，连接 View、State、Interactor、Converter、Router。
    *   **从 `State` 层读取数据**，并通过 `computed` 或 `ref` 实现 `IViewModel` 的 `state` 和 `computed` 属性，建立**响应式链接**暴露给 View。
    *   实现 `IViewModel` 的 `actions` 方法，处理用户交互：
        *   调用 `State` 层的**更新方法**来修改页面状态。
        *   调用 `Interactor` 执行业务逻辑和数据操作。
        *   调用 `Converter` 进行 **State <-> Entity/DTO** 的数据格式转换。
        *   调用 `Router` 进行导航。
    *   管理相关的 Vue 或 UniApp **生命周期**逻辑。
    *   **严禁**直接修改 `State` 层的数据（必须通过其方法）。
    *   **严禁**直接调用 API。
*   **`state.ts` (State):**
    *   定义并持有该页面的**权威（Single Source of Truth）、响应式**状态数据 (`reactive` 或 `ref`)。
    *   提供**唯一的方法入口**来修改这些状态数据。这些方法可包含简单的状态校验。
    *   确保状态的**中心化管理和受控更新**。
    *   **严禁**包含复杂的业务流程逻辑或 API 调用。
*   **`converter.ts` (Converter):**
    *   负责不同**层级/数据边界**之间的数据**结构和格式转换**。
    *   主要职责是 **`State` <-> `Entity`/DTO (External)** 之间的转换。
    *   确保数据格式的适配和一致性。
    *   **严禁**包含任何业务逻辑。
    *   **不负责** `State` 到 `ViewModel` 的响应式映射（此为 Presenter 职责）。
*   **`interactor.ts` (Interactor):**
    *   封装和实现页面的**核心业务逻辑**和用例。
    *   负责与数据源交互，如**调用 API**、访问本地存储等。
    *   通常操作 `Entity` 或 DTOs。
    *   处理来自数据源的数据并返回给 `Presenter`，或接收来自 `Presenter` (经 Converter 转换后) 的数据进行持久化。
    *   **严禁**直接操作 `ViewModel` 或 `State` 层。
    *   **严禁**包含任何 UI 相关的逻辑。

## 重要注意事项 (Important Considerations)

1.  **响应式引用 (`.value`) 的使用:**
    *   `IViewModel` 的 `state` 和 `computed` 属性在 `Presenter` 中实现时，其类型为 `Ref`、`ComputedRef` 或 `WritableComputedRef`。
    *   在 `index.vue` 模板中，访问这些响应式引用的值时：插值 `{{ }}`、`v-model`、属性绑定 (`:`, `v-bind`)、指令 (`v-if`, `v-for` 等)、模板内 JS 表达式，**都必须显式使用 `.value`**。
    *   在 `.ts` 文件内部（如 `Presenter`）访问这些 Ref 时，**必须**使用 `.value`。

2.  **类型安全 (`No 'any'`):**
    *   项目所有 TypeScript 文件中**严禁使用 `any` 类型**。
    *   所有变量、函数参数、返回值、接口属性都**必须**有明确的类型定义。
    *   `State`, `Entity`, `API` 返回值等的数据结构必须清晰定义。

3.  **State 层是权威数据源:**
    *   `state.ts` 中定义的响应式状态是该页面**唯一可信的数据来源**。
    *   任何对页面状态的修改**必须**通过调用 `state.ts` 导出的**更新方法**来完成。

4.  **严格的职责分离:**
    *   **View (`index.vue`):** 纯粹的 UI 展示和事件发出点。
    *   **Presenter (`presenter.ts`):** 协调者，连接各层，处理 UI 逻辑，调用更新。
    *   **State (`state.ts`):** 状态容器和受控更新入口。
    *   **Converter (`converter.ts`):** **跨层/边界**数据格式转换 (State <-> External)。
    *   **Interactor (`interactor.ts`):** 业务逻辑和数据访问 (API 调用)。
    *   **ViewModel (`viewmodel.ts`):** 只依赖View(`index.vue`)，不依赖任何其他层，即ViewModel中的数据结构定义只与页面相关，与entity无关。
    *   各司其职，例如，API 调用**只能**在 `Interactor` 中。

5.  **`IViewModel` 语义区分:**
    *   清晰区分 `IViewModel` 中 `state` (基础 UI 状态) 和 `computed` (派生 UI 状态) 的语义职责，有助于理解接口意图。

## 示例 (使用 VIPER-VueCS 结构)

*(以下为符合上述说明的计数器示例)*

### 1. index.vue

```vue:pages/counter/index.vue
<template>
  <!-- 示例使用 Tailwind CSS -->
  <div class="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
    <div class="mb-6 border-b pb-4">
      <h2 class="text-xl font-bold text-gray-800">VIPER-VueCS 计数器</h2>
    </div>

    <div class="space-y-6">
      <!-- 显示区域: **必须**使用 .value 访问 ComputedRef/Ref -->
      <div class="text-center">
        <div class="flex items-center justify-center gap-2 mb-2">
          <span class="text-2xl font-bold text-gray-700">{{ vm.computed.displayText.value }}</span>
          <span
            :class="{
              'text-green-600': vm.computed.isEven.value,
              'text-yellow-600': !vm.computed.isEven.value
            }"
            class="text-sm"
          >
            ({{ vm.computed.isEven.value ? '偶数' : '奇数' }})
          </span>
        </div>
      </div>

      <hr class="my-4">

      <!-- 名称输入: v-model 也需要 .value -->
      <div class="flex items-center gap-4">
        <label class="text-gray-600 shrink-0">名称：</label>
        <input
          v-model="vm.state.name.value" <!-- v-model 绑定, 必须 .value -->
          type="text"
          placeholder="输入计数器名称"
          :disabled="vm.state.isLoading.value" <!-- 属性绑定: 必须用 .value -->
          class="flex-1 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
        >
      </div>

      <hr class="my-4">

      <!-- 计数器操作: 模板插值和属性绑定都需要 .value -->
      <div class="flex items-center justify-center gap-4">
        <button
          @click="vm.actions.decrement"
          :disabled="vm.state.isLoading.value" <!-- 属性绑定: 必须用 .value -->
          class="w-12 h-12 rounded-full bg-red-500 text-white text-xl hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >-</button>

        <span class="text-2xl font-bold min-w-[40px] text-center">
          {{ vm.state.count.value }} <!-- 插值: 必须用 .value -->
        </span>

        <button
          @click="vm.actions.increment"
          :disabled="vm.state.isLoading.value" <!-- 属性绑定: 必须用 .value -->
          class="w-12 h-12 rounded-full bg-green-500 text-white text-xl hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >+</button>
      </div>

      <hr class="my-4">

      <!-- 控制按钮: 属性绑定和条件渲染都需要 .value -->
      <div class="flex flex-col items-center gap-4">
        <button
          @click="vm.actions.save"
          :disabled="vm.computed.saveButtonDisabled.value" <!-- 属性绑定: 必须用 .value -->
          class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <!-- 条件文本: 必须用 .value -->
          {{ vm.state.isLoading.value ? '保存中...' : '保存计数器' }}
        </button>

        <button
          @click="vm.actions.loadInitial"
          :disabled="vm.state.isLoading.value" <!-- 属性绑定: 必须用 .value -->
          class="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          重载初始数据
        </button>

        <!-- 条件渲染: 必须用 .value -->
        <div v-if="vm.state.isLoading.value" class="text-sm text-gray-500 italic">
          处理中...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCounterPresenter } from './presenter';
import type { ICounterViewModel } from './viewmodel'; // 可选，用于强类型 vm

// 获取 ViewModel 实例
const vm: ICounterViewModel = useCounterPresenter();

// View 层不允许有其他逻辑
</script>
```

### 2. viewmodel.ts

```typescript:pages/counter/viewmodel.ts
import type { ComputedRef, WritableComputedRef } from 'vue';

// 视图直接绑定的状态接口 (类型为响应式引用)
export interface ICounterState {
    readonly count: ComputedRef<number>;
    name: WritableComputedRef<string>; // v-model 目标
    readonly isLoading: ComputedRef<boolean>;
}

// 视图使用的计算属性接口
export interface ICounterComputed {
    readonly displayText: ComputedRef<string>;
    readonly isEven: ComputedRef<boolean>;
    readonly saveButtonDisabled: ComputedRef<boolean>;
}

// 视图可触发的操作接口
export interface ICounterActions {
    increment(): void;
    decrement(): void;
    updateName(name: string): void; // 由 name computed set 调用
    save(): Promise<void>;
    loadInitial(): Promise<void>;
}

// 组合的视图模型接口
export interface ICounterViewModel {
    state: ICounterState;
    computed: ICounterComputed;
    actions: ICounterActions;
}
```

### 3. state.ts

```typescript:pages/counter/state.ts
import { reactive, readonly } from 'vue';

// 定义权威页面状态的结构
export interface CounterPageState {
    count: number;
    userName: string;
    isLoading: boolean;
}

// 创建/提供页面状态实例的组合函数
export function useCounterPageState() {
    const state = reactive<CounterPageState>({
        count: 0,
        userName: '默认计数器',
        isLoading: false,
    });

    // --- 状态更新方法 (唯一入口) ---
    const increment = (): void => { state.count++; };
    const decrement = (): void => { state.count--; };
    const setName = (newName: string): void => {
        if (typeof newName === 'string') { // 简单校验
            state.userName = newName;
        }
    };
    const setLoading = (loading: boolean): void => { state.isLoading = loading; };
    const setInitialData = (initialData: { count: number; name: string }): void => {
        state.count = initialData.count;
        state.userName = initialData.name;
        state.isLoading = false;
    }

    return {
        state: readonly(state), // 只读状态暴露
        // 更新方法暴露
        increment,
        decrement,
        setName,
        setLoading,
        setInitialData,
    };
}

export type CounterPageStateAPI = ReturnType<typeof useCounterPageState>;
```

### 4. converter.ts

```typescript:pages/counter/converter.ts
import type { CounterPageState } from './state';

// 模拟实体结构 (或 DTO)
// 实际项目中应从 entity 或 api/types 导入
export interface CounterEntity {
    value: number;
    label: string;
    lastUpdated: string;
}

// 模拟从 API 获取的数据结构
export interface FetchedCounterData {
    initialCount: number;
    initialName: string;
}

// 模拟 State 更新所需的数据格式
export interface StateUpdateFormat {
    count: number;
    name: string;
}

/**
 * Converter负责不同层级/边界之间的数据结构转换。
 * 主要用于 State <-> Entity/DTO (外部) 之间。
 */
export class CounterConverter {
    /**
     * 将页面状态(State)转换为用于保存的实体(Entity)格式。
     * @param pageState - 只读的当前页面状态
     * @returns 转换后的实体对象
     */
    static toSaveData(pageState: Readonly<CounterPageState>): CounterEntity {
        console.log("Converter: State -> Entity for saving");
        return {
            value: pageState.count,
            label: pageState.userName,
            lastUpdated: new Date().toISOString(),
        };
    }

     /**
      * 将从Interactor获取的数据(FetchedData)转换为State层更新方法所需的格式(StateUpdateFormat)。
      * @param fetchedData - 从Interactor获取的数据
      * @returns 供State更新方法使用的格式化数据
      */
    static toStateFormat(fetchedData: FetchedCounterData): StateUpdateFormat {
        console.log("Converter: FetchedData -> StateUpdateFormat");
        return {
            count: fetchedData.initialCount,
            name: fetchedData.initialName,
        }
    }
}
```

### 5. interactor.ts

```typescript:pages/counter/interactor.ts
import type { CounterEntity, FetchedCounterData } from './converter'; // 使用 Converter 中定义的类型

// Mock API 调用 (应替换为真实 API 模块)
const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));
async function mockSaveCounterAPI(data: CounterEntity): Promise<{ success: boolean }> {
    console.log('API: Saving...', data); await delay(1000);
    const success: boolean = Math.random() > 0.2;
    if (!success) throw new Error('API Error: Save failed');
    console.log('API: Save successful'); return { success: true };
}
async function mockLoadCounterAPI(): Promise<FetchedCounterData> {
    console.log('API: Loading...'); await delay(800);
    console.log('API: Load successful');
    return { initialCount: 5, initialName: '已加载的计数器' };
}

/**
 * Interactor负责执行业务逻辑和数据访问 (如API调用)。
 * 它操作 Entity 或 DTOs。
 */
export class CounterInteractor {
    /**
     * 保存计数器数据 (调用API)。
     * @param data - 需要保存的计数器实体数据
     * @returns API调用的结果
     */
    async saveCounter(data: CounterEntity): Promise<{ success: boolean }> {
        try {
            // 调用真实的 API 函数
            return await mockSaveCounterAPI(data);
        } catch (error) {
            console.error('Interactor: Save failed during API call', error);
            throw error; // Re-throw 让 Presenter 处理
        }
    }

    /**
     * 加载计数器初始数据 (调用API)。
     * @returns 从API获取的原始数据
     */
    async loadCounter(): Promise<FetchedCounterData> {
         try {
            // 调用真实的 API 函数
            return await mockLoadCounterAPI();
        } catch (error) {
            console.error('Interactor: Load failed during API call', error);
            throw error; // Re-throw
        }
    }
}

// 工厂函数获取 Interactor 实例
export function useCounterInteractor(): CounterInteractor {
    return new CounterInteractor();
}
```

### 6. presenter.ts

```typescript:pages/counter/presenter.ts
import { computed, onMounted } from 'vue';
import type { ICounterViewModel, ICounterState, ICounterComputed, ICounterActions } from './viewmodel';
import { useCounterPageState, type CounterPageStateAPI } from './state';
import { useCounterInteractor } from './interactor';
import { CounterConverter, type CounterEntity, type StateUpdateFormat, type FetchedCounterData } from './converter'; // 导入所需类型

/**
 * Presenter是核心协调者，实现IViewModel接口。
 * 它从State层派生ViewModel，处理Actions，并调用其他层。
 */
export class CounterPresenter implements ICounterViewModel {
    private pageState: CounterPageStateAPI = useCounterPageState();
    private interactor: CounterInteractor = useCounterInteractor();

    // --- ICounterState 实现 (通过 computed 连接 State 层) ---
    public state: ICounterState = {
        count: computed(() => this.pageState.state.count),
        isLoading: computed(() => this.pageState.state.isLoading),
        // WritableComputedRef 用于 v-model
        name: computed({
            get: (): string => this.pageState.state.userName,
            // set 拦截 v-model 的写入，调用 action
            set: (value: string): void => { this.actions.updateName(value); },
        }),
    };

    // --- ICounterComputed 实现 (基于响应式 state 计算) ---
    public computed: ICounterComputed = {
        // 在 JS/TS 中访问 ref/computed 必须使用 .value
        displayText: computed((): string => `${this.state.name.value}: ${this.state.count.value}`),
        isEven: computed((): boolean => this.state.count.value % 2 === 0),
        saveButtonDisabled: computed((): boolean => this.state.isLoading.value),
    };

    // --- ICounterActions 实现 (业务流程编排) ---
    public actions: ICounterActions = {
        increment: (): void => { if (!this.state.isLoading.value) this.pageState.increment(); },
        decrement: (): void => { if (!this.state.isLoading.value) this.pageState.decrement(); },
        updateName: (name: string): void => {
            // Action 调用 State 的更新方法
            this.pageState.setName(name);
        },

        save: async (): Promise<void> => {
            if (this.state.isLoading.value) return; // 防止重复提交
            this.pageState.setLoading(true); // 开始加载状态
            try {
                // 准备数据: State -> Entity (通过 Converter)
                const dataToSave: CounterEntity = CounterConverter.toSaveData(this.pageState.state);
                // 执行业务: 调用 Interactor
                await this.interactor.saveCounter(dataToSave);
                console.log("Presenter: Save successful!");
            } catch (error) {
                console.error("Presenter: Save failed.", error);
                // 处理错误...
            } finally {
                this.pageState.setLoading(false); // 结束加载状态
            }
        },

        loadInitial: async (): Promise<void> => {
            if (this.state.isLoading.value) return;
            this.pageState.setLoading(true);
            try {
                // 获取数据: 调用 Interactor
                const fetchedData: FetchedCounterData = await this.interactor.loadCounter();
                // 准备数据: FetchedData -> StateUpdateFormat (通过 Converter)
                const initialStateData: StateUpdateFormat = CounterConverter.toStateFormat(fetchedData);
                // 更新状态: 调用 State 更新方法
                this.pageState.setInitialData(initialStateData);
                console.log("Presenter: Initial data loaded.");
            } catch(error) {
                console.error("Presenter: Load initial data failed.", error);
                // 处理错误...
            } finally {
                this.pageState.setLoading(false);
            }
        }
    };

    // --- 生命周期钩子 ---
    constructor() {
        onMounted(() => {
            console.log("Presenter: Mounted, loading initial data.");
            this.actions.loadInitial();
        });
    }
}

// Presenter 工厂函数
export function useCounterPresenter(): ICounterViewModel {
    return new CounterPresenter();
}
```


注：
1. state.ts中只存储entity相关的数据结构，以及entity中不存在的viewmodel中定义的字段，不要重复定义（viewmodel中的字段优先级低）
2. 采用读写分离模式处理viewmodel中state的读取与回写