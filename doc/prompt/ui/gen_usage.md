# 全量生成文档

你是一个专业的前端开发文档分析专家。请根据以下步骤分析组件README文档并生成精简的组件使用指南：

## 分析步骤

1. 从每个README中提取：
   - 组件的布局示意图
   - 基础用法代码
   - 不同使用场景的布局变体
   - 关键Props和Events

2. 对于每个布局模式：
   - 绘制ASCII布局图
   - 标注关键尺寸和样式信息
   - 说明适用场景

3. 合并相似布局模式：
   - 识别共同特征
   - 提取通用模式
   - 标注差异点

4. 生成组件使用指南，包含：
   - 布局模式总览
   - 场景匹配规则
   - 代码示例
   - 组件组合建议

## 输出格式

请以下面的结构输出组件使用指南：

````markdown
# THU 组件库使用指南

## 布局模式

### 1. [模式名称]
布局示意：
```
[ASCII布局图]
```
实现组件：`[组件名]`
```vue
[示例代码]
```
使用场景：
- [场景1]
- [场景2]

变体模式：
1. [变体1名称]
```
[变体1布局图]
```
2. [变体2名称]
```
[变体2布局图]
```

## 匹配规则
1. [规则1]
2. [规则2]

## 组件组合示例
### 1. [组合名称]
```
[组合布局图]
```
```vue
[组合代码示例]
```
````

## 处理规则

1. 布局模式提取：
   - 优先提取具有代表性的布局模式
   - 保留关键的样式信息
   - 突出组件的特色功能

2. 代码示例简化：
   - 保留必要的属性和事件
   - 使用简单的变量名
   - 添加关键注释

3. 场景匹配：
   - 基于布局特征匹配
   - 考虑组件的功能定位
   - 注意组件间的关系

4. 组合示例：
   - 选择常见的组合场景
   - 展示组件间的协作
   - 提供完整的实现参考

请分析提供的README文档，生成一份简洁但完整的组件使用指南。重点关注布局模式的展示和场景匹配规则的说明。

输入：[组件README文档内容]


# 增量添加组件使用说明 Prompt

你是一个专业的前端文档维护专家。请分析新组件的README文档，并按照现有文档的格式和风格，将新组件的使用说明整合到组件使用指南中。

## 分析步骤

1. 分析新组件README：
   - 识别组件类型（表单/布局/交互等）
   - 提取布局模式和变体
   - 总结使用场景
   - 提取关键示例代码

2. 定位合适的插入位置：
   - 匹配相似组件类型的章节
   - 确定是新建章节还是扩展现有章节
   - 保持文档结构的连贯性

3. 生成补充内容：
   - 遵循现有文档的格式规范
   - 保持一致的描述风格
   - 添加必要的关联说明

## 处理规则

1. 如果是新类型的组件：
   - 在合适位置创建新章节
   - 添加到目录索引
   - 补充相关的组合示例

2. 如果是现有类型的补充：
   - 扩展现有章节
   - 添加新的布局变体
   - 更新组合示例

3. 如果涉及组件关联：
   - 更新相关组件的说明
   - 添加组件间的关联说明
   - 补充新的组合场景

## 输出格式

请按以下格式输出增量更新内容：

````
# 组件使用指南补充说明

## 变更说明
1. [新增/更新] [具体变更内容]
2. [补充/调整] [具体调整内容]

## 新增/更新内容

### [组件类型]

#### [组件名称]
布局示意：
```
[ASCII布局图]
```

实现组件：`[组件名]`
```vue
[示例代码]
```

使用场景：
- [场景1]
- [场景2]

变体模式：(如果有)
1. [变体1]
...

## 组合示例补充
[如果需要补充新的组合场景]

## 文档插入位置
[指定具体的插入位置和方式]
````

## 注意事项

1. 保持格式一致性：
   - ASCII布局图风格统一
   - 代码示例格式统一
   - 描述语言风格统一

2. 维护文档结构：
   - 保持章节层级合理
   - 确保目录索引更新
   - 维护文档内部引用

3. 关注关联影响：
   - 检查是否影响现有示例
   - 更新相关组件说明
   - 补充必要的关联说明

请分析组件README，生成符合现有文档风格的补充内容：

输入：
1. 现有组件使用指南文档
2. 新组件的README文档

输出：
1. 补充内容
2. 插入位置说明
3. 相关更新建议