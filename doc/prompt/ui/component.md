thu-action-card是uniapp的自定义easycom组件，请使用thu-action-card组件，替换add_area页面中的网格区域样式，使用thu-action-card组件的默认UI样式，不要使用页面中的颜色、大小等样式。
仅改变UI样式，页面的业务逻辑不要改变，script中涉及UI样式的处理，请合理修改。
thu-action-card使用说明：1. 默认为showSetting为true，展示设置项。2. showSetting为true时，v-model绑定的不为空的字符串显示为蓝色，表示已设置、已选择等；若绑定的字符串为空，则显示灰色的placeholder字符串，表示未设置、未选择等；label为控件标题。



thu-action-card是uniapp的自定义easycom组件，请使用thu-action-card组件，替换room_type页面中的网格区域样式，使用thu-action-card组件的默认UI样式，不要使用页面中的颜色、大小等样式。
仅改变UI样式，页面的业务逻辑不要改变，script中涉及UI样式的处理，请合理修改。
thu-action-card使用说明：1. 默认为showSetting为true，展示设置项。2. showSetting为true时，v-model绑定的不为空的字符串显示为蓝色，表示已设置、已选择等；若绑定的字符串为空，则显示灰色的placeholder字符串，表示未设置、未选择等；title为控件标题。





thu-action-item是uniapp的自定义easycom组件，请使用thu-action-item组件(direction="col")，替换room_type页面中的网格区域样式，使用thu-action-item组件的默认UI样式，不要使用页面中的颜色、大小等样式。
仅改变UI样式，页面的业务逻辑不要改变，script中涉及UI样式的处理，请合理修改。
thu-action-item使用说明：1. 默认为showSetting为true，展示设置项。2. showSetting为true时，v-model绑定的不为空的字符串显示为蓝色，表示已设置、已选择等；若绑定的字符串为空，则显示灰色的placeholder字符串，表示未设置、未选择等；title为控件标题。



thu-action-item是uniapp的自定义easycom组件，请使用thu-action-item组件，替换页面中的表单设置栏样式，使用thu-action-item组件的默认UI样式，不要使用页面中的颜色、大小等样式。
仅改变UI样式，页面的业务逻辑不要改变，script中涉及UI样式的处理，请合理修改。
thu-action-item使用说明：1. 默认为showSetting为true，展示设置项。2. showSetting为true时，v-model绑定的不为空的字符串显示为蓝色，表示已设置、已选择等；若绑定的字符串为空，则显示灰色的placeholder字符串，表示未设置、未选择等；title为控件标题。



# 1. thu-action-item
### thu-action-item 组件说明

#### 组件用途
这是一个通用的可点击操作项组件，通常用于：
- 表单中的可点击跳转项
- 设置页面中的选项项目
- 需要跳转或触发操作的列表项

#### Props 属性
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| title | string | - | 标题文本（必填） |
| modelValue | string | '' | 显示的值 |
| showSetting | boolean | true | 是否显示设置值 |
| placeholder | string | '未设置' | 未设置时的占位文本 |
| settingColor | string | 'blue-checked' | 设置值的颜色 |
| height | string\|number | '' | 组件高度 |
| direction | 'row'\|'col' | 'row' | 布局方向 |
| required | boolean | false | 是否必填 |

#### Events 事件
- `click`: 点击项目时触发
- `update:modelValue`: 更新值时触发

#### 样式特点
1. 默认为白色背景、圆角的卡片式布局
2. 支持横向（row）和纵向（col）两种布局方式
3. 右侧带有箭头图标，表示可点击
4. 可以显示必填标记

#### 常见使用场景
```vue
<!-- 设置页面选项 -->
<thu-action-item
  title="个人信息"
  v-model="userInfo"
  @click="navigateToUserInfo"
/>

<!-- 表单跳转项 -->
<thu-action-item
  title="选择部门"
  v-model="department"
  required
  @click="chooseDepartment"
/>

<!-- 纵向布局示例 -->
<thu-action-item
  title="备注"
  direction="col"
  v-model="remark"
  @click="editRemark"
/>
```

这个组件的设计风格类似于微信小程序或移动端 App 中常见的设置项、表单项样式，适合用于：
1. 设置页面的选项列表
2. 表单中需要跳转选择的项目
3. 个人中心的菜单项
4. 需要跳转或触发操作的列表项


请分析页面中适合用thu-action-item替换的部分代码，按照说明使用thu-action-item组件替换。






# VIPER-VM 架构 (VIPER with Vue and Model Management)

## 核心层级和职责

1. **View层** (*.vue)
   - Template: UI结构
   - ComponentLogic: 组件逻辑
   - 依赖: @Presenter, @UIForms

2. **UIForms** (form.ts)
   - UI表单数据模型
   - 每个View对应一个UIForms文件

3. **Presenter层** (presenter.ts)
   - 状态管理和事件处理
   - 数据管理和路由管理
   - 依赖: @ViewModelConverter, @Interactor, @Router, @Store

4. **ViewModelConverter** (converter.ts)
   - 视图模型转换逻辑
   - 依赖: @Entity, @View.UIForms

5. **Interactor** (interactor.ts)
   - 业务用例和业务逻辑
   - 依赖: @DataManager, @Entity.DataModels

6. **DataManager** (*DataManager.ts)
   - API服务和本地存储
   - 缓存策略
   - 依赖: @Store.PersistenceManager, @Entity.DataModels

7. **Router** (router/*.ts)
   - 导航逻辑和组件切换
   - 依赖: @Store

8. **Entity** (entity/*.ts)
   - 数据模型和模型关系定义

9. **Store** (store/*.ts)
   - 全局状态管理(pinia)
   - 状态持久化管理

## 核心数据流向

1. **UI交互流**
   - View <-> Presenter: UI事件和状态更新

2. **数据处理流**
   - Presenter <-> ViewModelConverter: 数据转换
   - Presenter <-> Interactor: 业务操作
   - Interactor <-> DataManager: 数据操作

3. **状态管理流**
   - DataManager <-> Store: 状态同步
   - DataManager <-> Entity: 数据模型交互

## 目录结构核心

```
src/
├── pages/                              # 业务页面模块
│   └── [business_module]/             
│       ├── [page_name]/               
│       │   ├── index.vue              # View
│       │   ├── form.ts                # UIForms
│       │   ├── presenter.ts           # Presenter
│       │   ├── converter.ts           # ViewModelConverter
│       │   └── interactor.ts          # Interactor
│       ├── api/                       # 模块API
│       ├── entity/                    # 模块实体
│       └── store/                     # 模块状态
├── data/                              # 数据管理
├── entity/                            # 通用实体
├── store/                             # 全局状态
└── router/                            # 路由管理
```



请使用思维链的方式对比代码和storybook说明文档，一切以代码为主，不要新增代码中没有的部分，也不要删减代码中存在的部分，分析storybook说明文档中是否存在错误的部分，修正文档。



目标：更新storybook文档
请使用思维链的方式分析代码和storybook说明文档，一切以代码为主，不要新增代码中没有的部分，也不要删减代码中存在的部分，分析代码功能，更新storybook文档。


目标：编写storybook文档
请使用思维链的方式分析代码来编写storybook说明文档，一切以代码为主，不要新增代码中没有的部分，也不要删减代码中存在的部分，分析代码功能，更新storybook文档。
注：请按照样例storybook的结构编写文档，不要省略结构。


代码变更，更新文档
目标：更新storybook文档和说明文档
我的代码发生了变更，请使用思维链的方式分析代码、storybook文档和说明文档，一切以代码为准，不要新增代码中没有的部分，也不要删减代码中存在的部分，分析代码功能，更新我的storybook和说明文档，注意更改版本号，今天日期为11-01。


请更新组件依赖，以代码为准，列出组件依赖，组件不要多也不要少，自定义组件请在布局示意图用emoji符号表示，更新布局示意图