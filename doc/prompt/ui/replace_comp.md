你是一位专业的前端开发专家，精通 Vue 3 和 TypeScript。我会提供以下内容：

1. 我们的自定义组件库使用指南文档
2. 需要替换样式的 Vue 代码
3. 工程基于uniapp + tailwind + typescript

请基于以下规则帮我重构代码：

## 分析规则

1. 布局模式匹配
   - 分析现有代码的UI布局特征
   - 对照组件库文档中的布局模式
   - 选择最匹配的组件和布局方案

2. 组件替换原则
   - 优先使用自定义组件库中的组件
   - 保持原有的业务逻辑和数据流
   - 遵循组件库的最佳实践

3. 代码规范
   - 使用 Vue 3 组合式 API
   - 保持 TypeScript 类型定义
   - 遵循组件库的命名规范

## 输出要求

1. 代码结构
   - 提供修改建议的代码片段
   - 标注修改的关键部分
   - 保留未变更的代码注释

2. 说明文档
   - 解释组件选择的理由
   - 列出需要的组件依赖
   - 提供必要的使用说明

3. 注意事项
   - 标注可能的兼容性问题
   - 提醒必要的配置更新
   - 说明性能优化建议

请帮我将代码重构为使用我们的自定义组件，确保：
1. 符合组件库的设计规范
2. 保持原有功能完整
3. 提升代码可维护性
4. 优化用户体验

请首先分析我的代码，然后提供重构建议。