# Prompt2

目标:基于VIPER-VM架构，生成页面的converter.ts
## 第一步：字段映射分析
1. 列出所有相关的数据结构
   ```typescript
   // View State 结构
   interface ViewState {
     field1: type1;
     field2: type2;
     ...
   }
   
   // Entity 结构
   interface Entity {
     field1: type1;
     field3: type3;
     ...
   }
   ```

2. 创建字段映射表
   ```
   直接映射字段：
   - ViewState.field1 <-> Entity.field1 (完全相同的字段)
   
   需转换字段：
   - ViewState.field2 -> Entity.field3 (需要转换的字段)
   
   特殊处理字段：
   - ViewState独有字段：处理方式
   - Entity独有字段：处理方式
   ```

## 第二步：转换方法设计
1. 为每个转换方向定义方法签名
   ```typescript
   // Entity -> View
   static toViewState(entity: Entity): ViewState
   
   // View -> Entity
   static toEntity(state: ViewState): Entity
   ```

2. 确定默认值策略
   ```
   必填字段：明确的默认值
   可选字段：undefined 或 null
   数组字段：空数组
   对象字段：空对象或默认结构
   ```

## 第三步：实现检查清单
- [ ] 是否处理了所有字段？
- [ ] 是否提供了所有必要的默认值？
- [ ] 是否处理了 null/undefined 情况？
- [ ] 是否保持了类型安全？
- [ ] 是否添加了必要的数据验证？

## 第四步：代码生成要求
1. 必须包含完整的类型导入
2. 每个转换方法必须处理所有输出字段
3. 提供清晰的错误处理
4. 添加必要的类型注释
5. 包含字段映射说明的注释

## 输出格式
```typescript
import type { ... } from '...';

/**
 * 数据转换器
 * 负责 Entity 与 View State 之间的转换
 */
export class Converter {
  /**
   * 字段映射说明：
   * - field1: 直接映射
   * - field2: 特殊转换，处理逻辑...
   */
  
  // 转换方法实现
  static toViewState(entity: Entity): ViewState {
    // 完整的字段处理
  }
  
  static toEntity(state: ViewState): Entity {
    // 完整的字段处理
  }
}
```

这个改进后的 prompt：
1. 强调了完整的字段映射分析
2. 避免臆测字段用途
3. 要求明确的字段映射关系
4. 强调所有字段都需要处理
5. 提供了清晰的检查清单
6. 包含了代码格式要求

这样可以帮助生成更准确和完整的 converter 实现。您觉得这个改进的 prompt 如何？还需要补充什么吗？




# prompt1
请帮我分析并实现 converter.ts。我会提供以下信息：

1. 相关的实体（Entity）定义文件，包含：
   - 实体接口定义
   - 实体之间的关系
   - 字段类型和含义

2. ViewModel 定义文件，包含：
   - UI 状态接口定义
   - 每个状态字段的用途
   - 状态字段的类型定义

3. API 接口定义文件，包含：
   - API 返回的数据结构
   - 请求参数的数据结构
   - 可能的数据转换需求

4. 当前的业务场景和数据流转需求：
   - 哪些实体需要转换为 UI 状态
   - 哪些 UI 状态需要转换回实体
   - 是否需要处理特殊的数据格式（如 JSON 字符串）
   - 是否需要处理默认值或初始状态

请按照以下步骤分析并实现 converter：

1. 数据流分析
   - 列出所有需要转换的数据类型
   - 确定转换的方向（entity -> state 或 state -> entity）
   - 识别特殊的数据处理需求

2. 转换方法设计
   - 为每种转换场景设计转换方法
   - 处理数据类型转换和默认值
   - 确保类型安全

3. 工具方法补充
   - 添加必要的工具方法
   - 处理数据格式化
   - 处理特殊场景

4. 类型检查和验证
   - 确保所有转换方法都有正确的类型定义
   - 添加必要的类型断言
   - 处理可能的空值或未定义情况

请生成完整的 converter.ts 实现，包括：
- 必要的类型导入
- 转换方法实现
- 工具方法实现
- 类型定义和注释