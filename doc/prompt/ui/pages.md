# H5项目AI开发实践

## 1. 接口参数
### 1. 入参
    参数名称，参数类型，参数说明：功能说明、参数范围、options说明(参考product.js、producttime.js)等
### 2. 出参
    参数名称，参数类型，参数说明：功能说明、参数范围、options说明等

## 2. 页面关联
### 1. 上级页面
#### 1. 页面路由
#### 2. 传递的参数、传递参数的方式
### 2. 下级页面
#### 1. 页面路由
#### 2. 需要传递的参数、传递参数的方式
### 3. 当前页面
#### 1. 页面路由
#### 2. 当前页面交互分析（可点击区域识别）
#### 3. 数据来源分析
##### 1. 是否为页面初始加载数据
##### 2. 是否为上一页面传递的数据
##### 3. 是否为硬编码数据
##### 4. 是否为用户输入/选择数据
##### 5. 是否为下一页面返回的数据
##### 6. 是否为API接口请求获取的数据
##### 7. 是否为本地存储（如localStorage、sessionStorage）中的数据
##### 8. 是否为全局状态管理（如Redux、Vuex）中的数据
##### 9. 是否为WebSocket实时推送的数据
##### 10. 是否为离线缓存（如PWA、Service Worker）中的数据
##### 11. 是否为第三方SDK或插件提供的数据
##### 12. 是否为计算或处理后的派生数据

## 3. 接口分析
### 1. 关联的接口文件名称分析
### 2. 关联的接口分析





## MVVM模式思考
### 角色功能和责任
1. vue文件仅作为view层，负责处理view的生命周期
2. model层封装获取数据的方法，如api、store等
3. ViewModel层的责任
- 管理model和ui数据的转换
- 数据绑定，主要是ui数据与view的绑定
- 响应view的用户交互：事件处理
- 状态管理
### 








# AI开发提示
你是一位资深的前端开发工程师，精通Vue3和TypeScript。我正在使用VIPER-VM架构开发一个项目，这是一个基于Vue3的改良VIPER架构。工程技术栈为uni-app + tailwind + typescript + pinia。
## 架构概述
VIPER-VM (VIPER with Vue and Model Management) 是一个基于VIPER架构模式，专为Vue3应用设计的现代化前端架构。该架构整合了Vue3的组合式API、响应式系统以及完善的模型管理机制，旨在提供一个清晰、可维护且高度可测试的应用架构方案。
## 目录结构
src/
├── pages/                         # 业务页面模块
│   └── [business_module1]/        # 业务模块
│       └── [page_name]/           # 页面功能
│           ├── index.vue          # View层
│           ├── form.ts            # UI表单模型
│           ├── presenter.ts       # Presenter层
│           ├── converter.ts       # ViewModel转换层
│           └── interactor.ts      # 业务交互层
├── components/                    # easycom共享组件
├── api                            # api接口
│   └── [business_module]/         # 业务模块
│       ├── api1.ts                # 模块api接口1
│       ├── api2.ts                # 模块api接口2
│       └── index.ts               # 统一导出
├── data/                          # 数据管理模块
│   │── biz1DataManager.ts
│   └── biz2DataManager.ts
├── entity/                        # 实体模型
│   │── [business_module1]/        # 业务模块
│   │   ├── model1.ts              # 模块内实体1
│   │   ├── model2.ts              # 模块内实体2
│   │   └── index.ts               # 统一导出
│   └── common/                    # 通用类型
│       ├── model1.ts              # 通用实体1
│       ├── model2.ts              # 通用实体2
│       └── index.ts               # 统一导出
├── store/                         # 全局状态
│   ├── [business_module1]/        # 状态模块1
│   │   ├── store1.ts
│   │   ├── store2.ts
│   │   └── index.ts               # 统一导出
│   └── index.ts                   # 统一导出
├── utils/                         # 工具集
│   ├── utils.ts                   # 工具
│   └── navigation.ts              # 导航封装
└── persistence/                   # 持久化配置
## 项目架构说明
```
View（视图层，一个文件）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @UIForms (UI表单模型, 单独文件)

UIForms (UI表单模型, 一个View对应一个文件)

Presenter（主持层，一个文件）
    - StateManager (状态管理)
        - reactive data（响应式数据）
        - computed properties（计算属性）
    - EventHandlers (事件处理)
    - DataManagement（数据管理）
        - @ViewModelConverter
        - @Interactor
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (视图-模型转换层，一个文件)
    - ConversionLogic(转换逻辑)
    - @Entity
    - @View.UIForms

Interactor（业务交互层，一个文件）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层，一个文件）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（路由层）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)

Store (全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```
项目采用VIPER-VM (VIPER with Vue and Model Management)架构，这是一个基于VIPER模式的Vue3应用架构。每个业务页面都遵循以下结构：
1. View层 (index.vue)：
   - 纯展示层，只负责UI渲染和用户交互
   - 使用组合式API
   - 通过presenter管理所有业务逻辑
2. UI表单模型 (form.ts)：
   - 定义页面所需的表单数据结构
   - 用于数据绑定和验证
3. Presenter层 (presenter.ts)：
   - 使用Vue3 Composable形式
   - 管理组件状态和用户交互
   - 协调视图和业务逻辑
   - 负责管理数据转换
4. ViewModel转换层 (converter.ts)：
   - 处理视图模型和数据模型的转换
   - 确保数据格式的一致性
5. 业务交互层 (interactor.ts)：
   - 实现具体业务逻辑
   - 与DataManager交互
## 开发要求
1. 严格遵循VIPER-VM的分层架构
2. 使用TypeScript进行开发
3. 遵循uniapp最佳实践
4. 保持各层职责单一，避免跨层调用
## 输出要求
1. 按VIPER-VM架构设计解决方案
2. 生成相应的代码文件
3. 提供必要的说明和注释
4. 标明代码文件的相对路径和文件名称
5. View层代码请输出完整的代码，不要修改原页面的样式，保留原页面所有的元素。

请记住这个架构说明，后续我们的对话都将基于这个架构进行开发。







请按照VIPER-VM架构，仿照架构3.md的示例，将src/pages/room_management/add_buyout.vue页面拆成src/pages/room_management/add_buyout/index.vue和src/pages/room_management/add_buyout/viewmodel.ts，并编写完整的presenter，保持页面UI结构完整功能，不要修改和删除UI内容，给出presenter.ts、index.vue和viewmodel.ts的完整代码。
注：viewmodel.ts的定义仅与index.vue的模板中的UI数据或绑定事件相关，与任何其他model都没有关系，定义viewmodel时，请分析UI中完整的数据和action，列出即可，不要考虑其他。


请按照VIPER-VM架构，从src/pages/room_management/add_buyout.vue页面中抽离出interactor.ts。
注：Interactor只与DataManager和Entity层相关，Presenter是Interactor的上一层，隔离了View层。






请按照VIPER-VM架构，分析index.vue，更新viewmodel.ts的代码，
注：viewmodel.ts的定义仅与index.vue的模板中的UI数据或绑定事件相关，与任何其他model都没有关系，定义viewmodel时，请分析UI中完整的数据和action，列出即可，不要考虑其他。




请按照VIPER-VM架构，分析index.vue、viewmodel.ts和interactor.ts的代码，更新presenter.ts的代码。