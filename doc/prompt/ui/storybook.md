# Storybook 组件文档生成指南

## 文件结构
生成以下文件：
1. 组件名.stories.ts
2. 组件说明文档（嵌入在 stories 文件的 parameters.docs.description.component 中）

## Stories 文件结构
```typescript
// Meta 配置
- title: 'Components/组件名'
- component: 导入的组件
- tags: ['autodocs']
- parameters: 
  - docs.description.component: 组件说明文档
- argTypes: 参数类型定义及控制器配置

// Story 示例
- Basic: 基础用法
- 其他典型用例（2-3个）
```

### 格式示例
```typescript
const meta = {
  title: 'Components/ThuActionItem',
  component: ThuActionItem,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
            组件说明文档
        `,
      },
    },
  },
  argTypes: {
  },
} satisfies Meta<typeof ThuActionItem>
```

## 组件说明文档模板
### 文档格式
---
\`\`\`markdown
## 📋 组件信息
名称、版本、作者、时间

## 📚 技术栈
\`\`\`yaml
框架:
  - Vue 3
  - TypeScript
  - uni-app
UI框架:
\`\`\`

## 🎯 组件目标
简要描述组件的主要用途

## 💡 关键特性
\`\`\`yaml
布局:
功能:
\`\`\`

## 📝 代码示例
### 基础用法
### 完整示例

## 📐 布局示意图
ASCII 格式的布局图
组件结构说明

## 📚 API 文档
### Props 表格
| 属性 | 类型 | 默认值 | 必填 | 说明 | 示例值 | 备注 |

### Events 表格
| 事件名 | 参数 | 说明 |

## 🔍 实现细节
\`\`\`yaml
依赖组件:
样式依赖:
\`\`\`

## ⚠️ 注意事项
关键注意点列表

## 📈 更新日志
版本历史记录
\`\`\`

---

### 文档编写要求
#### 编写原则
1. 代码一致性
   - 所有API必须与代码实现匹配
   - 示例代码必须可运行
   - 不添加未实现的功能

2. 格式规范
   - 使用指定emoji标记标题
   - 代码块标注语言
   - 表格保持统一格式
   - 使用ASCII字符绘制布局图

3. 内容完整性
   - 必须包含所有必要章节
   - 信息准确且简洁
   - 示例完整且典型

#### 输出要求
- 使用Markdown格式
- 保持统一的标题层级
- 代码块正确标注语言
- 表格对齐且格式统一

#### 验证清单
- [ ] 所有Props与代码一致
- [ ] 所有Events与代码一致
- [ ] 示例代码可运行
- [ ] 布局图反映实际结构
- [ ] 文档结构完整