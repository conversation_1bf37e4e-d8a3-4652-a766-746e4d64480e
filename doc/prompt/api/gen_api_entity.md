我需要从Swagger JSON生成TypeScript代码。请帮我完成以下任务：

1. 生成实体接口定义(entity文件)：
- 分析definitions中的数据模型
- 提取请求DTO和响应VO的字段定义
- 合并相似的接口定义
- 去除重复的字段
- 添加中文注释
- 输出到entity/xxx.ts文件

2. 生成API接口定义(api文件)：
- 分析paths中的接口定义
- 根据HTTP方法和路径生成对应的方法
- 使用entity中定义的类型
- 添加JSDoc格式的注释，包含:
  - 方法说明
  - 参数说明
  - 返回值说明
- 统一错误处理
- 输出到api/xxx.ts文件

要求：
1. 代码简洁清晰
2. 类型定义准确
3. 注释完整规范
4. 遵循TypeScript最佳实践
5. 保持一致的代码风格
6. 所有的类型定义（api函数的入参和返回值）都要在entity中定义，不允许api文件中存在自定义或拼接的类型

请首先生成entity文件，然后基于entity的类型定义生成api文件。

源json文件：xx.json