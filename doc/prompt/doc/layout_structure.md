# 布局结构表达规则 (Layout Structure Expression Rules)

## 基本语法
- 使用类CSS嵌套语法
- 每个节点使用大括号 `{}` 包裹
- 属性使用 `属性名: 值;` 格式
- 必须以 ROOT 作为顶层容器

## 命名规则
1. 容器命名：
   - 顶层容器：必须为 ROOT
   - 子容器：
     - 一级子容器：单个大写字母（A、B、C）
     - 深层子容器：父容器名 + 大写字母（AA、AB、AAA、AAAA等）
     - 非叶子节点只能由大写字母组成，不允许包含数字
     - 支持无限层级嵌套

2. 元素命名（叶子节点）：
   - 必须以数字结尾
   - 格式：容器名 + 数字（可以是任意正整数）
   - 示例：A1、AA1、AAA1、B、BA1、BBA2等
   - 同一容器内的数字建议连续，但不强制

## 字母定义顺序
1. 容器字母顺序：
   - 同级容器按字母顺序依次命名（A、B、C...）
   - 不允许跳过字母，必须按顺序使用
   - 示例：有三个同级容器，必须命名为A、B、C，不能是B、D、E

2. 子容器字母顺序：
   - 每个父容器下的子容器从A开始命名
   - 示例：B容器下可以有BA、BB、BC，不能是BA、BC、BD
   - 多层级也遵循相同规则：BAA、BAB、BAC，不能是BAB、BAC、BAD

## 属性规则
1. 容器可用属性：
   - display: vertical | horizontal | group
   - background: <color> （透明颜色不要写）
   - position: fixed | relative | absolute
   - scroll: none | vertical | horizontal

2. 元素可用属性：
   - type: text | label | input | button | icon | value | datepicker
   - content: <string>
   - placeholder: <string>
   - value: <string>

3. display 说明：
   - vertical: 垂直排列子元素
   - horizontal: 水平排列子元素
   - grid: 网格排列
   - group: 逻辑分组容器，继承父容器的排列方向，仅用于逻辑归类，无实际布局影响

4. scroll 说明：
   - none: 禁用滚动（默认值）
   - vertical: 允许垂直方向滚动
   - horizontal: 允许水平方向滚动

## 缩进规则
- 每层缩进两个空格
- 属性与父节点对齐
- 子节点比父节点多缩进两格

## 示例
```scss
ROOT {
  display: vertical;

  1 { //叶子节点是ROOT的子节点，布局中直接使用数字，不需要容器
    type: label; content:"标题";
  }
  
  A {
    display: vertical;
    
    AA {
      display: horizontal;
      AA1 { type: label; content: "用户名"; }
      AA2 { type: input; placeholder: "请输入"; }
    }
    
    AB {
      display: horizontal;
      AB1 { type: label; content: "密码"; }
      AB2 { type: input; placeholder: "请输入"; }
      AB3 { type: icon; content: "eye"; }
    }
  }
  
  B {
    display: vertical;
    background: #F5F5F5;
    
    BA {
      display: horizontal;
      BA1 { type: button; content: "取消"; }
      BA2 { type: button; content: "确定"; }
    }
    B1 { type:text; content: "test1"; }
    BB {
      display: horizontal;
      BB1 { type: text; content: "test2"; }
      BB2 { type: text; content: "test3"; }
      BBA { 
        display: vertical;
        BBA1 { type: text; content: "test4"; }
        BBA2 { type: text; content: "test5"; }
      }
      BBB { 
        display: vertical;
        BBB1 { type: text; content: "test6"; }
        BBB2 { type: text; content: "test7"; }
      }
    }
  }

  C {
    display: grid;
    grid-col: 2;

    C1 { type:text; content:"grid1"; }
    C2 { type:text; content:"grid2"; }
  }

  2 {
    type: text; content: "这是底部";
  }
}
```

## 命名建议
1. 建议：
   - 元素编号尽量连续，便于阅读和维护
   
2. 命名策略：
   - 按功能区域划分一级容器（A、B、C...）
   - 按子模块划分次级容器（AA、AB、BA、BAA、BABB...）
   - 元素编号反映同级顺序（1、2、3...）

## 命名规则补充说明
1. 叶子节点命名强制要求：
   - 必须使用直接父容器的名称作为前缀
   - 示例：AA 容器下的叶子节点必须是 AA1、AA2 等
   - 不允许使用其他容器字母作为前缀
   - 如果是 ROOT 的直接子节点，直接使用数字编号，不需要任何前缀

2. 错误示范：
   - AA { A1, A2 }  ❌ 错误：A1 不属于 AA 的命名空间
   - A { B1, B2 } ❌ 错误：子节点应该为AA1、AA2，除了ROOT节点的子节点，其他节点的前缀必须为父容器名称，不能用其他容器字母开头
   - AA { AA1, AA2 } ✅ 正确：AA1 正确使用了父容器前缀
   - ROOT { A { A1 } } ❌ 错误：不应该为单个叶子节点创建容器
   - ROOT { 1 } ✅ 正确：直接使用数字作为 ROOT 的叶子节点

3. 单一叶子节点简化规则：
   - 当一个元素不需要特殊布局时，应当作为父容器的直接叶子节点
   - 适用于所有类型的单一元素（text/label/image/button等）
   - 示例：
     ```scss
     // ❌ 错误：不必要的容器嵌套
     ROOT {
      display: horizontal;
      A {
        display: group;
        A1 { type: image; content: "图片"; }
      }
     }
     
     // ✅ 正确：直接使用 ROOT 的叶子节点
     ROOT {
      display: horizontal;
      1 { type: image; content: "图片"; }
     }


     // ❌ 错误：不必要的容器嵌套
     ROOT {
      display: vertical;
      A {
        display: vertical;
        A1 { type: text; content: "标题"; }
        AA {
          display: group;
          AA1 { type: text; content: "描述"; }
        }
      }
     }
     
     // ✅ 正确：直接使用父容器的叶子节点
     ROOT {
      display: vertical;
      A {
        display: vertical;
        A1 { type: text; content: "标题"; }
        A2 { type: text; content: "描述"; }
      }
      1 {
        type: text; content: "text";
      }
     }
     ```
   - 此规则的目的是：
     1. 减少不必要的嵌套层级
     2. 使结构更清晰
     3. 提高代码可维护性
   - 只有当元素需要特定布局或分组时，才使用容器包装

## 定位说明
1. 默认定位：
   - 所有容器默认为 position: relative
   - 嵌套元素按照文档流进行排列

2. 固定定位：
   - position: fixed 用于固定在视口的特定位置
   - 常用于页面底部工具栏、顶部导航栏等

3. 绝对定位：
   - position: absolute 用于相对最近的非 static 定位祖先元素定位
   - 常用于弹出层、悬浮元素等

## 特别说明
**若子元素的display的方向与父元素的一致，且子元素没有background样式信息，请务必使用group标识，不要使用vertical或者horizontal**

### 错误示例
```scss
ROOT { 
  display: vertical;
  A { display: vertical; }
}
```
```scss
ROOT { 
  display: vertical;
  background: #F5F5F5;
  A { display: vertical; }
}
```

```scss
A { 
  display: horizontal;
  AA { display: horizontal; }
}
```

### 正确示例
```
ROOT {
  display:vertical;
  A { 
    display: group;
    AA: { display: group; }
    AB: { display: group; }
    AC: { display: horizontal; }
    AD: { display: vertical; background:#fff;}
  }
  B { display: horizontal;}
  C { display: group;}
  D { display: vertical; position: fixed;}
}
```

## 动态条件语法
1. visible 属性：
   - `visible: when(条件表达式);`
   - 支持的条件操作符：selected, unselected, checked, unchecked
   - 支持的逻辑运算符：&&（与）, ||（或）, !（非）
   
2. 引用语法：
   - 使用 # 加元素ID引用其他元素：#BD2
   - 支持多个条件组合：#BD2 && !#BD1

## 示例
```scss
ROOT {
  display: vertical;
  
  B {
    display: group;
    
    BD {
      display: horizontal;
      background: white;
      BD1 { type: button; content: "全开启"; }
      BD2 { type: button; content: "自定义"; }
      BD3 { type: button; content: "全关闭"; }
    }
    
    BE {
      display: horizontal;
      visible: when(#BD2.selected && !#BD1.selected && !#BD3.selected);
      background: white;
      BEA {
        display: group;
        BEA1 { type: text; content: "收银机代订人"; }
        BEA2 { type: text; content: "收银机上可选为代订人"; }
      }
      BE1 { type: checkbox; }
    }
  }
}
```

## 条件语法说明
1. 元素状态：
   - `selected`: 按钮被选中
   - `unselected`: 按钮未选中
   - `checked`: 复选框被勾选
   - `unchecked`: 复选框未勾选

2. 复杂条件示例：
```scss
visible: when(#BD2.selected && (#BE2.checked || #BF2.checked));
visible: when(!#BD1.selected && !#BD3.selected);
visible: when(#BA1.checked || (#BA2.checked && #BB1.selected));
```

3. 嵌套条件：
```scss
A {
  visible: when(#B1.selected);
  AA {
    visible: when(#B2.checked);
    AA1 { type: text; content: "嵌套条件示例"; }
  }
}
```

## 三元运算符语法
1. 基本语法：
   - 格式：`条件 ? 值1 : 值2`
   - 条件为真时返回值1，为假时返回值2
   - 可用于任何支持动态值的属性

2. 条件支持：
   - 元素状态：checked, unchecked, selected, unselected
   - 引用语法：使用#前缀引用其他元素
   - 逻辑运算：支持 && (与)、|| (或)、! (非)

3. 使用示例：
```scss
ROOT {
  display: horizontal;
  background: #A2.checked ? #5956FF : #FFFFFF;
  
  A {
    display: horizontal;
    A1 { 
      type: text;
      content: #B1.selected ? "已选中" : "未选中";
    }
    A2 { 
      type: checkbox;
      content: #A2.checked ? "✓" : "";
    }
  }
}
```

4. 复杂条件示例：
```scss
content: (#A1.checked && !#A2.checked) ? "部分选中" : "其他状态";
background: (#B1.selected || #B2.selected) ? #FF0000 : #CCCCCC;
visible: !#C1.checked ? true : false;
```

5. 注意事项：
   - 条件表达式必须返回布尔值
   - 值1和值2的类型必须一致
   - 不支持嵌套的三元运算符
   - 建议复杂条件使用括号提高可读性