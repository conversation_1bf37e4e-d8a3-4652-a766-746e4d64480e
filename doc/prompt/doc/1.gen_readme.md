请根据设计稿、需求文档或html静态代码，生成一个组件README文档。
重点是要绘制一个准确的布局示意图和布局结构示意图，并使用 `layout_structure.md` 中定义的布局结构表达规则来描述组件的布局。

## 分析步骤
1. 分析设计稿中的UI元素
   - 识别所有表单项和控件
   - 提取标签文字和占位提示
   - 确定必填项标记
   - 识别交互提示样式
   - 分析特殊布局和样式

2. 分析表单结构
   - 提取完整表单项列表
   - 确定必填项
   - 识别交互模式
   - 确定布局规则
   - 识别布局变体

3. 绘制布局示意图
   - 完整展示所有UI元素
   - 保持与设计稿一致的样式
   - 不省略任何表单项
   - 根据实际布局选择合适的表示方法

4. **使用 `layout_structure.md` 规则描述布局结构**
   - 根据 `layout_structure.md` 的语法，使用类 CSS 嵌套语法描述组件的布局结构
   - 确保每个节点使用大括号 `{}` 包裹
   - 属性使用 `属性名: 值;` 格式
   - 必须以 `ROOT` 作为顶层容器
   - 遵循 `layout_structure.md` 中定义的命名规则、字母定义顺序、属性规则和缩进规则
   - 使用 `type` 和 `content` 属性描述元素类型和内容
   - 使用 `display` 属性描述容器的排列方式
   - 使用 `position` 属性描述定位方式
   - 使用 `visible` 属性描述动态显示条件
   - 使用三元运算符描述动态属性

## 文档结构
请包含以下章节：
1. 组件信息（名称、版本、时间）
2. 技术栈
3. 组件目标
4. 关键特性
5. 组件结构与布局（最重要）
6. 目录结构
7. 更新日志

## 布局示意图规范
### 布局规则
1. 框线使用ASCII字符：┌ ┐└ ┘─ │
2. 注意页面的统一缩进和对齐样式
3. 必填项使用星号(*)标记
4. 布局根据实际设计灵活调整表示方式
5. 保持一致的视觉表达方式

### 注意事项
1. 表单项标题位置分两种：
   A. 标题在上方：
      - 普通输入框和选择框
      - 标题与输入框之间留适当间距
   
   B. 标题在内部：
      - 特殊控件（如选项卡）
      - 标题靠左对齐

2. 示例：
```
# 标题在上方的表单项
│ 手机号码                              │
│ ┌────────────────────────────────┐   │
│ │ 请输入                          │   │
│ └────────────────────────────────┘   │

# 标题在内部的表单项
│ ┌────────────────────────────────┐   │
│ │ 员工类型            工号添加  >   │   │
│ └────────────────────────────────┘   │
```

3. 布局注意事项：
   - 严格按照设计稿区分标题位置
   - 保持一致的间距和对齐
   - 必填项标记(*)紧跟标题

### 示例片段
```
┌──────────────────────────────────────┐
│                                      │
│ 姓名 *                                │
│ ┌────────────────────────────────┐   │
│ │ 请输入                          │   │
│ └────────────────────────────────┘   │
│                                 │
│ ┌────────────────────────────────┐   │
│ │ 年龄                  请输入     │   │
│ └────────────────────────────────┘   │
│                                      │
│ ┌────────────────────────────────┐   │
│ │ 性别                请选择   >   │   │
│ └────────────────────────────────┘   │
│                                      ���
│ 性格                                  │
│ ┌────────────────────────────────┐   │
│ │ 请选择                      >   │   │
│ └────────────────────────────────┘   │
│                                      │
│ 时间                                  │
│ ┌────────────────────────────────┐   │
│ │ 2000年01月01日                  │   │
│ └────────────────────────────────┘   │
└──────────────────────────────────────┘
```

## 布局结构描述规范
- 使用 `layout_structure.md` 中定义的规则，将组件的布局结构转换为代码表示。
- 确保生成的布局结构代码与设计稿的布局一致。
- 使用 `type` 属性指定元素类型（text, label, input, button, icon, value, datepicker）。
- 使用 `content` 属性指定元素内容。
- 使用 `display` 属性指定容器的排列方式（vertical, horizontal, grid, group）。
- 使用 `position` 属性指定定位方式（fixed, relative, absolute）。
- 使用 `visible` 属性指定动态显示条件。
- 使用三元运算符描述动态属性。

### 示例
```scss
ROOT {
  display: vertical;
  
  1 { 
    type: component; 
    component-type: thu-form-title;
    content: "收银机信息"; 
  }
  
  A {
    display: grid;
    grid-col: 2;
    
    // thu-cash-register-card 1
    A1 {
      type: component;
      component-type: thu-cash-register-card;
    }
    
    // thu-cash-register-card 2
    A2 {
      type: component;
      component-type: thu-cash-register-card;
    }
  }
}
```

## 注意事项
1. 保持文档结构清晰，使用markdown格式
2. 布局示意图必须完整展示所有表单项，不可省略
3. 确保示意图中的文字与设计稿完全一致
4. 注意缩进和对齐
5. 准确标注必填项(*)
6. 保持与设计稿一致的交互提示样式
7. 严格按照设计稿的布局和样式绘制
8. 遇到新的布局样式时，创造性地使用ASCII字符表达
9. 确保特殊布局的表达方式直观易懂
10. 在不违背原有布局规则的前提下，灵活处理特殊情况
11. 布局示意图列出所有的布局，不要省略，不要偷懒!!
12. **布局结构描述必须符合 `layout_structure.md` 的规范**