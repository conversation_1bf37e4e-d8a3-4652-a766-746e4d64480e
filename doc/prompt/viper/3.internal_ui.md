# 页面内部UI组件变量标记指南

## 目标
标记和处理页面内部的UI组件变量，与业务逻辑相关的变量分离。

## 标记步骤

1. 识别内部UI变量
- 仅用于控制组件显示/隐藏的状态变量
- 仅用于组件内部交互的方法
- 不涉及业务数据的UI控制变量
- 组件级别的临时状态

2. 内部变量命名规范
- 变量名添加下划线前缀: `_variableName`
- 接口名添加 Internal 前缀: `InternalXxxState`
- 方法名添加下划线前缀: `_methodName`
- 模板中的变量引用必须同步更新前缀

3. 添加注释标记
- 状态变量标记: `[Internal UI State]`
- 方法标记: `[Internal UI Methods]`
- 接口标记: `[Internal UI Interface]`

4. 外部变量命名规范
  移除数据对象前缀：
  - 将 `xxx.property` 改为直接使用 `property`
  - 例如：`formData.name` → `name`

## 代码模板

```vue
<template>
  <view>
    <button @click="_handleClick">点击</button>
    <popup v-model:show="_dialogVisible" @confirm="_handleConfirm" />
  </view>
</template>

<script setup lang="ts">
// [Internal UI Interface] 内部UI状态接口
interface InternalXxxState {
  isVisible: boolean
  isActive: boolean
}

// [Internal UI State] 内部状态变量
const _xxxStates = ref<InternalXxxState>({
  isVisible: false,
  isActive: false
})

// [Internal UI Methods] 内部UI方法
const _handleXxxClick = (): void => {
  _xxxStates.value.isVisible = true
}
</script>
```

## 判断标准

1. 需要标记的变量：
- 弹窗显示状态控制
- 组件展开/折叠状态
- 组件激活/选中状态
- 动画控制状态
- 临时UI交互状态

2. 不需要标记的变量：
- 业务数据相关的状态
- 需要持久化的状态
- 需要跨组件共享的状态
- 与API交互相关的状态
- 表单数据相关的状态

## 使用示例

```vue
<template>
  <!-- ✅ 正确示例：内部UI状态 -->
  <popup v-model:show="_dialogVisible" @confirm="_handleConfirm">
    <button @click="_showTypePopup">显示</button>
  </popup>

  <!-- ❌ 错误示例：业务相关状态 -->
  <input v-model="roomName" />  <!-- 这是业务数据，不应标记为内部状态 -->
  <button @click="handleSubmit">提交</button>  <!-- 这是业务方法，不应标记为内部方法 -->
</template>

<script setup>
import { ref } from 'vue';
// 正确示例：内部UI状态
const _dialogVisible = ref(false)
const _showTypePopup = (): void => {
  _dialogVisible.value = true
}

// 错误示例：业务相关状态
const roomName = ref('')  // 这是业务数据，不应标记为内部状态
const handleSubmit = () => {
  // 这是业务方法，不应标记为内部方法
}
</script>
```

## 变量重命名检查清单
1. Script 部分
   - [ ] 状态变量添加下划线前缀
   - [ ] 方法添加下划线前缀
   - [ ] 接口添加 Internal 前缀
   - [ ] 添加注释标记
2. Template 部分
   - [ ] v-model 绑定更新
   - [ ] 事件处理器(@click等)更新
   - [ ] 属性绑定(:value等)更新
   - [ ] 其他变量引用更新

## 注意事项
1. 只标记纯UI相关的变量和方法
2. 保持命名一致性，包括模板引用
3. 注释标记要清晰完整
4. 避免过度标记业务相关变量
5. 确保变量职责单一
6. 重命名时必须同步更新所有引用点