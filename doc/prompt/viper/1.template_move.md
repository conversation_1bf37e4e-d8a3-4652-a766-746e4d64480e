# UI模板迁移

我需要将[page_name].vue中的<template>部分迁移到新的index.vue文件中。要求如下：

## 基本要求
1. 只需要提供<template>部分的代码
2. 保持原有的模板结构和类名不变
3. 不需要包含任何<script>和<style>部分
4. 文件路径应为：src/pages/[module_name]/[page_name]/index.vue
5. 确保所有组件引用和class名称保持不变

## 变量命名规范
移除数据对象前缀：
- 将 `xxxData.property` 改为直接使用 `property`
- 例如：`pricePlanData.name` → `name`

## 示例
原始代码：
```vue
<template>
  <view>
    <input v-model="formData.name" />
    <button @click="submit">{{ formData.buttonText }}</button>
  </view>
</template>
```

迁移后：
```vue
<template>
  <view>
    <input v-model="name" />
    <button @click="submit">{{ buttonText }}</button>
  </view>
</template>
```

请提供迁移后的代码。请完整写出所有代码,不要使用省略号或者注释来代替代码。