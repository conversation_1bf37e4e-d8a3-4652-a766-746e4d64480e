# VIPER-VM Index.vue 完善指南

## 目标
完善 index.vue 文件，确保其符合 VIPER-VM 架构规范，只保留视图层职责。

## 核心原则

1. 视图层职责
   - 只负责 UI 渲染
   - 直接使用 ViewModel 中定义的状态和动作
   - 保留内部 UI 状态管理
   - 不包含业务逻辑

2. 代码组织
   - template 部分保持不变
   - script 部分只包含必要的引入和内部 UI 状态
   - 移除所有业务逻辑方法的中转定义

## 实现步骤

1. 基础引入
```typescript
import { ref } from 'vue'
import { usePresenter } from './presenter'
import type { IViewModel } from './viewmodel'

// 初始化 ViewModel
const vm: IViewModel = usePresenter()
```

2. 保留内部 UI 状态和方法
   - 保持已有的 Internal UI State
   - 保持已有的 Internal UI Methods
   - 确保它们都有正确的注释标记

3. 视图绑定
   - 直接使用 vm.state 绑定状态
   - 直接使用 vm.actions 绑定事件
   - 移除所有中间处理方法

## 代码规范

1. 状态使用
```vue
<template>
  <!-- 正确：直接使用 vm.state -->
  <input v-model="vm.state.fieldName" />
  
  <!-- 错误：创建中间变量 -->
  <input v-model="localFieldName" />
</template>
```

2. 事件处理
```vue
<template>
  <!-- 正确：直接使用 vm.actions -->
  <button @click="vm.actions.handleSubmit">提交</button>
  
  <!-- 错误：创建中间处理方法 -->
  <button @click="onSubmit">提交</button>
</template>
```

3. 计算属性使用
```vue
<template>
  <!-- 正确：使用 .value 访问 vm.computed 中的计算属性 -->
  <button :disabled="vm.computed.isFormValid.value">提交</button>
  
  <!-- 错误：直接使用计算属性 -->
  <button :disabled="vm.computed.isFormValid">提交</button>
</template>
```

## 检查清单

1. 移除项
   - [ ] 移除所有业务逻辑方法的中转定义
   - [ ] 移除不必要的计算属性
   - [ ] 移除本地状态管理（除内部UI状态外）

2. 保留项
   - [ ] 保留必要的类型导入
   - [ ] 保留 ViewModel 初始化
   - [ ] 保留带下划线前缀的内部 UI 方法
   - [ ] 保留 Internal UI State 相关代码

3. 更新项
   - [ ] 更新所有事件绑定为直接使用 vm.actions
   - [ ] 更新所有状态绑定为直接使用 vm.state
   - [ ] 确保所有内部 UI 状态有正确的类型定义

## 注意事项

1. 不要创建新的方法来包装 vm.actions 中的方法
2. 确保所有内部 UI 状态都有 Internal 标记
3. 保持代码整洁和可读性
4. 确保类型安全
5. 遵循单一职责原则
6. 访问 ViewModel 中的计算属性时必须使用 .value
   - vm.computed 中的所有计算属性都需要通过 .value 访问其值
   - 这是因为计算属性被包装在 ComputedRef 对象中
   - 确保在模板中正确使用 vm.computed.xxx.value 的形式

## 示例结构

```vue
<template>
  <!-- UI 结构 -->
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { usePresenter } from './presenter'
import type { IViewModel } from './viewmodel'

// 初始化 ViewModel
const vm: IViewModel = usePresenter()

// [Internal UI State] 内部UI状态
interface InternalXxxState {
  // ...
}

const _xxxStates = ref<InternalXxxState>({
  // ...
})

// [Internal UI Methods] 内部UI方法
const _handleXxx = (): void => {
  // ...
}
</script> 