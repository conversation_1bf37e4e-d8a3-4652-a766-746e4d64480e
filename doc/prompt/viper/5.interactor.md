# Interactor Generator Prompt

## 输入要求
1. 提供了以下信息：
   - 页面/功能名称
   - 相关的 API 文件及其方法
   - 实体类型定义文件

## 输出期望
生成一个符合 VIPER-VM 架构的 interactor.ts 文件，包含：
1. 必要的导入语句
2. Interactor 类定义
3. 每个业务用例的具体实现
4. 错误处理
5. 导出组合式函数

## 模板结构
```typescript
import { /* API方法 */ } from '../api';
import type { /* 实体类型 */ } from '../entity';

export class [PageName]Interactor {
  /**
   * [用例1描述]
   * @param [参数描述]
   * @returns [返回值描述]
   */
  async [用例1]([参数]): Promise<[返回类型]> {
    try {
      const result = await [API方法]([参数]);
      return result;
    } catch (error) {
      console.error('[错误描述]:', error);
      throw error;
    }
  }

  // 其他用例实现...
}

// 导出组合式函数
export function use[PageName]Interactor() {
  return new [PageName]Interactor();
}
```

## 示例输入
```
页面名称: CreateRoom
API文件: room.ts, roomType.ts, roomTheme.ts, area.ts
业务用例:
1. 创建/更新房间
2. 获取房间类型列表
3. 获取房间主题列表
4. 获取区域列表
5. 上传房间图片
```

## 注意事项
1. 每个方法都应该包含完整的错误处理
2. 使用 TypeScript 类型标注确保类型安全
3. 保持单一职责原则
4. 方法命名要清晰表达业务含义
5. 添加必要的注释说明

## 参考
VIPER-VM 架构文档：

````
## 架构示意说明
```
架构名称：VIPER-VM (VIPER with Vue and Model Management)
View（视图层，一个文件）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @IViewModel (UI表单模型, 单独文件)

IViewModel (视图层，IViewModel模块, 一个View对应一个文件, 状态和actions定义)
    - UI states
    - UI actions

Presenter（协调层，一个View对应一个文件，继承IViewModel）
    - @IViewModel(UI actions的实现，UI states数据的处理)
        - StateManager (状态管理)
            - reactive data（响应式数据）
            - computed properties（计算属性）
        - EventHandlers (事件处理)
    - DataConvert（数据转换）
        - @ViewModelConverter
        - @Interactor
        - @View.IViewModel
        - @Entity
    - RouterManager
        - @Router
        - @Store

ViewModelConverter (协调层，视图-模型转换模块，一个View对应一个文件)
    - ConversionLogic(转换逻辑)
    - @Entity
    - @View.IViewModel

Interactor（业务交互层，一个View对应一个文件）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @DataManager
    - @Entity.DataModels

DataManager（数据管理层）
    - APIService (API服务)
    - LocalStorage (本地存储服务)
        - @Store.PersistenceManager
    - CachingStrategy (缓存策略)
    - @Entity.DataModels

Router（协调层，路由模块-页面切换）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调
    - 切换Component

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)

Store (基础组件，全局状态管理，pinia)
    - GlobalState
    - Actions
    - Mutations
    - PersistenceManager (状态持久化管理)
```

## 核心架构层级

| 架构层 | 子层级 | 职责 | 依赖关系 | 文件位置 |
|--------|--------|------|----------|-----------|
| **View层** | **View** | UI展示层 | @Presenter<br>@IViewModel | `*.vue` |
| | | • Template: UI结构<br>• ComponentLogic: 组件逻辑<br>• @IViewModel: UI状态和行为定义(接口) | | |
| | **IViewModel** | UI状态和行为定义 | - | `viewModel.ts` |
| | | • UI states: UI状态定义<br>• UI actions: UI动作定义<br>• 一个View对应一个IViewModel文件 | | |
| **Presenter层** | **Presenter** | 协调层（继承IViewModel） | @IViewModel<br>@ViewModelConverter<br>@Interactor<br>@Router<br>@Store | `presenter.ts` |
| | | • @IViewModel(UI actions的实现，UI states数据的处理)<br>• StateManager: 响应式数据管理<br>• EventHandlers: 事件处理<br>• DataConvert: 数据转换<br>• RouterManager: 路由管理 | | |
| | **ViewModelConverter** | 视图-模型转换层 | @Entity<br>@View.IViewModel | `converter.ts` |
| | | • ConversionLogic: 转换逻辑 | | |
| | **Router** | 路由管理层 | @Store | `router/*.ts` |
| | | • NavigationLogic: 导航逻辑<br>• Component切换 | | |
| | **Store** | 全局状态管理 | - | `store/*.ts` |
| | | • GlobalState: 全局状态<br>• PersistenceManager: 状态持久化管理 | | |
| **Interactor层** | **Interactor** | 业务交互层 | @DataManager<br>@Entity.DataModels | `interactor.ts` |
| | | • UseCases: 业务用例<br>• BusinessLogic: 业务逻辑 | | |
| | **DataManager** | 数据管理层 | @Store.PersistenceManager<br>@Entity.DataModels | `*DataManager.ts` |
| | | • APIService: API服务<br>• LocalStorage: 本地存储<br>• CachingStrategy: 缓存策略 | | |
| **Entity层** | **Entity** | 实体层 | - | `entity/*.ts` |
| | | • DataModels: 数据模型<br>• ModelRelationships: 模型关系 | |


## 数据流向

### 数据流向图

```mermaid
graph TD
    %% 视图层交互
    View <--> |UI事件/状态更新| Presenter
    
    %% Presenter层交互
    Presenter <--> |数据转换| ViewModelConverter
    Presenter <--> |业务操作| Interactor
````
