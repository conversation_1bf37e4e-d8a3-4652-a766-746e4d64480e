# VIPER-VM Presenter Generator

基于以下文件生成符合 VIPER-VM 架构的 presenter.ts：
- index.vue：视图层定义
- converter.ts：数据转换层
- viewmodel.ts：视图模型接口定义
- interactor.ts：业务交互层

## 生成规则

1. 导入依赖
```typescript
import { reactive, computed, onMounted, onUnmounted } from 'vue';
import { onLoad, onUnload, onShow, onHide } from '@dcloudio/uni-app';
import type { IViewModel, IState, IComputed, IActions } from './viewmodel';
import { useInteractor } from './interactor';
import { Converter } from './converter';
```

2. 创建 Presenter 类
```typescript
export class Presenter implements IViewModel {
  public state = reactive(Converter.createInitialState());
  
  // 计算属性
  public computed: IComputed = {
    // 示例：组合显示文本
    displayText: computed(() => {
      return `${this.state.name}: ${this.state.value}`;
    }),
    // 示例：状态判断
    isValid: computed(() => {
      return this.validateForm().isValid;
    }),
    // 示例：数据转换
    formattedData: computed(() => {
      return Converter.formatViewData(this.state.data);
    })
  };

  constructor() {
    this.setupLifecycles();
  }
}
```

3. 实现生命周期管理
```typescript
private setupLifecycles() {
  // Vue 生命周期
  onMounted(() => {
    console.log('页面组件已挂载');
  });

  onUnmounted(() => {
    console.log('页面组件已卸载');
  });

  // UniApp 页面生命周期
  onLoad((options) => {
    console.log('页面加载', options);
    this.initializeData();
  });

  onShow(() => {
    console.log('页面显示');
  });

  onHide(() => {
    console.log('页面隐藏');
  });

  onUnload(() => {
    console.log('页面卸载');
    this.cleanup();
  });
}
```

4. 实现数据初始化
```typescript
private async initializeData() {
  try {
    // 并行加载初始数据
    const [data1, data2] = await Promise.all([
      this.interactor.fetchData1(),
      this.interactor.fetchData2()
    ]);
    
    // 更新状态
    this.state.data1 = Converter.convertData1(data1);
    this.state.data2 = Converter.convertData2(data2);
  } catch (error) {
    console.error('初始化数据失败:', error);
    uni.showToast({ 
      title: '加载数据失败，请重试', 
      icon: 'none' 
    });
  }
}

public loadExistingData(data: any) {
  this.state = Converter.toViewState(data);
  this.state.isEdit = true;
}
```

5. 实现表单验证
```typescript
private validateForm(): { isValid: boolean; message: string } {
  // 必填字段验证
  if (!this.state.formData.requiredField) {
    return { isValid: false, message: '请输入必填字段' };
  }

  // 数值字段验证
  const numValue = Number(this.state.formData.numField);
  if (isNaN(numValue) || numValue < 0) {
    return { isValid: false, message: '请输入有效数字' };
  }

  return { isValid: true, message: '' };
}
```

6. 实现动作处理器
```typescript
public actions: IActions = {
  // 数值输入验证
  onNumberInput: (value: string) => {
    const num = Number(value);
    if (isNaN(num)) {
      uni.showToast({ title: '请输入有效数字', icon: 'none' });
    }
  },

  // 开关切换处理
  onToggle: (value: boolean) => {
    if (!value) {
      // 清理相关状态
      this.state.formData.relatedField = [];
    }
  },

  // 保存操作
  onSave: async () => {
    const validation = this.validateForm();
    if (!validation.isValid) {
      uni.showToast({ title: validation.message, icon: 'none' });
      return;
    }

    try {
      uni.showLoading({ title: '保存中...' });
      const entityData = Converter.toEntityData(this.state);
      const result = await this.interactor.save(entityData);
      
      uni.hideLoading();
      uni.showToast({ title: '保存成功', icon: 'success' });
      
      // 更新ID并返回
      if (!this.state.formData.id) {
        this.state.formData.id = result.id;
      }
      setTimeout(() => uni.navigateBack(), 1500);
    } catch (error) {
      uni.hideLoading();
      uni.showToast({ 
        title: '保存失败：' + (error instanceof Error ? error.message : '未知错误'), 
        icon: 'none' 
      });
    }
  },

  // 删除操作
  onDelete: async () => {
    if (!this.state.isEdit || !this.state.formData.id) {
      return;
    }

    try {
      await new Promise<void>((resolve, reject) => {
        uni.showModal({
          title: '确认删除',
          content: '是否确认删除该记录？',
          success: (res) => res.confirm ? resolve() : reject(new Error('用户取消'))
        });
      });

      uni.showLoading({ title: '删除中...' });
      await this.interactor.delete(this.state.formData.id);
      
      uni.hideLoading();
      uni.showToast({ title: '删除成功', icon: 'success' });
      setTimeout(() => uni.navigateBack(), 1500);
    } catch (error) {
      if (error instanceof Error && error.message === '用户取消') {
        return;
      }
      uni.hideLoading();
      uni.showToast({ 
        title: '删除失败：' + (error instanceof Error ? error.message : '未知错误'), 
        icon: 'none' 
      });
    }
  }
};
```

7. 导出组合式函数
```typescript
export function use[PageName]Presenter(): I[PageName]ViewModel {
  return new Presenter();
}
```

## 注意事项

1. 生命周期管理
   - 合理处理 Vue 和 UniApp 生命周期钩子
   - 初始化：onLoad 中加载数据和检查状态
   - 清理：onUnload 中重置状态和释放资源

2. 状态与数据
   - 使用 reactive 管理响应式状态
   - 通过 actions 统一处理状态更新
   - 使用 Promise.all 优化并行数据加载
   - 统一的数据转换和错误处理

3. 用户交互与反馈
   - 完善的表单验证逻辑
   - 清晰的加载状态提示
   - 友好的错误提示和操作反馈
   - 必要的操作确认机制

4. 职责边界
   - Presenter 仅负责协调视图和业务逻辑
   - 数据转换交由 Converter 处理
   - 业务逻辑委托给 Interactor
   - 保持代码职责单一

## 实现步骤

1. 需求分析
   - 分析视图组件和数据绑定需求
   - 确定必要的交互事件
   - 规划状态结构和更新策略

2. 核心实现
   - 基于 viewmodel.ts 实现状态和动作
   - 使用 converter.ts 处理数据转换
   - 通过 interactor.ts 对接业务逻辑
   - 实现必要的验证和错误处理

3. 交互优化
   - 添加适当的加载状态
   - 完善操作反馈机制
   - 优化用户体验细节

4. 测试与完善
   - 验证功能完整性
   - 检查错误处理
   - 确保代码可维护性

