# ViewModel生成器Prompt

## 输入说明
请帮我完成以下任务：

1. 分析步骤
a) 请先分析index.vue模板中的所有：
   - 数据绑定 (如 v-model, :value 等)
   - 展示数据 (如 {{ }} 中的数据)
   - 计算属性 (computed)
   - 事件绑定 (@click 等)
   - 条件渲染 (v-if/v-show)
   - 列表渲染 (v-for)

b) 基于分析结果，列出：
   - UI States: 所有UI展示和绑定的数据
   - UI Computed: 所有计算属性
   - UI Actions: 所有UI事件处理方法

c) 排除以下内容：
   - 以 _ 开头的内部变量和方法
   - 带有 [Internal UI State] 注释标记的变量
   - 带有 [Internal UI Methods] 注释标记的方法
   - 仅用于控制组件内部UI状态的变量（如弹窗显示状态）

## 类型定义指导

### 1. 类型定义原则
- 所有类型必须在接口外单独定义
- 禁止在IXxxState/IXxxComputed中直接定义内联类型
- 相关的类型应该组织在一起
- 类型应该具有明确的业务含义

### 2. 表单输入类型规范
- 所有表单输入字段(v-model)必须使用string类型
- 数值输入框也使用string类型，在提交时再转换为number
- 示例:
```typescript
interface FormData {
  // 正确
  price: string
  amount: string
  
  // 错误
  price: number
  amount: number
}
```

### 3. 类型定义位置
a) 实体相关类型：
```typescript
// Entity Types
interface EntityType {
  id: string
  // ... other fields
}
```

b) UI组件类型：
```typescript
// Component Types
interface ComponentData {
  // ... fields
}
```

c) 业务类型：
```typescript
// Business Types
interface BusinessData {
  // ... fields
}
```

### 4. 实体类型定义规范
- 所有实体类型必须包含唯一标识符 id 字段
- 使用具体的类型而不是 any
- 优先使用 interface 而不是 type
- 为可选字段添加 ? 标记
- 使用联合类型表示有限的取值范围

### 5. 命名规范
- 接口名称使用 I 前缀
- 状态接口使用 IXxxState
- 计算属性接口使用 IXxxComputed
- 动作接口使用 IXxxActions
- ViewModel 接口使用 IXxxViewModel
- 实体类型直接使用名词

### 6. 类型提取示例
反例 - 内联定义：
```typescript
export interface IXxxState {
  data: {  // 错误：内联定义类型
    id: string
    name: string
    price: number  // 错误：表单输入应该用string
  }
}

export interface IXxxComputed {
  displayData: {  // 错误：内联定义计算属性类型
    text: string
    isValid: boolean
  }
}
```

正例 - 提取定义：
```typescript
// 提取为独立类型
interface DataType {
  id: string
  name: string
  price: string  // 正确：表单输入使用string
}

interface DisplayData {
  text: string
  isValid: boolean
}

export interface IXxxState {
  data: DataType  // 正确：使用已定义的类型
}

export interface IXxxComputed {
  displayData: ComputedRef<DisplayData>  // 正确：使用ComputedRef包装已定义的类型
}
```

### 7. 注释规范
a) 类型注释：
- 每个类型定义必须添加注释说明其用途
- 使用 JSDoc 格式的注释 /** ... */
- 类型注释应该简洁明了地说明业务含义

示例：
```typescript
/** 商品分类信息 */
interface GoodsCategory {
  id: string      // 分类ID
  name: string    // 分类名称
  sort: number    // 排序号
}
```

b) 字段注释：
- 每个字段都必须添加行尾注释说明其含义
- 对于非显而易见的字段，说明其单位、取值范围等
- 使用统一的注释格式: `// 注释内容`

示例：
```typescript
interface PriceConfig {
  amount: string          // 金额(元)
  duration: string        // 时长(分钟)
  discount: string        // 折扣率(0-100)
  isEnabled: boolean      // 是否启用
}

interface IPriceComputed {
  totalAmount: ComputedRef<string>    // 总金额(元)
  isDiscounted: ComputedRef<boolean>  // 是否有折扣
}
```

c) 方法注释：
- 所有方法必须添加 JSDoc 注释
- 说明方法的功能、参数和返回值
- 必要时说明副作用或注意事项

示例：
```typescript
/** 
 * 计算折扣后价格
 * @param price 原价(元)
 * @param discount 折扣率(0-100)
 * @returns 折扣后价格(元)
 */
calculateDiscountPrice: (price: string, discount: string) => string
```

### 8. 数据结构设计原则

a) 实体与表单数据关系：
- 如果定义了实体类型，UI State必须复用该类型而不是重复定义
- 表单数据应该使用实体类型作为基础模型
- 示例:
```typescript
// 实体类型
interface UserEntity {
  id: string
  name: string
  age: string
}

// 正确示例
interface IUserState {
  currentUser: UserEntity    // 复用实体类型
  isFormValid: boolean       // UI特有状态
}

interface IUserComputed {
  displayName: ComputedRef<string>      // 展示名称
  isAdult: ComputedRef<boolean>         // 是否成年
}

// 错误示例
interface IUserState {
  id: string                // 错误：重复定义实体字段
  name: string
  age: string
  isFormValid: boolean
}
```

b) 状态数据组织：
- 表单数据应该集中在一个对象中
- UI特有状态保持在顶层
- 计算属性应该反映数据的派生状态
- 示例:
```typescript
// 正确的状态组织
interface IFormState {
  formData: FormEntity        // 表单数据集中管理
  isFormValid: boolean        // UI状态置于顶层
}

interface IFormComputed {
  displayText: ComputedRef<string>      // 派生的展示文本
  canSubmit: ComputedRef<boolean>       // 派生的提交状态
}

// 错误的状态组织
interface IFormState {
  name: string               // 错误：表单字段散落
  age: string
  address: string
  isFormValid: boolean
}
```

c) 数据模型完整性：
- 相关的数据字段应该组织在同一个类型定义中
- 避免数据结构的重复定义
- 确保数据模型的完整性和一致性
```typescript
// 正确示例
interface OrderEntity {
  id: string
  items: OrderItem[]
  total: string
}

interface IOrderState {
  currentOrder: OrderEntity   // 完整的数据模型
  isEditing: boolean
}

interface IOrderComputed {
  totalAmount: ComputedRef<string>      // 计算总金额
  canCheckout: ComputedRef<boolean>     // 计算是否可结账
}

// 错误示例
interface IOrderState {
  orderId: string            // 错误：拆散了实体数据
  orderItems: OrderItem[]
  orderTotal: string
  isEditing: boolean
}
```

## 输出格式

1. 首先输出viewmodel.ts的完整定义
```typescript:src/pages/[module_name]/[page_name]/viewmodel.ts
import type { ComputedRef } from 'vue'

// Entity Types
/** 实体类型说明 */
interface EntityType {
  // ... fields
}

// Component Types
/** 组件类型说明 */
interface ComponentType {
  // ... fields
}

// Business Types
/** 业务类型说明 */
interface BusinessType {
  // ... fields
}

// UI States interface
export interface IXxxState {
  // 使用已定义的类型，不要内联定义
  componentData: ComponentType
  businessData: BusinessType
}

// UI Computed interface
export interface IXxxComputed {
  // 使用ComputedRef包装已定义的类型
  computedData: ComputedRef<ComputedType>
}

// UI Actions interface
export interface IXxxActions {
  // ... actions
}

// Combined ViewModel interface
export interface IXxxViewModel {
  state: IXxxState
  computed: IXxxComputed
  actions: IXxxActions
}
```

2. 然后提供index.vue的更新建议
```vue:src/pages/[module_name]/[page_name]/index.vue
<template>
<!-- 仅展示需要更新的部分 -->
<view>
  <!-- 原代码 -->
  <input v-model="searchText" />
  <text>{{ displayText }}</text>
  
  <!-- 更新后 -->
  <input v-model="vm.state.searchText" />
  <text>{{ vm.computed.displayText }}</text>
</view>
</template>

<script setup lang="ts">
import { use[PageName]Presenter } from './presenter'
import type { I[PageName]ViewModel } from './viewmodel'

// 初始化 ViewModel
const vm: I[PageName]ViewModel = use[PageName]Presenter()
</script>
```

3. 提供更新说明
- 列出所有需要更新的数据绑定
- 列出所有需要更新的计算属性
- 列出所有需要更新的事件处理器
- 说明移除的代码部分

## 注意事项
- 只关注业务相关的UI层面数据和行为
- 不包含组件内部状态控制相关的定义
- 不要包含业务逻辑相关的定义
- 使用TypeScript类型定义
- 所有属性都应该有明确的类型注解
- 遵循VIPER-VM架构的职责分离原则
- 确保所有实体类型都有唯一标识符
- 使用明确的类型而不是any
- 禁止在接口中内联定义类型
- 所有类型必须提取为独立定义
- 类型定义应该按照业务含义组织
- 相关的类型应该放在一起
- 类型名称应该能表达其用途
- 表单输入字段必须使用string类型
- 所有类型定义必须有注释说明用途
- 所有字段必须有行尾注释说明含义
- 所有方法必须有 JSDoc 格式注释
- 注释应该清晰说明业务含义
- 对于非显而易见的字段，注明单位和取值范围
- 保持注释风格统一
- 计算属性必须使用ComputedRef类型包装
- 计算属性应该反映数据的派生状态

请提供分析过程和最终代码。