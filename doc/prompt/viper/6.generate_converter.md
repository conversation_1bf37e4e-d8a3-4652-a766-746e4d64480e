# Converter生成器Prompt

## 输入说明
提供了以下信息:
1. 页面名称
2. viewmodel.ts中的状态接口定义
3. entity中的数据模型定义

## 类型使用规范
1. 严格使用已提供的类型:
   - viewmodel.ts中定义的UI状态类型
   - entity目录中定义的实体类型
   - 通用类型(如PageVO等)

2. 禁止在converter中:
   - 自定义新的类型
   - 重复定义已有类型
   - 在方法参数中内联定义类型

3. 如果缺少必要的类型:
   - 列出缺少的类型及其用途
   - 建议在合适的位置定义(viewmodel.ts或entity)
   - 不要在converter中临时定义

## 转换方法设计模式

### 1. 状态更新模式
- 使用void返回类型的更新方法，直接修改state
- 适用于响应式框架的数据更新
```typescript
static toViewState(state: IState, entityData: EntityData): void {
  state.field = entityData.field;
}
```

### 2. 独立转换方法设计

### 何时需要独立转换
1. 列表数据转换场景:
   - 下拉选项数据
   - 表格数据
   - 树形结构数据

2. 复杂对象转换场景:
   - 包含多个子属性的复杂对象
   - 需要特殊格式化的数据(如日期、金额)
   - 有特定业务规则的数据转换

3. 重复使用的转换逻辑:
   - 多个地方都需要用到的转换规则
   - 具有通用性的数据转换

### 命名规范
- 使用 `to[EntityName]State` 格式命名UI状态转换方法
- 使用 `to[EntityName]Data` 格式命名实体数据转换方法
- 对于工具方法使用 `convert[Purpose]` 格式命名

### 示例实现
```typescript
export class ExampleConverter {
  // 主要转换方法
  static toViewState(entityData: EntityType): IExampleState {
    return {
      items: this.toItemsState(entityData.items),
      config: this.toConfigState(entityData.config)
    };
  }

  // 独立的列表数据转换
  static toItemsState(items: ItemEntity[]): ItemState[] {
    return items.map(item => ({
      id: item.id,
      name: item.name,
      // ... 其他转换逻辑
    }));
  }

  // 独立的配置对象转换
  static toConfigState(config: ConfigEntity): ConfigState {
    return {
      // ... 转换逻辑
    };
  }
}
```

### 3. 数据转换原则
- JSON序列化处理
```typescript:src/pages/room_management/add_time_purchase/converter.ts
startLine: 42
endLine: 43
```

- 默认值处理
```typescript:src/pages/room_management/add_time_purchase/converter.ts
startLine: 82
endLine: 85
```

- 类型安全转换
```typescript:src/pages/room_management/add_time_purchase/converter.ts
startLine: 156
endLine: 164
```

### 4. 命名规范
- 主要转换方法：
  - `toViewState`: 更新UI状态
  - `toEntityData`: 生成实体数据
  - `createInitialState`: 创建初始状态

- 独立转换方法：
  - `update[Field]`: 更新特定字段
  - `to[Field]State`: 转换特定数据到UI状态
  - `convert[Purpose]`: 通用转换工具方法

### 5. 方法职责划分
- 创建方法：负责初始化
- 更新方法：负责状态更新
- 转换方法：负责数据转换
- 工具方法：负责通用转换

## 实现模板
```typescript
export class [PageName]Converter {
  static createInitialState(): IState {
    return {
      // 初始值
    };
  }

  static toViewState(state: IState, entityData: EntityData): void {
    // 直接更新状态
  }

  static toEntityData(state: IState): EntityData {
    return {
      // 转换为实体数据
    };
  }

  static update[Field](state: IState, data: FieldData): void {
    // 更新特定字段
  }

  private static convert[Purpose](data: SourceType): TargetType {
    // 通用转换逻辑
  }
}
```

## 最佳实践
1. 状态更新
- 使用void返回类型的更新方法
- 直接修改传入的state对象
- 适合响应式框架

2. 数据转换
- 处理JSON序列化/反序列化
- 提供合理的默认值
- 确保类型安全

3. 方法组织
- 相关字段转换放在一起
- 复杂转换逻辑独立成方法
- 通用转换逻辑提取为工具方法

4. 错误处理
- 添加必要的空值检查
- 提供合理的默认值
- 处理异常情况

## 注意事项
1. 确保所有必要的类型导入
2. 处理可选字段的默认值
3. 处理数组和嵌套对象的深度转换
4. 添加必要的类型转换(如string <-> number)
5. 处理日期等特殊类型的转换
6. 添加必要的数据验证
7. 确保不可变性(Immutability)
8. 合理拆分独立转换方法

## 输出要求
1. 生成的converter.ts文件应包含:
   - 必要的类型导入
   - Converter类定义
   - 所有必需的转换方法
   - 必要的独立转换方法
   - 必要的辅助方法
   - 类型注解
   - 方法注释
2. 如果发现缺少必要的类型，列出:
   - 缺少的类型名称
   - 类型的用途说明
   - 建议定义的位置
   - 建议的类型定义

## 分析步骤
1. 组件数据使用分析
   - 分析组件模板中使用的所有数据
   - 记录每个数据的来源和使用场景
   - 标识需要转换的数据结构

2. API数据流分析
   - 分析所有API调用
   - 识别API返回数据到视图状态的转换需求
   - 记录需要的转换方法

3. 交互状态分析
   - 分析用户交互涉及的数据变化
   - 识别状态更新的转换需求
   - 记录需要的更新方法

4. 转换方法设计
   - 基于上述分析设计必要的转换方法
   - 包括：
     * API结果转视图状态
     * 视图状态转实体数据
     * 交互状态更新方法

## 分析示例
1. 组件数据使用
```vue
<template>
  <select v-model="selected">
    <option v-for="item in items" :key="item.id">
      {{ item.name }}
    </option>
  </select>
</template>
```
需要转换方法：`toItemsState(apiItems: ApiItem[]): ViewItem[]`

2. API数据流
```typescript
async function getItems(): Promise<ApiItem[]>
```
需要转换方法：`toViewState(items: ApiItem[]): void`

3. 交互状态
```typescript
function onSelect(item: ViewItem): void
```
需要转换方法：`toEntityData(item: ViewItem): ApiItem`
