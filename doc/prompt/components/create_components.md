# 组件开发 Prompt 模板

## 0. 目录结构说明

### 0.1 组件存放位置
```
src/
└── components/
    └── thu-[component-name]/      # 组件目录
        ├── README.md              # 组件文档
        ├── types.ts               # 类型定义
        ├── thu-[component-name].vue  # 组件实现
        └── prompt.md             # 开发指南(可选)
```

### 0.2 测试相关位置
```
src/
├── pages/
│   └── test/
│       ├── index/                # 测试导航页
│       │   └── index.vue
│       └── [component-name]-test/  # 组件测试页
│           └── index.vue
└── pages.json                    # 路由配置文件
```

### 0.3 参考用例
可以参考以下现有组件的实现：
- 基础组件: `@/components/thu-switch-button`
- 表单组件: `@/components/thu-form-title`
- 列表组件: `@/components/thu-package-item`
- 导航组件: `@/components/thu-scrollable-navbar`

## 1. 组件创建

### 1.1 文件结构分析
```
component-name/
├── README.md          # 组件文档
├── types.ts           # 类型定义
├── component-name.vue # 组件实现
└── prompt.md         # 开发指南(可选)
```

### 1.2 类型定义模板 (types.ts)
```typescript
// 组件数据项类型
export interface ComponentItem {
  id: string | number
  // ... 其他字段
}

// Props 类型
export interface ComponentProps {
  // 必填属性
  requiredProp: PropType
  // 可选属性
  optionalProp?: PropType
}

// Emits 类型
export interface ComponentEmits {
  (e: 'update:modelValue', value: ValueType): void
  (e: 'customEvent', payload: PayloadType): void
}
```

### 1.3 组件实现规范

#### 基础模板结构
```vue
<template>
  <view class="component-root">
    <!-- 组件主体结构 -->
  </view>
</template>

<script lang="ts">
export default {
  options: {
    virtualHost: true
  }
}
</script>

<script lang="ts" setup>
import type { ComponentProps, ComponentEmits } from './types'

const props = defineProps<ComponentProps>()
const emits = defineEmits<ComponentEmits>()
</script>
```

## 2. 组件测试

### 2.1 测试页面创建

#### 路由配置位置
在 `src/pages.json` 的 `subPackages` 数组中的 `test` 分包下添加：
```json
{
  "root": "pages/test",
  "pages": [
    {
      "path": "[component-name]-test/index",
      "style": {
        "navigationBarTitleText": "组件测试"
      }
    }
  ]
}
```

#### 测试入口位置
在 `src/pages/test/index/index.vue` 的 `testPages` 数组中添加：
```typescript
{
  title: '组件名称测试',
  desc: '测试组件的功能和样式',
  path: '/pages/test/[component-name]-test/index'
}
```

### 2.2 通用测试用例框架

#### 基础功能测试
1. Props 传递验证
2. Events 触发验证
3. v-model 双向绑定(如果支持)
4. 插槽内容渲染(如果支持)

#### 交互功能测试
1. 用户输入响应
2. 状态切换效果
3. 动画过渡效果(如果有)
4. 边界条件处理

#### 样式测试
1. 默认样式渲染
2. 响应式布局
3. 主题定制支持
4. 不同状态样式

### 2.3 测试入口模板

```typescript
// 在测试页面导航中添加
{
  title: '组件名称测试',
  desc: '测试组件的功能和样式',
  path: '/pages/test/[component-name]-test/index'
}
```

## 3. 通用注意事项

### 3.1 性能优化要点
- 使用 v-memo 优化列表渲染
- 避免不必要的响应式数据
- 合理使用计算属性
- 及时清理副作用

### 3.2 兼容性考虑
- 跨平台兼容性测试
- 不同尺寸设备适配
- 边界情况处理
- 网络状态处理

### 3.3 代码规范要求
- 使用 TypeScript 类型定义
- 遵循组件命名规范
- 添加必要的注释
- 保持代码整洁和可维护性

### 3.4 组件文档要求
- 完整的 Props 说明
- Events 触发说明
- 使用示例代码
- 注意事项说明

## 4. 补充说明
请在此处添加:
1. 组件具体需求说明
2. UI设计图参考
3. 特定样式参数
4. 特殊功能需求
5. 其他补充说明 

## 5. 开发流程示例

### 5.1 创建新组件
```bash
# 1. 创建组件目录
mkdir src/components/thu-new-component

# 2. 创建必要文件
touch src/components/thu-new-component/README.md
touch src/components/thu-new-component/types.ts
touch src/components/thu-new-component/thu-new-component.vue

# 3. 创建测试目录
mkdir -p src/pages/test/new-component-test
touch src/pages/test/new-component-test/index.vue
```

### 5.2 更新配置
1. 在 pages.json 中添加测试页面路由
2. 在测试导航页添加入口
3. 在 easycom 配置中确认组件自动导入规则

### 5.3 参考实现
1. 基础组件参考:
   - 开关按钮: `@/components/thu-switch-button`
   - 计数器: `@/components/thu-counter`

2. 表单组件参考:
   - 表单标题: `@/components/thu-form-title`
   - 输入框: `@/components/thu-input`

3. 列表组件参考:
   - 商品项: `@/components/thu-package-item`
   - 订单项: `@/components/thu-order-item`

4. 导航组件参考:
   - 滚动导航: `@/components/thu-scrollable-navbar`
   - 标签页: `@/components/thu-tabs`

## 6. 小程序平台适配指南

### 6.1 模板语法转换对照表
```
Vue                    |  小程序
----------------------|-------------------------
v-for="item in items" |  wx:for="{{items}}"
:key="item.id"        |  wx:key="id"
@tap="handler"        |  bindtap="handler"
:style="{ ... }"      |  style="{{...}}"
v-if="condition"      |  wx:if="{{condition}}"
```

### 6.2 常见适配要点

#### 事件处理
- Vue 写法: `@tap="handleSelect(item)"`
- 小程序写法: `bindtap="handleSelect" data-item="{{item}}"`

#### 样式绑定
- Vue 写法: `:style="{ color: isActive ? '#5956FF' : '#000' }"`
- 小程序写法: `style="color: {{isActive ? '#5956FF' : '#000'}}"`

#### 条件渲染
- Vue 写法: `v-if="isShow"`
- 小程序写法: `wx:if="{{isShow}}"`

#### 列表渲染
- Vue 写法:
```html
<view v-for="item in items" :key="item.id">
  {{item.name}}
</view>
```
- 小程序写法:
```html
<view wx:for="{{items}}" wx:key="id">
  {{item.name}}
</view>
```

### 6.3 TypeScript 类型注意事项

1. 小程序环境下的事件类型定义
```typescript
interface TapEvent {
  type: 'tap'
  currentTarget: {
    dataset: {
      [key: string]: any
    }
  }
}
```

2. Props 类型检查
```typescript
// 确保类型定义同时兼容 Vue 和小程序的数据传递方式
export interface ComponentProps {
  modelValue: string | number  // v-model 绑定值
  items: Array<{
    id: string | number
    name: string
  }>
}
```

### 6.4 常见问题与解决方案

1. 事件处理兼容性
```typescript
// 统一的事件处理函数，同时兼容两种方式
const handleEvent = (e: TapEvent | Item) => {
  const item = 'currentTarget' in e ? e.currentTarget.dataset.item : e
  // 处理逻辑...
}
```

2. 样式绑定兼容性
```html
<!-- 使用计算属性处理样式逻辑 -->
<view :class="computedClass" :style="computedStyle">
  <!-- 内容 -->
</view>
```

3. 生命周期对应关系
```typescript
// Vue    | 小程序
// mounted | ready
// unmounted | detached
```

### 6.5 开发建议

1. 使用条件编译
```typescript
// #ifdef MP-WEIXIN
// 小程序特有代码
// #endif

// #ifndef MP-WEIXIN
// 非小程序代码
// #endif
```

2. 组件属性透传
```typescript
// 确保组件可以透传原生组件的属性
export interface ComponentProps extends WechatMiniprogram.Component.PropertyOption {
  // 自定义属性
}
```