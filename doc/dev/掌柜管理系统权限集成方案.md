# 掌柜管理系统权限集成方案（Code概念版）

## 核心理念

基于**Code概念**的权限系统，前端使用直观的编码而不是UUID，只需要4个核心接口就能完成所有权限管理功能：
1. 员工列表（含角色权限）
2. 角色列表  
3. 权限资源树（已过滤到模块级别）
4. 角色权限配置（一步完成创建+分配）

## 最新优化特性

### ✅ Code概念重构
- **参数优化**：所有接口参数从ID改为Code，使用直观编码如`admin`、`cashier`
- **自动转换**：服务层自动处理Code到ID的转换，对前端透明
- **向后兼容**：内部仍使用ID进行数据库操作，确保性能和一致性
- **错误处理**：当Code不存在时提供清晰的错误信息

### ✅ onlyMine筛选功能
- **业务系统隔离**：根据clientType过滤出只属于本系统的数据
- **最小化改动**：只在请求DTO中添加`onlyMine`字段
- **x-device解析**：自动解析请求头获取clientType
- **向后兼容**：当`onlyMine`为false或未设置时返回所有数据

### ✅ 权限层级简化
- **自动过滤**：权限资源树API已自动过滤页面级别（PAGE）和操作级别（OPERATION）的权限
- **只显示核心权限**：只返回系统级别（SYSTEM）和模块级别（MODULE）的权限
- **简化前端选择**：前端界面只需要选择到模块级别，大大简化用户操作

### ✅ 角色创建一步完成
- **一次API调用**：创建角色的同时分配权限，无需额外的权限分配步骤
- **自动生成编码**：角色编码可选，系统自动生成唯一标识
- **事务安全**：角色创建和权限分配在同一事务中完成

### ✅ 中文显示优化
- **Unicode问题解决**：所有API响应中的中文字符都能正确显示
- **无需前端处理**：前端直接使用API返回的中文内容，无需额外解码

### ✅ 技术优化
- **lo库使用**：使用`lo.Map`、`lo.Contains`等优化数据处理
- **分页标准化**：统一使用`util.GetItPtr`处理分页参数
- **state字段移除**：简化查询条件，移除不必要的state=0判断

## 接口调用方案

### 1. 员工管理页面

#### 1.1 获取员工列表（含角色权限信息）
```http
POST /api/employee/list
```

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",
  "pageNum": 1,
  "pageSize": 20,
  "name": "张三",           // 可选：员工姓名搜索
  "phone": "138****1234"   // 可选：手机号搜索
}
```

**响应数据：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "pageNum": 1,
    "pageSize": 20,
    "list": [
      {
        "id": "emp001",
        "name": "张三",
        "phone": "13812341234",
        "employeeNumber": "E001",
        "venueId": "94YTNnVUk",
        "permissionRole": "admin",        // 角色编码（Code概念）
        "roleName": "管理员",             // 角色名称
        "roleCode": "admin",             // 角色编码（用于分配）
        "permissionCount": 11,            // 权限数量
        "permissions": [                  // 权限代码列表
          "thunder_erp_manager",
          "thunder_erp_cashier"
        ]
      }
    ]
  }
}
```

**关键字段说明：**
- `roleCode`: 角色编码，用于角色分配操作（Code概念）
- `permissionRole`: 当前角色编码，显示用
- `roleName`: 当前角色名称，显示用
- `permissionCount`: 权限数量，显示用
- `permissions`: 具体权限列表，用于权限检查

#### 1.2 分配员工角色（基于Code）
```http
POST /api/employee/assign-role
```

**请求参数：**
```json
{
  "employeeId": "emp001",
  "roleCode": "admin",        // 使用角色编码而不是ID
  "venueId": "94YTNnVUk"
}
```

#### 1.3 批量分配员工角色（基于Code）
```http
POST /api/employee/batch-assign-roles
```

**请求参数：**
```json
{
  "employeeIds": ["emp001", "emp002"],
  "roleCodes": ["admin", "cashier"],    // 使用角色编码数组
  "venueId": "94YTNnVUk"
}
```

#### 1.4 撤销员工角色（基于Code）
```http
POST /api/employee/revoke-role
```

**请求参数：**
```json
{
  "employeeId": "emp001",
  "roleCode": "admin",        // 使用角色编码
  "venueId": "94YTNnVUk"
}
```

### 2. 角色管理页面

#### 2.1 获取角色列表（支持onlyMine）
```http
POST /api/permission-role/query
```

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",
  "pageNum": 1,
  "pageSize": 20,
  "name": "管理员",          // 可选：角色名称搜索
  "code": "admin",         // 可选：角色编码搜索
  "onlyMine": true         // 可选：只获取当前业务系统的角色
}
```

**请求头（使用onlyMine时）：**
```json
{
  "x-device": "{\"clientType\":\"thunder_erp_manager\",\"version\":\"1.0.0\",\"platform\":\"web\"}"
}
```

**响应数据：**
```json
{
  "code": 0,
  "message": "success", 
  "data": [
    {
      "id": "18xs6mKEE0",
      "code": "admin",                    // 角色编码（Code概念）
      "name": "管理员",
      "description": "系统管理员，拥有所有权限",
      "venueId": "94YTNnVUk",
      "isEnabled": true,
      "ctime": 1703123456789,
      "utime": 1703123456789
    },
    {
      "id": "22a9a33e1e334bc9b30681374e06f7bb",
      "code": "收银员角色_94YTNnVU",
      "name": "收银员角色",
      "description": "拥有收银端系统权限的收银员角色",
      "venueId": "94YTNnVUk",
      "isEnabled": true,
      "ctime": 1750062753,
      "utime": 1750062753
    }
  ]
}
```

**关键字段说明：**
- `code`: 角色编码，前端主要使用这个字段（Code概念）
- `name`: 角色名称，显示用
- `description`: 角色描述，显示用
- `id`: 角色ID，内部使用（兼容性保留）

#### 2.2 获取权限资源树（已过滤，支持onlyMine）
```http
POST /api/permission-resource/tree
```

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",    // 可选：门店ID
  "enabled": true,          // 只获取启用的权限
  "onlyMine": true          // 可选：只获取当前业务系统的权限
}
```

**请求头（使用onlyMine时）：**
```json
{
  "x-device": "{\"clientType\":\"thunder_erp_manager\",\"version\":\"1.0.0\",\"platform\":\"web\"}"
}
```

**响应数据：**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": "f68234e9538b455b813bdc381e352f02",
      "name": "掌柜系统",
      "code": "thunder_erp_manager",      // 权限编码（Code概念）
      "type": "SYSTEM",
      "typeName": "系统",
      "sort": 10,
      "description": "掌柜系统主模块，提供店铺管理、员工管理、数据统计等核心功能",
      "isEnabled": true,
      "level": 1,
      "hasChildren": true,
      "ctime": 1749693008,
      "utime": 1749693008,
      "children": [
        {
          "id": "f9e0e6d6d58548bfb047c3f3ea01a22d",
          "parentId": "f68234e9538b455b813bdc381e352f02",
          "name": "掌柜系统-管理tab",
          "code": "thunder_erp_manager.management",
          "type": "MODULE",
          "typeName": "模块",
          "sort": 12,
          "description": "掌柜系统管理功能模块，包含员工管理、权限设置等管理功能",
          "isEnabled": true,
          "level": 2,
          "hasChildren": true,
          "ctime": 1749718514,
          "utime": 1749718514,
          "children": [
            {
              "id": "f68234e9538b455b813bdc381e352f03",
              "parentId": "f9e0e6d6d58548bfb047c3f3ea01a22d",
              "name": "掌柜系统-管理tab-员工管理模块",
              "code": "thunder_erp_manager.management.employee",
              "type": "MODULE",
              "typeName": "模块",
              "sort": 13,
              "description": "员工管理模块，负责员工信息维护、角色分配等人员管理功能",
              "isEnabled": true,
              "level": 3,
              "hasChildren": true,
              "ctime": 1749653099,
              "utime": 1749653099,
              "children": [
                {
                  "id": "f9e0e6d6d58548bfb047c3f3ea01a37b",
                  "parentId": "f68234e9538b455b813bdc381e352f03",
                  "name": "掌柜系统-管理tab-员工管理模块-权限管理",
                  "code": "thunder_erp_manager.management.employee.permission",
                  "type": "MODULE",
                  "typeName": "模块",
                  "sort": 14,
                  "description": "权限管理功能，用于设置员工角色权限、管理系统访问控制",
                  "isEnabled": true,
                  "level": 4,
                  "hasChildren": false,
                  "ctime": 1749665815,
                  "utime": 1749665815
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "id": "f68234e9538b455b813bdc381e352f01",
      "name": "收银端系统",
      "code": "thunder_erp_cashier",
      "type": "SYSTEM",
      "typeName": "系统",
      "sort": 20,
      "description": "收银端系统，提供收银、订单管理等功能",
      "isEnabled": true,
      "level": 1,
      "hasChildren": true,
      "ctime": 1749693008,
      "utime": 1749693008,
      "children": [
        {
          "id": "f68234e9538b455b813bdc381e352f65",
          "parentId": "f68234e9538b455b813bdc381e352f01",
          "name": "收银端系统-登录",
          "code": "thunder_erp_cashier.login",
          "type": "SYSTEM",
          "typeName": "系统",
          "sort": 21,
          "description": "收银端系统登录权限，允许用户访问收银端系统",
          "isEnabled": true,
          "level": 2,
          "hasChildren": false,
          "ctime": 1749665815,
          "utime": 1749665815
        }
      ]
    }
  ]
}
```

**重要说明：**
- **权限层级过滤**：API已自动过滤页面级别（PAGE）和操作级别（OPERATION）的权限，只返回系统级别（SYSTEM）和模块级别（MODULE）的权限，简化前端选择
- **中文显示正常**：所有中文字符都能正确显示，不会被转义成Unicode编码
- **Code概念**：每个权限都有对应的编码，前端主要使用`code`字段

**关键字段说明：**
- `code`: 权限编码，前端权限检查和分配使用（Code概念）
- `name`: 权限名称，显示用（中文正常显示）
- `type`: 权限类型（SYSTEM/MODULE，已过滤PAGE/OPERATION）
- `level`: 权限层级（1-4级）
- `hasChildren`: 是否有子权限
- `children`: 子权限，递归结构

#### 2.3 创建角色并分配权限（一步完成，基于Code）
```http
POST /api/permission-role/add
```

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",
  "name": "店长角色",
  "code": "store_manager",         // 可选：前端留空由后端自动生成
  "description": "拥有掌柜系统完整权限的店长角色",
  "permissions": [                 // 选中的权限编码列表（Code概念）
    "thunder_erp_manager",
    "thunder_erp_manager.management",
    "thunder_erp_manager.management.employee",
    "thunder_erp_manager.management.employee.permission"
  ]
}
```

**说明：**
1. **Code概念权限分配**：`permissions`数组现在使用权限编码而不是UUID
2. **自动权限分配**：后端在创建角色成功后，会自动调用权限分配逻辑
3. **Code字段自动生成**：如果不传`code`字段，系统会自动生成格式为`{角色名称}_{门店ID前8位}`的唯一编码
4. **可选权限分配**：如果不想立刻分配权限，`permissions`字段可以不传或传空数组
5. **事务安全**：角色创建和权限分配在同一事务中完成，确保数据一致性

**响应数据：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "55eb722a762e418fbf07695cb42fff5a",
    "code": "店长角色_94YTNnVU",           // 自动生成的角色编码
    "name": "店长角色",
    "venueId": "94YTNnVUk",
    "description": "拥有掌柜系统完整权限的店长角色",
    "isDefault": false,
    "isSystemRole": false,
    "isEnabled": true,
    "sortOrder": 0,
    "ctime": 1750062753,
    "utime": 1750062753,
    "state": 0,
    "version": 0
  },
  "requestID": "751458bc-4691-4503-94ad-37c57a46af55",
  "serverTime": 1750062754
}
```

**实际测试示例：**
```bash
# 创建店长角色（基于Code）
curl -X POST http://localhost:18501/api/permission-role/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "venueId": "94YTNnVUk",
    "name": "店长角色",
    "description": "拥有掌柜系统完整权限的店长角色",
    "permissions": [
      "thunder_erp_manager",
      "thunder_erp_manager.management",
      "thunder_erp_manager.management.employee",
      "thunder_erp_manager.management.employee.permission"
    ]
  }'

# 创建收银员角色（基于Code）
curl -X POST http://localhost:18501/api/permission-role/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "venueId": "94YTNnVUk",
    "name": "收银员角色",
    "description": "拥有收银端系统权限的收银员角色",
    "permissions": [
      "thunder_erp_cashier",
      "thunder_erp_cashier.login"
    ]
  }'
```

### 3. 权限分配管理（基于Code）

#### 3.1 分配角色权限（基于Code）
```http
POST /api/permission/role/assign-permissions
```

**请求参数：**
```json
{
  "roleCode": "admin",              // 角色编码（Code概念）
  "permissionCodes": [              // 权限编码列表（Code概念）
    "thunder_erp_manager",
    "thunder_erp_cashier"
  ],
  "venueId": "94YTNnVUk",
  "operatorId": "test_operator",
  "operatorName": "测试操作员",
  "reason": "权限调整"
}
```

#### 3.2 撤销角色权限（基于Code）
```http
POST /api/permission/role/revoke-permissions
```

**请求参数：**
```json
{
  "roleCode": "admin",              // 角色编码（Code概念）
  "permissionCodes": [              // 权限编码列表（Code概念）
    "thunder_erp_manager"
  ],
  "venueId": "94YTNnVUk",
  "operatorId": "test_operator",
  "operatorName": "测试操作员",
  "reason": "权限回收"
}
```

#### 3.3 复制角色权限（基于Code）
```http
POST /api/permission/role/copy
```

**请求参数：**
```json
{
  "sourceRoleCode": "admin",        // 源角色编码（Code概念）
  "targetRoleCode": "manager",      // 目标角色编码（Code概念）
  "venueId": "94YTNnVUk",
  "operatorId": "test_operator",
  "operatorName": "测试操作员",
  "reason": "权限复制"
}
```

#### 3.4 验证员工权限（基于Code）
```http
POST /api/permission/role/validate
```

**请求参数：**
```json
{
  "employeeId": "emp001",
  "permissionCode": "thunder_erp_manager",  // 权限编码（Code概念）
  "venueId": "94YTNnVUk"
}
```

## 调用时序图

### 员工管理页面时序（Code概念版）

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API服务
    participant D as 数据库

    Note over F,D: 员工管理页面初始化
    F->>A: POST /api/employee/list
    A->>D: 查询员工+角色+权限
    D-->>A: 返回员工数据（含roleCode）
    A-->>F: 员工列表（含角色权限Code）

    Note over F,D: 用户点击更换角色
    F->>A: POST /api/permission-role/query
    A->>D: 查询可用角色
    D-->>A: 返回角色列表（含code字段）
    A-->>F: 角色选择列表（Code概念）

    Note over F,D: 用户选择角色并确认
    F->>A: POST /api/employee/assign-role
    Note right of F: 使用roleCode而不是roleId
    A->>D: 根据roleCode查找roleId并更新
    D-->>A: 更新成功
    A-->>F: 分配结果

    Note over F,D: 刷新员工列表
    F->>A: POST /api/employee/list
    A->>D: 查询最新员工数据
    D-->>A: 返回更新后数据
    A-->>F: 最新员工列表
```

### 角色管理页面时序（Code概念版）

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API服务
    participant D as 数据库

    Note over F,D: 角色管理页面初始化
    par 并行加载
        F->>A: POST /api/permission-role/query
        A->>D: 查询角色列表
        D-->>A: 返回角色数据（含code字段）
        A-->>F: 角色列表（Code概念）
    and
        F->>A: POST /api/permission-resource/tree
        A->>D: 查询权限资源树
        D-->>A: 返回权限树（含code字段）
        A-->>F: 权限资源树（Code概念）
    end

    Note over F,D: 用户点击添加角色
    F->>A: POST /api/permission-role/add
    Note right of F: permissions数组使用权限编码
    A->>D: 创建角色+根据编码分配权限
    D-->>A: 创建成功
    A-->>F: 新角色信息（含生成的code）

    Note over F,D: 刷新角色列表
    F->>A: POST /api/permission-role/query
    A->>D: 查询最新角色
    D-->>A: 返回角色列表
    A-->>F: 最新角色列表

    Note over F,D: 用户修改权限并保存
    F->>A: POST /api/permission/role/assign-permissions
    Note right of F: 使用roleCode和permissionCodes
    A->>D: 根据编码更新角色权限
    D-->>A: 更新成功
    A-->>F: 更新结果
```

## 数据流转说明

### 权限数据结构（Code概念版）

```mermaid
erDiagram
    Employee ||--o{ EmployeeRoleAssignment : "员工角色分配"
    PermissionRole ||--o{ EmployeeRoleAssignment : "角色分配"
    PermissionRole ||--o{ RolePermission : "角色权限"
    PermissionResource ||--o{ RolePermission : "权限资源"
    
    Employee {
        string id "员工ID"
        string name "员工姓名"
        string venueId "门店ID"
        string permissionRole "主要角色编码(Code)"
    }
    
    PermissionRole {
        string id "角色ID(内部使用)"
        string code "角色编码(Code概念)"
        string name "角色名称"
        string description "角色描述"
        string venueId "门店ID"
    }
    
    PermissionResource {
        string id "权限资源ID(内部使用)"
        string code "权限编码(Code概念)"
        string name "权限名称"
        string type "权限类型"
        string parentId "父级ID"
    }
    
    EmployeeRoleAssignment {
        string employeeId "员工ID"
        string roleId "角色ID(内部使用)"
        string venueId "门店ID"
        bool isActive "是否激活"
    }
    
    RolePermission {
        string roleId "角色ID(内部使用)"
        string resourceId "权限资源ID(内部使用)"
        string venueId "门店ID"
        bool isEnabled "是否启用"
    }
```

### 关键字段映射（Code概念版）

| 接口字段 | 数据库字段 | 说明 | 用途 |
|---------|-----------|------|------|
| `roleCode` | `permission_role.code` | 角色编码 | 员工角色分配、权限配置（主要使用） |
| `permissionCode` | `permission_resource.code` | 权限编码 | 权限分配、前端权限检查 |
| `roleName` | `permission_role.name` | 角色名称 | 显示 |
| `permissions[]` | `permission_resource.code` | 权限编码列表 | 前端权限检查、权限分配 |
| `permissionCodes[]` | `permission_resource.code` | 权限编码列表 | 权限分配 |
| `permissionCount` | 计算字段 | 权限数量 | 显示统计 |

### Code概念优势

1. **用户体验提升**：
   - 使用直观的编码如`admin`、`cashier`而不是UUID
   - 便于前端开发和调试
   - 减少错误和混淆

2. **向后兼容**：
   - 内部仍使用ID进行数据库操作
   - 确保性能和一致性
   - 平滑迁移

3. **自动转换**：
   - 服务层透明处理Code到ID的转换
   - 前端无需关心内部实现
   - 错误处理清晰

### API调用规则（Code概念版）

1. **所有接口都使用POST请求**
2. **统一的请求头**：
   ```
   Content-Type: application/json
   Authorization: Bearer {token}
   ```

3. **统一的响应格式**：
   ```json
   {
     "code": 0,           // 0=成功，非0=失败
     "message": "success", // 消息
     "data": {}           // 数据
   }
   ```

4. **Code概念参数**：
   - `roleCode`: 角色编码（如：admin、cashier、manager）
   - `permissionCode`: 权限编码（如：thunder_erp_manager）
   - `permissionCodes`: 权限编码数组

5. **分页参数**：
   - `pageNum`: 页码（从1开始）
   - `pageSize`: 页大小（默认20）

6. **门店参数**：
   - `venueId`: 必传，用于数据隔离

7. **权限检查**：
   - 使用`permissions`数组中的权限编码
   - 格式：`thunder_erp_manager.management.employee`

## 实现要点

### 1. Code概念处理
- 前端提交时使用编码（如：`admin`、`thunder_erp_manager`）
- 服务层自动转换为ID进行数据库操作
- 响应时返回编码供前端使用
- 错误时提供清晰的编码相关错误信息

### 2. 权限树处理
- 权限资源按5级层次组织：系统→页面→模块→操作→数据
- 前端提交时使用`permissionCodes`数组（包含所有选中的权限编码）
- 显示时使用权限编码进行标记和选择

### 3. 员工角色分配
- 使用`roleCode`进行分配（不是`roleId`）
- 分配后自动更新员工的缓存字段
- 支持单角色模式（简化业务逻辑）

### 4. 数据一致性
- 角色权限变更后，相关员工权限自动更新
- 权限资源禁用后，相关角色权限自动失效
- 所有操作都有事务保护
- Code到ID的转换在事务内完成

### 5. 性能优化
- 员工列表接口已包含角色权限信息，减少额外查询
- 权限树支持缓存，提高响应速度
- 分页查询避免大数据量问题
- Code查询优化，建立相应索引

## 数据示例

### 角色数据示例
```json
{
  "id": "182e9773eaf049409705a337f0905861",
  "code": "测试店长角色_94YTNnVU",
  "name": "测试店长角色"
}
```

### 权限资源数据示例
```json
{
  "id": "f68234e9538b455b813bdc381e352f02",
  "code": "thunder_erp_manager",
  "name": "掌柜系统",
  "type": "SYSTEM"
}
```

### 角色权限关联示例
```json
{
  "roleCode": "收银员角色_94YTNnVU",
  "permissionCode": "thunder_erp_cashier"
}
```

这个基于Code概念的方案优势是接口少、逻辑清晰、数据完整，前端开发者使用直观的编码而不是复杂的UUID，大大提升了开发体验和系统可维护性。 