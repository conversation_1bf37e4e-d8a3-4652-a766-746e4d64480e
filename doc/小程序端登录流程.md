flowchart TD
    %% 入口
    Start[小程序启动] --> GetWxCode[获取微信Code]
    GetWxCode --> CheckParams{检查页面参数}

    %% 场景分支
    CheckParams -->|无参数| NormalFlow[普通登录流程]
    CheckParams -->|venue_id| QRFlow[扫码登录流程]
    CheckParams -->|sceneStr| StoreFlow[门店绑定流程]

    %% 1. 普通登录流程
    NormalFlow --> CallLogin[调用/login接口]
    CallLogin --> CheckLoginRes{检查响应}
    CheckLoginRes -->|成功| SaveToken1[保存Token]
    SaveToken1 --> EnterApp[进入应用]
    CheckLoginRes -->|失败| ShowError[显示错误]

    %% 2. 扫码登录流程
    QRFlow --> CallClient[调用/loginClient接口]
    CallClient --> CheckClientRes{检查响应}
    CheckClientRes -->|成功| SaveToken2[保存Token]
    SaveToken2 --> SyncToken[同步Token到收银台]
    SyncToken --> EnterApp
    CheckClientRes -->|失败| ShowError

    %% 3. 门店绑定流程
    StoreFlow --> CallScene[调用/loginScene接口]
    CallScene --> CheckSceneRes{检查绑定状态}
    
    CheckSceneRes -->|未绑定| InputPhone[输入手机号]
    InputPhone --> Agreement{同意协议}
    Agreement -->|同意| CreateStore[创建门店]
    CreateStore --> SaveToken3[保存Token]
    
    CheckSceneRes -->|已绑定| SaveToken3
    SaveToken3 --> CallStoreAPI[调用门店API]
    CallStoreAPI --> CheckStoreRes{检查门店状态}
    CheckStoreRes -->|成功| EnterStore[进入门店]
    CheckStoreRes -->|失败| ShowError
    
    %% 统一错误处理
    ShowError --> RetryLogin[重试登录]
    RetryLogin --> GetWxCode

    %% 样式定义
    classDef start fill:#9f9,stroke:#333,stroke-width:2px;
    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef decision fill:#bbf,stroke:#333,stroke-width:2px;
    classDef endpoint fill:#f99,stroke:#333,stroke-width:2px;
    
    class Start start;
    class CheckParams,CheckLoginRes,CheckClientRes,CheckSceneRes,Agreement,CheckStoreRes decision;
    class EnterApp,EnterStore,ShowError endpoint;
    class GetWxCode,CallLogin,CallClient,CallScene,CallStoreAPI process;