import { readFileSync, writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';

// 读取swagger文档
function readSwaggerDoc() {
  try {
    const data = readFileSync('./doc.json', 'utf8');
    return JSON.parse(data);
  } catch (err) {
    console.error('Error reading swagger file:', err);
    process.exit(1);
  }
}

// 按模块分组API路径
function groupApisByModule(paths) {
  const modules = {};
  
  for (const [path, methods] of Object.entries(paths)) {
    // 提取模块名 /api/module-name/xxx => module-name
    const matches = path.match(/^\/api\/([^\/]+)/);
    if (!matches) continue;
    
    const moduleName = matches[1];
    
    if (!modules[moduleName]) {
      modules[moduleName] = {
        paths: {}
      };
    }
    
    modules[moduleName].paths[path] = methods;
  }
  
  return modules;
}

// 保存模块文件
function saveModuleFiles(swagger, modules, outputDir) {
  if (!existsSync(outputDir)) {
    mkdirSync(outputDir, { recursive: true });
  }
  
  for (const [moduleName, moduleData] of Object.entries(modules)) {
    // 收集该模块用到的所有 definitions
    const usedDefinitions = {};
    
    // 遍历该模块的所有接口
    for (const [path, methods] of Object.entries(moduleData.paths)) {
      // 遍历接口的所有方法
      for (const method of Object.values(methods)) {
        // 检查请求参数中的 schema 引用
        if (method.parameters) {
          for (const param of method.parameters) {
            if (param.schema && param.schema.$ref) {
              collectDefinitions(param.schema.$ref, swagger.definitions, usedDefinitions);
            }
          }
        }
        
        // 检查响应中的 schema 引用
        if (method.responses) {
          for (const response of Object.values(method.responses)) {
            if (response.schema && response.schema.$ref) {
              collectDefinitions(response.schema.$ref, swagger.definitions, usedDefinitions);
            }
          }
        }
      }
    }

    const moduleSwagger = {
      ...swagger,
      paths: moduleData.paths,
      definitions: usedDefinitions
    };
    
    const outputPath = join(outputDir, `${moduleName}.json`);
    
    try {
      writeFileSync(
        outputPath,
        JSON.stringify(moduleSwagger, null, 2),
        'utf8'
      );
      console.log(`Created module file: ${outputPath}`);
    } catch (err) {
      console.error(`Error writing module file ${moduleName}:`, err);
    }
  }
}

// 递归收集引用的 definitions
function collectDefinitions(ref, allDefinitions, collectedDefinitions) {
  // 从 "#/definitions/xxx" 提取 definition 名称
  const defName = ref.replace('#/definitions/', '');
  
  // 如果已经收集过，则跳过
  if (collectedDefinitions[defName]) return;
  
  // 获取 definition 对象
  const definition = allDefinitions[defName];
  if (!definition) return;
  
  // 添加到收集结果中
  collectedDefinitions[defName] = definition;
  
  // 递归处理该 definition 中的引用
  for (const prop of Object.values(definition.properties || {})) {
    // 处理直接引用
    if (prop.$ref) {
      collectDefinitions(prop.$ref, allDefinitions, collectedDefinitions);
    }
    // 处理数组项中的引用
    if (prop.items && prop.items.$ref) {
      collectDefinitions(prop.items.$ref, allDefinitions, collectedDefinitions);
    }
    // 处理 allOf 中的引用
    if (prop.allOf) {
      for (const item of prop.allOf) {
        if (item.$ref) {
          collectDefinitions(item.$ref, allDefinitions, collectedDefinitions);
        }
      }
    }
  }
}

function main() {
  const inputFile = './doc.json';
  const outputDir = './allapi/';

  // 读取swagger文档
  const swagger = readSwaggerDoc(inputFile);
  
  // 按模块分组API
  const modules = groupApisByModule(swagger.paths);
  
  // 保存模块文件
  saveModuleFiles(swagger, modules, outputDir);
}

main();