# 收银台用户验证和交互流程

## 1. 基础验证流程

### 1.1 MAC地址验证
- 请求参数：req.mac, req.token（包含unionid、手机号）
- 验证流程：
  - 查询MAC记录
    - 存在记录：检查venueId
      - 有venueId -> 进入登录流程
      - 无venueId -> 获取scene_str，跳转公众号
    - 不存在记录：创建MAC记录并生成关注二维码（scene_str）
![alt text](image.png)
```mermaid
flowchart TD
    A[收银台请求] --> B{检查MAC记录}
    B -->|存在| C{检查venueId}
    B -->|不存在| D[创建MAC记录]
    D --> E[生成二维码]
    C -->|存在| F[登录流程]
    C -->|不存在| E
```

### 1.2 登录验证
- Token状态检查：
  - 无token -> 返回407
  - token过期 -> 返回407
  - token有效 -> 解析获取手机号、unionid
    - 验证employee表对应关系
      - 存在 -> 更新token+权限+门店信息
      - 不存在 -> 返回401
![alt text](image-1.png)
```mermaid
flowchart TD
    A[开始登录验证] --> B{检查Token}
    B -->|无token| C[返回407]
    B -->|token过期| C
    B -->|token有效| D[解析token]
    D --> E{验证employee关系}
    E -->|存在| F[更新token和权限]
    E -->|不存在| G[返回401]
```

## 2. 小程序验证流程

### 2.1 无scene_str情况
- 有token
  - 过期 -> 返回407，进入登录流程
  - 未过期 -> 验证unionid
    - 存在对应关系 -> 返回门店id
    - 不存在对应关系 -> 返回401，进入创建门店流程
- 无token -> 返回407，进入登录流程
![alt text](image-2.png)
```mermaid
flowchart TD
    A[小程序请求] --> B{检查token}
    B -->|无token| C[返回407]
    B -->|token过期| C
    B -->|token有效| D{验证unionid}
    D -->|存在对应关系| E[返回门店id]
    D -->|不存在对应关系| F[返回401]
    F --> G[进入创建门店流程]
```

### 2.2 有scene_str情况

场景说明：
- scene_str从小程序启动参数中获取，表示从收银台扫码进入
- token中可能包含unionId信息
- unionId可用于关联用户手机号

处理流程：
1. 解析scene_str获取MAC地址
2. 检查token中的unionId
3. 根据unionId状态决定后续流程
![alt text](image-6.png)
```mermaid
flowchart TD
    A[小程序启动] --> B[获取scene_str]
    B --> C[解析MAC地址]
    
    C --> D{检查token}
    
    D -->|有token| E{解析unionId}
    D -->|无token| F[获取unionId流程]
    
    E -->|解析成功| G{查找手机号}
    E -->|解析失败| F
    
    G -->|找到手机号| H[直接登录]
    G -->|未找到手机号| F
    
    F --> I[wx.login获取code]
    I --> J[获取unionId]
    J --> K[获取手机号]
    
    K --> L[绑定关系]
    L --> M[生成新token]
    M --> N[登录成功]
    
    H --> N
```

关键步骤说明：

1. 快速登录路径：
   ```
   token -> unionId -> 手机号 -> 直接登录
   ```

2. 完整验证路径：
   ```
   wx.login -> unionId -> 手机号 -> 绑定 -> 登录
   ```

3. token验证逻辑：
   - 有token且能解析出unionId：
     - 找到手机号 -> 直接登录
     - 未找到手机号 -> 获取手机号
   - 无token或解析失败：
     - 进入完整的unionId和手机号获取流程

4. 绑定关系处理：
   - MAC地址与unionId关联
   - unionId与手机号关联
   - 生成新的包含完整信息的token

状态判断说明：
1. token状态：
   ```
   有token -> 尝试解析unionId
   无token -> 获取新unionId
   ```

2. unionId状态：
   ```
   解析成功 -> 查找手机号
   解析失败 -> 重新获取
   ```

3. 手机号状态：
   ```
   找到手机号 -> 直接登录
   未找到手机号 -> 获取手机号
   ```

注意事项：
- token中的unionId必须是有效的
- 手机号必须与unionId有正确的关联关系
- 新token需要包含完整的unionId和手机号信息
- MAC地址绑定关系需要维护
- 需要处理各种异常情况（解析失败、查询失败等）

### 2.3 门店绑定流程
1. 获取用户手机号
2. 查询已有门店列表
3. 执行绑定操作：
   - 更新对应关系
   - 更新employee表权限
![alt text](image-4.png)
```mermaid
flowchart TD
    A[开始绑定] --> B[获取用户手机号]
    B --> C[查询已有门店列表]
    C --> D[选择门店]
    D --> E[更新对应关系]
    E --> F[更新employee表权限]
    F --> G[返回token]
```

### 2.4 门店创建流程
1. 获取用户手机号
2. 创建新门店
3. 建立绑定关系
![alt text](image-5.png)
```mermaid
flowchart TD
    A[开始创建] --> B[获取用户手机号]
    B --> C[创建新门店]
    C --> D[建立绑定关系]
    D --> E[返回token]
```

## 注意事项
1. 所有token过期情况统一返回407
2. 未授权或无权限统一返回401
3. MAC地址作为设备唯一标识
4. 门店创建/绑定需要手机号验证
5. unionid用于微信用户唯一标识
