```mermaid
flowchart TD
    subgraph 初始化阶段
        subgraph 小程序端[老板]
            A1[手机号注册] --> A2[登录账号]
            A2 --> A[创建门店]
            A --> B[提交商务审核]
            B --> C[配置门店信息]
            C --> D[生成员工邀请二维码]
            R --> E[审核管理员加入]
        end
        
        subgraph 商务后台[商务人员]
            B --> F{资质审核}
            F -->|通过| G[激活试用授权码]
            G --> H[签订合同]
            H --> I[生成正式授权码]
            I --> J[授权码交付老板]
        end
        
        subgraph 服务端
            A --> K[创建试用授权码]
            K --> L[设置未激活状态]
            G --> M[激活试用授权码]
            M --> N[收银端可用]
            J --> O[老板输入正式授权码]
            O --> P[激活正式授权码]
        end
        
        subgraph 小程序端[管理员]
            D --> Q[管理员扫码接受邀请]
            Q --> R[等待老板审核]
            R --> S[审核通过，加入门店]
        end
    end

    subgraph 日常运营
        subgraph 员工管理
            S --> T{权限分配}
            T -->|管理员| U[继续配置]
            T -->|收银员| V[收银机登录]
            T -->|服务员| W[移动点单权限]
            T -->|出品员| X[出品确认权限]
        end
        
        subgraph 收银机端[收银员]
            V --> Y[小程序账号登录]
            Y --> Z[开台选择包厢]
            Z --> AA[关联移动点单]
            AA --> AB[支付结算]
            AB --> AC[交班清账]
        end
        
        subgraph 移动点单端[服务员]
            W --> AD[小程序账号登录]
            AD --> AE[实时点单/加单]
            AE --> AF[同步到出品端]
        end
        
        subgraph 出品端
            X --> AG[小程序账号登录]
            AF --> AH{出品方式}
            AH -->|自动| AI[直接打印小票]
            AH -->|手动| AJ[收银机确认完成]
        end
        
        subgraph 经营分析
            AC --> AK[店长查看日报]
            AK --> AL[调整运营策略]
            AM[老板查看汇总报表]
        end
    end

    style A fill:#FFD700,stroke:#333
    style F fill:#87CEEB,stroke:#333
    style N fill:#4B5582,stroke:#fff
    style T fill:#FFB6C1,stroke:#333
    style U fill:#98FB98,stroke:#333
    style X fill:#FFA500,stroke:#333
    style AK fill:#9370DB,stroke:#333
```

```mermaid
sequenceDiagram
    participant 老板小程序
    participant 管理员小程序
    participant 商务后台
    participant 服务端
    participant 收银机
    participant 移动点单
    participant 出品端

    %% 初始化阶段 %%
    rect rgb(255,215,0,0.1)
        老板小程序->>服务端: 手机号注册
        老板小程序->>服务端: 登录账号
        老板小程序->>服务端: 创建门店请求
        服务端->>服务端: 创建试用授权码(未激活)
        服务端-->>商务后台: 推送审核任务
        商务后台->>服务端: 审批通过
        服务端->>服务端: 激活试用授权码
        商务后台->>商务后台: 签订合同
        商务后台->>商务后台: 生成正式授权码
        商务后台->>老板小程序: 交付正式授权码
        老板小程序->>服务端: 输入正式授权码
        服务端->>服务端: 激活正式授权码
    end

    rect rgb(75,0,130,0.1)
        老板小程序->>服务端: 生成员工邀请二维码
        服务端-->>老板小程序: 返回二维码
        管理员小程序->>服务端: 扫码接受邀请
        服务端-->>老板小程序: 推送审核请求
        老板小程序->>服务端: 审核通过
        服务端-->>管理员小程序: 加入门店成功
        老板小程序->>服务端: 分配管理员权限
        管理员小程序->>服务端: 配置包厢/商品
        管理员小程序->>服务端: 生成员工邀请二维码
        服务端-->>管理员小程序: 返回二维码
        员工小程序->>服务端: 扫码接受邀请
        服务端-->>管理员小程序: 推送审核请求
        管理员小程序->>服务端: 审核通过
        服务端-->>员工小程序: 加入门店成功
        管理员小程序->>服务端: 分配员工角色权限
    end

    rect rgb(152,251,152,0.1)
        收银机->>服务端: 小程序账号登录
        服务端-->>收银机: 验证权限并返回门店配置
        移动点单->>服务端: 小程序账号登录
        服务端-->>移动点单: 验证权限并返回点单权限
        出品端->>服务端: 小程序账号登录
        服务端-->>出品端: 验证权限并返回出品权限
    end

    %% 日常营业 %%
    rect rgb(255,182,193,0.1)
        收银机->>服务端: 开台(包厢A)
        服务端-->>移动点单: 同步开台信息
    end

    rect rgb(147,112,219,0.1)
        移动点单->>服务端: 提交点单数据
        服务端-->>出品端: 推送出品信息
        出品端->>服务端: 出品状态更新
    end

    rect rgb(255,165,0,0.1)
        服务端->>收银机: 更新消费金额
        收银机->>服务端: 发起支付请求
        服务端->>服务端: 调用支付网关
        服务端-->>收银机: 返回支付结果
        收银机->>服务端: 关台结算
    end

    rect rgb(128,0,128,0.1)
        服务端->>服务端: 生成营业数据
        服务端-->>管理员小程序: 推送日报(店长)
        服务端-->>老板小程序: 推送总报表
    end

    note left of 老板小程序: 颜色标识：<br>黄：老板<br>蓝：商务<br>紫：管理员<br>粉：收银员<br>绿：服务员<br>橙：出品员<br>紫：店长
```

**关键更新说明：**
1. 修改管理员加入流程：老板生成二维码，管理员扫码接受邀请
2. 增加老板审核环节，管理员需等待审核通过
3. 明确服务端在管理员加入流程中的协调作用