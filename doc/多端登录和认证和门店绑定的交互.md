# 多端登录认证和门店绑定流程图

## 1. 员工登录流程

```mermaid
sequenceDiagram
    participant C as 收银台
    participant S as 服务端
    participant M as 小程序

    C->>S: 检查token
    alt token有效
        S-->>C: 返回用户信息
    else token无效/过期
        S-->>C: 返回需要重新登录
        C->>S: 请求生成微信二维码
        S-->>C: 返回微信二维码
        Note over C,M: 用户扫描二维码
        M->>S: 授权获取unionId
        S->>S: 生成新token
        S-->>M: 返回token
        M-->>C: 同步token
    end
```

## 2. 员工注册流程

```mermaid
sequenceDiagram
    participant C as 收银台
    participant S as 服务端
    participant M as 小程序

    M->>S: 微信授权登录
    S->>S: 获取unionId
    alt 已注册用户
        S-->>M: 返回已有用户信息
    else 未注册用户
        S-->>M: 返回需要注册
        M->>M: 填写注册信息(手机号等)
        M->>S: 提交注册信息
        S->>S: 创建用户账号
        S-->>M: 返回注册成功
    end
```

## 3. 绑定门店流程

```mermaid
sequenceDiagram
    participant C as 收银台
    participant S as 服务端
    participant M as 小程序
    participant U as 用户微信

    C->>S: 上报MAC地址和token
    alt MAC已绑定
        S-->>C: 返回门店信息
    else MAC未绑定
        S-->>C: 返回公众号二维码(带sceneStr)
        Note over C,U: 用户扫描二维码
        U->>S: 关注公众号
        S->>U: 推送小程序码
        Note over U,M: 用户点击进入小程序
        M->>S: 上报code和sceneStr
        S->>S: 验证身份和获取信息
        S-->>M: 返回用户和门店相关信息
        
        alt 场景1: 有手机号+unionId+门店Id+老板手机号
            Note over M: 检查手机号匹配
            M->>M: 直接进入门店管理页面
        else 场景2: 有手机号+unionId，无门店信息
            M->>M: 进入创建门店流程
            M->>S: 提交门店信息
            S->>S: 创建门店并绑定MAC
            S-->>M: 返回创建成功
        else 场景3: 有unionId，无手机号和门店信息
            M->>M: 获取/填写手机号
            alt 自动获取手机号
                M->>S: 通过unionId获取手机号
                S-->>M: 返回手机号
            else 手动填写手机号
                M->>M: 用户填写手机号
            end
            M->>M: 进入创建门店流程
            M->>S: 提交门店和手机号信息
            S->>S: 创建门店并绑定MAC
            S-->>M: 返回创建成功
        end
    end
```

这三个流程图分别展示了：

1. 员工登录流程：包含token验证和重新登录的场景
2. 员工注册流程：新用户注册的完整流程
3. 绑定门店流程：包含MAC地址验证、扫码、创建门店等完整流程

每个流程图都清晰地展示了收银台、服务端和小程序三端之间的交互关系。使用不同的分支（alt）来表示不同的场景处理方式。