-- 库存盘点记录表结构
-- 创建时间：2024-01-20
-- 说明：用于记录库存盘点的主记录和明细记录

-- 盘点记录主表
CREATE TABLE IF NOT EXISTS `inventory_check_record` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `venue_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '所属门店ID',
    `handler` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '经手人',
    `operator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '操作人',
    `time` INT NOT NULL DEFAULT 0 COMMENT '盘点时间（Unix时间戳）',
    `record_number` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '盘点单号',
    `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `profit_quantity_total` INT NOT NULL DEFAULT 0 COMMENT '盘盈数量合计',
    `loss_quantity_total` INT NOT NULL DEFAULT 0 COMMENT '盘亏数量合计',
    `ctime` INT NOT NULL DEFAULT 0 COMMENT '创建时间（Unix时间戳）',
    `utime` INT NOT NULL DEFAULT 0 COMMENT '更新时间（Unix时间戳）',
    `state` INT NOT NULL DEFAULT 0 COMMENT '状态（0:正常 1:删除）',
    `version` INT NOT NULL DEFAULT 0 COMMENT '版本号',
    PRIMARY KEY (`id`),
    INDEX `idx_venue_id` (`venue_id`),
    INDEX `idx_record_number` (`record_number`),
    INDEX `idx_time` (`time`),
    INDEX `idx_state` (`state`),
    INDEX `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存盘点记录主表';

-- 盘点记录明细表
CREATE TABLE IF NOT EXISTS `inventory_check_record_item` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `check_record_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '盘点记录ID',
    `product_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '商品ID',
    `stock_quantity` INT NOT NULL DEFAULT 0 COMMENT '库存数量（盘点之前）',
    `check_quantity` INT NOT NULL DEFAULT 0 COMMENT '盘点数量（盘点之后）',
    `profit_loss_quantity` INT NOT NULL DEFAULT 0 COMMENT '盈亏数量（盘点数量-库存数量）',
    `ctime` INT NOT NULL DEFAULT 0 COMMENT '创建时间（Unix时间戳）',
    `utime` INT NOT NULL DEFAULT 0 COMMENT '更新时间（Unix时间戳）',
    `state` INT NOT NULL DEFAULT 0 COMMENT '状态（0:正常 1:删除）',
    PRIMARY KEY (`id`),
    INDEX `idx_check_record_id` (`check_record_id`),
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_state` (`state`),
    INDEX `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存盘点记录明细表';

-- 添加外键约束（可选，根据项目需要决定是否启用）
-- ALTER TABLE `inventory_check_record_item` 
-- ADD CONSTRAINT `fk_check_record_item_record_id` 
-- FOREIGN KEY (`check_record_id`) REFERENCES `inventory_check_record`(`id`) 
-- ON DELETE CASCADE ON UPDATE CASCADE;

-- 插入测试数据（可选）
-- INSERT INTO `inventory_check_record` (
--     `id`, `venue_id`, `handler`, `operator`, `time`, `record_number`, 
--     `remark`, `profit_quantity_total`, `loss_quantity_total`, 
--     `ctime`, `utime`, `state`, `version`
-- ) VALUES (
--     'test_record_001', 'venue_001', '张三', '李四', 
--     UNIX_TIMESTAMP(), 'CHECK20240120001', '测试盘点记录', 
--     5, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 0
-- );

-- INSERT INTO `inventory_check_record_item` (
--     `id`, `check_record_id`, `product_id`, `stock_quantity`, 
--     `check_quantity`, `profit_loss_quantity`, `ctime`, `utime`, `state`
-- ) VALUES 
-- ('test_item_001', 'test_record_001', 'product_001', 100, 105, 5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- ('test_item_002', 'test_record_001', 'product_002', 50, 47, -3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
-- ('test_item_003', 'test_record_001', 'product_003', 30, 30, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 查询验证
-- SELECT 
--     r.id,
--     r.record_number,
--     r.handler,
--     r.operator,
--     FROM_UNIXTIME(r.time) as check_time,
--     r.profit_quantity_total,
--     r.loss_quantity_total,
--     COUNT(i.id) as item_count
-- FROM inventory_check_record r
-- LEFT JOIN inventory_check_record_item i ON r.id = i.check_record_id AND i.state = 0
-- WHERE r.state = 0
-- GROUP BY r.id
-- ORDER BY r.time DESC;
