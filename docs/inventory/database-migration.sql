-- 盘点记录功能数据库迁移脚本
-- 创建时间：2024-01-20
-- 版本：v1.0

-- 创建盘点记录主表
CREATE TABLE IF NOT EXISTS `inventory_check_record` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `venue_id` VARCHAR(64) NOT NULL COMMENT '门店ID',
    `handler` VARCHAR(64) NOT NULL COMMENT '经手人',
    `operator` VARCHAR(64) NOT NULL COMMENT '操作人',
    `time` BIGINT NOT NULL COMMENT '盘点时间',
    `record_number` VARCHAR(64) NOT NULL COMMENT '盘点单号',
    `remark` TEXT COMMENT '备注',
    `profit_quantity_total` INT DEFAULT 0 COMMENT '盘盈数量合计',
    `loss_quantity_total` INT DEFAULT 0 COMMENT '盘亏数量合计',
    `ctime` BIGINT DEFAULT 0 COMMENT '创建时间',
    `utime` BIGINT DEFAULT 0 COMMENT '更新时间',
    `state` INT DEFAULT 1 COMMENT '状态：1-有效，0-删除',
    `version` INT DEFAULT 1 COMMENT '版本号',
    PRIMARY KEY (`id`),
    INDEX `idx_venue_time` (`venue_id`, `time`),
    INDEX `idx_record_number` (`record_number`),
    INDEX `idx_handler` (`handler`),
    INDEX `idx_operator` (`operator`),
    INDEX `idx_state` (`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='盘点记录主表';

-- 创建盘点记录明细表
CREATE TABLE IF NOT EXISTS `inventory_check_record_item` (
    `id` VARCHAR(64) NOT NULL COMMENT '主键ID',
    `check_record_id` VARCHAR(64) NOT NULL COMMENT '盘点记录ID',
    `product_id` VARCHAR(64) NOT NULL COMMENT '商品ID',
    `stock_quantity` INT NOT NULL COMMENT '库存数量（盘点之前）',
    `check_quantity` INT NOT NULL COMMENT '盘点数量（盘点之后）',
    `profit_loss_quantity` INT NOT NULL COMMENT '盈亏数量',
    `ctime` BIGINT DEFAULT 0 COMMENT '创建时间',
    `utime` BIGINT DEFAULT 0 COMMENT '更新时间',
    `state` INT DEFAULT 1 COMMENT '状态：1-有效，0-删除',
    PRIMARY KEY (`id`),
    INDEX `idx_check_record` (`check_record_id`),
    INDEX `idx_product` (`product_id`),
    INDEX `idx_state` (`state`),
    FOREIGN KEY (`check_record_id`) REFERENCES `inventory_check_record`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='盘点记录明细表';

-- 插入测试数据（可选）
-- INSERT INTO `inventory_check_record` (
--     `id`, `venue_id`, `handler`, `operator`, `time`, `record_number`, 
--     `remark`, `profit_quantity_total`, `loss_quantity_total`, 
--     `ctime`, `utime`, `state`, `version`
-- ) VALUES (
--     'test_check_001', 'venue_001', '张三', '李四', 1705747200, 'CHECK20240120001',
--     '月度盘点', 5, 2, 1705747200, 1705747200, 1, 1
-- );

-- INSERT INTO `inventory_check_record_item` (
--     `id`, `check_record_id`, `product_id`, `stock_quantity`, 
--     `check_quantity`, `profit_loss_quantity`, `ctime`, `utime`, `state`
-- ) VALUES 
-- ('test_item_001', 'test_check_001', 'product_001', 100, 103, 3, 1705747200, 1705747200, 1),
-- ('test_item_002', 'test_check_001', 'product_002', 50, 48, -2, 1705747200, 1705747200, 1);

-- 验证表结构
-- DESCRIBE `inventory_check_record`;
-- DESCRIBE `inventory_check_record_item`;

-- 验证索引
-- SHOW INDEX FROM `inventory_check_record`;
-- SHOW INDEX FROM `inventory_check_record_item`;
