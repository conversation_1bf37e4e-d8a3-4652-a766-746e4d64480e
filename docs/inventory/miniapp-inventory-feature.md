# 小程序端库存功能设计文档

## 1. 概述

### 1.1 功能定位
小程序端库存功能基于"商家无感知校准"的设计理念，通过ProductStockSnapshot（快照表）+ 增量计算的方式，为商家提供直观的库存变化分析，避免复杂的技术概念。

### 1.2 核心设计原则
- **无感知校准**：商家看到的库存都是基于快照+增量的实时计算结果
- **变化分析**：重点展示"从某个时间点开始的库存变化"
- **信息聚焦**：每个页面职责单一，避免功能混杂
- **操作简化**：减少不必要的用户操作和认知负担

## 2. 页面架构设计

### 2.1 页面层级关系
```
库存管理 (入口页面)
├── 📊 库存分析 (独立页面)
│   └── 商品库存详情 (弹窗/独立页面)
├── 📝 入库记录 (独立页面)
│   └── 新增入库 (独立页面)
└── 🔍 盘点记录 (独立页面)
    └── 新增盘点 (独立页面)
```

### 2.2 功能分布
- **库存管理**：功能入口，提供导航
- **库存分析**：核心功能，展示库存变化分析
- **入库记录**：入库操作历史管理，支持新增入库
- **盘点记录**：盘点操作历史管理，支持新增盘点（简化版：仅数量管理）

## 3. 详细页面设计

### 3.1 库存管理入口页面

#### 3.1.1 页面结构
```
┌─────────────────────────────────────────┐
│ ◀ 返回         库存管理                 │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📊 库存分析                    >   │ │
│ │ 查看库存变化和趋势分析              │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📝 入库记录                    >   │ │
│ │ 管理商品入库操作记录                │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 盘点记录                    >   │ │
│ │ 管理库存盘点操作记录                │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

#### 3.1.2 使用组件
- **页面容器**：`view class="w-full min-h-screen flex flex-col px-4 pt-4"`
- **功能卡片**：`thu-action-item` 组件
- **图标**：现有的 `icons` 组件

### 3.2 库存分析页面

#### 3.2.1 页面结构
```
┌─────────────────────────────────────────┐
│ ◀ 返回         库存分析         🔄刷新  │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📊 分析基准                         │ │
│ │ 2024年1月15日 14:30                │ │
│ │ (上次盘点数据)                      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 [搜索商品名称____________]       │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ [全部] [饮料] [零食] [日用品] [其他] │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ ┌───┐ 可口可乐 500ml            >  │ │
│ │ │🥤 │ 当前: 45瓶                    │ │
│ │ └───┘ 入库:+15 | 销售:-8 | 净:+7   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [加载更多...]                           │
│                                         │
└─────────────────────────────────────────┘
```

#### 3.2.2 核心功能
- **基准信息展示**：明确显示分析的起始时间和依据
- **商品搜索**：支持商品名称搜索
- **分类筛选**：横向滚动的分类标签
- **库存变化展示**：每个商品显示入库、销售、净变化
- **异常标识**：用⚠️标识异常商品（如负库存）

#### 3.2.3 使用组件
- **基准信息卡片**：`view class="bg-white rounded-lg p-4 mb-4"`
- **搜索框**：`thu-search-bar` 组件
- **分类标签**：`scroll-view` + `thu-button` 组件
- **商品项**：自定义商品分析卡片组件

#### 3.2.4 数据模型
```typescript
interface StockAnalysisItem {
  productId: string;
  productName: string;
  productImage: string;
  unit: string;
  
  // 当前库存
  currentStock: number;
  
  // 变化信息
  inboundChange: number;    // 入库变化
  saleChange: number;       // 销售变化（含退款）
  netChange: number;        // 净变化
  
  // 状态信息
  hasAlert: boolean;        // 是否有异常
}

interface BaselineInfo {
  snapshotTime: string;     // 快照时间（格式化）
  description: string;      // 描述（如"上次盘点"）
  snapshotType: string;     // 快照类型
}
```

### 3.3 商品库存详情页面

#### 3.3.1 页面结构
```
┌─────────────────────────────────────────┐
│ ◀ 返回         可口可乐 500ml           │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │        ┌─────────────┐               │ │
│ │        │     🥤      │               │ │
│ │        └─────────────┘               │ │
│ │         可口可乐 500ml               │ │
│ │          单位: 瓶                   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📊 库存信息                         │ │
│ │ 基准时间: 01-15 14:30 (盘点数据)    │ │
│ │ 基准库存: 38瓶                      │ │
│ │ ────────────────────────────────── │ │
│ │ 计算结果: 38 + 15 - 7 = 46瓶       │ │
│ │ 当前库存: 46瓶                      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📦 入库明细 (共+15瓶)               │ │
│ │ ┌─────────────────────────────────┐ │ │
│ │ │ 01-16 09:15  入库单#001  +10瓶 │ │ │
│ │ └─────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────┐ │ │
│ │ │ 01-18 14:20  入库单#003   +5瓶 │ │ │
│ │ └─────────────────────────────────┘ │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🛒 销售明细 (共-7瓶)                │ │
│ │ ┌─────────────────────────────────┐ │ │
│ │ │ 01-17 15:22  订单#A001   -3瓶  │ │ │
│ │ └─────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────┐ │ │
│ │ │ 01-18 11:45  退款#R001   +1瓶  │ │ │
│ │ └─────────────────────────────────┘ │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

#### 3.3.2 核心功能
- **商品基本信息**：图片、名称、单位
- **库存信息合并**：基准信息和计算结果在同一卡片
- **计算公式展示**：直观显示库存计算过程
- **详细变化记录**：按时间顺序展示所有变化
- **销售退款合并**：销售和退款记录在同一区域

#### 3.3.3 使用组件
- **商品信息**：`view class="bg-white rounded-lg p-4 text-center mb-4"`
- **库存信息卡片**：`view class="bg-white rounded-lg p-4 mb-4"`
- **明细卡片**：`view class="bg-white rounded-lg p-4 mb-4"`
- **明细项**：`view class="bg-gray-50 rounded-lg p-3 mb-2"`

## 4. 交互设计

### 4.1 导航流程
```
库存管理入口
    ↓ 点击"库存分析"
库存分析页面
    ↓ 点击商品卡片
商品库存详情页面
```

### 4.2 状态管理
```typescript
// Pinia Store
export const useInventoryStore = defineStore('inventory', {
  state: () => ({
    baselineInfo: null as BaselineInfo | null,
    stockItems: [] as StockAnalysisItem[],
    currentProduct: null as ProductStockDetail | null,
    isLoading: false,
  }),
  
  actions: {
    async loadStockAnalysis(venueId: string) {
      this.isLoading = true;
      try {
        const response = await api.getStockAnalysisList({ venueId });
        this.baselineInfo = response.baselineInfo;
        this.stockItems = response.items;
      } finally {
        this.isLoading = false;
      }
    },
    
    async loadProductDetail(productId: string, venueId: string) {
      const detail = await api.getProductStockDetail({ productId, venueId });
      this.currentProduct = detail;
    }
  }
});
```

### 4.3 性能优化
- **分页加载**：支持下拉加载更多商品
- **缓存策略**：基准信息短期缓存
- **搜索防抖**：搜索输入防抖处理
- **图片懒加载**：商品图片懒加载

## 5. 视觉设计

### 5.1 色彩系统
```scss
// 库存状态颜色
.stock-positive { color: #18bc37; } // 绿色 - 库存增加
.stock-negative { color: #e43d33; } // 红色 - 库存减少
.stock-neutral { color: #6a6a6a; }  // 灰色 - 无变化
.stock-warning { color: #f3a73f; }  // 橙色 - 异常警告
```

### 5.2 图标系统
- **📊** - 统计分析
- **📦** - 入库操作
- **🛒** - 销售记录
- **🔄** - 退款记录
- **⚠️** - 异常警告
- **>** - 可点击跳转

### 5.3 布局规范
- **页面边距**：统一使用 `px-4`
- **组件间距**：统一使用 `mb-4`
- **圆角**：统一使用 `rounded-lg`
- **背景**：统一使用白色卡片

## 6. API接口设计

### 6.1 库存分析列表
```typescript
// GET /api/inventory/analysis/list
interface StockAnalysisRequest {
  venueId: string;
  categoryId?: string;
  searchKey?: string;
  pageNum?: number;
  pageSize?: number;
}

interface StockAnalysisResponse {
  baselineInfo: BaselineInfo;
  items: StockAnalysisItem[];
  total: number;
}
```

### 6.2 商品库存详情
```typescript
// GET /api/inventory/analysis/detail
interface ProductStockDetailRequest {
  productId: string;
  venueId: string;
}

interface ProductStockDetailResponse {
  productInfo: ProductBasicInfo;
  baselineInfo: BaselineInfo;
  currentStock: number;
  calculationFormula: string;
  inboundDetails: StockChangeDetail[];
  saleDetails: StockChangeDetail[];
}
```

## 7. 实施计划

### 7.1 开发阶段
1. **第一阶段**：库存管理入口页面 + 库存分析页面基础功能
2. **第二阶段**：商品详情页面 + 搜索筛选功能
3. **第三阶段**：性能优化 + 用户体验完善

### 7.2 测试重点
- **数据准确性**：库存计算结果的准确性
- **性能表现**：大量商品时的加载性能
- **用户体验**：操作流程的流畅性
- **异常处理**：网络异常和数据异常的处理

## 8. 注意事项

### 8.1 数据一致性
- 所有展示的库存数据都基于快照+增量计算
- 不直接使用ProductStock表的数据展示给用户
- 确保计算逻辑与服务端保持一致

### 8.2 用户体验
- 避免暴露技术概念（如"校准"、"快照"等）
- 重点突出业务价值（库存变化分析）
- 提供清晰的操作引导和状态反馈

### 8.3 扩展性
- 预留盘点功能的接口和页面结构
- 支持未来的高级筛选和分析功能
- 保持与现有设计系统的一致性

## 9. 技术实现细节

### 9.1 组件复用策略
```vue
<!-- 基于现有thu-action-item扩展的库存分析项组件 -->
<template>
  <view class="stock-analysis-item" @click="onItemClick">
    <view class="item-header">
      <image :src="item.productImage" class="product-image" />
      <view class="product-info">
        <text class="product-name">{{ item.productName }}</text>
        <text class="product-unit">{{ item.unit }}</text>
      </view>
      <view v-if="item.hasAlert" class="alert-badge">⚠️</view>
    </view>

    <view class="stock-info">
      <view class="current-stock">
        <text class="label">当前:</text>
        <text class="value">{{ item.currentStock }}</text>
      </view>
    </view>

    <view class="change-info">
      <text class="change-item">入库:{{ formatChange(item.inboundChange) }}</text>
      <text class="separator">|</text>
      <text class="change-item">销售:{{ formatChange(-item.saleChange) }}</text>
      <text class="separator">|</text>
      <text class="change-item net">净:{{ formatChange(item.netChange) }}</text>
    </view>
  </view>
</template>

<script setup>
const formatChange = (value) => {
  return value > 0 ? `+${value}` : value.toString();
};

const onItemClick = () => {
  // 跳转到商品详情页面
  uni.navigateTo({
    url: `/pages/inventory/product-detail?productId=${item.productId}`
  });
};
</script>
```

### 9.2 数据管理策略
```typescript
// 使用Pinia进行状态管理
export const useInventoryAnalysisStore = defineStore('inventoryAnalysis', {
  state: () => ({
    // 基准信息
    baselineInfo: null as BaselineInfo | null,

    // 商品列表
    stockItems: [] as StockAnalysisItem[],
    currentPage: 1,
    hasMore: true,

    // 筛选条件
    currentCategory: 'all',
    searchKeyword: '',

    // 加载状态
    isLoading: false,
    isRefreshing: false,
  }),

  actions: {
    // 加载库存分析数据
    async loadStockAnalysis(refresh = false) {
      if (refresh) {
        this.currentPage = 1;
        this.stockItems = [];
        this.isRefreshing = true;
      } else {
        this.isLoading = true;
      }

      try {
        const response = await api.getStockAnalysisList({
          venueId: getCurrentVenueId(),
          categoryId: this.currentCategory === 'all' ? undefined : this.currentCategory,
          searchKey: this.searchKeyword || undefined,
          pageNum: this.currentPage,
          pageSize: 20
        });

        if (refresh) {
          this.baselineInfo = response.baselineInfo;
          this.stockItems = response.items;
        } else {
          this.stockItems.push(...response.items);
        }

        this.hasMore = response.items.length === 20;
        this.currentPage++;

      } catch (error) {
        console.error('加载库存分析数据失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        this.isRefreshing = false;
      }
    },

    // 搜索商品
    async searchProducts(keyword: string) {
      this.searchKeyword = keyword;
      await this.loadStockAnalysis(true);
    },

    // 切换分类
    async switchCategory(categoryId: string) {
      this.currentCategory = categoryId;
      await this.loadStockAnalysis(true);
    }
  }
});
```

### 9.3 错误处理机制
```typescript
// 统一的错误处理
class InventoryErrorHandler {
  static handleApiError(error: any, context: string) {
    console.error(`${context} 错误:`, error);

    if (error.code === 'NETWORK_ERROR') {
      uni.showToast({
        title: '网络连接失败，请检查网络',
        icon: 'none',
        duration: 3000
      });
    } else if (error.code === 'DATA_ERROR') {
      uni.showToast({
        title: '数据异常，请联系客服',
        icon: 'none',
        duration: 3000
      });
    } else {
      uni.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  static handleDataInconsistency(productId: string) {
    console.warn(`商品 ${productId} 数据不一致`);
    // 可以在这里添加数据修复逻辑或上报机制
  }
}
```

## 10. 测试策略

### 10.1 单元测试
```typescript
// 库存计算逻辑测试
describe('库存分析计算', () => {
  test('正确计算净变化', () => {
    const item = {
      baselineStock: 100,
      inboundChange: 20,
      saleChange: 15,
      refundChange: 2
    };

    const netChange = item.inboundChange - item.saleChange + item.refundChange;
    expect(netChange).toBe(7);
  });

  test('异常库存检测', () => {
    const item = { currentStock: -5 };
    expect(checkStockAlert(item)).toBe(true);
  });
});
```

### 10.2 集成测试
- **API接口测试**：验证与服务端接口的正确对接
- **页面跳转测试**：验证页面间导航的正确性
- **数据同步测试**：验证数据更新后的页面刷新

### 10.3 用户体验测试
- **加载性能测试**：大量商品时的加载速度
- **交互响应测试**：用户操作的响应速度
- **异常场景测试**：网络异常、数据异常时的处理

## 11. 部署和发布

### 11.1 版本管理
- **功能开关**：使用功能开关控制新功能的启用
- **灰度发布**：先向部分用户开放新功能
- **版本回滚**：准备快速回滚到旧版本的方案

### 11.2 性能监控
```typescript
// 性能监控埋点
class PerformanceMonitor {
  static trackPageLoad(pageName: string, loadTime: number) {
    // 上报页面加载时间
    analytics.track('page_load', {
      page: pageName,
      loadTime: loadTime,
      timestamp: Date.now()
    });
  }

  static trackApiCall(apiName: string, duration: number, success: boolean) {
    // 上报API调用性能
    analytics.track('api_call', {
      api: apiName,
      duration: duration,
      success: success,
      timestamp: Date.now()
    });
  }
}
```

## 12. 总结

这个小程序端库存功能设计方案具有以下优势：

1. **用户体验优先**：基于"商家无感知校准"理念，简化用户操作
2. **架构清晰**：页面职责单一，功能分布合理
3. **技术先进**：使用现代化的前端技术栈和最佳实践
4. **可维护性强**：组件化设计，代码结构清晰
5. **扩展性好**：为未来功能扩展预留了充足空间

该方案能够为商家提供直观、高效的库存管理体验，同时为开发团队提供了可维护、可扩展的技术架构。
