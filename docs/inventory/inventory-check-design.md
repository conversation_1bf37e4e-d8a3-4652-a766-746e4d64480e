# 库存盘点功能设计说明

## 1. 设计理念修正

### 1.1 从"任务"到"记录"的转变
**原设计问题：**
- 盘点被设计为"任务"概念，包含任务创建、执行、完成等复杂状态
- 与入库操作的简单记录模式不一致
- 增加了用户的认知负担和操作复杂度

**修正后设计：**
- 盘点改为"记录"概念，与入库记录保持一致的操作模式
- 用户直接录入盘点结果，系统自动计算盈亏
- 简化操作流程，降低用户使用门槛

### 1.2 操作模式一致性
```
入库操作：选择商品 → 录入数量 → 保存入库记录
盘点操作：选择商品 → 录入盘点数量 → 保存盘点记录
```

## 2. 数据模型设计

### 2.1 盘点记录主表
```sql
CREATE TABLE inventory_check_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL,
    handler VARCHA<PERSON>(64) NOT NULL COMMENT '经手人',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    time INT NOT NULL COMMENT '盘点时间',
    record_number VARCHAR(64) NOT NULL COMMENT '盘点单号',
    remark TEXT COMMENT '备注',
    profit_quantity_total INT DEFAULT 0 COMMENT '盘盈数量合计',
    loss_quantity_total INT DEFAULT 0 COMMENT '盘亏数量合计',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    version INT DEFAULT 0
);
```

### 2.2 盘点记录明细表
```sql
CREATE TABLE inventory_check_record_item (
    id VARCHAR(64) PRIMARY KEY,
    check_record_id VARCHAR(64) NOT NULL,
    product_id VARCHAR(64) NOT NULL,
    stock_quantity INT NOT NULL COMMENT '库存数量（盘点之前）',
    check_quantity INT NOT NULL COMMENT '盘点数量（盘点之后）',
    profit_loss_quantity INT NOT NULL COMMENT '盈亏数量',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0
);
```

### 2.3 字段说明

#### 2.3.1 盘点记录主表字段
- **经手人**：实际执行盘点的人员
- **操作人**：系统录入人员（可能与经手人不同）
- **盘点时间**：执行盘点的时间
- **盘点单号**：系统生成的唯一单号
- **备注**：盘点说明信息
- **盘盈数量合计**：所有商品盘盈数量的总和
- **盘亏数量合计**：所有商品盘亏数量的总和（绝对值）

#### 2.3.2 盘点记录明细表字段
- **商品ID**：被盘点的商品
- **库存数量**：盘点之前的库存数量
- **盘点数量**：盘点之后的实际数量
- **盈亏数量**：盘点数量 - 库存数量（正数为盘盈，负数为盘亏）

## 3. 业务逻辑设计

### 3.1 盘点流程
```
1. 用户创建盘点记录，填写经手人、操作人、备注等基本信息
2. 用户添加要盘点的商品：
   - 选择商品
   - 系统自动获取当前库存数量和成本单价
   - 用户录入实际盘点数量
   - 系统实时计算盈亏数量和金额
3. 用户可以继续添加多个商品到同一个盘点记录中
4. 系统自动计算盘点记录的汇总信息：
   - 盘盈数量合计 = SUM(盈亏数量 WHERE 盈亏数量 > 0)
   - 盘亏数量合计 = SUM(ABS(盈亏数量) WHERE 盈亏数量 < 0)
5. 保存盘点记录（主表 + 明细表）
6. 更新ProductStockSnapshot（以盘点结果为新基准）
```

### 3.2 库存计算集成
```go
// 修正后的库存计算公式
当前库存 = ProductStockSnapshot.stock + 入库增量 - 消费增量 + 退款增量 + 盘点调整增量

// 盘点调整增量计算
盘点调整增量 = SUM(InventoryCheckRecord.items.profitLossQuantity) WHERE time > snapshot.utime
```

### 3.3 与库存分析的集成
- 盘点记录作为库存变化的一种类型
- 在库存分析页面中，盘点调整会体现在库存变化中
- 盘点完成后，会更新ProductStockSnapshot，成为新的分析基准点

## 4. 小程序端设计

### 4.1 盘点记录页面
```
┌─────────────────────────────────────────┐
│ ◀ 返回         盘点记录         + 新增  │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 [搜索盘点单号或操作人_______]     │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 盘点单 #CHECK001         >      │ │
│ │ 2024-01-20 16:00                   │ │
│ │ 经手人: 张三 | 操作人: 李四          │ │
│ │ 商品: 5种 | 盘盈: 8件 | 盘亏: 2件   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 盘点单 #CHECK002         >      │ │
│ │ 2024-01-18 14:30                   │ │
│ │ 经手人: 王五 | 操作人: 赵六          │ │
│ │ 商品: 3种 | 盘盈: 1件 | 盘亏: 4件   │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 4.2 新增盘点页面（支持多商品批量盘点）
```
┌─────────────────────────────────────────┐
│ ◀ 返回         新增盘点         💾保存  │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 经手人: [选择经手人_____________] > │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 [搜索商品名称________________] + │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ ┌───┐ 可口可乐 500ml             │ │
│ │ │🥤 │ 账面库存: 45瓶               │ │
│ │ └───┘ 盘点数量: [47]瓶       [×] │ │
│ │       盈亏: +2瓶                   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ ┌───┐ 矿泉水 550ml               │ │
│ │ │💧 │ 账面库存: 23瓶               │ │
│ │ └───┘ 盘点数量: [22]瓶       [×] │ │
│ │       盈亏: -1瓶                   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ ┌───┐ 薯片 100g                  │ │
│ │ │🍟 │ 账面库存: 30包               │ │
│ │ └───┘ 盘点数量: [32]包       [×] │ │
│ │       盈亏: +2包                   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📊 盘点汇总                        │ │
│ │ 盘盈: 4件 | 盘亏: 1件              │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 备注: [可选填写备注信息__________] │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

## 5. API接口设计

### 5.1 新增盘点记录
```typescript
POST /api/inventory/check/record/create

interface CreateCheckRecordRequest {
  venueId: string;
  handler: string;        // 经手人
  operator: string;       // 操作人
  remark?: string;        // 备注
  items: CheckRecordItem[];
}

interface CheckRecordItem {
  productId: string;
  stockQuantity: number;  // 库存数量（盘点之前）
  checkQuantity: number;  // 盘点数量（盘点之后）
}

interface CreateCheckRecordResponse {
  recordId: string;
  recordNumber: string;
  profitQuantityTotal: number;    // 盘盈数量合计
  lossQuantityTotal: number;      // 盘亏数量合计
}
```

### 5.2 盘点记录列表
```typescript
GET /api/inventory/check/record/list

interface CheckRecordListRequest {
  venueId: string;
  pageNum?: number;
  pageSize?: number;
  searchKey?: string;     // 搜索关键词
}

interface CheckRecordListResponse {
  records: CheckRecordVO[];
  total: number;
}

interface CheckRecordVO {
  recordId: string;
  recordNumber: string;
  handler: string;
  operator: string;
  checkTime: number;
  totalProducts: number;
  profitQuantityTotal: number;    // 盘盈数量合计
  lossQuantityTotal: number;      // 盘亏数量合计
  remark?: string;
}
```

### 5.3 盘点记录详情
```typescript
GET /api/inventory/check/record/detail

interface CheckRecordDetailRequest {
  recordId: string;
}

interface CheckRecordDetailResponse {
  record: CheckRecordVO;
  items: CheckRecordItemVO[];
}

interface CheckRecordItemVO {
  productId: string;
  productName: string;
  productImage: string;
  unit: string;
  stockQuantity: number;        // 库存数量（盘点之前）
  checkQuantity: number;        // 盘点数量（盘点之后）
  profitLossQuantity: number;   // 盈亏数量
}
```

## 6. 与现有功能的集成

### 6.1 库存分析集成
- 盘点记录会影响库存分析的计算结果
- 盘点完成后，ProductStockSnapshot会更新，成为新的分析基准
- 在商品详情页面中，盘点记录会显示在变化明细中

### 6.2 库存校准集成
- 盘点本质上是一种手动的库存校准
- 盘点完成后，相关商品的ProductStock会更新为盘点结果
- 盘点记录可以作为库存校准的依据和历史记录

### 6.3 数据一致性保证
- 盘点操作需要在事务中完成
- 确保盘点记录保存和库存更新的原子性
- 建立盘点操作的审计日志

## 7. 实施优势

### 7.1 用户体验优势
- **操作简单**：与入库操作保持一致的交互模式
- **认知负担小**：无需理解复杂的任务状态和流程
- **即时反馈**：实时显示盈亏情况，便于用户确认

### 7.2 技术实现优势
- **架构一致**：与入库记录使用相同的设计模式
- **代码复用**：可以复用入库记录的部分代码逻辑
- **维护简单**：减少了复杂的状态管理和流程控制

### 7.3 业务价值优势
- **数据准确**：直接基于实际盘点结果更新库存
- **历史可追溯**：完整记录每次盘点的详细信息
- **分析支持**：为库存分析提供准确的基准数据

这个修正后的盘点功能设计更加简洁、实用，与整体的库存管理体系保持一致，能够有效支撑商家的日常盘点需求。
