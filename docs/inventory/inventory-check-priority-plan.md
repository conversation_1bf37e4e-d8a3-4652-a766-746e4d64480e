# 盘点功能优先开发计划

## 1. 开发策略调整

### 1.1 优先级重新排序
**调整原因：**
- 盘点功能是商家的刚需，需要立即使用
- 盘点功能相对独立，可以先行开发和上线
- 库存分析功能依赖盘点数据，盘点先行有利于后续功能

**新的优先级：**
1. **P0（立即开发）**：盘点记录功能
2. **P1（后续开发）**：库存分析功能
3. **P2（可延后）**：商品详情页和高级功能

### 1.2 开发时间线
```
第1周：盘点功能开发（4天）+ 基础设施（3天）
第2周：库存分析功能开发（4天）+ 集成测试（1天）
第3周：性能优化和上线准备
```

## 2. 盘点功能详细开发计划

### 2.1 第一天：数据模型和基础设施
**时间**：1天
**负责人**：数据库设计师 + 后端架构师

#### 2.1.1 数据库设计
```sql
-- 盘点记录主表
CREATE TABLE inventory_check_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL,
    handler VARCHAR(64) NOT NULL COMMENT '经手人',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    time INT NOT NULL COMMENT '盘点时间',
    record_number VARCHAR(64) NOT NULL COMMENT '盘点单号',
    remark TEXT COMMENT '备注',
    profit_quantity_total INT DEFAULT 0 COMMENT '盘盈数量合计',
    loss_quantity_total INT DEFAULT 0 COMMENT '盘亏数量合计',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    version INT DEFAULT 0,
    INDEX idx_venue_time (venue_id, time),
    INDEX idx_record_number (record_number)
);

-- 盘点记录明细表
CREATE TABLE inventory_check_record_item (
    id VARCHAR(64) PRIMARY KEY,
    check_record_id VARCHAR(64) NOT NULL,
    product_id VARCHAR(64) NOT NULL,
    cost_price DECIMAL(10,2) NOT NULL COMMENT '成本单价',
    stock_quantity INT NOT NULL COMMENT '库存数量（盘点之前）',
    check_quantity INT NOT NULL COMMENT '盘点数量（盘点之后）',
    check_amount DECIMAL(10,2) NOT NULL COMMENT '盘点金额',
    profit_loss_quantity INT NOT NULL COMMENT '盈亏数量',
    profit_loss_amount DECIMAL(10,2) NOT NULL COMMENT '盈亏金额',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    INDEX idx_check_record (check_record_id),
    INDEX idx_product (product_id)
);
```

#### 2.1.2 Go数据模型定义
```go
// 创建文件：erp_managent/model/InventoryCheckRecord.go
type InventoryCheckRecord struct {
    Id                      string                        `gorm:"column:id;primaryKey"`
    VenueId                 string                        `gorm:"column:venue_id"`
    Handler                 string                        `gorm:"column:handler"`
    Operator                string                        `gorm:"column:operator"`
    Time                    int64                         `gorm:"column:time"`
    RecordNumber            string                        `gorm:"column:record_number"`
    Remark                  string                        `gorm:"column:remark"`
    ProfitQuantityTotal     int                           `gorm:"column:profit_quantity_total"`
    LossQuantityTotal       int                           `gorm:"column:loss_quantity_total"`
    ProfitLossAmountTotal   float64                       `gorm:"column:profit_loss_amount_total"`
    Items                   []InventoryCheckRecordItem    `gorm:"foreignKey:CheckRecordId"`
    Ctime                   int64                         `gorm:"column:ctime"`
    Utime                   int64                         `gorm:"column:utime"`
    State                   int                           `gorm:"column:state"`
    Version                 int                           `gorm:"column:version"`
}

type InventoryCheckRecordItem struct {
    Id                   string  `gorm:"column:id;primaryKey"`
    CheckRecordId        string  `gorm:"column:check_record_id"`
    ProductId            string  `gorm:"column:product_id"`
    CostPrice            float64 `gorm:"column:cost_price"`
    StockQuantity        int     `gorm:"column:stock_quantity"`
    CheckQuantity        int     `gorm:"column:check_quantity"`
    CheckAmount          float64 `gorm:"column:check_amount"`
    ProfitLossQuantity   int     `gorm:"column:profit_loss_quantity"`
    ProfitLossAmount     float64 `gorm:"column:profit_loss_amount"`
    Ctime                int64   `gorm:"column:ctime"`
    Utime                int64   `gorm:"column:utime"`
    State                int     `gorm:"column:state"`
}
```

### 2.2 第二天：Repository层实现
**时间**：1天
**负责人**：后端开发工程师

#### 2.2.1 Repository接口定义
```go
// 创建文件：erp_managent/repository/InventoryCheckRecordRepository.go
type InventoryCheckRecordRepository interface {
    Create(ctx *gin.Context, record *model.InventoryCheckRecord) error
    FindByVenueId(ctx *gin.Context, venueId string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
    FindById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error)
    FindByRecordNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error)
    Search(ctx *gin.Context, venueId, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
}
```

#### 2.2.2 Repository实现
```go
// 创建文件：erp_managent/repository/impl/InventoryCheckRecordRepositoryImpl.go
type InventoryCheckRecordRepositoryImpl struct {
    db *gorm.DB
}

func (r *InventoryCheckRecordRepositoryImpl) Create(ctx *gin.Context, record *model.InventoryCheckRecord) error {
    return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 1. 保存主记录
        if err := tx.Create(record).Error; err != nil {
            return err
        }
        
        // 2. 保存明细记录
        for _, item := range record.Items {
            item.CheckRecordId = record.Id
            if err := tx.Create(&item).Error; err != nil {
                return err
            }
        }
        
        return nil
    })
}
```

### 2.3 第三天：Service层实现
**时间**：1天
**负责人**：后端开发工程师

#### 2.3.1 Service接口定义
```go
// 创建文件：erp_managent/service/InventoryCheckRecordService.go
type InventoryCheckRecordService interface {
    CreateCheckRecord(ctx *gin.Context, reqDto *req.CreateCheckRecordReqDto) (*vo.CreateCheckRecordRespVO, error)
    GetCheckRecordList(ctx *gin.Context, reqDto *req.CheckRecordListReqDto) (*vo.CheckRecordListRespVO, error)
    GetCheckRecordDetail(ctx *gin.Context, recordId string) (*vo.CheckRecordDetailRespVO, error)
}
```

#### 2.3.2 Service实现
```go
// 创建文件：erp_managent/service/impl/InventoryCheckRecordServiceImpl.go
func (s *InventoryCheckRecordServiceImpl) CreateCheckRecord(ctx *gin.Context, reqDto *req.CreateCheckRecordReqDto) (*vo.CreateCheckRecordRespVO, error) {
    // 1. 生成盘点单号
    recordNumber := s.generateRecordNumber(ctx, reqDto.VenueId)
    
    // 2. 构建盘点记录
    record := &model.InventoryCheckRecord{
        Id:           utils.GenerateId(),
        VenueId:      reqDto.VenueId,
        Handler:      reqDto.Handler,
        Operator:     reqDto.Operator,
        Time:         time.Now().Unix(),
        RecordNumber: recordNumber,
        Remark:       reqDto.Remark,
        Ctime:        time.Now().Unix(),
        Utime:        time.Now().Unix(),
        State:        1,
        Version:      1,
    }
    
    // 3. 处理明细项并计算汇总
    var profitQuantityTotal, lossQuantityTotal int
    var profitLossAmountTotal float64
    
    for _, itemDto := range reqDto.Items {
        // 计算盈亏
        profitLossQuantity := itemDto.CheckQuantity - itemDto.StockQuantity
        profitLossAmount := float64(profitLossQuantity) * itemDto.CostPrice
        checkAmount := float64(itemDto.CheckQuantity) * itemDto.CostPrice
        
        // 累计汇总数据
        if profitLossQuantity > 0 {
            profitQuantityTotal += profitLossQuantity
        } else {
            lossQuantityTotal += -profitLossQuantity
        }
        profitLossAmountTotal += profitLossAmount
        
        // 构建明细项
        item := model.InventoryCheckRecordItem{
            Id:                 utils.GenerateId(),
            ProductId:          itemDto.ProductId,
            CostPrice:          itemDto.CostPrice,
            StockQuantity:      itemDto.StockQuantity,
            CheckQuantity:      itemDto.CheckQuantity,
            CheckAmount:        checkAmount,
            ProfitLossQuantity: profitLossQuantity,
            ProfitLossAmount:   profitLossAmount,
            Ctime:              time.Now().Unix(),
            Utime:              time.Now().Unix(),
            State:              1,
        }
        
        record.Items = append(record.Items, item)
    }
    
    // 4. 设置汇总数据
    record.ProfitQuantityTotal = profitQuantityTotal
    record.LossQuantityTotal = lossQuantityTotal
    record.ProfitLossAmountTotal = profitLossAmountTotal
    
    // 5. 保存记录
    if err := s.repository.Create(ctx, record); err != nil {
        return nil, err
    }
    
    // 6. 更新ProductStockSnapshot（可选，根据业务需求）
    // s.updateStockSnapshot(ctx, record)
    
    return &vo.CreateCheckRecordRespVO{
        RecordId:              record.Id,
        RecordNumber:          record.RecordNumber,
        ProfitQuantityTotal:   profitQuantityTotal,
        LossQuantityTotal:     lossQuantityTotal,
        ProfitLossAmountTotal: profitLossAmountTotal,
    }, nil
}
```

### 2.4 第四天：Controller和API实现
**时间**：1天
**负责人**：后端开发工程师

#### 2.4.1 Controller实现
```go
// 创建文件：erp_managent/controller/InventoryCheckRecordController.go
type InventoryCheckRecordController struct {
    service service.InventoryCheckRecordService
}

// @Summary 新增盘点记录
// @Description 创建新的盘点记录，支持多商品批量盘点
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.CreateCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.CreateCheckRecordRespVO] "成功"
// @Router /api/inventory/check/record/create [post]
func (c *InventoryCheckRecordController) CreateCheckRecord(ctx *gin.Context) {
    reqDto := &req.CreateCheckRecordReqDto{}
    if err := ctx.ShouldBindJSON(reqDto); err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    result, err := c.service.CreateCheckRecord(ctx, reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, result)
}
```

#### 2.4.2 路由配置
```go
// 在路由配置文件中添加
func SetupInventoryCheckRoutes(route *gin.Engine, controller *InventoryCheckRecordController) {
    route.POST("/api/inventory/check/record/create", controller.CreateCheckRecord)
    route.GET("/api/inventory/check/record/list", controller.GetCheckRecordList)
    route.GET("/api/inventory/check/record/detail", controller.GetCheckRecordDetail)
}
```

## 3. 验收标准

### 3.1 功能验收
- [ ] 盘点记录创建功能正常
- [ ] 支持多商品批量盘点
- [ ] 盈亏计算准确
- [ ] 汇总数据正确
- [ ] 盘点记录查询功能正常

### 3.2 技术验收
- [ ] 数据库事务处理正确
- [ ] 错误处理完善
- [ ] API接口规范
- [ ] 代码质量符合标准

### 3.3 性能验收
- [ ] 单次盘点支持100个商品
- [ ] API响应时间<500ms
- [ ] 数据库查询优化

## 4. 风险控制

### 4.1 技术风险
- **数据一致性**：使用数据库事务确保数据一致性
- **性能风险**：批量操作时的性能优化
- **并发风险**：盘点记录的并发控制

### 4.2 业务风险
- **数据准确性**：盈亏计算的准确性验证
- **操作便利性**：用户操作流程的简化
- **数据恢复**：错误操作的数据恢复机制

## 5. 后续集成

### 5.1 与库存分析的集成
- 盘点记录会影响库存分析的计算
- 盘点完成后更新ProductStockSnapshot
- 在库存变化明细中显示盘点记录

### 5.2 与现有系统的集成
- 与ProductStock表的同步
- 与库存校准功能的集成
- 审计日志的记录

这个优先开发计划确保盘点功能能够快速上线，满足商家的紧急需求，同时为后续的库存分析功能奠定基础。
