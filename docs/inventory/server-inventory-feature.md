# 服务端库存功能设计文档

## 1. 概述

### 1.1 功能定位
服务端库存功能基于DDD架构设计，采用"快照表+增量计算"的数据模型，为小程序端提供实时、准确的库存分析数据，同时保持现有库存校准功能作为数据修复工具。

### 1.2 核心设计理念
- **商家无感知校准**：通过实时计算替代用户手动校准
- **数据一致性保证**：基于ProductStockSnapshot作为可靠基准
- **性能优化**：批量查询和缓存策略提升响应速度
- **架构兼容性**：在现有架构基础上扩展，不破坏现有功能

## 2. UE/UI设计概述

### 2.1 页面架构
小程序端采用三层页面架构：
- **库存管理入口**：功能导航页面
- **库存分析页面**：核心功能，展示基于快照+增量的库存变化
- **商品详情页面**：单个商品的详细变化时间线

### 2.2 数据展示逻辑
- **基准信息**：明确显示分析起始时间（最后一次快照时间）
- **变化分析**：展示入库、销售、净变化三个维度
- **实时计算**：所有库存数据基于快照+增量实时计算
- **异常标识**：自动识别和标记异常库存（如负库存）

### 2.3 用户交互流程
```
库存管理入口 → 库存分析 → 商品详情
                ↓
            搜索/筛选 → 分类浏览
```

## 3. 数据模型设计

### 3.1 现有数据模型
```go
// ProductStock - 当前库存快照表（实际库存）
type ProductStock struct {
    id        string
    productId string
    venueId   string
    stock     int      // 当前库存数量
    ctime     time.Time
    utime     time.Time
    version   int
}

// ProductStockSnapshot - 历史库存快照表（校准基准）
type ProductStockSnapshot struct {
    id        string
    productId string
    venueId   string
    stock     int      // 快照时的库存数量
    ctime     time.Time // 快照创建时间
    utime     time.Time // 快照更新时间
    state     int
    version   int
}

// InboundRecord - 入库记录主表
type InboundRecord struct {
    id               string
    venueId          string
    handler          string
    time             time.Time
    recordNumber     string
    totalAmount      int64
    items            []InboundRecordItem
    // ... 其他字段
}

// OrderProduct - 订单商品表（销售记录）
type OrderProduct struct {
    productId     string
    quantity      int64
    orderDirection string // NORMAL/REFUND
    // ... 其他字段
}
```

### 3.2 新增数据模型
```go
// StockAnalysisItemVO - 库存分析项视图对象
type StockAnalysisItemVO struct {
    ProductId        string `json:"productId"`
    ProductName      string `json:"productName"`
    ProductImage     string `json:"productImage"`
    Unit            string `json:"unit"`
    
    // 基准信息
    BaselineTime     int64  `json:"baselineTime"`
    BaselineType     string `json:"baselineType"`     // "盘点"/"导入"
    BaselineStock    int    `json:"baselineStock"`
    
    // 变化信息
    InboundChange    int    `json:"inboundChange"`    // 入库变化
    SaleChange       int    `json:"saleChange"`       // 销售变化（含退款）
    NetChange        int    `json:"netChange"`        // 净变化
    
    // 当前库存
    CurrentStock     int    `json:"currentStock"`     // 计算库存
    HasAlert         bool   `json:"hasAlert"`         // 是否异常
}

// ProductStockDetailVO - 商品库存详情视图对象
type ProductStockDetailVO struct {
    ProductInfo      ProductBasicInfoVO       `json:"productInfo"`
    BaselineInfo     BaselineInfoVO           `json:"baselineInfo"`
    CurrentStock     int                      `json:"currentStock"`
    CalculationFormula string                 `json:"calculationFormula"`
    
    InboundDetails   []StockChangeDetailVO    `json:"inboundDetails"`
    SaleDetails      []StockChangeDetailVO    `json:"saleDetails"`
}

// StockChangeDetailVO - 库存变化明细
type StockChangeDetailVO struct {
    Time         int64  `json:"time"`
    Type         string `json:"type"`         // "入库"/"销售"/"退款"
    RecordNumber string `json:"recordNumber"` // 单号
    Quantity     int    `json:"quantity"`     // 数量变化
    Operator     string `json:"operator"`     // 操作人
}
```

## 4. 核心业务逻辑

### 4.1 库存计算公式
```go
// 核心计算逻辑
当前库存 = ProductStockSnapshot.stock + 入库增量 - 消费增量 + 退款增量

// 增量计算
入库增量 = SUM(InboundRecord.items.quantity) WHERE time > snapshot.utime
消费增量 = SUM(OrderProduct.quantity) WHERE orderDirection = "NORMAL" AND time > snapshot.utime
退款增量 = SUM(OrderProduct.quantity) WHERE orderDirection = "REFUND" AND time > snapshot.utime

// 净销售变化
净销售变化 = 消费增量 - 退款增量
```

### 4.2 增量计算实现
```go
func (r *InventoryRepositoryImpl) CalculateStockIncrements(ctx *gin.Context, venueId, productId string, afterTime int64) (inboundIncrement, consumeIncrement, refundIncrement int, err error) {
    // 1. 计算入库增量（包含正常入库和冲销记录）
    inboundRecords, err := r.GetAllInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, 0, 0, err
    }

    inboundIncrement = 0
    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if item.GetProductId() == productId {
                // 入库数量可能为负（冲销记录），直接累加
                inboundIncrement += item.GetQuantity()
            }
        }
    }

    // 2. 计算消费增量（正常订单消费）
    orderProducts, err := r.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, 0, 0, err
    }

    consumeIncrement = 0
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId && orderProduct.OrderDirection == "NORMAL" {
            consumeIncrement += int(orderProduct.Quantity)
        }
    }

    // 3. 计算退款增量（退款订单恢复库存）
    refundIncrement = 0
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId && orderProduct.OrderDirection == "REFUND" {
            refundIncrement += int(orderProduct.Quantity)
        }
    }

    return inboundIncrement, consumeIncrement, refundIncrement, nil
}
```

## 5. 应用服务设计

### 5.1 StockAnalysisAppService接口
```go
type StockAnalysisAppService interface {
    // 获取库存分析列表
    GetStockAnalysisList(ctx *gin.Context, reqDto StockAnalysisReqDto) (*StockAnalysisRespVO, error)
    
    // 获取商品库存详情
    GetProductStockDetail(ctx *gin.Context, reqDto ProductStockDetailReqDto) (*ProductStockDetailVO, error)
    
    // 获取基准信息
    GetBaselineInfo(ctx *gin.Context, venueId string) (*BaselineInfoVO, error)
}

// 请求DTO
type StockAnalysisReqDto struct {
    VenueId    string   `json:"venueId" binding:"required"`
    CategoryId *string  `json:"categoryId,omitempty"`
    SearchKey  *string  `json:"searchKey,omitempty"`
    PageNum    *int     `json:"pageNum,omitempty"`
    PageSize   *int     `json:"pageSize,omitempty"`
}

type ProductStockDetailReqDto struct {
    ProductId string `json:"productId" binding:"required"`
    VenueId   string `json:"venueId" binding:"required"`
}

// 响应VO
type StockAnalysisRespVO struct {
    BaselineInfo     BaselineInfoVO           `json:"baselineInfo"`
    Items           []StockAnalysisItemVO     `json:"items"`
    Total           int64                     `json:"total"`
}
```

### 5.2 核心实现逻辑
```go
func (s *StockAnalysisAppServiceImpl) GetStockAnalysisList(ctx *gin.Context, reqDto StockAnalysisReqDto) (*StockAnalysisRespVO, error) {
    // 1. 获取基准信息（最新快照信息）
    baselineInfo, err := s.inventoryRepository.GetLatestSnapshotInfo(ctx, reqDto.VenueId)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取商品列表（支持分类筛选和搜索）
    products, total, err := s.getFilteredProducts(ctx, reqDto)
    if err != nil {
        return nil, err
    }
    
    // 3. 批量计算库存分析数据
    var items []StockAnalysisItemVO
    for _, product := range products {
        item, err := s.calculateStockAnalysisItem(ctx, product, baselineInfo.SnapshotTime)
        if err != nil {
            continue // 记录错误但不中断
        }
        items = append(items, *item)
    }
    
    return &StockAnalysisRespVO{
        BaselineInfo: *baselineInfo,
        Items:        items,
        Total:        total,
    }, nil
}

func (s *StockAnalysisAppServiceImpl) calculateStockAnalysisItem(ctx *gin.Context, product *po.Product, baselineTime int64) (*StockAnalysisItemVO, error) {
    // 1. 获取快照库存
    snapshot, err := s.productStockSnapshotService.FindByProductAndVenue(ctx, *product.Id, reqDto.VenueId)
    if err != nil {
        return nil, err
    }
    
    // 2. 计算增量变化
    inboundIncrement, consumeIncrement, refundIncrement, err := s.inventoryRepository.CalculateStockIncrements(
        ctx, reqDto.VenueId, *product.Id, baselineTime)
    if err != nil {
        return nil, err
    }
    
    // 3. 计算当前库存
    currentStock := snapshot.Stock + inboundIncrement - consumeIncrement + refundIncrement
    
    // 4. 合并销售和退款为净销售变化
    netSaleChange := consumeIncrement - refundIncrement
    netChange := inboundIncrement - netSaleChange
    
    return &StockAnalysisItemVO{
        ProductId:     *product.Id,
        ProductName:   *product.Name,
        ProductImage:  *product.ImageUrl,
        Unit:         *product.Unit,
        BaselineTime:  baselineTime,
        BaselineStock: snapshot.Stock,
        InboundChange: inboundIncrement,
        SaleChange:    netSaleChange,
        NetChange:     netChange,
        CurrentStock:  currentStock,
        HasAlert:      s.checkStockAlert(currentStock, netChange),
    }, nil
}
```

## 6. 仓储层扩展

### 6.1 InventoryRepository接口扩展
```go
type InventoryRepository interface {
    // 现有方法...
    
    // 获取商品的详细变化记录
    GetProductStockChangeDetails(ctx *gin.Context, venueId, productId string, afterTime int64) (*ProductStockChangeDetails, error)
    
    // 获取门店最新快照信息
    GetLatestSnapshotInfo(ctx *gin.Context, venueId string) (*BaselineInfoVO, error)
    
    // 批量计算库存分析数据
    BatchCalculateStockAnalysis(ctx *gin.Context, venueId string, productIds []string) ([]StockAnalysisItemVO, error)
    
    // 获取指定时间后的入库记录
    GetAllInboundRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InboundRecord, error)
    
    // 获取指定时间后的订单商品记录
    GetOrderProductsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]OrderProductVO, error)
}
```

### 6.2 详细变化记录查询
```go
func (r *InventoryRepositoryImpl) GetProductStockChangeDetails(ctx *gin.Context, venueId, productId string, afterTime int64) (*ProductStockChangeDetails, error) {
    var inboundDetails []StockChangeDetailVO
    var saleDetails []StockChangeDetailVO
    
    // 1. 查询入库明细
    inboundRecords, err := r.GetInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, err
    }
    
    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if item.GetProductId() == productId {
                inboundDetails = append(inboundDetails, StockChangeDetailVO{
                    Time:         record.GetTime().Unix(),
                    Type:         "入库",
                    RecordNumber: record.GetRecordNumber(),
                    Quantity:     item.GetQuantity(),
                    Operator:     record.GetHandler(),
                })
            }
        }
    }
    
    // 2. 查询销售明细（包含退款）
    orderProducts, err := r.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, err
    }
    
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId {
            changeType := "销售"
            quantity := -int(orderProduct.Quantity) // 销售为负数
            
            // 如果是退款订单，调整类型和数量
            if orderProduct.OrderDirection == "REFUND" {
                changeType = "退款"
                quantity = int(orderProduct.Quantity) // 退款为正数
            }
            
            saleDetails = append(saleDetails, StockChangeDetailVO{
                Time:         orderProduct.CreateTime,
                Type:         changeType,
                RecordNumber: orderProduct.OrderNumber,
                Quantity:     quantity,
                Operator:     orderProduct.OperatorName,
            })
        }
    }
    
    // 3. 按时间排序
    sort.Slice(saleDetails, func(i, j int) bool {
        return saleDetails[i].Time < saleDetails[j].Time
    })
    
    return &ProductStockChangeDetails{
        InboundDetails: inboundDetails,
        SaleDetails:    saleDetails,
        TotalInbound:   r.calculateTotalInbound(inboundDetails),
        TotalSale:      r.calculateTotalSale(saleDetails),
    }, nil
}
```

## 7. 控制器设计

### 7.1 StockAnalysisController
```go
type StockAnalysisController struct {
    stockAnalysisAppService StockAnalysisAppService
}

// @Summary 获取库存分析列表
// @Description 获取门店库存分析数据，基于快照+增量计算
// @Tags 库存分析
// @Accept json
// @Produce json
// @Param body body StockAnalysisReqDto true "请求体"
// @Success 200 {object} Result[StockAnalysisRespVO] "成功"
// @Router /api/inventory/analysis/list [post]
func (c *StockAnalysisController) GetStockAnalysisList(ctx *gin.Context) {
    reqDto := StockAnalysisReqDto{}
    err := ctx.ShouldBindJSON(&reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    result, err := c.stockAnalysisAppService.GetStockAnalysisList(ctx, reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, result)
}

// @Summary 获取商品库存详情
// @Description 获取单个商品的详细库存变化记录
// @Tags 库存分析
// @Accept json
// @Produce json
// @Param body body ProductStockDetailReqDto true "请求体"
// @Success 200 {object} Result[ProductStockDetailVO] "成功"
// @Router /api/inventory/analysis/detail [post]
func (c *StockAnalysisController) GetProductStockDetail(ctx *gin.Context) {
    reqDto := ProductStockDetailReqDto{}
    err := ctx.ShouldBindJSON(&reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    result, err := c.stockAnalysisAppService.GetProductStockDetail(ctx, reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, result)
}
```

### 7.2 路由配置
```go
func SetupStockAnalysisRoutes(route *gin.Engine, controller *StockAnalysisController) {
    // 库存分析相关路由
    route.POST("/api/inventory/analysis/list", controller.GetStockAnalysisList)
    route.POST("/api/inventory/analysis/detail", controller.GetProductStockDetail)
    route.POST("/api/inventory/analysis/baseline", controller.GetBaselineInfo)
}
```

## 8. 性能优化策略

### 8.1 缓存策略
```go
// 基准信息缓存（5分钟）
func (s *StockAnalysisAppServiceImpl) GetBaselineInfoWithCache(ctx *gin.Context, venueId string) (*BaselineInfoVO, error) {
    cacheKey := fmt.Sprintf("baseline_info:%s", venueId)
    
    // 尝试从缓存获取
    if cached := s.cacheService.Get(cacheKey); cached != nil {
        return cached.(*BaselineInfoVO), nil
    }
    
    // 缓存未命中，查询数据库
    baselineInfo, err := s.inventoryRepository.GetLatestSnapshotInfo(ctx, venueId)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    s.cacheService.Set(cacheKey, baselineInfo, 5*time.Minute)
    
    return baselineInfo, nil
}

// 商品列表缓存（10分钟）
func (s *StockAnalysisAppServiceImpl) GetProductsWithCache(ctx *gin.Context, categoryId string) ([]*po.Product, error) {
    cacheKey := fmt.Sprintf("products:%s", categoryId)
    
    if cached := s.cacheService.Get(cacheKey); cached != nil {
        return cached.([]*po.Product), nil
    }
    
    products, err := s.productService.FindProductsByCategory(ctx, categoryId)
    if err != nil {
        return nil, err
    }
    
    s.cacheService.Set(cacheKey, products, 10*time.Minute)
    
    return products, nil
}
```

### 8.2 查询优化
```go
// 批量查询优化
func (r *InventoryRepositoryImpl) BatchCalculateStockAnalysis(ctx *gin.Context, venueId string, productIds []string) ([]StockAnalysisItemVO, error) {
    // 1. 批量获取快照数据
    snapshots, err := r.productStockSnapshotService.FindByProductIds(ctx, venueId, productIds)
    if err != nil {
        return nil, err
    }
    
    // 2. 批量获取增量数据
    baselineTime := r.getEarliestSnapshotTime(snapshots)
    inboundRecords, err := r.GetAllInboundRecordsAfterTime(ctx, venueId, baselineTime)
    if err != nil {
        return nil, err
    }
    
    orderProducts, err := r.GetOrderProductsAfterTime(ctx, venueId, baselineTime)
    if err != nil {
        return nil, err
    }
    
    // 3. 批量计算结果
    var results []StockAnalysisItemVO
    for _, snapshot := range snapshots {
        item := r.calculateSingleItem(snapshot, inboundRecords, orderProducts)
        results = append(results, item)
    }
    
    return results, nil
}
```

### 8.3 数据库索引优化
```sql
-- 库存快照表索引
CREATE INDEX idx_product_stock_snapshot_venue_product ON product_stock_snapshot(venue_id, product_id);
CREATE INDEX idx_product_stock_snapshot_utime ON product_stock_snapshot(utime);

-- 入库记录索引
CREATE INDEX idx_inventory_record_venue_time ON inventory_record(venue_id, time);
CREATE INDEX idx_inventory_record_item_product ON inventory_record_item(product_id);

-- 订单商品索引
CREATE INDEX idx_order_product_venue_time ON order_product(venue_id, create_time);
CREATE INDEX idx_order_product_product_direction ON order_product(product_id, order_direction);
```

## 9. 与现有系统的兼容性

### 9.1 保留现有功能
- **库存校准功能**：保留作为后台数据修复工具
- **ProductStock表**：继续维护，作为缓存和备份
- **现有API接口**：保持向后兼容

### 9.2 数据一致性保证
```go
// 定期数据一致性检查
func (s *StockAnalysisAppServiceImpl) CheckDataConsistency(ctx *gin.Context, venueId string) error {
    // 1. 获取所有商品的计算库存
    calculatedStocks, err := s.BatchCalculateStockAnalysis(ctx, venueId, nil)
    if err != nil {
        return err
    }
    
    // 2. 获取ProductStock表中的库存
    actualStocks, err := s.productStockService.FindByVenueId(ctx, venueId)
    if err != nil {
        return err
    }
    
    // 3. 比较差异
    inconsistencies := s.findInconsistencies(calculatedStocks, actualStocks)
    
    // 4. 记录异常并触发校准
    if len(inconsistencies) > 0 {
        s.logInconsistencies(ctx, inconsistencies)
        return s.triggerAutoReconcile(ctx, venueId, inconsistencies)
    }
    
    return nil
}
```

## 10. 监控和日志

### 10.1 性能监控
- **API响应时间**：监控库存分析接口的响应时间
- **查询性能**：监控数据库查询的执行时间
- **缓存命中率**：监控缓存的命中率和效果

### 10.2 业务监控
- **数据一致性**：定期检查计算结果与实际库存的一致性
- **异常库存**：监控负库存和异常变化的商品
- **计算错误**：监控增量计算过程中的错误

### 10.3 日志记录
```go
// 操作日志
func (s *StockAnalysisAppServiceImpl) logStockAnalysisOperation(ctx *gin.Context, operation string, details map[string]interface{}) {
    logEntry := map[string]interface{}{
        "operation": operation,
        "timestamp": time.Now().Unix(),
        "details":   details,
    }
    
    s.logger.Info("StockAnalysis Operation", logEntry)
}

// 异常日志
func (s *StockAnalysisAppServiceImpl) logStockAbnormal(ctx *gin.Context, productId, venueId string, abnormalType string, details map[string]interface{}) {
    logEntry := map[string]interface{}{
        "type":      "stock_abnormal",
        "productId": productId,
        "venueId":   venueId,
        "abnormalType": abnormalType,
        "details":   details,
        "timestamp": time.Now().Unix(),
    }
    
    s.logger.Warn("Stock Abnormal Detected", logEntry)
}
```

## 11. 部署和运维

### 11.1 部署策略
- **灰度发布**：先在部分门店测试新功能
- **数据迁移**：确保现有数据的完整性
- **回滚方案**：准备快速回滚到原有功能的方案

### 11.2 运维监控
- **健康检查**：定期检查服务健康状态
- **数据备份**：重要数据的定期备份
- **性能调优**：根据监控数据进行性能优化

## 12. 实施计划

### 12.1 开发阶段
1. **第一阶段（2周）**：
   - 新增StockAnalysisAppService接口和实现
   - 扩展InventoryRepository相关方法
   - 实现基础的库存分析列表API

2. **第二阶段（2周）**：
   - 实现商品库存详情API
   - 完善数据查询优化
   - 添加缓存策略

3. **第三阶段（1周）**：
   - 性能测试和优化
   - 监控和日志完善
   - 文档和部署准备

### 12.2 测试策略
- **单元测试**：核心计算逻辑的单元测试覆盖率达到90%
- **集成测试**：API接口的集成测试
- **性能测试**：大数据量下的性能测试
- **数据一致性测试**：计算结果与实际数据的一致性验证

### 12.3 风险控制
- **数据备份**：实施前完整备份现有数据
- **灰度发布**：先在测试门店验证功能
- **监控告警**：实时监控新功能的运行状态
- **快速回滚**：准备紧急回滚方案

## 13. 总结

这个服务端设计方案具有以下特点：

1. **架构兼容性**：在现有DDD架构基础上扩展，不破坏现有功能
2. **数据准确性**：基于可靠的快照+增量计算模式
3. **性能优化**：通过缓存、批量查询等策略提升性能
4. **用户体验**：为小程序端提供流畅的库存分析体验
5. **可维护性**：清晰的代码结构和完善的监控体系

该方案能够有效支撑小程序端的库存分析功能需求，同时为未来的功能扩展奠定了良好的基础。
