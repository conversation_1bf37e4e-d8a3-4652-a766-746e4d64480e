# 服务端库存功能设计文档

## 1. 概述

### 1.1 功能定位
服务端库存功能基于DDD架构设计，采用"快照表+增量计算"的数据模型，为小程序端提供实时、准确的库存分析数据，同时保持现有库存校准功能作为数据修复工具。

### 1.2 核心设计理念
- **商家无感知校准**：通过实时计算替代用户手动校准
- **数据一致性保证**：基于ProductStockSnapshot作为可靠基准
- **性能优化**：批量查询和缓存策略提升响应速度
- **架构兼容性**：在现有架构基础上扩展，不破坏现有功能

## 2. UE/UI设计概述

### 2.1 页面架构
小程序端采用三层页面架构：
- **库存管理入口**：功能导航页面
- **库存分析页面**：核心功能，展示基于快照+增量的库存变化
- **商品详情页面**：单个商品的详细变化时间线

### 2.2 数据展示逻辑
- **基准信息**：明确显示分析起始时间（最后一次快照时间）
- **变化分析**：展示入库、销售、净变化三个维度
- **实时计算**：所有库存数据基于快照+增量实时计算
- **异常标识**：自动识别和标记异常库存（如负库存）

### 2.3 用户交互流程
```
库存管理入口 → 库存分析 → 商品详情
                ↓
            搜索/筛选 → 分类浏览
```

## 3. 数据模型设计

### 3.1 现有数据模型
```go
// ProductStock - 当前库存快照表（实际库存）
type ProductStock struct {
    id        string
    productId string
    venueId   string
    stock     int      // 当前库存数量
    ctime     time.Time
    utime     time.Time
    version   int
}

// ProductStockSnapshot - 历史库存快照表（校准基准）
type ProductStockSnapshot struct {
    id        string
    productId string
    venueId   string
    stock     int      // 快照时的库存数量
    ctime     time.Time // 快照创建时间
    utime     time.Time // 快照更新时间
    state     int
    version   int
}

// InboundRecord - 入库记录主表
type InboundRecord struct {
    id               string
    venueId          string
    handler          string
    time             time.Time
    recordNumber     string
    totalAmount      int64
    items            []InboundRecordItem
    // ... 其他字段
}

// OrderProduct - 订单商品表（销售记录）
type OrderProduct struct {
    productId     string
    quantity      int64
    orderDirection string // NORMAL/REFUND
    // ... 其他字段
}

// InventoryCheckRecord - 盘点记录主表
type InventoryCheckRecord struct {
    id                      string
    venueId                 string
    handler                 string    // 经手人
    operator                string    // 操作人
    time                    time.Time // 盘点时间
    recordNumber            string    // 盘点单号
    remark                  string    // 备注
    profitQuantityTotal     int       // 盘盈数量合计
    lossQuantityTotal       int       // 盘亏数量合计
    items                   []InventoryCheckRecordItem
    // ... 其他字段
}

// InventoryCheckRecordItem - 盘点记录明细表
type InventoryCheckRecordItem struct {
    id                  string
    checkRecordId       string
    productId           string
    stockQuantity       int     // 库存数量（盘点之前）
    checkQuantity       int     // 盘点数量（盘点之后）
    profitLossQuantity  int     // 盈亏数量
    // ... 其他字段
}
```

### 3.2 新增数据模型
```go
// StockAnalysisItemVO - 库存分析项视图对象
type StockAnalysisItemVO struct {
    ProductId        string `json:"productId"`
    ProductName      string `json:"productName"`
    ProductImage     string `json:"productImage"`
    Unit            string `json:"unit"`
    
    // 基准信息
    BaselineTime     int64  `json:"baselineTime"`
    BaselineType     string `json:"baselineType"`     // "盘点"/"导入"
    BaselineStock    int    `json:"baselineStock"`
    
    // 变化信息
    InboundChange    int    `json:"inboundChange"`    // 入库变化
    SaleChange       int    `json:"saleChange"`       // 销售变化（含退款）
    NetChange        int    `json:"netChange"`        // 净变化
    
    // 当前库存
    CurrentStock     int    `json:"currentStock"`     // 计算库存
    HasAlert         bool   `json:"hasAlert"`         // 是否异常
}

// ProductStockDetailVO - 商品库存详情视图对象
type ProductStockDetailVO struct {
    ProductInfo      ProductBasicInfoVO       `json:"productInfo"`
    BaselineInfo     BaselineInfoVO           `json:"baselineInfo"`
    CurrentStock     int                      `json:"currentStock"`
    CalculationFormula string                 `json:"calculationFormula"`
    
    InboundDetails   []StockChangeDetailVO    `json:"inboundDetails"`
    SaleDetails      []StockChangeDetailVO    `json:"saleDetails"`
}

// StockChangeDetailVO - 库存变化明细
type StockChangeDetailVO struct {
    Time         int64  `json:"time"`
    Type         string `json:"type"`         // "入库"/"销售"/"退款"
    RecordNumber string `json:"recordNumber"` // 单号
    Quantity     int    `json:"quantity"`     // 数量变化
    Operator     string `json:"operator"`     // 操作人
}
```

## 4. 核心业务逻辑

### 4.1 库存计算公式
```go
// 核心计算逻辑
当前库存 = ProductStockSnapshot.stock + 入库增量 - 消费增量 + 退款增量 + 盘点调整增量

// 增量计算
入库增量 = SUM(InboundRecord.items.quantity) WHERE time > snapshot.utime
消费增量 = SUM(OrderProduct.quantity) WHERE orderDirection = "NORMAL" AND time > snapshot.utime
退款增量 = SUM(OrderProduct.quantity) WHERE orderDirection = "REFUND" AND time > snapshot.utime
盘点调整增量 = SUM(InventoryCheckRecord.items.profitLossQuantity) WHERE time > snapshot.utime

// 净销售变化
净销售变化 = 消费增量 - 退款增量
```

### 4.2 增量计算实现
```go
func (r *InventoryRepositoryImpl) CalculateStockIncrements(ctx *gin.Context, venueId, productId string, afterTime int64) (inboundIncrement, consumeIncrement, refundIncrement int, err error) {
    // 1. 计算入库增量（包含正常入库和冲销记录）
    inboundRecords, err := r.GetAllInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, 0, 0, err
    }

    inboundIncrement = 0
    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if item.GetProductId() == productId {
                // 入库数量可能为负（冲销记录），直接累加
                inboundIncrement += item.GetQuantity()
            }
        }
    }

    // 2. 计算消费增量（正常订单消费）
    orderProducts, err := r.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, 0, 0, err
    }

    consumeIncrement = 0
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId && orderProduct.OrderDirection == "NORMAL" {
            consumeIncrement += int(orderProduct.Quantity)
        }
    }

    // 3. 计算退款增量（退款订单恢复库存）
    refundIncrement = 0
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId && orderProduct.OrderDirection == "REFUND" {
            refundIncrement += int(orderProduct.Quantity)
        }
    }

    return inboundIncrement, consumeIncrement, refundIncrement, nil
}
```

## 5. DDD架构设计

### 5.1 领域划分
按照DDD原则，将功能划分为不同的领域：

- **Inventory领域**：负责库存相关的业务逻辑（入库、快照、校准）
- **Order领域**：负责订单相关的业务逻辑（销售、退款）
- **Product领域**：负责商品相关的业务逻辑（商品信息、分类）

### 5.2 应用服务设计

#### 5.2.1 StockAnalysisAppService接口
```go
type StockAnalysisAppService interface {
    // 获取库存分析列表
    GetStockAnalysisList(ctx *gin.Context, reqDto StockAnalysisReqDto) (*StockAnalysisRespVO, error)

    // 获取商品库存详情
    GetProductStockDetail(ctx *gin.Context, reqDto ProductStockDetailReqDto) (*ProductStockDetailVO, error)

    // 获取基准信息
    GetBaselineInfo(ctx *gin.Context, venueId string) (*BaselineInfoVO, error)
}

// 请求DTO
type StockAnalysisReqDto struct {
    VenueId    string   `json:"venueId" binding:"required"`
    CategoryId *string  `json:"categoryId,omitempty"`
    SearchKey  *string  `json:"searchKey,omitempty"`
    PageNum    *int     `json:"pageNum,omitempty"`
    PageSize   *int     `json:"pageSize,omitempty"`
}

type ProductStockDetailReqDto struct {
    ProductId string `json:"productId" binding:"required"`
    VenueId   string `json:"venueId" binding:"required"`
}

// 响应VO
type StockAnalysisRespVO struct {
    BaselineInfo     BaselineInfoVO           `json:"baselineInfo"`
    Items           []StockAnalysisItemVO     `json:"items"`
    Total           int64                     `json:"total"`
}
```

#### 5.2.2 领域服务接口设计
```go
// Inventory领域服务
type InventoryDomainService interface {
    // 获取基准信息
    GetBaselineInfo(ctx *gin.Context, venueId string) (*BaselineInfoVO, error)

    // 获取入库明细
    GetInboundDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]InboundDetailVO, error)

    // 计算入库增量
    CalculateInboundIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error)
}

// Order领域服务
type OrderDomainService interface {
    // 获取销售明细（包含退款）
    GetSaleDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]SaleDetailVO, error)

    // 计算销售增量
    CalculateSaleIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (consume, refund int, error)
}

// Product领域服务
type ProductDomainService interface {
    // 获取商品列表（支持分类筛选和搜索）
    GetFilteredProducts(ctx *gin.Context, venueId string, categoryId, searchKey *string, pageNum, pageSize int) ([]*po.Product, int64, error)

    // 获取商品基本信息
    GetProductBasicInfo(ctx *gin.Context, productId string) (*ProductBasicInfoVO, error)
}
```

### 5.3 应用服务实现（DDD架构）

#### 5.3.1 StockAnalysisAppServiceImpl
```go
type StockAnalysisAppServiceImpl struct {
    inventoryDomainService InventoryDomainService
    orderDomainService     OrderDomainService
    productDomainService   ProductDomainService
}

func (s *StockAnalysisAppServiceImpl) GetStockAnalysisList(ctx *gin.Context, reqDto StockAnalysisReqDto) (*StockAnalysisRespVO, error) {
    // 1. 获取基准信息 - Inventory领域
    baselineInfo, err := s.inventoryDomainService.GetBaselineInfo(ctx, reqDto.VenueId)
    if err != nil {
        return nil, err
    }

    // 2. 获取商品列表 - Product领域
    products, total, err := s.productDomainService.GetFilteredProducts(
        ctx, reqDto.VenueId, reqDto.CategoryId, reqDto.SearchKey,
        *reqDto.PageNum, *reqDto.PageSize)
    if err != nil {
        return nil, err
    }

    // 3. 批量计算库存分析数据 - Application层协调
    var items []StockAnalysisItemVO
    for _, product := range products {
        item, err := s.calculateStockAnalysisItem(ctx, product, baselineInfo, reqDto.VenueId)
        if err != nil {
            // 记录错误但不中断处理
            logrus.Errorf("计算商品 %s 库存分析失败: %v", *product.Id, err)
            continue
        }
        items = append(items, *item)
    }

    return &StockAnalysisRespVO{
        BaselineInfo: *baselineInfo,
        Items:        items,
        Total:        total,
    }, nil
}

func (s *StockAnalysisAppServiceImpl) calculateStockAnalysisItem(ctx *gin.Context, product *po.Product, baselineInfo *BaselineInfoVO, venueId string) (*StockAnalysisItemVO, error) {
    // 1. 计算入库增量 - Inventory领域
    inboundIncrement, err := s.inventoryDomainService.CalculateInboundIncrement(
        ctx, venueId, *product.Id, baselineInfo.SnapshotTime)
    if err != nil {
        return nil, err
    }

    // 2. 计算销售增量 - Order领域
    consumeIncrement, refundIncrement, err := s.orderDomainService.CalculateSaleIncrement(
        ctx, venueId, *product.Id, baselineInfo.SnapshotTime)
    if err != nil {
        return nil, err
    }

    // 3. 在Application层进行业务计算
    currentStock := baselineInfo.BaselineStock + inboundIncrement - consumeIncrement + refundIncrement
    netSaleChange := consumeIncrement - refundIncrement
    netChange := inboundIncrement - netSaleChange

    return &StockAnalysisItemVO{
        ProductId:     *product.Id,
        ProductName:   *product.Name,
        ProductImage:  *product.ImageUrl,
        Unit:         *product.Unit,
        BaselineTime:  baselineInfo.SnapshotTime,
        BaselineStock: baselineInfo.BaselineStock,
        InboundChange: inboundIncrement,
        SaleChange:    netSaleChange,
        NetChange:     netChange,
        CurrentStock:  currentStock,
        HasAlert:      s.checkStockAlert(currentStock, netChange),
    }, nil
}
```

#### 5.3.2 商品库存详情实现
```go
func (s *StockAnalysisAppServiceImpl) GetProductStockDetail(ctx *gin.Context, reqDto ProductStockDetailReqDto) (*ProductStockDetailVO, error) {
    // 1. 获取商品基本信息 - Product领域
    productInfo, err := s.productDomainService.GetProductBasicInfo(ctx, reqDto.ProductId)
    if err != nil {
        return nil, err
    }

    // 2. 获取基准信息 - Inventory领域
    baselineInfo, err := s.inventoryDomainService.GetBaselineInfo(ctx, reqDto.VenueId)
    if err != nil {
        return nil, err
    }

    // 3. 获取入库明细 - Inventory领域
    inboundDetails, err := s.inventoryDomainService.GetInboundDetailsAfterTime(
        ctx, reqDto.VenueId, reqDto.ProductId, baselineInfo.SnapshotTime)
    if err != nil {
        return nil, err
    }

    // 4. 获取销售明细 - Order领域
    saleDetails, err := s.orderDomainService.GetSaleDetailsAfterTime(
        ctx, reqDto.VenueId, reqDto.ProductId, baselineInfo.SnapshotTime)
    if err != nil {
        return nil, err
    }

    // 5. 在Application层组装数据
    totalInbound := s.calculateTotalInbound(inboundDetails)
    totalSale := s.calculateTotalSale(saleDetails)
    currentStock := baselineInfo.BaselineStock + totalInbound - totalSale
    formula := fmt.Sprintf("%d + %d - %d = %d",
        baselineInfo.BaselineStock, totalInbound, totalSale, currentStock)

    return &ProductStockDetailVO{
        ProductInfo:        *productInfo,
        BaselineInfo:       *baselineInfo,
        CurrentStock:       currentStock,
        CalculationFormula: formula,
        InboundDetails:     inboundDetails,
        SaleDetails:        saleDetails,
    }, nil
}
```

## 6. 领域服务实现

### 6.1 Inventory领域服务实现
```go
type InventoryDomainServiceImpl struct {
    inventoryRepository          repository.InventoryRepository
    productStockSnapshotService  *impl.ProductStockSnapshotService
}

func (s *InventoryDomainServiceImpl) GetBaselineInfo(ctx *gin.Context, venueId string) (*BaselineInfoVO, error) {
    // 获取最新的库存快照
    snapshots, err := s.productStockSnapshotService.FindProductStockSnapshotsByVenueId(ctx, venueId)
    if err != nil {
        return nil, err
    }

    if len(snapshots) == 0 {
        return nil, errors.New("未找到库存快照数据")
    }

    // 找到最新的快照时间
    latestSnapshot := snapshots[0]
    for _, snapshot := range snapshots {
        if *snapshot.Utime > *latestSnapshot.Utime {
            latestSnapshot = snapshot
        }
    }

    return &BaselineInfoVO{
        SnapshotTime:    *latestSnapshot.Utime,
        Description:     s.getSnapshotDescription(latestSnapshot),
        SnapshotType:    s.getSnapshotType(latestSnapshot),
        BaselineStock:   *latestSnapshot.Stock,
        TotalProducts:   len(snapshots),
    }, nil
}

func (s *InventoryDomainServiceImpl) GetInboundDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]InboundDetailVO, error) {
    // 查询入库记录
    inboundRecords, err := s.inventoryRepository.GetInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, err
    }

    var details []InboundDetailVO
    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if item.GetProductId() == productId {
                details = append(details, InboundDetailVO{
                    Time:         record.GetTime().Unix(),
                    Type:         "入库",
                    RecordNumber: record.GetRecordNumber(),
                    Quantity:     item.GetQuantity(),
                    Operator:     record.GetHandler(),
                })
            }
        }
    }

    return details, nil
}

func (s *InventoryDomainServiceImpl) CalculateInboundIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error) {
    inboundRecords, err := s.inventoryRepository.GetInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, err
    }

    increment := 0
    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if item.GetProductId() == productId {
                increment += item.GetQuantity()
            }
        }
    }

    return increment, nil
}
```

### 6.2 Order领域服务实现
```go
type OrderDomainServiceImpl struct {
    orderRepository repository.OrderRepository
}

func (s *OrderDomainServiceImpl) GetSaleDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]SaleDetailVO, error) {
    // 通过OrderRepository查询order领域的数据
    orderProducts, err := s.orderRepository.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, err
    }

    var saleDetails []SaleDetailVO
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId {
            changeType := "销售"
            quantity := -int(orderProduct.Quantity) // 销售为负数

            // 如果是退款订单，调整类型和数量
            if orderProduct.OrderDirection == "REFUND" {
                changeType = "退款"
                quantity = int(orderProduct.Quantity) // 退款为正数
            }

            saleDetails = append(saleDetails, SaleDetailVO{
                Time:         orderProduct.CreateTime,
                Type:         changeType,
                RecordNumber: orderProduct.OrderNumber,
                Quantity:     quantity,
                Operator:     orderProduct.OperatorName,
            })
        }
    }

    // 按时间排序
    sort.Slice(saleDetails, func(i, j int) bool {
        return saleDetails[i].Time < saleDetails[j].Time
    })

    return saleDetails, nil
}

func (s *OrderDomainServiceImpl) CalculateSaleIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (consume, refund int, error) {
    orderProducts, err := s.orderRepository.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return 0, 0, err
    }

    consume = 0
    refund = 0
    for _, orderProduct := range orderProducts {
        if orderProduct.ProductId == productId {
            if orderProduct.OrderDirection == "NORMAL" {
                consume += int(orderProduct.Quantity)
            } else if orderProduct.OrderDirection == "REFUND" {
                refund += int(orderProduct.Quantity)
            }
        }
    }

    return consume, refund, nil
}
```

### 6.3 Product领域服务实现
```go
type ProductDomainServiceImpl struct {
    productService *impl.ProductService
}

func (s *ProductDomainServiceImpl) GetFilteredProducts(ctx *gin.Context, venueId string, categoryId, searchKey *string, pageNum, pageSize int) ([]*po.Product, int64, error) {
    // 构建查询条件
    condition := map[string]interface{}{
        "venue_id": venueId,
        "state":    1, // 有效商品
    }

    if categoryId != nil && *categoryId != "all" {
        condition["category_id"] = *categoryId
    }

    if searchKey != nil && *searchKey != "" {
        condition["name_like"] = *searchKey
    }

    // 分页查询商品
    products, total, err := s.productService.FindProductsByConditionWithPagination(
        ctx, condition, pageNum, pageSize)
    if err != nil {
        return nil, 0, err
    }

    return products, total, nil
}

func (s *ProductDomainServiceImpl) GetProductBasicInfo(ctx *gin.Context, productId string) (*ProductBasicInfoVO, error) {
    product, err := s.productService.FindProductById(ctx, productId)
    if err != nil {
        return nil, err
    }

    return &ProductBasicInfoVO{
        ProductId:    productId,
        ProductName:  *product.Name,
        ProductImage: *product.ImageUrl,
        Unit:        *product.Unit,
    }, nil
}
```

## 7. 仓储层设计（DDD架构）

### 7.1 InventoryRepository接口（仅Inventory领域）
```go
type InventoryRepository interface {
    // 现有方法...

    // 获取指定时间后的入库记录（仅Inventory领域）
    GetInboundRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InboundRecord, error)

    // 保存入库记录
    SaveInboundRecord(ctx *gin.Context, record *model.InboundRecord) error

    // 获取库存快照
    GetProductStockSnapshots(ctx *gin.Context, venueId string) ([]*model.ProductStockSnapshot, error)
}
```

### 7.2 OrderRepository接口（仅Order领域）
```go
type OrderRepository interface {
    // 获取指定时间后的订单商品记录（仅Order领域）
    GetOrderProductsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]OrderProductVO, error)

    // 获取订单信息
    GetOrdersByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*model.Order, error)
}
```

### 7.3 ProductRepository接口（仅Product领域）
```go
type ProductRepository interface {
    // 根据条件查询商品（支持分页）
    FindProductsByConditionWithPagination(ctx *gin.Context, condition map[string]interface{}, pageNum, pageSize int) ([]*po.Product, int64, error)

    // 根据ID查询商品
    FindProductById(ctx *gin.Context, productId string) (*po.Product, error)

    // 获取商品分类
    GetProductCategories(ctx *gin.Context, venueId string) ([]*po.ProductCategory, error)
}
```

## 8. 控制器设计

### 8.1 StockAnalysisController
```go
type StockAnalysisController struct {
    stockAnalysisAppService StockAnalysisAppService
}

// @Summary 获取库存分析列表
// @Description 获取门店库存分析数据，基于快照+增量计算
// @Tags 库存分析
// @Accept json
// @Produce json
// @Param body body StockAnalysisReqDto true "请求体"
// @Success 200 {object} Result[StockAnalysisRespVO] "成功"
// @Router /api/inventory/analysis/list [post]
func (c *StockAnalysisController) GetStockAnalysisList(ctx *gin.Context) {
    reqDto := StockAnalysisReqDto{}
    err := ctx.ShouldBindJSON(&reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    result, err := c.stockAnalysisAppService.GetStockAnalysisList(ctx, reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, result)
}

// @Summary 获取商品库存详情
// @Description 获取单个商品的详细库存变化记录
// @Tags 库存分析
// @Accept json
// @Produce json
// @Param body body ProductStockDetailReqDto true "请求体"
// @Success 200 {object} Result[ProductStockDetailVO] "成功"
// @Router /api/inventory/analysis/detail [post]
func (c *StockAnalysisController) GetProductStockDetail(ctx *gin.Context) {
    reqDto := ProductStockDetailReqDto{}
    err := ctx.ShouldBindJSON(&reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    result, err := c.stockAnalysisAppService.GetProductStockDetail(ctx, reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, result)
}
```

### 8.2 路由配置
```go
func SetupStockAnalysisRoutes(route *gin.Engine, controller *StockAnalysisController) {
    // 库存分析相关路由
    route.POST("/api/inventory/analysis/list", controller.GetStockAnalysisList)
    route.POST("/api/inventory/analysis/detail", controller.GetProductStockDetail)
    route.POST("/api/inventory/analysis/baseline", controller.GetBaselineInfo)
}
```

## 9. DDD架构优势

### 9.1 领域边界清晰
- **Inventory领域**：专注于库存相关的业务逻辑，不依赖其他领域
- **Order领域**：专注于订单相关的业务逻辑，独立维护
- **Product领域**：专注于商品相关的业务逻辑，可独立扩展

### 9.2 职责分离明确
- **Application层**：负责用例编排和跨领域协调
- **Domain Service层**：负责单一领域内的复杂业务逻辑
- **Repository层**：负责单一领域的数据访问

### 9.3 可测试性强
```go
// 单元测试示例 - Inventory领域服务
func TestInventoryDomainService_CalculateInboundIncrement(t *testing.T) {
    // Mock InventoryRepository
    mockRepo := &MockInventoryRepository{}
    service := &InventoryDomainServiceImpl{
        inventoryRepository: mockRepo,
    }

    // 设置Mock数据
    mockRepo.On("GetInboundRecordsAfterTime", mock.Anything, "venue1", int64(1640995200)).
        Return([]*model.InboundRecord{
            {ProductId: "product1", Quantity: 10},
            {ProductId: "product1", Quantity: 5},
        }, nil)

    // 执行测试
    increment, err := service.CalculateInboundIncrement(ctx, "venue1", "product1", 1640995200)

    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 15, increment)
}

// 集成测试示例 - Application服务
func TestStockAnalysisAppService_GetStockAnalysisList(t *testing.T) {
    // Mock各个领域服务
    mockInventoryService := &MockInventoryDomainService{}
    mockOrderService := &MockOrderDomainService{}
    mockProductService := &MockProductDomainService{}

    appService := &StockAnalysisAppServiceImpl{
        inventoryDomainService: mockInventoryService,
        orderDomainService:     mockOrderService,
        productDomainService:   mockProductService,
    }

    // 设置Mock期望
    mockInventoryService.On("GetBaselineInfo", mock.Anything, "venue1").
        Return(&BaselineInfoVO{SnapshotTime: 1640995200}, nil)

    // 执行测试...
}
```

## 10. 性能优化策略

### 10.1 缓存策略（领域级别）
```go
// Inventory领域服务缓存
func (s *InventoryDomainServiceImpl) GetBaselineInfoWithCache(ctx *gin.Context, venueId string) (*BaselineInfoVO, error) {
    cacheKey := fmt.Sprintf("inventory:baseline:%s", venueId)

    // 尝试从缓存获取
    if cached := s.cacheService.Get(cacheKey); cached != nil {
        return cached.(*BaselineInfoVO), nil
    }

    // 缓存未命中，查询数据库
    baselineInfo, err := s.GetBaselineInfo(ctx, venueId)
    if err != nil {
        return nil, err
    }

    // 写入缓存（5分钟）
    s.cacheService.Set(cacheKey, baselineInfo, 5*time.Minute)

    return baselineInfo, nil
}

// Product领域服务缓存
func (s *ProductDomainServiceImpl) GetFilteredProductsWithCache(ctx *gin.Context, venueId string, categoryId *string) ([]*po.Product, error) {
    cacheKey := fmt.Sprintf("product:filtered:%s:%s", venueId, *categoryId)

    if cached := s.cacheService.Get(cacheKey); cached != nil {
        return cached.([]*po.Product), nil
    }

    products, _, err := s.GetFilteredProducts(ctx, venueId, categoryId, nil, 1, 1000)
    if err != nil {
        return nil, err
    }

    // 写入缓存（10分钟）
    s.cacheService.Set(cacheKey, products, 10*time.Minute)

    return products, nil
}
```

### 10.2 批量查询优化（领域内优化）
```go
// Inventory领域 - 批量获取入库增量
func (s *InventoryDomainServiceImpl) BatchCalculateInboundIncrements(ctx *gin.Context, venueId string, productIds []string, afterTime int64) (map[string]int, error) {
    // 一次性获取所有入库记录
    inboundRecords, err := s.inventoryRepository.GetInboundRecordsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, err
    }

    // 按商品ID分组计算
    increments := make(map[string]int)
    for _, productId := range productIds {
        increments[productId] = 0
    }

    for _, record := range inboundRecords {
        for _, item := range record.GetItems() {
            if _, exists := increments[item.GetProductId()]; exists {
                increments[item.GetProductId()] += item.GetQuantity()
            }
        }
    }

    return increments, nil
}

// Order领域 - 批量获取销售增量
func (s *OrderDomainServiceImpl) BatchCalculateSaleIncrements(ctx *gin.Context, venueId string, productIds []string, afterTime int64) (map[string]int, map[string]int, error) {
    // 一次性获取所有订单商品记录
    orderProducts, err := s.orderRepository.GetOrderProductsAfterTime(ctx, venueId, afterTime)
    if err != nil {
        return nil, nil, err
    }

    // 按商品ID分组计算
    consumeIncrements := make(map[string]int)
    refundIncrements := make(map[string]int)

    for _, productId := range productIds {
        consumeIncrements[productId] = 0
        refundIncrements[productId] = 0
    }

    for _, orderProduct := range orderProducts {
        if _, exists := consumeIncrements[orderProduct.ProductId]; exists {
            if orderProduct.OrderDirection == "NORMAL" {
                consumeIncrements[orderProduct.ProductId] += int(orderProduct.Quantity)
            } else if orderProduct.OrderDirection == "REFUND" {
                refundIncrements[orderProduct.ProductId] += int(orderProduct.Quantity)
            }
        }
    }

    return consumeIncrements, refundIncrements, nil
}
```

### 10.3 数据库索引优化
```sql
-- 库存快照表索引
CREATE INDEX idx_product_stock_snapshot_venue_product ON product_stock_snapshot(venue_id, product_id);
CREATE INDEX idx_product_stock_snapshot_utime ON product_stock_snapshot(utime);

-- 入库记录索引
CREATE INDEX idx_inventory_record_venue_time ON inventory_record(venue_id, time);
CREATE INDEX idx_inventory_record_item_product ON inventory_record_item(product_id);

-- 订单商品索引
CREATE INDEX idx_order_product_venue_time ON order_product(venue_id, create_time);
CREATE INDEX idx_order_product_product_direction ON order_product(product_id, order_direction);

-- 商品表索引
CREATE INDEX idx_product_venue_category ON product(venue_id, category_id);
CREATE INDEX idx_product_name ON product(name);
```

## 11. 与现有系统的兼容性

### 11.1 保留现有功能
- **库存校准功能**：保留作为后台数据修复工具
- **ProductStock表**：继续维护，作为缓存和备份
- **现有API接口**：保持向后兼容

### 11.2 DDD架构下的数据一致性保证
```go
// Application层 - 数据一致性检查服务
type DataConsistencyAppService struct {
    inventoryDomainService InventoryDomainService
    orderDomainService     OrderDomainService
    productDomainService   ProductDomainService
    stockReconcileService  StockReconcileAppService
}

func (s *DataConsistencyAppService) CheckDataConsistency(ctx *gin.Context, venueId string) error {
    // 1. 获取基准信息 - Inventory领域
    baselineInfo, err := s.inventoryDomainService.GetBaselineInfo(ctx, venueId)
    if err != nil {
        return err
    }

    // 2. 获取所有商品 - Product领域
    products, _, err := s.productDomainService.GetFilteredProducts(ctx, venueId, nil, nil, 1, 1000)
    if err != nil {
        return err
    }

    var inconsistencies []InconsistencyVO
    for _, product := range products {
        // 3. 计算理论库存
        inboundIncrement, err := s.inventoryDomainService.CalculateInboundIncrement(
            ctx, venueId, *product.Id, baselineInfo.SnapshotTime)
        if err != nil {
            continue
        }

        consumeIncrement, refundIncrement, err := s.orderDomainService.CalculateSaleIncrement(
            ctx, venueId, *product.Id, baselineInfo.SnapshotTime)
        if err != nil {
            continue
        }

        calculatedStock := baselineInfo.BaselineStock + inboundIncrement - consumeIncrement + refundIncrement

        // 4. 获取实际库存（从ProductStock表）
        actualStock, err := s.getActualStock(ctx, venueId, *product.Id)
        if err != nil {
            continue
        }

        // 5. 检查一致性
        if calculatedStock != actualStock {
            inconsistencies = append(inconsistencies, InconsistencyVO{
                ProductId:       *product.Id,
                CalculatedStock: calculatedStock,
                ActualStock:     actualStock,
                Difference:      calculatedStock - actualStock,
            })
        }
    }

    // 6. 处理不一致数据
    if len(inconsistencies) > 0 {
        s.logInconsistencies(ctx, inconsistencies)
        return s.stockReconcileService.AutoReconcile(ctx, venueId, inconsistencies)
    }

    return nil
}
```

### 11.3 领域事件机制
```go
// 领域事件 - 库存变化事件
type StockChangedEvent struct {
    VenueId     string
    ProductId   string
    ChangeType  string // "INBOUND"/"SALE"/"REFUND"
    Quantity    int
    Timestamp   int64
}

// Inventory领域 - 发布入库事件
func (s *InventoryDomainServiceImpl) PublishInboundEvent(ctx *gin.Context, venueId, productId string, quantity int) {
    event := StockChangedEvent{
        VenueId:    venueId,
        ProductId:  productId,
        ChangeType: "INBOUND",
        Quantity:   quantity,
        Timestamp:  time.Now().Unix(),
    }

    s.eventBus.Publish("stock.changed", event)
}

// Order领域 - 发布销售事件
func (s *OrderDomainServiceImpl) PublishSaleEvent(ctx *gin.Context, venueId, productId string, quantity int, orderDirection string) {
    changeType := "SALE"
    if orderDirection == "REFUND" {
        changeType = "REFUND"
    }

    event := StockChangedEvent{
        VenueId:    venueId,
        ProductId:  productId,
        ChangeType: changeType,
        Quantity:   quantity,
        Timestamp:  time.Now().Unix(),
    }

    s.eventBus.Publish("stock.changed", event)
}
```

## 10. 监控和日志

### 10.1 性能监控
- **API响应时间**：监控库存分析接口的响应时间
- **查询性能**：监控数据库查询的执行时间
- **缓存命中率**：监控缓存的命中率和效果

### 10.2 业务监控
- **数据一致性**：定期检查计算结果与实际库存的一致性
- **异常库存**：监控负库存和异常变化的商品
- **计算错误**：监控增量计算过程中的错误

### 10.3 日志记录
```go
// 操作日志
func (s *StockAnalysisAppServiceImpl) logStockAnalysisOperation(ctx *gin.Context, operation string, details map[string]interface{}) {
    logEntry := map[string]interface{}{
        "operation": operation,
        "timestamp": time.Now().Unix(),
        "details":   details,
    }
    
    s.logger.Info("StockAnalysis Operation", logEntry)
}

// 异常日志
func (s *StockAnalysisAppServiceImpl) logStockAbnormal(ctx *gin.Context, productId, venueId string, abnormalType string, details map[string]interface{}) {
    logEntry := map[string]interface{}{
        "type":      "stock_abnormal",
        "productId": productId,
        "venueId":   venueId,
        "abnormalType": abnormalType,
        "details":   details,
        "timestamp": time.Now().Unix(),
    }
    
    s.logger.Warn("Stock Abnormal Detected", logEntry)
}
```

## 11. 部署和运维

### 11.1 部署策略
- **灰度发布**：先在部分门店测试新功能
- **数据迁移**：确保现有数据的完整性
- **回滚方案**：准备快速回滚到原有功能的方案

### 11.2 运维监控
- **健康检查**：定期检查服务健康状态
- **数据备份**：重要数据的定期备份
- **性能调优**：根据监控数据进行性能优化

## 12. DDD架构实施计划

### 12.1 开发阶段（DDD架构）
1. **第一阶段（3周）**：
   - 设计和实现领域服务接口（InventoryDomainService、OrderDomainService、ProductDomainService）
   - 重构现有Repository，按领域边界分离
   - 实现StockAnalysisAppService基础功能

2. **第二阶段（2周）**：
   - 完善各领域服务的实现
   - 实现批量查询优化
   - 添加缓存策略和性能优化

3. **第三阶段（1周）**：
   - 集成测试和性能测试
   - 监控和日志完善
   - 文档和部署准备

### 12.2 DDD测试策略
```go
// 领域服务单元测试
func TestInventoryDomainService(t *testing.T) {
    // 测试Inventory领域的业务逻辑
}

func TestOrderDomainService(t *testing.T) {
    // 测试Order领域的业务逻辑
}

// Application服务集成测试
func TestStockAnalysisAppService(t *testing.T) {
    // 测试跨领域的协调逻辑
}

// 端到端测试
func TestStockAnalysisAPI(t *testing.T) {
    // 测试完整的API流程
}
```

### 12.3 架构迁移策略
1. **渐进式重构**：逐步将现有代码重构为DDD架构
2. **向后兼容**：保持现有API的兼容性
3. **并行开发**：新功能使用DDD架构，现有功能逐步迁移
4. **数据一致性**：确保重构过程中数据的一致性

### 12.4 风险控制
- **领域边界验证**：确保领域划分的合理性
- **性能影响评估**：评估DDD架构对性能的影响
- **团队培训**：确保团队理解DDD概念和实践
- **代码审查**：严格的代码审查确保架构质量

## 13. DDD架构优势总结

### 13.1 技术优势
1. **领域边界清晰**：每个领域职责单一，便于维护和扩展
2. **可测试性强**：领域服务可以独立测试，提高代码质量
3. **松耦合设计**：领域间通过接口交互，降低耦合度
4. **代码复用性**：领域服务可以在不同的应用场景中复用

### 13.2 业务优势
1. **业务逻辑集中**：相关的业务逻辑集中在对应的领域服务中
2. **需求变更适应性**：单一领域的变更不会影响其他领域
3. **团队协作效率**：不同团队可以专注于不同的领域开发
4. **知识传承**：领域模型有助于业务知识的传承和积累

### 13.3 长期价值
1. **架构演进能力**：为未来的架构演进提供了良好的基础
2. **微服务准备**：DDD架构为未来可能的微服务拆分做好了准备
3. **技术债务控制**：清晰的架构边界有助于控制技术债务
4. **开发效率提升**：长期来看，DDD架构能够提升开发效率

这个基于DDD原则重新设计的服务端方案，不仅解决了当前的业务需求，更为系统的长期发展奠定了坚实的架构基础。通过清晰的领域划分和职责分离，系统将具备更好的可维护性、可扩展性和可测试性。
