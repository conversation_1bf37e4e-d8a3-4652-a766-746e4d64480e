# 库存盘点功能开发说明文档

## 1. 项目架构分析

### 1.1 入库记录架构分析

通过分析入库记录的实现，发现项目采用了**混合架构模式**：

#### 1.1.1 DDD架构（入库记录使用）
```
Controller -> Application Service -> Domain Service -> Repository -> Infrastructure
```

**文件结构：**
- `controller/InboundRecordController.go` - 控制器层
- `application/inventory/InboundAppService.go` - 应用服务接口
- `application/inventory/impl/InboundAppServiceImpl.go` - 应用服务实现
- `domain/inventory/service/InventoryDomainService.go` - 领域服务
- `domain/inventory/model/InboundRecord.go` - 领域模型
- `infrastructure/repository/InventoryRepository.go` - 仓储接口
- `infrastructure/repository/InventoryRepositoryImpl.go` - 仓储实现

#### 1.1.2 传统三层架构（其他功能使用）
```
Controller -> Service -> PO/Transfer
```

**文件结构：**
- `controller/XxxController.go` - 控制器层
- `service/impl/XxxService.go` - 服务层
- `service/po/Xxx.go` - 持久化对象
- `service/transfer/XxxTransfer.go` - 数据转换
- `api/req/XxxReqDto.go` - 请求DTO
- `api/vo/XxxVO.go` - 响应VO

### 1.2 项目规范要求

根据feedback要求，**必须严格按照InventoryRecord的实现规范**，即使用**传统三层架构**而非DDD架构。

## 2. 库存盘点功能设计

### 2.1 功能概述

库存盘点功能用于定期核查商品库存数量，记录盘盈盘亏情况，为库存管理提供数据支持。

### 2.2 核心实体

#### 2.2.1 盘点记录主表（inventory_check_record）
- 记录盘点的基本信息：门店、经手人、操作人、时间等
- 汇总盘盈盘亏数量

#### 2.2.2 盘点记录明细表（inventory_check_record_item）
- 记录每个商品的盘点详情：库存数量、盘点数量、盈亏数量

### 2.3 业务流程

1. **创建盘点记录** - 录入盘点信息和商品明细
2. **查询盘点记录** - 分页查询、详情查询
3. **更新盘点记录** - 修改盘点信息
4. **删除盘点记录** - 软删除

## 3. 目录结构和文件功能

### 3.1 核心文件列表

```
erp_managent/
├── api/
│   ├── req/
│   │   └── InventoryCheckRecordReqDto.go          # 请求DTO定义
│   └── vo/
│       └── InventoryCheckRecordVO.go              # 响应VO定义
├── controller/
│   └── InventoryCheckRecordController.go          # 控制器层
├── service/
│   ├── impl/
│   │   └── InventoryCheckRecordService.go         # 服务层实现
│   ├── po/
│   │   └── InventoryCheckRecord.go                # 持久化对象
│   └── transfer/
│       └── InventoryCheckRecordTransfer.go        # 数据转换
├── router/
│   └── InventoryRoute.go                          # 路由配置
└── sql/
    └── inventory_check_record.sql                 # 数据库表结构
```

### 3.2 文件功能说明

#### 3.2.1 PO层（service/po/）
- **InventoryCheckRecord.go** - 定义数据库实体结构
- 包含GORM标签、表名映射、GetId方法

#### 3.2.2 Service层（service/impl/）
- **InventoryCheckRecordService.go** - 业务逻辑处理
- 包含CRUD操作、业务规则验证、数据处理

#### 3.2.3 Controller层（controller/）
- **InventoryCheckRecordController.go** - HTTP请求处理
- 参数验证、调用Service、返回响应

#### 3.2.4 DTO/VO层（api/）
- **req/InventoryCheckRecordReqDto.go** - 请求参数定义
- **vo/InventoryCheckRecordVO.go** - 响应数据定义

#### 3.2.5 Transfer层（service/transfer/）
- **InventoryCheckRecordTransfer.go** - PO与VO转换

#### 3.2.6 Router层（router/）
- **InventoryRoute.go** - 路由配置和Controller绑定

## 4. 参考代码模式

### 4.1 参考AsExampleController模式

```go
type InventoryCheckRecordController struct{}

var (
    inventoryCheckRecordService  = impl.InventoryCheckRecordService{}
    inventoryCheckRecordTransfer = transfer.InventoryCheckRecordTransfer{}
)

func (controller *InventoryCheckRecordController) CreateInventoryCheckRecord(ctx *gin.Context) {
    reqDto := req.CreateInventoryCheckRecordReqDto{}
    err := ctx.ShouldBindJSON(&reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }
    
    // 业务逻辑处理...
    
    Result_success[any](ctx, result)
}
```

### 4.2 参考InventoryRecordService模式

```go
type InventoryCheckRecordService struct {}

func (service *InventoryCheckRecordService) CreateInventoryCheckRecord(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
    return Save(record)
}

func (service *InventoryCheckRecordService) FindAllInventoryCheckRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryInventoryCheckRecordReqDto) (list *[]po.InventoryCheckRecord, total int64, err error) {
    // 分页查询逻辑...
}
```

## 5. API接口设计

### 5.1 路由配置
```go
// 盘点管理路由
route.POST("/api/inventory/check/record/create", inventoryCheckRecordController.CreateInventoryCheckRecord)
route.POST("/api/inventory/check/record/list", inventoryCheckRecordController.ListInventoryCheckRecords)
route.POST("/api/inventory/check/record/detail", inventoryCheckRecordController.GetInventoryCheckRecordDetail)
route.POST("/api/inventory/check/record/update", inventoryCheckRecordController.UpdateInventoryCheckRecord)
route.POST("/api/inventory/check/record/delete", inventoryCheckRecordController.DeleteInventoryCheckRecord)
```

### 5.2 接口功能
1. **POST /api/inventory/check/record/create** - 创建盘点记录
2. **POST /api/inventory/check/record/list** - 分页查询盘点记录列表
3. **POST /api/inventory/check/record/detail** - 查询盘点记录详情
4. **POST /api/inventory/check/record/update** - 更新盘点记录
5. **POST /api/inventory/check/record/delete** - 删除盘点记录

## 6. 数据库设计

### 6.1 主表结构
```sql
CREATE TABLE inventory_check_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL,
    handler VARCHAR(64) NOT NULL,
    operator VARCHAR(64) NOT NULL,
    time INT NOT NULL,
    record_number VARCHAR(64) NOT NULL,
    remark VARCHAR(255),
    profit_quantity_total INT DEFAULT 0,
    loss_quantity_total INT DEFAULT 0,
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    version INT DEFAULT 0
);
```

### 6.2 明细表结构
```sql
CREATE TABLE inventory_check_record_item (
    id VARCHAR(64) PRIMARY KEY,
    check_record_id VARCHAR(64) NOT NULL,
    product_id VARCHAR(64) NOT NULL,
    stock_quantity INT DEFAULT 0,
    check_quantity INT DEFAULT 0,
    profit_loss_quantity INT DEFAULT 0,
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0
);
```

## 7. 开发规范

### 7.1 命名规范
- 文件名：大驼峰命名，如`InventoryCheckRecordController.go`
- 结构体：大驼峰命名，如`InventoryCheckRecord`
- 方法名：大驼峰命名，如`CreateInventoryCheckRecord`
- 变量名：小驼峰命名，如`recordId`

### 7.2 错误处理
- 使用项目统一的错误码：`GeneralCodes.ParamError.Code`、`GeneralCodes.ServerIsBuzy.Code`
- 使用统一的响应格式：`Result_success`、`Result_fail`

### 7.3 数据验证
- Controller层进行基础参数验证
- Service层进行业务规则验证
- 使用指针类型处理可选字段

### 7.4 事务处理
- 主记录和明细记录的创建/更新使用事务保证一致性
- 提供带事务和不带事务的方法版本

## 8. 测试要求

### 8.1 单元测试
- 测试PO模型的基本功能
- 测试Service层的业务逻辑
- 测试数据转换功能

### 8.2 集成测试
- 测试完整的API调用流程
- 测试数据库操作
- 测试异常情况处理

## 9. 注意事项

1. **严格遵循项目现有架构模式**，不使用DDD架构
2. **参考InventoryRecord的实现方式**，保持代码风格一致
3. **使用项目现有的工具函数**，如`util.GetUUID()`、`util.GetItPtr()`
4. **遵循软删除原则**，使用state字段标记删除状态
5. **注意字段类型一致性**，时间字段使用int64类型存储Unix时间戳
