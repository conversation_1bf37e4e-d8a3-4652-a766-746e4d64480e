# 库存盘点接口功能详细说明文档

## 1. 接口概览

库存盘点功能提供了完整的盘点记录管理接口，支持盘点记录的创建、查询、更新和删除操作。所有接口采用POST方法，使用JSON格式进行数据交互。

### 1.1 接口列表

| 序号 | 接口路径 | 功能描述 | 方法 |
|------|----------|----------|------|
| 1 | `/api/inventory/check/record/create` | 创建盘点记录 | POST |
| 2 | `/api/inventory/check/record/list` | 分页查询盘点记录列表 | POST |
| 3 | `/api/inventory/check/record/detail` | 查询盘点记录详情 | POST |
| 4 | `/api/inventory/check/record/update` | 更新盘点记录 | POST |
| 5 | `/api/inventory/check/record/delete` | 删除盘点记录 | POST |

## 2. 接口详细说明

### 2.1 创建盘点记录

#### 2.1.1 基本信息
- **接口路径**: `/api/inventory/check/record/create`
- **请求方法**: POST
- **功能描述**: 创建新的库存盘点记录，包含主记录和明细记录

#### 2.1.2 请求参数
```json
{
  "venueId": "venue_001",           // 门店ID，必填
  "handler": "张三",                // 经手人，必填
  "operator": "李四",               // 操作人，必填
  "remark": "月度盘点",             // 备注，可选
  "items": [                        // 盘点明细，必填，至少一项
    {
      "productId": "product_001",   // 商品ID，必填
      "stockQuantity": 100,         // 库存数量，必填，>=0
      "checkQuantity": 103          // 盘点数量，必填，>=0
    },
    {
      "productId": "product_002",
      "stockQuantity": 50,
      "checkQuantity": 48
    }
  ]
}
```

#### 2.1.3 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": "check_record_001",     // 盘点记录ID
    "recordNumber": "CHECK20240120001", // 盘点单号（自动生成）
    "profitQuantityTotal": 3,           // 盘盈数量合计
    "lossQuantityTotal": 2              // 盘亏数量合计
  }
}
```

#### 2.1.4 业务逻辑
1. **参数验证**: 验证必填字段和数据格式
2. **盘点单号生成**: 自动生成格式为"CHECK+年月日+3位序号"的盘点单号
3. **盈亏计算**: 自动计算每个商品的盈亏数量（盘点数量-库存数量）
4. **汇总计算**: 自动计算盘盈总数和盘亏总数
5. **事务保存**: 使用数据库事务确保主记录和明细记录的一致性

#### 2.1.5 错误处理
- **400**: 参数错误（必填字段为空、数据格式错误等）
- **500**: 服务器内部错误（数据库操作失败等）

### 2.2 分页查询盘点记录列表

#### 2.2.1 基本信息
- **接口路径**: `/api/inventory/check/record/list`
- **请求方法**: POST
- **功能描述**: 分页查询盘点记录列表，支持关键词搜索和门店筛选

#### 2.2.2 请求参数
```json
{
  "venueId": "venue_001",    // 门店ID，可选，为空则查询所有门店
  "searchKey": "张三",       // 搜索关键词，可选，支持盘点单号、经手人、操作人搜索
  "pageNum": 1,              // 页码，必填，>=1
  "pageSize": 20             // 每页大小，必填，1-100
}
```

#### 2.2.3 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 20,
    "total": 100,
    "data": [
      {
        "recordId": "check_record_001",
        "recordNumber": "CHECK20240120001",
        "handler": "张三",
        "operator": "李四",
        "checkTime": 1705747200,           // Unix时间戳
        "remark": "月度盘点",
        "totalProducts": 3,                // 盘点商品总数
        "profitQuantityTotal": 3,          // 盘盈数量合计
        "lossQuantityTotal": 2             // 盘亏数量合计
      }
    ]
  }
}
```

#### 2.2.4 业务逻辑
1. **参数处理**: 设置默认分页参数，处理搜索条件
2. **数据查询**: 根据条件查询盘点记录，支持模糊搜索
3. **分页处理**: 计算总数和分页数据
4. **数据转换**: 将PO对象转换为VO对象
5. **排序**: 按盘点时间倒序排列

#### 2.2.5 搜索功能
- **盘点单号**: 支持模糊匹配
- **经手人**: 支持模糊匹配
- **操作人**: 支持模糊匹配
- **门店筛选**: 精确匹配

### 2.3 查询盘点记录详情

#### 2.3.1 基本信息
- **接口路径**: `/api/inventory/check/record/detail`
- **请求方法**: POST
- **功能描述**: 根据盘点记录ID查询完整的盘点记录详情，包含所有明细信息

#### 2.3.2 请求参数
```json
{
  "recordId": "check_record_001"    // 盘点记录ID，必填
}
```

#### 2.3.3 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "record": {
      "recordId": "check_record_001",
      "recordNumber": "CHECK20240120001",
      "handler": "张三",
      "operator": "李四",
      "checkTime": 1705747200,
      "remark": "月度盘点",
      "totalProducts": 3,
      "profitQuantityTotal": 3,
      "lossQuantityTotal": 2
    },
    "items": [
      {
        "productId": "product_001",
        "productName": "茅台酒",           // 自动关联商品信息
        "productImage": "image_url",      // 商品图片
        "unit": "瓶",                     // 商品单位
        "stockQuantity": 100,             // 库存数量
        "checkQuantity": 103,             // 盘点数量
        "profitLossQuantity": 3           // 盈亏数量（正数为盘盈，负数为盘亏）
      },
      {
        "productId": "product_002",
        "productName": "五粮液",
        "productImage": "image_url",
        "unit": "瓶",
        "stockQuantity": 50,
        "checkQuantity": 48,
        "profitLossQuantity": -2
      }
    ]
  }
}
```

#### 2.3.4 业务逻辑
1. **记录查询**: 根据ID查询主记录信息
2. **明细查询**: 查询所有相关的明细记录
3. **商品关联**: 自动关联商品信息（名称、图片、单位等）
4. **数据组装**: 组装完整的响应数据
5. **异常处理**: 处理商品信息缺失的情况

#### 2.3.5 商品信息处理
- **正常情况**: 显示完整的商品信息
- **商品不存在**: 显示"未知商品"，不影响盘点数据
- **获取失败**: 使用默认值，确保接口正常响应

### 2.4 更新盘点记录

#### 2.4.1 基本信息
- **接口路径**: `/api/inventory/check/record/update`
- **请求方法**: POST
- **功能描述**: 更新已存在的盘点记录，支持修改主记录信息和明细信息

#### 2.4.2 请求参数
```json
{
  "recordId": "check_record_001",   // 盘点记录ID，必填
  "handler": "张三",                // 经手人，必填
  "operator": "李四",               // 操作人，必填
  "remark": "月度盘点（已修正）",    // 备注，可选
  "items": [                        // 新的盘点明细，必填
    {
      "productId": "product_001",
      "stockQuantity": 100,
      "checkQuantity": 102          // 修正后的盘点数量
    },
    {
      "productId": "product_002",
      "stockQuantity": 50,
      "checkQuantity": 49
    }
  ]
}
```

#### 2.4.3 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": "check_record_001",
    "profitQuantityTotal": 2,       // 重新计算的盘盈总数
    "lossQuantityTotal": 1          // 重新计算的盘亏总数
  }
}
```

#### 2.4.4 业务逻辑
1. **存在性验证**: 验证盘点记录是否存在
2. **参数验证**: 验证更新参数的有效性
3. **重新计算**: 重新计算盈亏数量和汇总数据
4. **事务更新**: 使用事务更新主记录和明细记录
5. **明细处理**: 删除原有明细，创建新明细

#### 2.4.5 更新策略
- **主记录**: 更新可修改字段（经手人、操作人、备注、汇总数据）
- **明细记录**: 完全替换（删除原有，创建新的）
- **盘点单号**: 不允许修改
- **盘点时间**: 不允许修改

### 2.5 删除盘点记录

#### 2.5.1 基本信息
- **接口路径**: `/api/inventory/check/record/delete`
- **请求方法**: POST
- **功能描述**: 删除指定的盘点记录（软删除）

#### 2.5.2 请求参数
```json
{
  "recordId": "check_record_001"    // 盘点记录ID，必填
}
```

#### 2.5.3 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 2.5.4 业务逻辑
1. **存在性验证**: 验证盘点记录是否存在
2. **软删除**: 将记录状态标记为删除（state=1）
3. **级联删除**: 同时删除相关的明细记录
4. **数据保留**: 保留历史数据，支持数据恢复

#### 2.5.5 删除策略
- **软删除**: 不物理删除数据，只修改状态标记
- **级联删除**: 主记录和明细记录同时删除
- **权限控制**: 可根据需要添加删除权限验证
- **审计日志**: 记录删除操作的审计信息

## 3. 通用特性

### 3.1 统一响应格式
所有接口都使用统一的响应格式：
```json
{
  "code": 200,          // 状态码：200成功，400参数错误，500服务器错误
  "message": "success", // 响应消息
  "data": {}           // 响应数据，失败时为null
}
```

### 3.2 错误码说明
- **200**: 操作成功
- **400**: 参数错误（必填字段缺失、格式错误等）
- **404**: 资源不存在（盘点记录不存在）
- **500**: 服务器内部错误（数据库操作失败等）

### 3.3 数据验证
- **必填字段**: 严格验证必填字段
- **数据格式**: 验证数据类型和格式
- **业务规则**: 验证业务逻辑规则
- **数据范围**: 验证数值范围和长度限制

### 3.4 性能优化
- **分页查询**: 支持大数据量的分页处理
- **索引优化**: 数据库查询使用索引优化
- **缓存策略**: 可根据需要添加缓存机制
- **异步处理**: 支持异步操作提升性能

### 3.5 安全特性
- **参数验证**: 防止SQL注入和XSS攻击
- **权限控制**: 可根据需要添加权限验证
- **审计日志**: 记录关键操作的审计信息
- **数据加密**: 敏感数据可进行加密处理

## 4. 使用示例

### 4.1 完整的盘点流程示例
```bash
# 1. 创建盘点记录
curl -X POST http://localhost:8080/api/inventory/check/record/create \
  -H "Content-Type: application/json" \
  -d '{"venueId":"venue_001","handler":"张三","operator":"李四","remark":"月度盘点","items":[{"productId":"product_001","stockQuantity":100,"checkQuantity":103}]}'

# 2. 查询盘点记录列表
curl -X POST http://localhost:8080/api/inventory/check/record/list \
  -H "Content-Type: application/json" \
  -d '{"venueId":"venue_001","pageNum":1,"pageSize":20}'

# 3. 查询盘点记录详情
curl -X POST http://localhost:8080/api/inventory/check/record/detail \
  -H "Content-Type: application/json" \
  -d '{"recordId":"check_record_001"}'

# 4. 更新盘点记录
curl -X POST http://localhost:8080/api/inventory/check/record/update \
  -H "Content-Type: application/json" \
  -d '{"recordId":"check_record_001","handler":"张三","operator":"李四","remark":"月度盘点（已修正）","items":[{"productId":"product_001","stockQuantity":100,"checkQuantity":102}]}'

# 5. 删除盘点记录
curl -X POST http://localhost:8080/api/inventory/check/record/delete \
  -H "Content-Type: application/json" \
  -d '{"recordId":"check_record_001"}'
```

## 5. 注意事项

### 5.1 数据一致性
- 使用数据库事务确保主记录和明细记录的一致性
- 盈亏数量和汇总数据自动计算，确保数据准确性

### 5.2 并发处理
- 支持多用户并发操作
- 使用乐观锁或悲观锁处理并发更新

### 5.3 数据备份
- 软删除机制保证数据可恢复
- 建议定期备份重要的盘点数据

### 5.4 监控告警
- 监控接口响应时间和成功率
- 设置异常情况的告警机制

库存盘点接口功能完整、稳定、易用，支持完整的盘点业务流程，为库存管理提供了强有力的技术支持。
