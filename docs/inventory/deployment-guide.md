# 盘点功能部署指南

## 1. 数据库迁移

### 1.1 执行迁移脚本
```bash
# 连接到MySQL数据库
mysql -u username -p database_name

# 执行迁移脚本
source docs/inventory/database-migration.sql
```

### 1.2 验证表结构
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE '%inventory_check%';

-- 检查表结构
DESCRIBE inventory_check_record;
DESCRIBE inventory_check_record_item;

-- 检查索引
SHOW INDEX FROM inventory_check_record;
SHOW INDEX FROM inventory_check_record_item;
```

## 2. 代码集成

### 2.1 依赖注入配置
在主程序中添加依赖注入配置：

```go
// main.go 或 wire.go
func InitializeInventoryCheckServices(db *gorm.DB) *controller.InventoryCheckRecordController {
    // 创建Repository
    checkRecordRepo := repository.NewInventoryCheckRecordRepository(db)
    
    // 创建Domain Service
    checkRecordService := impl.NewInventoryCheckRecordService(checkRecordRepo)
    
    // 创建Application Service
    productService := impl.NewProductService() // 假设已存在
    checkRecordAppService := impl.NewInventoryCheckRecordAppService(checkRecordService, productService)
    
    // 创建Controller
    return controller.NewInventoryCheckRecordController(checkRecordAppService)
}
```

### 2.2 路由配置
在路由配置文件中添加盘点相关路由：

```go
// router.go
func SetupInventoryCheckRoutes(r *gin.Engine, controller *controller.InventoryCheckRecordController) {
    inventoryGroup := r.Group("/api/inventory/check")
    {
        inventoryGroup.POST("/record/create", controller.CreateCheckRecord)
        inventoryGroup.POST("/record/list", controller.GetCheckRecordList)
        inventoryGroup.POST("/record/detail", controller.GetCheckRecordDetail)
        inventoryGroup.POST("/record/update", controller.UpdateCheckRecord)
        inventoryGroup.POST("/record/delete", controller.DeleteCheckRecord)
    }
}
```

## 3. 配置文件

### 3.1 数据库配置
确保数据库配置正确：

```yaml
# config.yaml
database:
  host: localhost
  port: 3306
  username: your_username
  password: your_password
  database: your_database
  charset: utf8mb4
  parseTime: true
  loc: Local
```

### 3.2 日志配置
配置日志记录：

```yaml
# config.yaml
log:
  level: info
  format: json
  output: stdout
  file: logs/app.log
```

## 4. 测试验证

### 4.1 单元测试
```bash
# 运行单元测试
go test ./erp_managent/test/inventory_check_test.go -v

# 运行性能测试
go test ./erp_managent/test/inventory_check_test.go -bench=. -v
```

### 4.2 API测试
使用Postman或curl测试API接口：

```bash
# 创建盘点记录
curl -X POST http://localhost:8080/api/inventory/check/record/create \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "venue_001",
    "handler": "张三",
    "operator": "李四",
    "remark": "月度盘点",
    "items": [
      {
        "productId": "product_001",
        "stockQuantity": 100,
        "checkQuantity": 103
      },
      {
        "productId": "product_002",
        "stockQuantity": 50,
        "checkQuantity": 48
      }
    ]
  }'

# 获取盘点记录列表
curl -X POST http://localhost:8080/api/inventory/check/record/list \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "venue_001",
    "pageNum": 1,
    "pageSize": 20
  }'
```

## 5. 监控和日志

### 5.1 性能监控
添加性能监控中间件：

```go
// middleware/performance.go
func PerformanceMonitor() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        c.Next()
        duration := time.Since(start)
        
        // 记录慢查询
        if duration > 1*time.Second {
            log.Warnf("Slow API: %s %s took %v", c.Request.Method, c.Request.URL.Path, duration)
        }
    }
}
```

### 5.2 业务日志
在关键业务节点添加日志：

```go
// 在Service实现中添加日志
func (s *InventoryCheckRecordServiceImpl) CreateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error {
    log.Infof("开始创建盘点记录: venueId=%s, handler=%s, items=%d", 
        record.GetVenueId(), record.GetHandler(), len(record.GetItems()))
    
    err := s.checkRecordRepository.Create(ctx, record)
    if err != nil {
        log.Errorf("创建盘点记录失败: %v", err)
        return err
    }
    
    log.Infof("盘点记录创建成功: recordId=%s, recordNumber=%s", 
        record.GetId(), record.GetRecordNumber())
    return nil
}
```

## 6. 安全配置

### 6.1 权限控制
添加权限验证中间件：

```go
// middleware/auth.go
func CheckInventoryPermission() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 验证用户是否有盘点权限
        userRole := c.GetString("userRole")
        if userRole != "admin" && userRole != "inventory_manager" {
            c.JSON(403, gin.H{"error": "权限不足"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

### 6.2 数据验证
确保输入数据的安全性：

```go
// 在Controller中添加额外验证
func (c *InventoryCheckRecordController) CreateCheckRecord(ctx *gin.Context) {
    var reqDto req.CreateCheckRecordReqDto
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        common.Result_fail[any](ctx, common.GeneralCodes.ParamError.Code, err.Error())
        return
    }
    
    // 额外的业务验证
    if len(reqDto.Items) > 100 {
        common.Result_fail[any](ctx, common.GeneralCodes.ParamError.Code, "单次盘点商品数量不能超过100个")
        return
    }
    
    // ... 其他验证逻辑
}
```

## 7. 部署步骤

### 7.1 开发环境部署
1. 执行数据库迁移脚本
2. 更新代码并重新编译
3. 重启应用服务
4. 运行测试验证功能

### 7.2 生产环境部署
1. 备份现有数据库
2. 在测试环境验证迁移脚本
3. 维护窗口期间执行迁移
4. 部署新版本代码
5. 验证功能正常
6. 监控系统运行状态

## 8. 回滚方案

### 8.1 代码回滚
```bash
# 回滚到上一个版本
git checkout previous_version_tag
go build -o app main.go
systemctl restart app
```

### 8.2 数据库回滚
```sql
-- 如果需要回滚数据库更改
DROP TABLE IF EXISTS inventory_check_record_item;
DROP TABLE IF EXISTS inventory_check_record;
```

## 9. 常见问题

### 9.1 数据库连接问题
- 检查数据库配置是否正确
- 确认数据库服务是否正常运行
- 验证网络连接是否正常

### 9.2 权限问题
- 确认数据库用户权限
- 检查应用程序权限配置
- 验证API访问权限

### 9.3 性能问题
- 检查数据库索引是否正确创建
- 监控API响应时间
- 分析慢查询日志

## 10. 联系支持

如遇到部署问题，请联系：
- 技术支持：[技术支持邮箱]
- 开发团队：[开发团队联系方式]
- 紧急联系：[紧急联系方式]
