# 服务端库存功能开发任务文档

## 1. 项目概述

### 1.1 开发目标
基于DDD架构实现库存分析和盘点功能，为小程序端提供高性能、准确的库存管理服务。

### 1.2 优先级排序
1. **最高优先级**：盘点记录功能（立即开发，简化版：仅数量管理）
2. **高优先级**：库存分析功能（核心功能）
3. **中优先级**：商品详情页（可后期实现）
4. **低优先级**：盘点金额功能、高级功能和优化

### 1.3 技术架构
- **DDD架构**：Inventory、Order、Product三个领域
- **分层设计**：Application → Domain Service → Repository
- **数据模式**：快照表 + 增量计算

## 2. 开发模块拆解

### 2.1 基础设施模块（Foundation）
**目标**：建立DDD架构基础和核心数据模型

#### 2.1.1 领域服务接口定义
- `InventoryDomainService` 接口
- `OrderDomainService` 接口  
- `ProductDomainService` 接口

#### 2.1.2 数据模型扩展
- 库存分析相关VO定义
- 盘点相关VO定义
- 基准信息VO定义

#### 2.1.3 Repository接口重构
- `InventoryRepository` 职责分离
- `OrderRepository` 接口定义
- `ProductRepository` 接口定义

### 2.2 盘点记录模块（InventoryCheck）
**目标**：实现简化版库存盘点记录功能，与入库记录保持一致的操作模式

#### 2.2.1 盘点记录数据模型（简化版）
- 盘点记录主表设计（类似入库记录，无金额字段）
- 盘点记录明细表设计（仅数量字段）
- 盈亏数量计算逻辑

#### 2.2.2 盘点记录业务逻辑（简化版）
- 盘点记录创建和保存
- 盈亏数量计算（不涉及金额）
- 盘盈/盘亏数量汇总
- 与库存快照的集成

#### 2.2.3 盘点记录API接口
- 新增盘点记录API
- 盘点记录列表查询API
- 盘点记录详情API

### 2.3 库存分析模块（StockAnalysis）
**目标**：实现库存分析核心功能

#### 2.3.1 领域服务实现
- `InventoryDomainServiceImpl`
- `OrderDomainServiceImpl`
- `ProductDomainServiceImpl`

#### 2.3.2 应用服务实现
- `StockAnalysisAppServiceImpl`
- 库存分析列表API
- 基准信息API

#### 2.3.3 控制器和路由
- `StockAnalysisController`
- API路由配置
- 参数验证和错误处理

### 2.4 性能优化模块（Performance）
**目标**：提升系统性能和用户体验

#### 2.4.1 缓存策略
- 基准信息缓存
- 商品列表缓存
- 计算结果缓存

#### 2.4.2 查询优化
- 批量查询实现
- 数据库索引优化
- SQL查询优化

## 3. 详细开发任务

### 3.1 第一阶段：基础设施建设（预计3天）

#### 任务1.1：领域服务接口定义（1天）
**负责人**：后端架构师
**优先级**：P0（最高）

**具体任务：**
```go
// 创建文件：erp_managent/domain/inventory/service/InventoryDomainService.go
type InventoryDomainService interface {
    GetBaselineInfo(ctx *gin.Context, venueId string) (*vo.BaselineInfoVO, error)
    GetInboundDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]vo.InboundDetailVO, error)
    CalculateInboundIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error)
}

// 创建文件：erp_managent/domain/order/service/OrderDomainService.go
type OrderDomainService interface {
    GetSaleDetailsAfterTime(ctx *gin.Context, venueId, productId string, afterTime int64) ([]vo.SaleDetailVO, error)
    CalculateSaleIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (consume, refund int, error)
}

// 创建文件：erp_managent/domain/product/service/ProductDomainService.go
type ProductDomainService interface {
    GetFilteredProducts(ctx *gin.Context, venueId string, categoryId, searchKey *string, pageNum, pageSize int) ([]*po.Product, int64, error)
    GetProductBasicInfo(ctx *gin.Context, productId string) (*vo.ProductBasicInfoVO, error)
}
```

**验收标准：**
- [ ] 三个领域服务接口定义完成
- [ ] 接口方法签名符合DDD规范
- [ ] 代码通过编译检查

#### 任务1.2：数据模型定义（1天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
```go
// 创建文件：erp_managent/api/vo/StockAnalysisVO.go
type StockAnalysisItemVO struct {
    ProductId        string `json:"productId"`
    ProductName      string `json:"productName"`
    ProductImage     string `json:"productImage"`
    Unit            string `json:"unit"`
    BaselineTime     int64  `json:"baselineTime"`
    BaselineStock    int    `json:"baselineStock"`
    InboundChange    int    `json:"inboundChange"`
    SaleChange       int    `json:"saleChange"`
    NetChange        int    `json:"netChange"`
    CurrentStock     int    `json:"currentStock"`
    HasAlert         bool   `json:"hasAlert"`
}

type BaselineInfoVO struct {
    SnapshotTime    int64  `json:"snapshotTime"`
    Description     string `json:"description"`
    SnapshotType    string `json:"snapshotType"`
    TotalProducts   int    `json:"totalProducts"`
}

// 创建文件：erp_managent/api/req/StockAnalysisReqDto.go
type StockAnalysisReqDto struct {
    VenueId    string   `json:"venueId" binding:"required"`
    CategoryId *string  `json:"categoryId,omitempty"`
    SearchKey  *string  `json:"searchKey,omitempty"`
    PageNum    *int     `json:"pageNum,omitempty"`
    PageSize   *int     `json:"pageSize,omitempty"`
}
```

**验收标准：**
- [ ] 所有VO和DTO定义完成
- [ ] JSON标签和验证标签正确
- [ ] 字段类型和命名规范

#### 任务1.3：Repository接口重构（1天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
- 重构现有`InventoryRepository`，移除跨领域方法
- 创建`OrderRepository`接口
- 创建`ProductRepository`接口
- 确保接口职责单一

**验收标准：**
- [ ] Repository接口符合DDD边界
- [ ] 现有功能不受影响
- [ ] 接口方法命名清晰

### 3.2 第二阶段：盘点记录功能实现（预计4天）

#### 任务2.1：盘点记录数据模型设计（1天）
**负责人**：数据库设计师 + 后端开发工程师
**优先级**：P0

**具体任务：**
```sql
-- 创建盘点记录主表（类似入库记录结构）
CREATE TABLE inventory_check_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL,
    handler VARCHAR(64) NOT NULL COMMENT '经手人',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    time INT NOT NULL COMMENT '盘点时间',
    record_number VARCHAR(64) NOT NULL COMMENT '盘点单号',
    remark TEXT COMMENT '备注',
    profit_quantity_total INT DEFAULT 0 COMMENT '盘盈数量合计',
    loss_quantity_total INT DEFAULT 0 COMMENT '盘亏数量合计',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    version INT DEFAULT 0
);

-- 创建盘点记录明细表
CREATE TABLE inventory_check_record_item (
    id VARCHAR(64) PRIMARY KEY,
    check_record_id VARCHAR(64) NOT NULL,
    product_id VARCHAR(64) NOT NULL,
    stock_quantity INT NOT NULL COMMENT '库存数量（盘点之前）',
    check_quantity INT NOT NULL COMMENT '盘点数量（盘点之后）',
    profit_loss_quantity INT NOT NULL COMMENT '盈亏数量',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0
);

-- 创建索引
CREATE INDEX idx_inventory_check_record_venue_time ON inventory_check_record(venue_id, time);
CREATE INDEX idx_inventory_check_record_item_record ON inventory_check_record_item(check_record_id);
CREATE INDEX idx_inventory_check_record_item_product ON inventory_check_record_item(product_id);
```

**验收标准：**
- [ ] 数据表结构与入库记录保持一致的设计模式
- [ ] 简化版设计：仅包含数量字段，无成本和金额字段
- [ ] 盈亏数量计算字段设计合理
- [ ] 索引配置正确

#### 任务2.2：盘点记录业务逻辑实现（1.5天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
```go
// 盘点记录服务实现
type InventoryCheckRecordService interface {
    CreateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error
    GetCheckRecordsByVenue(ctx *gin.Context, venueId string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
    GetCheckRecordDetail(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error)
}

// 盈亏计算逻辑
func calculateProfitLoss(stockQuantity, checkQuantity int) (profitLossQuantity int) {
    profitLossQuantity = checkQuantity - stockQuantity
    return
}

// 盘点后库存更新逻辑
func updateStockAfterCheck(stockQuantity, profitLossQuantity int) int {
    return stockQuantity + profitLossQuantity
}
```

**验收标准：**
- [ ] 盘点记录创建和保存功能正常
- [ ] 盈亏数量计算逻辑正确
- [ ] 汇总数据计算准确
- [ ] 与ProductStockSnapshot集成正确

#### 任务2.3：盘点记录API接口实现（1.5天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
```go
// API接口定义
POST /api/inventory/check/record/create    // 新增盘点记录
GET  /api/inventory/check/record/list      // 盘点记录列表
GET  /api/inventory/check/record/detail    // 盘点记录详情
```

**验收标准：**
- [ ] 新增盘点记录API功能完整
- [ ] 盘点记录查询API正常
- [ ] 数据验证和错误处理规范

### 3.3 第三阶段：库存分析核心功能（预计4天）

#### 任务3.1：Inventory领域服务实现（1.5天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
```go
// 实现文件：erp_managent/domain/inventory/service/impl/InventoryDomainServiceImpl.go
func (s *InventoryDomainServiceImpl) GetBaselineInfo(ctx *gin.Context, venueId string) (*vo.BaselineInfoVO, error) {
    // 1. 获取最新的库存快照
    snapshots, err := s.productStockSnapshotService.FindProductStockSnapshotsByVenueId(ctx, venueId)
    if err != nil {
        return nil, err
    }

    // 2. 找到最新快照时间
    // 3. 构建基准信息VO
    // 4. 返回结果
}

func (s *InventoryDomainServiceImpl) CalculateInboundIncrement(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error) {
    // 1. 查询入库记录
    // 2. 计算增量
    // 3. 返回结果
}
```

**验收标准：**
- [ ] 基准信息获取功能正常
- [ ] 入库增量计算准确
- [ ] 单元测试覆盖率>80%

#### 任务3.2：Order领域服务实现（1.5天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
- 实现销售明细查询
- 实现销售增量计算
- 处理退款逻辑

**验收标准：**
- [ ] 销售数据查询正确
- [ ] 退款逻辑处理正确
- [ ] 单元测试覆盖率>80%

#### 任务3.3：Product领域服务实现（1天）
**负责人**：后端开发工程师
**优先级**：P1

**具体任务：**
- 实现商品筛选查询
- 实现商品基本信息获取
- 支持分页和搜索

**验收标准：**
- [ ] 商品查询功能正常
- [ ] 分页和搜索正确
- [ ] 性能满足要求

### 3.4 第四阶段：应用服务和API实现（预计3天）

#### 任务4.1：StockAnalysisAppService实现（2天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
```go
// 实现文件：erp_managent/application/inventory/StockAnalysisAppServiceImpl.go
func (s *StockAnalysisAppServiceImpl) GetStockAnalysisList(ctx *gin.Context, reqDto req.StockAnalysisReqDto) (*vo.StockAnalysisRespVO, error) {
    // 1. 获取基准信息 - Inventory领域
    baselineInfo, err := s.inventoryDomainService.GetBaselineInfo(ctx, reqDto.VenueId)

    // 2. 获取商品列表 - Product领域
    products, total, err := s.productDomainService.GetFilteredProducts(...)

    // 3. 批量计算库存分析数据 - Application层协调
    var items []vo.StockAnalysisItemVO
    for _, product := range products {
        item, err := s.calculateStockAnalysisItem(ctx, product, baselineInfo, reqDto.VenueId)
        items = append(items, *item)
    }

    return &vo.StockAnalysisRespVO{
        BaselineInfo: *baselineInfo,
        Items:        items,
        Total:        total,
    }, nil
}
```

**验收标准：**
- [ ] 库存分析列表API功能完整
- [ ] 跨领域协调逻辑正确
- [ ] 错误处理完善

#### 任务4.2：Controller和路由实现（1天）
**负责人**：后端开发工程师
**优先级**：P0

**具体任务：**
- 实现`StockAnalysisController`
- 配置API路由
- 添加参数验证和错误处理

**验收标准：**
- [ ] API接口可正常调用
- [ ] 参数验证正确
- [ ] 错误响应规范

### 3.5 第五阶段：性能优化和测试（预计3天）

#### 任务5.1：缓存策略实现（1天）
**负责人**：后端开发工程师
**优先级**：P1

**具体任务：**
- 基准信息缓存
- 商品列表缓存
- 计算结果缓存

#### 任务5.2：性能优化（1天）
**负责人**：后端开发工程师
**优先级**：P1

**具体任务：**
- 批量查询优化
- 数据库索引优化
- SQL查询优化

#### 任务5.3：集成测试（1天）
**负责人**：测试工程师 + 后端开发工程师
**优先级**：P0

**具体任务：**
- API接口测试
- 业务流程测试
- 性能测试

## 4. 开发排期

### 4.1 总体时间安排
- **总工期**：15个工作日（约3周）
- **简化收益**：盘点功能简化节省2天开发时间
- **并行开发**：部分任务可以并行进行
- **缓冲时间**：预留20%的缓冲时间

### 4.2 里程碑节点
- **第1周末**：基础设施和简化版盘点记录功能完成，可进行前端联调
- **第2周末**：库存分析核心功能完成
- **第3周末**：应用服务、API和性能优化完成，准备上线

### 4.3 风险控制
- **技术风险**：DDD架构实施的学习成本
- **进度风险**：跨领域协调的复杂性
- **质量风险**：新架构的稳定性验证

## 5. 资源配置

### 5.1 人员配置
- **后端架构师**：1人，负责架构设计和关键模块
- **后端开发工程师**：2人，负责具体功能实现
- **数据库设计师**：1人，负责数据模型设计
- **测试工程师**：1人，负责测试用例和质量保证

### 5.2 技术栈
- **开发语言**：Go
- **框架**：Gin + GORM
- **数据库**：MySQL
- **缓存**：Redis
- **架构模式**：DDD

### 5.3 开发工具
- **代码管理**：Git
- **项目管理**：JIRA/Trello
- **API文档**：Swagger
- **测试工具**：Postman + 自动化测试

## 6. 质量保证

### 6.1 代码质量
- **代码审查**：所有代码必须经过审查
- **单元测试**：核心逻辑单元测试覆盖率>80%
- **集成测试**：API接口集成测试覆盖率100%

### 6.2 性能要求
- **响应时间**：API响应时间<500ms
- **并发能力**：支持100并发用户
- **数据准确性**：库存计算准确率100%

### 6.3 监控和日志
- **API监控**：响应时间、成功率监控
- **业务监控**：库存数据一致性监控
- **错误日志**：完整的错误日志记录

## 7. 详细实施指南

### 7.1 第一阶段实施细节

#### 7.1.1 目录结构规划
```
erp_managent/
├── domain/
│   ├── inventory/
│   │   ├── service/
│   │   │   ├── InventoryDomainService.go
│   │   │   └── impl/
│   │   │       └── InventoryDomainServiceImpl.go
│   │   └── repository/
│   │       └── InventoryRepository.go
│   ├── order/
│   │   ├── service/
│   │   │   ├── OrderDomainService.go
│   │   │   └── impl/
│   │   │       └── OrderDomainServiceImpl.go
│   │   └── repository/
│   │       └── OrderRepository.go
│   └── product/
│       ├── service/
│       │   ├── ProductDomainService.go
│       │   └── impl/
│       │       └── ProductDomainServiceImpl.go
│       └── repository/
│           └── ProductRepository.go
├── application/
│   └── inventory/
│       ├── StockAnalysisAppService.go
│       ├── InventoryCheckAppService.go
│       └── impl/
│           ├── StockAnalysisAppServiceImpl.go
│           └── InventoryCheckAppServiceImpl.go
├── api/
│   ├── controller/
│   │   ├── StockAnalysisController.go
│   │   └── InventoryCheckController.go
│   ├── req/
│   │   ├── StockAnalysisReqDto.go
│   │   └── InventoryCheckReqDto.go
│   └── vo/
│       ├── StockAnalysisVO.go
│       └── InventoryCheckVO.go
└── infrastructure/
    └── repository/
        └── impl/
            ├── InventoryRepositoryImpl.go
            ├── OrderRepositoryImpl.go
            └── ProductRepositoryImpl.go
```

#### 7.1.2 开发规范
- **命名规范**：使用驼峰命名，接口以Service结尾，实现以Impl结尾
- **错误处理**：统一使用自定义错误类型，包含错误码和错误信息
- **日志规范**：使用结构化日志，包含请求ID、用户ID、操作类型等
- **注释规范**：所有公开方法必须有完整的注释说明

### 7.2 关键技术实现要点

#### 7.2.1 DDD架构实现要点
```go
// 依赖注入配置示例
type ServiceContainer struct {
    // Domain Services
    InventoryDomainService InventoryDomainService
    OrderDomainService     OrderDomainService
    ProductDomainService   ProductDomainService

    // Application Services
    StockAnalysisAppService StockAnalysisAppService
    InventoryCheckAppService InventoryCheckAppService

    // Repositories
    InventoryRepository InventoryRepository
    OrderRepository     OrderRepository
    ProductRepository   ProductRepository
}

func NewServiceContainer() *ServiceContainer {
    container := &ServiceContainer{}

    // 初始化Repository
    container.InventoryRepository = NewInventoryRepositoryImpl()
    container.OrderRepository = NewOrderRepositoryImpl()
    container.ProductRepository = NewProductRepositoryImpl()

    // 初始化Domain Services
    container.InventoryDomainService = NewInventoryDomainServiceImpl(container.InventoryRepository)
    container.OrderDomainService = NewOrderDomainServiceImpl(container.OrderRepository)
    container.ProductDomainService = NewProductDomainServiceImpl(container.ProductRepository)

    // 初始化Application Services
    container.StockAnalysisAppService = NewStockAnalysisAppServiceImpl(
        container.InventoryDomainService,
        container.OrderDomainService,
        container.ProductDomainService,
    )

    return container
}
```

#### 7.2.2 错误处理机制
```go
// 自定义错误类型
type DomainError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Domain  string `json:"domain"`
    Details map[string]interface{} `json:"details,omitempty"`
}

func (e *DomainError) Error() string {
    return fmt.Sprintf("[%s] %s: %s", e.Domain, e.Code, e.Message)
}

// 错误码定义
const (
    ErrInventoryNotFound     = "INVENTORY_NOT_FOUND"
    ErrInvalidStockQuantity  = "INVALID_STOCK_QUANTITY"
    ErrCalculationFailed     = "CALCULATION_FAILED"
    ErrDataInconsistency     = "DATA_INCONSISTENCY"
)

// 错误创建函数
func NewInventoryError(code, message string, details map[string]interface{}) *DomainError {
    return &DomainError{
        Code:    code,
        Message: message,
        Domain:  "INVENTORY",
        Details: details,
    }
}
```

#### 7.2.3 缓存策略实现
```go
// 缓存接口定义
type CacheService interface {
    Get(key string) interface{}
    Set(key string, value interface{}, duration time.Duration) error
    Delete(key string) error
    Clear(pattern string) error
}

// 缓存键定义
const (
    CacheKeyBaselineInfo = "inventory:baseline:%s"           // venueId
    CacheKeyProductList  = "product:list:%s:%s"             // venueId:categoryId
    CacheKeyStockAnalysis = "inventory:analysis:%s:%s"       // venueId:hash
)

// 缓存装饰器
func (s *InventoryDomainServiceImpl) GetBaselineInfoWithCache(ctx *gin.Context, venueId string) (*vo.BaselineInfoVO, error) {
    cacheKey := fmt.Sprintf(CacheKeyBaselineInfo, venueId)

    // 尝试从缓存获取
    if cached := s.cacheService.Get(cacheKey); cached != nil {
        return cached.(*vo.BaselineInfoVO), nil
    }

    // 缓存未命中，查询数据库
    result, err := s.GetBaselineInfo(ctx, venueId)
    if err != nil {
        return nil, err
    }

    // 写入缓存（5分钟）
    s.cacheService.Set(cacheKey, result, 5*time.Minute)

    return result, nil
}
```

### 7.3 测试策略详细说明

#### 7.3.1 单元测试示例
```go
// Inventory领域服务测试
func TestInventoryDomainService_CalculateInboundIncrement(t *testing.T) {
    // 准备测试数据
    mockRepo := &MockInventoryRepository{}
    service := &InventoryDomainServiceImpl{
        inventoryRepository: mockRepo,
    }

    // 设置Mock期望
    mockRepo.On("GetInboundRecordsAfterTime", mock.Anything, "venue1", int64(1640995200)).
        Return([]*model.InboundRecord{
            {ProductId: "product1", Quantity: 10},
            {ProductId: "product1", Quantity: 5},
        }, nil)

    // 执行测试
    increment, err := service.CalculateInboundIncrement(ctx, "venue1", "product1", 1640995200)

    // 验证结果
    assert.NoError(t, err)
    assert.Equal(t, 15, increment)
    mockRepo.AssertExpectations(t)
}

// Application服务集成测试
func TestStockAnalysisAppService_GetStockAnalysisList(t *testing.T) {
    // 准备测试环境
    container := NewTestServiceContainer()
    appService := container.StockAnalysisAppService

    // 准备测试数据
    reqDto := req.StockAnalysisReqDto{
        VenueId:  "test-venue",
        PageNum:  &[]int{1}[0],
        PageSize: &[]int{10}[0],
    }

    // 执行测试
    result, err := appService.GetStockAnalysisList(ctx, reqDto)

    // 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.NotNil(t, result.BaselineInfo)
    assert.True(t, len(result.Items) >= 0)
}
```

#### 7.3.2 API测试示例
```go
func TestStockAnalysisController_GetStockAnalysisList(t *testing.T) {
    // 准备测试环境
    router := setupTestRouter()

    // 准备请求数据
    reqBody := `{
        "venueId": "test-venue",
        "pageNum": 1,
        "pageSize": 10
    }`

    // 发送请求
    req, _ := http.NewRequest("POST", "/api/inventory/analysis/list", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")

    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)

    // 验证响应
    assert.Equal(t, 200, w.Code)

    var response map[string]interface{}
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(t, err)
    assert.Equal(t, "success", response["status"])
}
```

### 7.4 部署和监控

#### 7.4.1 部署配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  erp-backend:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - LOG_LEVEL=info
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: erp_db
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.2
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

#### 7.4.2 监控配置
```go
// 性能监控中间件
func PerformanceMonitorMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()

        c.Next()

        duration := time.Since(start)

        // 记录API性能指标
        metrics.RecordAPICall(
            c.Request.Method,
            c.Request.URL.Path,
            c.Writer.Status(),
            duration,
        )

        // 慢查询告警
        if duration > 1*time.Second {
            logrus.Warnf("Slow API call: %s %s took %v",
                c.Request.Method, c.Request.URL.Path, duration)
        }
    }
}

// 业务监控
func (s *StockAnalysisAppServiceImpl) monitorDataConsistency(ctx *gin.Context, venueId string) {
    // 定期检查数据一致性
    go func() {
        ticker := time.NewTicker(1 * time.Hour)
        defer ticker.Stop()

        for range ticker.C {
            if err := s.checkDataConsistency(ctx, venueId); err != nil {
                logrus.Errorf("Data consistency check failed for venue %s: %v", venueId, err)
                // 发送告警
                s.alertService.SendAlert("DATA_INCONSISTENCY", venueId, err.Error())
            }
        }
    }()
}
```

## 8. 风险管控和应急预案

### 8.1 技术风险
- **DDD架构学习成本**：提供培训和代码示例
- **性能风险**：提前进行性能测试和优化
- **数据一致性风险**：建立完善的监控和告警机制

### 8.2 进度风险
- **任务依赖风险**：识别关键路径，提前准备
- **人员风险**：交叉培训，避免单点依赖
- **需求变更风险**：预留缓冲时间，建立变更流程

### 8.3 应急预案
- **回滚方案**：保持现有功能不受影响的回滚能力
- **降级方案**：关键功能的降级处理机制
- **数据恢复**：完整的数据备份和恢复流程

这个详细的开发任务文档为库存分析和盘点功能的实施提供了全面的指导，确保项目能够按时、高质量地交付。
