# 盘点功能API测试示例

## 1. 创建盘点记录

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/create \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "venue_001",
    "handler": "张三",
    "operator": "李四",
    "remark": "月度盘点",
    "items": [
      {
        "productId": "product_001",
        "stockQuantity": 100,
        "checkQuantity": 103
      },
      {
        "productId": "product_002",
        "stockQuantity": 50,
        "checkQuantity": 48
      },
      {
        "productId": "product_003",
        "stockQuantity": 30,
        "checkQuantity": 30
      }
    ]
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": "check_record_001",
    "recordNumber": "CHECK20240120001",
    "profitQuantityTotal": 3,
    "lossQuantityTotal": 2
  }
}
```

## 2. 获取盘点记录列表

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/list \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "venue_001",
    "searchKey": "",
    "pageNum": 1,
    "pageSize": 20
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "recordId": "check_record_001",
        "recordNumber": "CHECK20240120001",
        "handler": "张三",
        "operator": "李四",
        "checkTime": 1705747200,
        "remark": "月度盘点",
        "totalProducts": 3,
        "profitQuantityTotal": 3,
        "lossQuantityTotal": 2
      }
    ],
    "total": 1
  }
}
```

## 3. 获取盘点记录详情

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/detail \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": "check_record_001"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "record": {
      "recordId": "check_record_001",
      "recordNumber": "CHECK20240120001",
      "handler": "张三",
      "operator": "李四",
      "checkTime": 1705747200,
      "remark": "月度盘点",
      "totalProducts": 3,
      "profitQuantityTotal": 3,
      "lossQuantityTotal": 2
    },
    "items": [
      {
        "productId": "product_001",
        "productName": "茅台酒",
        "productImage": "https://example.com/image1.jpg",
        "unit": "瓶",
        "stockQuantity": 100,
        "checkQuantity": 103,
        "profitLossQuantity": 3
      },
      {
        "productId": "product_002",
        "productName": "五粮液",
        "productImage": "https://example.com/image2.jpg",
        "unit": "瓶",
        "stockQuantity": 50,
        "checkQuantity": 48,
        "profitLossQuantity": -2
      },
      {
        "productId": "product_003",
        "productName": "剑南春",
        "productImage": "https://example.com/image3.jpg",
        "unit": "瓶",
        "stockQuantity": 30,
        "checkQuantity": 30,
        "profitLossQuantity": 0
      }
    ]
  }
}
```

## 4. 更新盘点记录

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/update \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": "check_record_001",
    "handler": "张三",
    "operator": "李四",
    "remark": "月度盘点（已修正）",
    "items": [
      {
        "productId": "product_001",
        "stockQuantity": 100,
        "checkQuantity": 102
      },
      {
        "productId": "product_002",
        "stockQuantity": 50,
        "checkQuantity": 49
      }
    ]
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": "check_record_001",
    "profitQuantityTotal": 2,
    "lossQuantityTotal": 1
  }
}
```

## 5. 删除盘点记录

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/delete \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": "check_record_001"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "recordId": "check_record_001",
    "success": true
  }
}
```

## 6. 搜索盘点记录

### 请求示例
```bash
curl -X POST http://localhost:8080/api/inventory/check/record/list \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "venue_001",
    "searchKey": "张三",
    "pageNum": 1,
    "pageSize": 20
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "recordId": "check_record_001",
        "recordNumber": "CHECK20240120001",
        "handler": "张三",
        "operator": "李四",
        "checkTime": 1705747200,
        "remark": "月度盘点",
        "totalProducts": 3,
        "profitQuantityTotal": 3,
        "lossQuantityTotal": 2
      }
    ],
    "total": 1
  }
}
```

## 7. 错误响应示例

### 参数错误
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

### 记录不存在
```json
{
  "code": 404,
  "message": "盘点记录不存在",
  "data": null
}
```

### 服务器错误
```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 8. 测试场景

### 8.1 正常盘点流程
1. 创建盘点记录
2. 查看盘点记录列表
3. 查看盘点记录详情
4. 根据需要更新盘点记录
5. 完成盘点

### 8.2 异常处理测试
1. 测试必填字段为空的情况
2. 测试不存在的记录ID
3. 测试数量为负数的情况
4. 测试并发更新的情况

### 8.3 性能测试
1. 大量明细项的盘点记录创建
2. 大量盘点记录的列表查询
3. 并发创建盘点记录

## 9. 注意事项

### 9.1 数据验证
- 门店ID、经手人、操作人为必填项
- 库存数量和盘点数量不能为负数
- 商品ID必须存在

### 9.2 业务规则
- 盘点单号自动生成，格式为CHECK+日期+序号
- 盈亏数量自动计算：盘点数量 - 库存数量
- 汇总数据自动计算：盘盈总数和盘亏总数

### 9.3 权限控制
- 需要相应的盘点权限才能操作
- 不同角色可能有不同的操作权限

### 9.4 数据一致性
- 使用事务确保主记录和明细记录的一致性
- 软删除保证数据的可追溯性
