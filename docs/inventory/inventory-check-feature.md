# 盘点功能设计说明

## 1. 功能设计说明

### 1.1 设计原则
- **快速上线**：优先实现核心功能，满足商家基本盘点需求
- **操作简单**：专注于数量管理，避免复杂的价格计算
- **与入库一致**：采用与入库记录相同的操作模式
- **分阶段实现**：先实现数量盘点，后续可扩展金额功能

### 1.2 核心功能
**当前版本功能：**
- 多商品批量盘点
- 盈亏数量计算
- 盘盈/盘亏数量汇总
- 盘点记录查询和管理

**后续扩展功能：**
- 成本单价管理
- 盘点金额计算
- 盈亏金额统计
- 盘点成本分析

## 2. 数据模型设计

### 2.1 盘点记录主表
```sql
CREATE TABLE inventory_check_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL,
    handler VARCHAR(64) NOT NULL COMMENT '经手人',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    time INT NOT NULL COMMENT '盘点时间',
    record_number VARCHAR(64) NOT NULL COMMENT '盘点单号',
    remark TEXT COMMENT '备注',
    profit_quantity_total INT DEFAULT 0 COMMENT '盘盈数量合计',
    loss_quantity_total INT DEFAULT 0 COMMENT '盘亏数量合计',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0,
    version INT DEFAULT 0
);
```

### 2.2 盘点记录明细表
```sql
CREATE TABLE inventory_check_record_item (
    id VARCHAR(64) PRIMARY KEY,
    check_record_id VARCHAR(64) NOT NULL,
    product_id VARCHAR(64) NOT NULL,
    stock_quantity INT NOT NULL COMMENT '库存数量（盘点之前）',
    check_quantity INT NOT NULL COMMENT '盘点数量（盘点之后）',
    profit_loss_quantity INT NOT NULL COMMENT '盈亏数量',
    ctime INT DEFAULT 0,
    utime INT DEFAULT 0,
    state INT DEFAULT 0
);
```

## 3. 业务逻辑设计

### 3.1 盘点流程
```
1. 用户创建盘点记录，填写基本信息
2. 添加要盘点的商品：
   - 选择商品
   - 系统获取当前库存数量
   - 用户录入实际盘点数量
   - 系统计算盈亏数量
3. 系统计算汇总信息：
   - 盘盈数量合计 = SUM(盈亏数量 WHERE 盈亏数量 > 0)
   - 盘亏数量合计 = SUM(ABS(盈亏数量) WHERE 盈亏数量 < 0)
4. 保存盘点记录
```

### 3.2 计算公式
```go
// 盈亏数量计算
盈亏数量 = 盘点数量 - 库存数量

// 汇总计算
盘盈数量合计 = SUM(盈亏数量 WHERE 盈亏数量 > 0)
盘亏数量合计 = SUM(ABS(盈亏数量) WHERE 盈亏数量 < 0)
```

## 4. API接口设计

### 4.1 新增盘点记录
```typescript
POST /api/inventory/check/record/create

interface CreateCheckRecordRequest {
  venueId: string;
  handler: string;        // 经手人
  operator: string;       // 操作人
  remark?: string;        // 备注
  items: CheckRecordItem[];
}

interface CheckRecordItem {
  productId: string;
  stockQuantity: number;  // 库存数量（盘点之前）
  checkQuantity: number;  // 盘点数量（盘点之后）
}

interface CreateCheckRecordResponse {
  recordId: string;
  recordNumber: string;
  profitQuantityTotal: number;    // 盘盈数量合计
  lossQuantityTotal: number;      // 盘亏数量合计
}
```

### 4.2 盘点记录列表
```typescript
GET /api/inventory/check/record/list

interface CheckRecordVO {
  recordId: string;
  recordNumber: string;
  handler: string;
  operator: string;
  checkTime: number;
  totalProducts: number;
  profitQuantityTotal: number;    // 盘盈数量合计
  lossQuantityTotal: number;      // 盘亏数量合计
  remark?: string;
}
```

### 4.3 盘点记录详情
```typescript
GET /api/inventory/check/record/detail

interface CheckRecordItemVO {
  productId: string;
  productName: string;
  productImage: string;
  unit: string;
  stockQuantity: number;        // 库存数量（盘点之前）
  checkQuantity: number;        // 盘点数量（盘点之后）
  profitLossQuantity: number;   // 盈亏数量
}
```

## 5. 小程序端界面设计

### 5.1 新增盘点页面
```
┌─────────────────────────────────────────┐
│ ◀ 返回         新增盘点         💾保存  │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 经手人: [选择经手人_____________] > │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 [搜索商品名称________________] + │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ ┌───┐ 可口可乐 500ml             │ │
│ │ │🥤 │ 账面库存: 45瓶               │ │
│ │ └───┘ 盘点数量: [47]瓶       [×] │ │
│ │       盈亏: +2瓶                   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 📊 盘点汇总                        │ │
│ │ 盘盈: 4件 | 盘亏: 1件              │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 备注: [可选填写备注信息__________] │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 5.2 盘点记录列表
```
┌─────────────────────────────────────────┐
│ ◀ 返回         盘点记录         + 新增  │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 🔍 盘点单 #CHECK001         >      │ │
│ │ 2024-01-20 16:00                   │ │
│ │ 经手人: 张三 | 操作人: 李四          │ │
│ │ 商品: 5种 | 盘盈: 8件 | 盘亏: 2件   │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

## 6. 实施优势

### 6.1 开发优势
- **实现简单**：数据模型和计算逻辑简化
- **开发高效**：预计节省1-2天开发时间
- **测试简化**：减少复杂的价格计算测试用例
- **维护成本低**：功能简单，维护成本低

### 6.2 用户体验优势
- **操作简单**：用户只需关注数量，无需考虑价格
- **认知负担小**：界面信息简洁，易于理解
- **上手快**：功能直观，学习成本低
- **与入库一致**：操作模式统一，用户容易掌握

## 7. 后续扩展计划

### 7.1 第二期功能
当基础盘点功能稳定后，可以考虑添加：
- 成本单价管理
- 盘点金额计算
- 盈亏金额统计
- 盘点成本分析

### 7.2 扩展方案
```sql
-- 后续可以通过ALTER TABLE添加字段
ALTER TABLE inventory_check_record ADD COLUMN profit_loss_amount_total DECIMAL(10,2) DEFAULT 0 COMMENT '盈亏金额合计';

ALTER TABLE inventory_check_record_item ADD COLUMN cost_price DECIMAL(10,2) DEFAULT 0 COMMENT '成本单价';
ALTER TABLE inventory_check_record_item ADD COLUMN check_amount DECIMAL(10,2) DEFAULT 0 COMMENT '盘点金额';
ALTER TABLE inventory_check_record_item ADD COLUMN profit_loss_amount DECIMAL(10,2) DEFAULT 0 COMMENT '盈亏金额';
```

### 7.3 兼容性保证
- 现有API接口保持不变
- 新增字段使用默认值，不影响现有功能
- 前端界面可以渐进式增强

## 8. 总结

这个简化版的盘点功能专注于核心需求，通过移除成本和金额相关的复杂功能，实现了：

1. **快速上线**：满足商家基本盘点需求
2. **操作简单**：用户只需关注数量变化
3. **开发高效**：减少开发和测试工作量
4. **易于扩展**：为后续功能扩展预留空间

这个方案既能快速满足商家的紧急需求，又为未来的功能扩展奠定了良好的基础。
