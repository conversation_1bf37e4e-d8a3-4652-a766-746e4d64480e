package router

import (
	"voderpltvv/erp_business/controller"

	"github.com/gin-gonic/gin"
)

// BusinessInventoryRoute 商务后台库存路由
type BusinessInventoryRoute struct {
}

// InitBusinessInventoryRouter 初始化商务后台库存路由
func (s *BusinessInventoryRoute) InitBusinessInventoryRouter(g *gin.Engine) {
	// 商务后台库存管理路由组
	inventoryGroup := g.Group("/api/business/inventory")
	{
		businessInventoryController := controller.NewBusinessInventoryController()

		// 库存导入接口（商务后台）
		// 注意：这些接口需要商务后台登录权限和库存管理权限
		inventoryGroup.POST("/import/preview", businessInventoryController.PreviewImportStock) // 预览导入库存
		inventoryGroup.POST("/import/execute", businessInventoryController.ExecuteImportStock) // 执行导入库存

	}
}
