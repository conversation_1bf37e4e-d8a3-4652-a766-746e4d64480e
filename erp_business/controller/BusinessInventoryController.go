package controller

import (
	"fmt"
	"strings"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/application/inventory"
	"voderpltvv/erp_managent/infrastructure/repository"
	"voderpltvv/erp_managent/service/impl"

	"github.com/gin-gonic/gin"
)

// BusinessInventoryController 商务后台库存控制器
type BusinessInventoryController struct {
	stockReconcileAppService inventory.StockReconcileAppService
}

// NewBusinessInventoryController 创建商务后台库存控制器
func NewBusinessInventoryController() *BusinessInventoryController {
	// 初始化依赖服务
	inventoryRepository := repository.NewInventoryRepository()
	productStockSnapshotService := impl.GetProductStockSnapshotService()
	productStockService := &impl.ProductStockService{}
	productService := impl.GetProductService()
	reconcileTokenService := impl.GetReconcileTokenService()

	// 创建应用服务
	stockReconcileAppService := inventory.NewStockReconcileAppService(
		inventoryRepository,
		productStockSnapshotService,
		productStockService,
		productService,
		reconcileTokenService,
	)

	return &BusinessInventoryController{
		stockReconcileAppService: stockReconcileAppService,
	}
}

// containsAny 检查字符串是否包含指定关键词中的任意一个
func containsAny(str string, keywords []string) bool {
	for _, keyword := range keywords {
		if strings.Contains(str, keyword) {
			return true
		}
	}
	return false
}

// PreviewImportStock 商务后台预览导入库存数据
// @Summary 商务后台预览导入库存数据
// @Description 商务人员预览导入库存数据，验证数据有效性并生成导入令牌
// @Tags 商务后台-库存管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body req.StockImportPreviewReqDto true "预览导入请求"
// @Success 200 {object} Result[vo.StockImportPreviewVO] "预览结果"
// @Failure 400 {object} Result[any] "请求参数错误"
// @Failure 401 {object} Result[any] "权限不足"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/business/inventory/import/preview [post]
func (c *BusinessInventoryController) PreviewImportStock(ctx *gin.Context) {
	var request req.StockImportPreviewReqDto
	if err := ctx.ShouldBindJSON(&request); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "Invalid request parameters: "+err.Error())
		return
	}

	// 参数验证
	if request.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "VenueId is required")
		return
	}
	if request.Ctime <= 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "Ctime must be greater than 0")
		return
	}
	if request.Utime <= 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "Utime must be greater than 0")
		return
	}
	if len(request.Items) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "Items cannot be empty")
		return
	}

	// 验证导入项
	for i, item := range request.Items {
		if item.ProductId == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("Items[%d].ProductId is required", i))
			return
		}
		if item.Stock < 0 {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("Items[%d].Stock cannot be negative", i))
			return
		}
	}

	// TODO: 添加商务权限验证
	// 验证用户是否有该门店的库存管理权限
	// if !hasInventoryPermission(ctx, request.VenueId) {
	//     Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "No permission to manage inventory for this venue")
	//     return
	// }

	// 调用应用服务
	result, err := c.stockReconcileAppService.PreviewImportStockData(ctx, &request)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "Failed to preview import stock data: "+err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// ExecuteImportStock 商务后台执行导入库存数据
// @Summary 商务后台执行导入库存数据
// @Description 商务人员基于导入令牌执行库存数据导入
// @Tags 商务后台-库存管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body req.StockImportExecuteReqDto true "执行导入请求"
// @Success 200 {object} Result[vo.StockImportExecuteVO] "执行结果"
// @Failure 400 {object} Result[any] "请求参数错误"
// @Failure 401 {object} Result[any] "权限不足或令牌无效"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/business/inventory/import/execute [post]
func (c *BusinessInventoryController) ExecuteImportStock(ctx *gin.Context) {
	var request req.StockImportExecuteReqDto
	if err := ctx.ShouldBindJSON(&request); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "Invalid request parameters: "+err.Error())
		return
	}

	// 参数验证
	if request.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "VenueId is required")
		return
	}
	if request.ImportToken == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "ImportToken is required")
		return
	}

	// TODO: 添加商务权限验证
	// 验证用户是否有该门店的库存管理权限
	// if !hasInventoryPermission(ctx, request.VenueId) {
	//     Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "No permission to manage inventory for this venue")
	//     return
	// }

	// 调用应用服务
	result, err := c.stockReconcileAppService.ExecuteImportStockData(ctx, &request)
	if err != nil {
		// 判断是否为令牌相关错误
		if containsAny(err.Error(), []string{"invalid import token", "import token not found", "import token expired", "import token venue mismatch"}) {
			Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "Invalid or expired import token: "+err.Error())
			return
		}
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "Failed to execute import stock data: "+err.Error())
		return
	}

	Result_success[any](ctx, result)
}
