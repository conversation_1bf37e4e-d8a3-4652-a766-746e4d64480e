package model

import (
	"fmt"
	"time"
	"voderpltvv/erp_managent/service/po"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database 是数据库实例
type Database struct {
	Self *gorm.DB
}

// DB 是个单例
var DBMaster *Database // mysql数据库实例
var DBSlave *Database  // mysql数据库实例

// Init 初始化数据库单例
func Init(config_dbname string) *Database {
	return &Database{
		Self: GetDB(config_dbname),
	}
}

func InitDB() {
	DBMaster = Init("mysqldbMaster")
	DBSlave = Init("mysqldbSlave")
}

// Close 关闭数据库连接
func (db *Database) DBMasterClose() {
	if sqlDB, err := DBMaster.Self.DB(); err != nil {
		logrus.Fatal("获取底层sqlDB失败:", err)
	} else if err = sqlDB.Close(); err != nil {
		logrus.Fatal("关闭MYSQL数据库连接时发生错误:", err)
	}
}

func (db *Database) DBSlaveClose() {
	if sqlDB, err := DBSlave.Self.DB(); err != nil {
		logrus.Fatal("获取底层sqlDB失败:", err)
	} else if err = sqlDB.Close(); err != nil {
		logrus.Fatal("关闭MYSQL数据库连接时发生错误:", err)
	}
}

func openDB(config_dbname string) *gorm.DB {
	username := viper.GetString(fmt.Sprintf("%s.username", config_dbname))
	password := viper.GetString(fmt.Sprintf("%s.password", config_dbname))
	addr := viper.GetString(fmt.Sprintf("%s.addr", config_dbname))
	port := viper.GetString(fmt.Sprintf("%s.port", config_dbname))
	name := viper.GetString(fmt.Sprintf("%s.name", config_dbname))
	dbtype := viper.GetString(fmt.Sprintf("%s.dbtype", config_dbname))

	// if viper.GetString("env") == "local" && dbtype == "mysql" {
	// 	addr, port = SetupMysqlOverSSHTunnel(addr, port, config_dbname)
	// }

	enableAutoMigrate := viper.GetBool("enable_auto_migrate")
	var db *gorm.DB
	var err error

	maxRetries := 3                  // 最大重试次数
	retryInterval := time.Second * 3 // 重试间隔

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			logrus.Warnf("第%d次尝试重新连接数据库...", i+1)
			time.Sleep(retryInterval)
		}

		if dbtype == "mysql" {
			mysqlConfig := mysql.Config{
				DSN: fmt.Sprintf(
					"%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=%t&loc=%s&timeout=10s",
					username,
					password,
					addr,
					port,
					name,
					true,
					"Local",
				),
			}
			db, err = gorm.Open(mysql.New(mysqlConfig), &gorm.Config{})
		} else if dbtype == "mssql" {
			sqlserverConfig := sqlserver.Config{
				DSN: fmt.Sprintf("server=%s;user id=%s;password=%s;port=%s;database=%s;encrypt=disable",
					addr,
					username,
					password,
					port,
					name,
				),
			}
			db, err = gorm.Open(sqlserver.New(sqlserverConfig), &gorm.Config{})
		}

		if err == nil {
			break
		}

		logrus.Errorf("连接%s失败 err:%s", dbtype, err.Error())
	}

	if err != nil {
		logrus.Fatalf("数据库连接失败，已重试%d次. 数据库名字: %s. 错误信息: %s", maxRetries, name, err)
	}
	logrus.Infof("数据库连接成功, 数据库名字: %s, 数据库地址: %s", name, addr)

	setupDB(db, enableAutoMigrate)
	return db
}

func setupDB(db *gorm.DB, enableAutoMigrate bool) {
	sqlDB, err := db.DB()
	if err != nil {
		logrus.Fatal(err)
	}

	// 设置连接池
	sqlDB.SetMaxOpenConns(viper.GetInt("db_pool_config.max_open_conns"))
	sqlDB.SetMaxIdleConns(viper.GetInt("db_pool_config.max_idle_conns"))

	// 日志模式
	db.Config.Logger = logger.Default.LogMode(logger.Info)

	// 自动适应结构
	if enableAutoMigrate {
		autoMigrateModels := []any{
			&po.Area{}, &po.Booking{}, &po.AbnormalPaymentOrder{},
			&po.AuthorizationRecord{}, &po.BirthdayGreeting{}, &po.CashierMachine{},
			&po.CashierSystem{}, &po.CloudPrinter{}, &po.Commission{}, &po.CommissionPlan{},
			&po.CommonRemark{}, &po.ConstructionAssistance{}, &po.ConsumptionCashback{},
			&po.Coupon{}, &po.CouponClaim{}, &po.CouponUsage{}, &po.CreditAccount{}, &po.CreditUnit{},
			&po.CustomerGroup{}, &po.CustomerSource{}, &po.CustomerTag{}, &po.DataCleanup{},
			&po.DouyinGroupBuyingPlan{}, &po.ElectronicCard{}, &po.Employee{}, &po.EmployeeGroup{},
			&po.FinancialReport{}, &po.Flavor{}, &po.GiftGroup{}, &po.GiftRecord{}, &po.HistoricalRecord{},
			&po.Holiday{}, &po.IngredientType{}, &po.InventoryRecord{}, &po.InventoryRecordItem{},
			&po.M1Card{}, &po.MarketingCampaign{}, &po.MarketingRole{}, &po.MarketService{}, &po.Member{},
			&po.MemberCardLevel{}, &po.MemberDay{}, &po.MemberCardOperation{}, &po.MemberOrder{},
			&po.MemberTransferMoney{}, &po.MemberRechargePackage{},
			&po.MerchantService{}, &po.MobileOrder{}, &po.MonthlyGiftCoupon{}, &po.Network{},
			&po.NotificationSetting{}, &po.OnlineOperationSettings{}, &po.OperationSettings{},
			&po.Order{}, &po.OrderPricePlan{}, &po.OrderProduct{}, &po.OrderRoomPlan{}, &po.PaymentMethod{},
			&po.PermissionRole{}, &po.PhoneBlacklist{}, &po.PhysicalCard{}, &po.PointsExchange{},
			&po.PosMachine{}, &po.PrepaidCard{}, &po.PrepaidCardType{}, &po.PricePlan{}, &po.PriceScheme{},
			&po.PrintTemplate{}, &po.Product{}, &po.ProductBinding{}, &po.ProductDisplayCategory{},
			&po.ProductDisplayCategory{}, &po.ProductMultipleBuyFree{}, &po.ProductOutType{},
			&po.ProductPackage{}, &po.ProductPackageType{}, &po.ProductSalesTemplate{},
			&po.ProductStatisticsCategory{}, &po.ProductTimeSlot{}, &po.ProductType{},
			&po.RechargePackage{}, &po.Recipe{}, &po.RedemptionRecord{}, &po.Report{},
			&po.ReportType{}, &po.Reward{},
			&po.Room{}, &po.RoomGreeting{}, &po.RoomTheme{}, &po.RoomType{}, &po.Router{},
			&po.SalesPerformance{}, &po.Sequencer{}, &po.ServiceRewardSettings{}, &po.Session{},
			&po.ShiftReport{}, &po.SingingDevice{}, &po.SmsService{}, &po.StatisticsCategory{},
			&po.StatisticsPeriod{}, &po.SubtitleInfo{}, &po.TurnoverData{},
			&po.TvScreenActivity{}, &po.Venue{}, &po.VodSettings{}, &po.Voucher{},
			&po.Warehouse{}, &po.WineStorage{}, &po.WineStorageSetting{}, &po.RoomFault{},
			&po.OrderAndPay{}, &po.PayBill{}, &po.VenuePaySetting{}, &po.EmployeeGroupEmployee{},
			&po.VenueShouyinGrantGzhQR{}, &po.VenueAndEmployee{}, &po.VenueAndMember{},
			&po.ERPUser{},
			&po.ERPUserAndEmployee{},
			&po.ShiftHandoverForm{},
			&po.ShiftHandoverFormAndPayBill{},
			&po.VenueAuthCode{},
			&po.BusinessStaff{},
			&po.SystemOperationRecord{},
			&po.PayRecord{},
			&po.ProductStorage{},
			&po.ProductWithdraw{},
			&po.ProductStorageOrder{},
			&po.ProductWithdrawOrder{},
			&po.ProductStorageOperationLog{},
			&po.PrintRecord{},
			&po.AppUpgrade{},
			&po.AppH5Version{},
			&po.RoomOperation{},
			&po.MemberRechargeBill{},
			&po.MemberRechargeRecord{},
			&po.MemberCardVenue{},
			&po.MemberCard{},
			&po.MemberCardConsume{},
			&po.ProductUnit{},
			&po.CallTypes{},
			&po.CallMessage{},
			&po.EmployeeGiftRecord{},
			&po.RoomException{},
			// 权限系统相关实体
			&po.EmployeeRoleAssignment{},
			&po.PermissionResource{},
			&po.RolePermission{},
			&po.RolePermissionTemplate{},
			&po.RolePermissionConfig{},
			&po.VenuePayTypeSetting{},
			&po.ProductStock{},
			&po.ProductStockSnapshot{},
			&po.SmsProduct{},
			&po.SmsOrder{},
			&po.SmsPayBill{},
			&po.SmsBalance{},
			&po.SmsConsume{},
			&po.VenueSmsSignature{},
		}
		db = db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4")
		db.AutoMigrate(autoMigrateModels...)
	}
}

// GetDB 获取数据库实例
func GetDB(config_dbname string) *gorm.DB {
	return openDB(config_dbname)
}
