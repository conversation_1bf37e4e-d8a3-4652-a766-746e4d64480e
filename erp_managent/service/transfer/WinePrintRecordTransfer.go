package transfer

import (
	"encoding/json"
	"log"
	"strings"
	"time"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/print/model/valueobject"
	"voderpltvv/erp_managent/domain/print/model/winerenewal"
	"voderpltvv/erp_managent/domain/print/model/winestorage"
	"voderpltvv/erp_managent/domain/print/model/winewithdraw"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/samber/lo"
)

// ConvertToWineStorageVO 将存酒单领域模型转换为VO
func ConvertToWineStorageVO(record *winestorage.WineStoragePrintRecord) vo.WineStoragePrintRecordVO {
	result := vo.WineStoragePrintRecordVO{
		ID:              record.ID(),
		VenueID:         record.VenueID(),
		PrintType:       record.PrintType(),
		PrintNo:         record.PrintNo(),
		PrintTime:       record.PrintTime().UnixNano() / int64(time.Millisecond),
		SessionID:       "", // 存取酒无SessionId概念
		StorageRecordId: record.StorageRecordId,
		OperatorID:      record.OperatorID(),
		OperatorName:    record.OperatorName(),
		DeviceName:      record.DeviceName(),
		Status:          record.Status(),
		ErrorMsg:        record.ErrorMsg(),
		Remark:          record.Remark(),
		CreateTime:      record.CreateTime().UnixNano() / int64(time.Millisecond),
		UpdateTime:      record.UpdateTime().UnixNano() / int64(time.Millisecond),
	}

	// 提取内容数据 - 使用lo.Ternary优化条件判断
	wineStorageBillData, err := record.GetWineStorageBillData()
	result.WineStorageBillData = lo.Ternary(
		err == nil && wineStorageBillData != nil,
		&vo.WineStorageBillDataVO{
			VenueName:         wineStorageBillData.VenueName,
			StorageRecordId:   wineStorageBillData.StorageRecordId,
			SessionId:         "", // 存取酒无SessionId概念
			StorageNo:         wineStorageBillData.StorageNo,
			StorageDate:       wineStorageBillData.StorageDate,
			StorageTime:       wineStorageBillData.StorageTime,
			MemberInfo:        wineStorageBillData.MemberInfo,
			Items:             wineStorageBillData.Items,
			TotalQuantity:     wineStorageBillData.TotalQuantity,
			TotalAmount:       wineStorageBillData.TotalAmount,
			ValidityDate:      wineStorageBillData.ValidityDate,
			StorageLocation:   wineStorageBillData.StorageLocation,
			CashierName:       wineStorageBillData.CashierName,
			CashierId:         wineStorageBillData.CashierId,
			PrintTime:         wineStorageBillData.PrintTime,
			Remark:            wineStorageBillData.Remark,
			StorageConditions: wineStorageBillData.StorageConditions,
		},
		nil,
	)

	return result
}

// ConvertToWineWithdrawVO 将取酒单领域模型转换为VO
func ConvertToWineWithdrawVO(record *winewithdraw.WineWithdrawPrintRecord) vo.WineWithdrawPrintRecordVO {
	result := vo.WineWithdrawPrintRecordVO{
		ID:                 record.ID(),
		VenueID:            record.VenueID(),
		PrintType:          record.PrintType(),
		PrintNo:            record.PrintNo(),
		PrintTime:          record.PrintTime().UnixNano() / int64(time.Millisecond),
		SessionID:          "", // 存取酒无SessionId概念
		WithdrawalRecordId: record.WithdrawalRecordId,
		OperatorID:         record.OperatorID(),
		OperatorName:       record.OperatorName(),
		DeviceName:         record.DeviceName(),
		Status:             record.Status(),
		ErrorMsg:           record.ErrorMsg(),
		Remark:             record.Remark(),
		CreateTime:         record.CreateTime().UnixNano() / int64(time.Millisecond),
		UpdateTime:         record.UpdateTime().UnixNano() / int64(time.Millisecond),
	}

	// 提取内容数据 - 使用lo.Ternary优化条件判断
	wineWithdrawalBillData, err := record.GetWineWithdrawalBillData()
	result.WineWithdrawalBillData = lo.Ternary(
		err == nil && wineWithdrawalBillData != nil,
		&vo.WineWithdrawalBillDataVO{
			VenueName:           wineWithdrawalBillData.VenueName,
			WithdrawalRecordId:  wineWithdrawalBillData.WithdrawalRecordId,
			SessionId:           "", // 存取酒无SessionId概念
			WithdrawalNo:        wineWithdrawalBillData.WithdrawalNo,
			WithdrawalDate:      wineWithdrawalBillData.WithdrawalDate,
			WithdrawalTime:      wineWithdrawalBillData.WithdrawalTime,
			MemberInfo:          wineWithdrawalBillData.MemberInfo,
			Items:               wineWithdrawalBillData.Items,
			TotalQuantity:       wineWithdrawalBillData.TotalQuantity,
			TotalAmount:         wineWithdrawalBillData.TotalAmount,
			StorageLocation:     wineWithdrawalBillData.StorageLocation,
			CashierName:         wineWithdrawalBillData.CashierName,
			CashierId:           wineWithdrawalBillData.CashierId,
			PrintTime:           wineWithdrawalBillData.PrintTime,
			Remark:              wineWithdrawalBillData.Remark,
			WithdrawalReason:    wineWithdrawalBillData.WithdrawalReason,
			RemainingItemsTotal: wineWithdrawalBillData.RemainingItemsTotal,
		},
		nil,
	)

	return result
}

// ConvertToWineStorageVOList 批量转换存酒单打印记录列表，使用samber/lo优化
func ConvertToWineStorageVOList(records []*winestorage.WineStoragePrintRecord) []vo.WineStoragePrintRecordVO {
	return lo.Map(records, func(record *winestorage.WineStoragePrintRecord, _ int) vo.WineStoragePrintRecordVO {
		return ConvertToWineStorageVO(record)
	})
}

// ConvertToWineWithdrawVOList 批量转换取酒单打印记录列表，使用samber/lo优化
func ConvertToWineWithdrawVOList(records []*winewithdraw.WineWithdrawPrintRecord) []vo.WineWithdrawPrintRecordVO {
	return lo.Map(records, func(record *winewithdraw.WineWithdrawPrintRecord, _ int) vo.WineWithdrawPrintRecordVO {
		return ConvertToWineWithdrawVO(record)
	})
}

// ConvertToWineRenewalVO 将续存打印记录PO转换为VO
func ConvertToWineRenewalVO(record *po.PrintRecord) vo.WineRenewalPrintRecordVO {
	result := vo.WineRenewalPrintRecordVO{
		Id:           lo.FromPtrOr(record.Id, ""),
		VenueId:      lo.FromPtrOr(record.VenueId, ""),
		PrintType:    lo.FromPtrOr(record.PrintType, ""),
		PrintNo:      lo.FromPtrOr(record.PrintNo, ""),
		PrintTime:    lo.FromPtrOr(record.PrintTime, 0),
		BusinessId:   lo.FromPtrOr(record.BusinessId, ""),
		OperatorId:   lo.FromPtrOr(record.OperatorId, ""),
		OperatorName: lo.FromPtrOr(record.OperatorName, ""),
		Status:       lo.FromPtrOr(record.Status, 0),
	}

	// 组装续存单据数据 - 从Content字段解析
	if record.Content != nil && *record.Content != "" {
		renewalData := parseRenewalContentJSON(*record.Content)
		if renewalData != nil {
			result.WineRenewalBillData = *renewalData
		} else {
			// 解析失败时返回空数据结构
			result.WineRenewalBillData = createEmptyRenewalBillData(result.BusinessId)
		}
	} else {
		// Content为空时返回空数据结构
		result.WineRenewalBillData = createEmptyRenewalBillData(result.BusinessId)
	}

	return result
}

// createEmptyRenewalBillData 创建空的续存单据数据
func createEmptyRenewalBillData(businessId string) winerenewal.WineRenewalBillData {
	return winerenewal.WineRenewalBillData{
		RenewalRecordId: businessId,
		StorageNo:       "",
		RenewalDate:     "",
		RenewalTime:     "",
		MemberInfo:      valueobject.MemberInfo{},
		RenewalItems:    []valueobject.WineRenewalItem{},
		TotalQuantity:   0,
		TotalAmount:     0,
		PrintTime:       "",
	}
}

// parseRenewalContentJSON 解析续存内容JSON数据
func parseRenewalContentJSON(contentJSON string) *winerenewal.WineRenewalBillData {
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(contentJSON), &rawData); err != nil {
		log.Printf("ERROR: 解析续存内容JSON失败: %v, content: %s", err, contentJSON)
		return nil
	}

	// 创建续存单据数据
	renewalData := &winerenewal.WineRenewalBillData{
		RenewalRecordId: lo.ValueOr(rawData, "businessId", "").(string),
		StorageNo:       lo.ValueOr(rawData, "storageNo", "").(string),
		VenueName:       lo.ValueOr(rawData, "venueName", "").(string),
		RenewalDate:     extractDateFromTimeString(lo.ValueOr(rawData, "renewalTime", "").(string)),
		RenewalTime:     extractTimeFromTimeString(lo.ValueOr(rawData, "renewalTime", "").(string)),
		OperatorName:    lo.ValueOr(rawData, "operatorName", "").(string),
		RenewalItems:    []valueobject.WineRenewalItem{},
		TotalQuantity:   0,
		TotalAmount:     0,
		PrintTime:       time.Now().Format("2006-01-02 15:04:05"),
	}

	// 设置会员信息
	renewalData.MemberInfo = valueobject.MemberInfo{
		MemberName:  lo.ValueOr(rawData, "customerName", "").(string),
		MemberPhone: lo.ValueOr(rawData, "phoneNumber", "").(string),
		CardNo:      "", // 需要从其他字段获取
		LevelName:   "", // 需要从其他字段获取
	}

	// 解析续存商品（支持两种格式）
	if items, exists := rawData["items"].([]interface{}); exists {
		// 订单级别续存：多个商品
		renewalData.RenewalItems = lo.FilterMap(items, func(itemData interface{}, _ int) (valueobject.WineRenewalItem, bool) {
			if itemMap, ok := itemData.(map[string]interface{}); ok {
				return valueobject.WineRenewalItem{
					ProductName:        lo.ValueOr(itemMap, "productName", "").(string),
					Specification:      lo.ValueOr(itemMap, "productSpec", "").(string),
					Quantity:           safeGetIntFromInterface(lo.ValueOr(itemMap, "quantity", 0)),
					OriginalExpireDate: lo.ValueOr(itemMap, "oldExpireTime", "").(string),
					NewExpireDate:      lo.ValueOr(itemMap, "expireTime", "").(string),
					ExtendedDays:       safeGetIntFromInterface(lo.ValueOr(rawData, "extendedDays", 0)),
					Remark:             lo.ValueOr(itemMap, "remark", "").(string),
				}, true
			}
			return valueobject.WineRenewalItem{}, false
		})
	} else if item, exists := rawData["item"].(map[string]interface{}); exists {
		// 单个记录续存：单个商品
		renewalItem := valueobject.WineRenewalItem{
			ProductName:        lo.ValueOr(item, "productName", "").(string),
			Specification:      lo.ValueOr(item, "productSpec", "").(string),
			Quantity:           safeGetIntFromInterface(lo.ValueOr(item, "quantity", 0)),
			OriginalExpireDate: lo.ValueOr(rawData, "oldExpireTime", "").(string),
			NewExpireDate:      lo.ValueOr(rawData, "newExpireTime", "").(string),
			ExtendedDays:       safeGetIntFromInterface(lo.ValueOr(rawData, "extendedDays", 0)),
			Remark:             lo.ValueOr(item, "remark", "").(string),
		}
		renewalData.RenewalItems = []valueobject.WineRenewalItem{renewalItem}
	}

	// 计算总数量和总金额
	renewalData.UpdateTotals()

	return renewalData
}

// safeGetIntFromInterface 安全地从interface{}转换为int（使用util.ParseInt）
func safeGetIntFromInterface(value interface{}) int {
	switch v := value.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		return util.ParseInt(v)
	default:
		return 0
	}
}

// extractDateFromTimeString 从时间字符串中提取日期部分
func extractDateFromTimeString(timeStr string) string {
	if timeStr == "" {
		return ""
	}
	// 使用strings包分割，如果包含空格则取第一部分（日期）
	parts := strings.Fields(timeStr)
	if len(parts) > 0 {
		datePart := parts[0]
		// 确保日期部分长度正确
		if len(datePart) >= 10 {
			return datePart[:10] // 提取 YYYY-MM-DD 部分
		}
		return datePart
	}
	return timeStr
}

// extractTimeFromTimeString 从时间字符串中提取时间部分
func extractTimeFromTimeString(timeStr string) string {
	if timeStr == "" {
		return ""
	}
	// 使用strings包分割，如果包含空格则取第二部分（时间）
	parts := strings.Fields(timeStr)
	if len(parts) > 1 {
		timePart := parts[1]
		// 确保时间部分长度正确
		if len(timePart) >= 8 {
			return timePart[:8] // 提取 HH:MM:SS 部分
		}
		return timePart
	}
	// 如果没有空格，尝试从完整字符串中提取时间部分
	if len(timeStr) >= 19 {
		return timeStr[11:19] // 提取 HH:MM:SS 部分
	}
	return timeStr
}

// ConvertToWineRenewalVOList 批量转换续存打印记录列表
func ConvertToWineRenewalVOList(records []*po.PrintRecord) []vo.WineRenewalPrintRecordVO {
	return lo.Map(records, func(record *po.PrintRecord, _ int) vo.WineRenewalPrintRecordVO {
		return ConvertToWineRenewalVO(record)
	})
}
