package transfer

import (
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

type InventoryCheckRecordTransfer struct{}

// PoToVo 将PO转换为VO
func (t *InventoryCheckRecordTransfer) PoToVo(record po.InventoryCheckRecord) vo.InventoryCheckRecordVO {
	totalProducts := 0
	if record.ProfitQuantityTotal != nil && record.LossQuantityTotal != nil {
		// 这里简化处理，实际应该查询明细数量
		totalProducts = *record.ProfitQuantityTotal + *record.LossQuantityTotal
	}

	return vo.InventoryCheckRecordVO{
		RecordId:            record.Id,
		RecordNumber:        record.RecordNumber,
		Handler:             record.Handler,
		Operator:            record.Operator,
		CheckTime:           record.Time,
		Remark:              record.Remark,
		TotalProducts:       util.GetItPtr(totalProducts),
		ProfitQuantityTotal: record.ProfitQuantityTotal,
		LossQuantityTotal:   record.LossQuantityTotal,
	}
}

// PoToDetailVo 将PO转换为详情VO
func (t *InventoryCheckRecordTransfer) PoToDetailVo(record po.InventoryCheckRecord) vo.InventoryCheckRecordDetailVO {
	totalProducts := 0
	if record.ProfitQuantityTotal != nil && record.LossQuantityTotal != nil {
		// 这里简化处理，实际应该查询明细数量
		totalProducts = *record.ProfitQuantityTotal + *record.LossQuantityTotal
	}

	return vo.InventoryCheckRecordDetailVO{
		RecordId:            record.Id,
		RecordNumber:        record.RecordNumber,
		Handler:             record.Handler,
		Operator:            record.Operator,
		CheckTime:           record.Time,
		Remark:              record.Remark,
		TotalProducts:       util.GetItPtr(totalProducts),
		ProfitQuantityTotal: record.ProfitQuantityTotal,
		LossQuantityTotal:   record.LossQuantityTotal,
	}
}

// ItemPoToVo 将明细PO转换为VO
func (t *InventoryCheckRecordTransfer) ItemPoToVo(item po.InventoryCheckRecordItem) vo.InventoryCheckRecordItemVO {
	emptyStr := ""
	return vo.InventoryCheckRecordItemVO{
		ProductId:          item.ProductId,
		ProductName:        &emptyStr, // 需要从Product表查询
		ProductImage:       &emptyStr, // 需要从Product表查询
		Unit:               &emptyStr, // 需要从Product表查询
		StockQuantity:      item.StockQuantity,
		CheckQuantity:      item.CheckQuantity,
		ProfitLossQuantity: item.ProfitLossQuantity,
	}
}
