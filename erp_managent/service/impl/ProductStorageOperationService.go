package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

var log = logrus.New()

// ProductStorageOperationService 商品存储操作服务
type ProductStorageOperationService struct{}

// NewProductStorageOperationService 创建商品存储操作服务实例
func NewProductStorageOperationService() *ProductStorageOperationService {
	return &ProductStorageOperationService{}
}

// ExtendProductStorage 续存操作：延长商品的到期时间
func (service *ProductStorageOperationService) ExtendProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) (*vo.PrintRecordInfoVO, error) {
	// 1. 检查续存次数限制
	if err := service.checkRenewalTimesLimit(ctx, storage); err != nil {
		return nil, err
	}

	// 2. 获取续期天数配置
	renewalDays := _const.DefaultRenewalDays // 兜底值
	if storage.VenueId != nil && *storage.VenueId != "" {
		wineService := WineStorageSettingService{}
		renewalDays = wineService.GetRenewalDaysConfig(ctx, *storage.VenueId)
	}

	// 3. 计算新的到期时间：续存应该始终基于原到期时间延长
	if storage.ExpireTime == nil {
		return nil, fmt.Errorf("原到期时间为空，无法进行续存")
	}

	// 从原到期时间开始续期（无论是否已过期）
	baseTime := *storage.ExpireTime
	newExpireTime := time.Unix(baseTime, 0).AddDate(0, 0, renewalDays).Unix()

	// 更新到期时间
	storage.ExpireTime = &newExpireTime

	// 更新状态码和状态名称
	statusCode := _const.StatusCodeExtended
	statusName := _const.StatusNameExtended
	storage.StatusCode = &statusCode
	storage.StatusName = &statusName

	// 只有用户提供了备注时才添加到存储记录的备注中
	if reqDto.Remark != "" {
		if storage.Remark != nil && *storage.Remark != "" {
			remark := *storage.Remark + "; " + reqDto.Remark
			storage.Remark = &remark
		} else {
			remark := reqDto.Remark
			storage.Remark = &remark
		}
	}

	// 更新操作人
	if reqDto.OperatorId != "" {
		operatorId := reqDto.OperatorId
		storage.OperatorId = &operatorId
	}

	// 更新操作人名称
	if reqDto.OperatorName != "" {
		operatorName := reqDto.OperatorName
		storage.OperatorName = &operatorName
	}

	// 更新记录
	productStorageService := ProductStorageService{}
	err := productStorageService.UpdateProductStorage(ctx, storage)
	if err != nil {
		return nil, err
	}

	// 记录操作日志 - 操作日志可以记录基础的续存信息
	currentTime := util.TimeNowUnixInt64()
	opLogService := NewProductStorageOperationLogService()

	// 操作日志备注：记录续存基本信息，用户备注单
	var remarkMessage string
	if reqDto.Remark != "" {
		remarkMessage = reqDto.Remark
	} else {
		remarkMessage = ""
	}

	_, logErr := opLogService.AddOperationLog(
		ctx,
		*storage.Id,
		*storage.OrderNo,
		_const.OperationTypeExtend, // 操作类型
		_const.OperationNameExtend, // 操作名称
		currentTime,                // 操作时间
		0,                          // 操作数量(续存不改变数量)
		*storage.OperatorId,        // 操作人ID
		*storage.OperatorName,      // 操作人姓名
		*storage.RemainingQty,      // 操作后剩余数量
		remarkMessage,              // 备注
	)
	if logErr != nil {
		// 记录日志失败，但不影响主流程，只记录错误
		log.Errorf("记录续存操作日志失败: %s", logErr.Error())
	}

	// 同步创建续存打印记录并返回打印记录信息
	printRecordInfo, err := service.createRenewalPrintRecordSync(ctx, storage, reqDto, baseTime, newExpireTime)
	if err != nil {
		// 打印记录创建失败不影响主流程，记录错误但返回空信息
		log.Errorf("创建续存打印记录失败: 存酒记录ID=%s, 错误=%s", *storage.Id, err.Error())
		return nil, nil // 返回空的打印记录信息，不阻断主流程
	}

	fmt.Printf("[续存打印记录] 创建成功: 存酒记录ID=%s, 业务单号=%s\n", *storage.Id, printRecordInfo.BusinessId)
	return printRecordInfo, nil
}

// checkRenewalTimesLimit 检查续存次数限制
func (service *ProductStorageOperationService) checkRenewalTimesLimit(ctx *gin.Context, storage *po.ProductStorage) error {
	// 获取续存次数配置
	maxRenewalTimes := 3 // 默认值
	if storage.VenueId != nil && *storage.VenueId != "" {
		wineService := WineStorageSettingService{}
		maxRenewalTimes = wineService.GetRenewalTimesConfig(ctx, *storage.VenueId)
	}

	// 记录调试信息
	log.Printf("DEBUG: 场馆ID: %s, 配置的最大续存次数: %d", lo.FromPtr(storage.VenueId), maxRenewalTimes)

	// 如果配置为0，表示禁止续存
	if maxRenewalTimes == 0 {
		return fmt.Errorf("该场馆禁止续存操作")
	}

	// 查询已续存次数
	queryBuilder := dal.Use(model.DBSlave.Self)
	existingRenewalCount, err := queryBuilder.ProductStorageOperationLog.WithContext(ctx).
		Where(queryBuilder.ProductStorageOperationLog.StorageId.Eq(*storage.Id)).
		Where(queryBuilder.ProductStorageOperationLog.OperationType.Eq(_const.OperationTypeExtend)).
		Count()

	if err != nil {
		return fmt.Errorf("查询续存次数失败: %s", err.Error())
	}

	// 记录调试信息
	log.Printf("DEBUG: 存酒记录ID: %s, 已续存次数: %d, 最大允许次数: %d", *storage.Id, existingRenewalCount, maxRenewalTimes)

	// 检查是否超过限制
	if int(existingRenewalCount) >= maxRenewalTimes {
		return fmt.Errorf("续存次数已达上限(%d次)，无法继续续存", maxRenewalTimes)
	}

	return nil
}

// CancelProductStorage 撤销操作：标记撤销状态，保留记录和数量
func (service *ProductStorageOperationService) CancelProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
	// 记录当前数量，用于记录日志
	currentQuantity := *storage.Quantity
	originalRemainingQty := *storage.RemainingQty // Capture original remaining quantity

	// 更新状态
	state := _const.StorageStateCancelled // 设置为 const 定义的已撤销状态值
	storage.State = &state

	// 清空剩余数量
	storage.RemainingQty = util.GetItPtr(0)

	// 更新状态码和状态名称
	statusCode := _const.StatusCodeCancelled // 使用 _const 定义
	statusName := _const.StatusNameCancelled // 使用 _const 定义
	storage.StatusCode = &statusCode
	storage.StatusName = &statusName

	// 添加撤销备注 - new logic
	actionText := _const.OperationNameCancel // "撤销"
	fullActionDescription := actionText
	if reqDto.Remark != "" {
		fullActionDescription += " (操作员备注: " + reqDto.Remark + ")"
	}

	if storage.Remark != nil && *storage.Remark != "" {
		finalRemarkStr := *storage.Remark + "; " + fullActionDescription
		storage.Remark = &finalRemarkStr
	} else {
		storage.Remark = &fullActionDescription
	}

	// 更新操作人
	if reqDto.OperatorId != "" {
		operatorId := reqDto.OperatorId
		storage.OperatorId = &operatorId
	}

	// 更新操作人名称
	if reqDto.OperatorName != "" {
		operatorName := reqDto.OperatorName
		storage.OperatorName = &operatorName
	}

	// 更新记录(标记状态，保留数量)
	productStorageService := ProductStorageService{}
	err := productStorageService.UpdateProductStorage(ctx, storage)
	if err != nil {
		return err
	}

	// Update parent ProductStorageOrder's remaining quantity using DAL
	if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
		go func(gCtx context.Context, parentOrderNo string, db *gorm.DB) {
			query := dal.Use(db)
			var totalRemaining int64

			// Define a struct to scan the sum result
			var sumResult struct {
				SumQty int64 `gorm:"column:sum_qty"` // Tag for GORM to map the aliased sum
			}

			// Calculate sum of remaining_qty using DAL
			dbErr := query.ProductStorage.WithContext(gCtx).
				Where(query.ProductStorage.ParentOrderNo.Eq(parentOrderNo), query.ProductStorage.State.Eq(_const.StorageStateNormal)).
				Select(query.ProductStorage.RemainingQty.Sum().IfNull(0).As("sum_qty")).
				Scan(&sumResult)

			if dbErr != nil {
				log.Errorf("Error calculating remaining quantity for order %s after item cancellation (DAL): %v", parentOrderNo, dbErr)
				return
			}
			totalRemaining = sumResult.SumQty

			currentTimeForOrderUpdate := time.Now().Unix()

			// Update ProductStorageOrder using DAL
			updateData := map[string]interface{}{
				query.ProductStorageOrder.RemainingQuantity.ColumnName().String(): int(totalRemaining),
				query.ProductStorageOrder.Utime.ColumnName().String():             currentTimeForOrderUpdate,
			}

			info, updateErr := query.ProductStorageOrder.WithContext(gCtx).
				Where(query.ProductStorageOrder.OrderNo.Eq(parentOrderNo)).
				Updates(updateData)

			if updateErr != nil {
				log.Errorf("Error updating remaining quantity for order %s after item cancellation (DAL): %v", parentOrderNo, updateErr)
			} else if info.RowsAffected == 0 {
				log.Infof("No rows affected when updating remaining quantity for order %s after item cancellation (DAL) (order might not exist or quantity was already correct).", parentOrderNo)
			}
		}(ctx.Copy(), *storage.ParentOrderNo, model.DBMaster.Self) // Pass a copy of the gin context
	}

	// 记录操作日志
	currentTime := util.TimeNowUnixInt64()
	opLogService := NewProductStorageOperationLogService()
	logRemark := fmt.Sprintf("撤销存酒记录. 原总数量: %d, 原剩余数量: %d, 现剩余数量: 0", currentQuantity, originalRemainingQty)
	if reqDto.Remark != "" {
		logRemark += fmt.Sprintf("; 操作员备注: %s", reqDto.Remark)
	}
	_, logErr := opLogService.AddOperationLog(
		ctx,
		*storage.Id,
		*storage.OrderNo,
		_const.OperationTypeCancel, // 操作类型
		_const.OperationNameCancel, // 操作名称
		currentTime,                // 操作时间
		0,                          // 操作数量(0表示不改变数量, 是状态变更)
		*storage.OperatorId,        // 操作人ID
		*storage.OperatorName,      // 操作人姓名
		0,                          // 操作后剩余数量 (item's own remaining qty is now 0)
		logRemark,                  // 备注
	)
	if logErr != nil {
		// 记录日志失败，但不影响主流程，只记录错误
		log.Errorf("记录撤销操作日志失败: %s", logErr.Error())
	}

	return nil
}

// DiscardProductStorage 报废操作：仅打标记而不更新数量
func (service *ProductStorageOperationService) DiscardProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
	// 记录当前剩余数量，用于记录日志
	currentRemainingQty := *storage.RemainingQty

	// 更新状态
	state := _const.StorageStateDiscarded // 设置为 const 定义的已报废状态值
	storage.State = &state

	// 更新状态码和状态名称
	statusCode := _const.StatusCodeDiscarded // 使用 _const 定义
	statusName := _const.StatusNameDiscarded // 使用 _const 定义
	storage.StatusCode = &statusCode
	storage.StatusName = &statusName

	// 添加报废备注 - new logic
	actionText := _const.OperationNameDiscard // "报废"
	fullActionDescription := actionText
	if reqDto.Remark != "" {
		fullActionDescription += " (操作员备注: " + reqDto.Remark + ")"
	}

	if storage.Remark != nil && *storage.Remark != "" {
		finalRemarkStr := *storage.Remark + "; " + fullActionDescription
		storage.Remark = &finalRemarkStr
	} else {
		storage.Remark = &fullActionDescription
	}

	// 更新操作人
	if reqDto.OperatorId != "" {
		operatorId := reqDto.OperatorId
		storage.OperatorId = &operatorId
	}

	// 更新操作人名称
	if reqDto.OperatorName != "" {
		operatorName := reqDto.OperatorName
		storage.OperatorName = &operatorName
	}

	// 更新记录（仅修改状态和标记，不更改数量）
	productStorageService := ProductStorageService{}
	err := productStorageService.UpdateProductStorage(ctx, storage)
	if err != nil {
		return err
	}

	// 记录操作日志
	currentTime := util.TimeNowUnixInt64()
	opLogService := NewProductStorageOperationLogService()
	_, logErr := opLogService.AddOperationLog(
		ctx,
		*storage.Id,
		*storage.OrderNo,
		_const.OperationTypeDiscard, // 操作类型
		_const.OperationNameDiscard, // 操作名称
		currentTime,                 // 操作时间
		0,                           // 操作数量(报废不改变实际数量，只是标记状态)
		*storage.OperatorId,         // 操作人ID
		*storage.OperatorName,       // 操作人姓名
		currentRemainingQty,         // 操作后剩余数量不变
		fmt.Sprintf("报废存酒记录，数量: %d", currentRemainingQty), // 备注
	)
	if logErr != nil {
		// 记录日志失败，但不影响主流程，只记录错误
		fmt.Printf("记录报废操作日志失败: %s\n", logErr.Error())
	}

	return nil
}

// UpdateProductStorageInfo 更新操作：更新商品存储记录信息
func (service *ProductStorageOperationService) UpdateProductStorageInfo(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
	// 记录更新的内容
	var updateItems []string

	// 更新产品名称
	if reqDto.ProductName != "" && *storage.ProductName != reqDto.ProductName {
		oldName := *storage.ProductName
		productName := reqDto.ProductName
		storage.ProductName = &productName
		updateItems = append(updateItems, "产品名称: "+oldName+" → "+productName)
	}

	// 更新产品单位
	if reqDto.ProductUnit != "" && (storage.ProductUnit == nil || *storage.ProductUnit != reqDto.ProductUnit) {
		oldUnit := ""
		if storage.ProductUnit != nil {
			oldUnit = *storage.ProductUnit
		}
		productUnit := reqDto.ProductUnit
		storage.ProductUnit = &productUnit
		updateItems = append(updateItems, "产品单位: "+oldUnit+" → "+productUnit)
	}

	// 更新产品规格
	if reqDto.ProductSpec != "" && (storage.ProductSpec == nil || *storage.ProductSpec != reqDto.ProductSpec) {
		oldSpec := ""
		if storage.ProductSpec != nil {
			oldSpec = *storage.ProductSpec
		}
		productSpec := reqDto.ProductSpec
		storage.ProductSpec = &productSpec
		updateItems = append(updateItems, "产品规格: "+oldSpec+" → "+productSpec)
	}

	// 更新存放位置
	if reqDto.StorageLocation != "" && *storage.StorageLocation != reqDto.StorageLocation {
		oldLocation := *storage.StorageLocation
		storageLocation := reqDto.StorageLocation
		storage.StorageLocation = &storageLocation
		updateItems = append(updateItems, "存放位置: "+oldLocation+" → "+storageLocation)
	}

	// 更新数量(注意:如果更新了数量，也需要相应地更新剩余数量)
	var diff int = 0
	var oldQuantity, oldRemaining int = 0, 0
	if reqDto.Quantity > 0 && *storage.Quantity != reqDto.Quantity {
		// 计算差值
		diff = reqDto.Quantity - *storage.Quantity
		oldQuantity = *storage.Quantity
		oldRemaining = *storage.RemainingQty

		// 更新数量和剩余数量
		quantity := reqDto.Quantity
		storage.Quantity = &quantity

		// 更新剩余数量
		newRemaining := oldRemaining + diff
		if newRemaining < 0 {
			newRemaining = 0
		}
		storage.RemainingQty = &newRemaining

		updateItems = append(updateItems, fmt.Sprintf("数量: %d → %d", oldQuantity, quantity))
	}

	// 添加更新备注 - new logic
	if reqDto.Remark != "" || len(updateItems) > 0 { // Only update remark if there's new info
		var existingRemark string
		if storage.Remark != nil {
			existingRemark = *storage.Remark
		}

		// OperationNameUpdate was local "更新". Using string literal "更新" as it's not in _const.
		newChangeDescription := "更新"

		var details []string
		if reqDto.Remark != "" { // Operator's general comment for the update
			details = append(details, "操作员备注: "+reqDto.Remark)
		}
		if len(updateItems) > 0 { // Specific field changes
			details = append(details, "字段变更: ["+strings.Join(updateItems, ", ")+"]")
		}

		if len(details) > 0 {
			newChangeDescription += " (" + strings.Join(details, "; ") + ")"
		}

		if existingRemark != "" {
			finalRemarkStr := existingRemark + "; " + newChangeDescription
			storage.Remark = &finalRemarkStr
		} else {
			storage.Remark = &newChangeDescription
		}
	}

	// 更新操作人
	if reqDto.OperatorId != "" {
		operatorId := reqDto.OperatorId
		storage.OperatorId = &operatorId
	}

	// 更新操作人名称
	if reqDto.OperatorName != "" {
		operatorName := reqDto.OperatorName
		storage.OperatorName = &operatorName
	}

	// 更新记录
	productStorageService := ProductStorageService{}
	err := productStorageService.UpdateProductStorage(ctx, storage)
	if err != nil {
		return err
	}

	// 如果存在父订单且更新了数量，也需要更新父订单的总数量和剩余数量
	if diff != 0 && storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
		go func() {
			orderService := NewProductStorageOrderService()
			order, err := orderService.GetOrderByOrderNo(ctx, *storage.ParentOrderNo)
			if err == nil && order != nil {
				newTotalQuantity := *order.TotalQuantity + diff
				newRemainingQuantity := *order.RemainingQuantity + diff
				if newRemainingQuantity < 0 {
					newRemainingQuantity = 0
				}

				model.DBMaster.Self.Model(&po.ProductStorageOrder{}).
					Where("order_no = ?", *storage.ParentOrderNo).
					Updates(map[string]interface{}{
						"total_quantity":     newTotalQuantity,
						"remaining_quantity": newRemainingQuantity,
						"utime":              util.TimeNowUnixInt64(),
					})
			}
		}()
	}

	return nil
}

// OperateProductStorage 统一处理商品存储操作
func (service *ProductStorageOperationService) OperateProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
	var err error

	// 根据操作类型执行不同的操作
	switch reqDto.OperationType {
	case _const.OperationTypeExtend: // 续存
		_, err = service.ExtendProductStorage(ctx, storage, reqDto)
	case _const.OperationTypeCancel: // 撤销存酒
		err = service.CancelProductStorage(ctx, storage, reqDto)
	case _const.OperationTypeDiscard: // 报废
		err = service.DiscardProductStorage(ctx, storage, reqDto)
	case "update": // 更新存酒记录
		err = service.UpdateProductStorageInfo(ctx, storage, reqDto)
	default:
		err = fmt.Errorf("不支持的操作类型: %s", reqDto.OperationType)
	}

	return err
}

// createRenewalPrintRecord 为单个存酒记录创建续存打印记录
func (service *ProductStorageOperationService) createRenewalPrintRecord(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto, oldExpireTime, newExpireTime int64) error {
	// 构建续存单的完整打印内容
	content, err := service.buildSingleRenewalContent(ctx, storage, reqDto, oldExpireTime, newExpireTime)
	if err != nil {
		return fmt.Errorf("构建续存打印内容失败: %w", err)
	}

	// 查询当前存酒记录的续存次数（包含当前这次）
	queryBuilder := dal.Use(model.DBSlave.Self)
	renewalCount, err := queryBuilder.ProductStorageOperationLog.WithContext(ctx).
		Where(queryBuilder.ProductStorageOperationLog.StorageId.Eq(*storage.Id)).
		Where(queryBuilder.ProductStorageOperationLog.OperationType.Eq(_const.OperationTypeExtend)).
		Count()

	if err != nil {
		return fmt.Errorf("查询续存次数失败: %w", err)
	}

	// 生成带序号的续存打印记录标识
	renewalSeq := int(renewalCount) + 1 // 当前是第几次续存（从1开始）

	// 创建续存打印记录
	printRecordService := &PrintRecordService{}

	// 构建打印记录PO
	now := time.Now().UnixMilli()
	printType := "WINE_RENEWAL"                                       // 使用续存单打印类型
	printNo := fmt.Sprintf("RN%s-%02d", *storage.OrderNo, renewalSeq) // 续存单号：RN+存酒记录单号+序号
	businessId := printNo                                             // 使用带序号的续存单号作为BusinessId

	printRecord := &po.PrintRecord{
		VenueId:      storage.VenueId,
		PrintType:    &printType,
		PrintNo:      &printNo,
		PrintTime:    &now,
		BusinessId:   &businessId, // 核心标识字段：RN+原单号+序号，确保多次续存的唯一性
		OperatorId:   storage.OperatorId,
		OperatorName: storage.OperatorName,
		DeviceName:   util.GetItPtr(""), // 设备名称为空
		Status:       util.GetItPtr(0),  // 默认成功状态
		ErrorMsg:     util.GetItPtr(""), // 无错误信息
		Content:      &content,          // 续存单的完整内容JSON
		Remark:       &reqDto.Remark,    // 续存备注
		Ctime:        &now,
		Utime:        &now,
		State:        util.GetItPtr(0),
		Version:      util.GetItPtr(0),
	}

	// 创建续存打印记录
	return printRecordService.CreatePrintRecord(ctx, printRecord)
}

// buildSingleRenewalContent 构建单个存酒记录的续存打印内容
func (service *ProductStorageOperationService) buildSingleRenewalContent(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto, oldExpireTime, newExpireTime int64) (string, error) {
	// 计算延长天数
	extendedDays := int((newExpireTime - oldExpireTime) / 86400) // 86400秒 = 1天

	// 构建续存单内容JSON结构
	renewalData := map[string]interface{}{
		"venueId":       util.GetPtrSafe(storage.VenueId),
		"storageNo":     util.GetPtrSafe(storage.OrderNo),
		"customerName":  util.GetPtrSafe(storage.CustomerName),
		"phoneNumber":   util.GetPtrSafe(storage.PhoneNumber),
		"storageTime":   time.Unix(util.GetPtrSafe(storage.StorageTime), 0).Format("2006-01-02 15:04:05"),
		"oldExpireTime": time.Unix(oldExpireTime, 0).Format("2006-01-02 15:04:05"),
		"newExpireTime": time.Unix(newExpireTime, 0).Format("2006-01-02 15:04:05"),
		"extendedDays":  extendedDays,
		"operatorName":  util.GetPtrSafe(storage.OperatorName),
		"renewalTime":   time.Now().Format("2006-01-02 15:04:05"),
		"remark":        reqDto.Remark,
		"item": map[string]interface{}{
			"productId":       util.GetPtrSafe(storage.ProductId),
			"productName":     util.GetPtrSafe(storage.ProductName),
			"productType":     util.GetPtrSafe(storage.ProductType),
			"productSpec":     util.GetPtrSafe(storage.ProductSpec),
			"quantity":        util.GetPtrSafe(storage.Quantity),
			"remainingQty":    util.GetPtrSafe(storage.RemainingQty),
			"storageLocation": util.GetPtrSafe(storage.StorageLocation),
			"remark":          util.GetPtrSafe(storage.Remark),
		},
	}

	// 序列化为JSON字符串
	jsonBytes, err := json.Marshal(renewalData)
	if err != nil {
		return "", fmt.Errorf("序列化续存单内容失败: %w", err)
	}

	return string(jsonBytes), nil
}

// createRenewalPrintRecordSync 同步创建续存打印记录并返回打印记录信息
func (service *ProductStorageOperationService) createRenewalPrintRecordSync(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto, oldExpireTime, newExpireTime int64) (*vo.PrintRecordInfoVO, error) {
	// 构建续存单的完整打印内容
	content, err := service.buildSingleRenewalContent(ctx, storage, reqDto, oldExpireTime, newExpireTime)
	if err != nil {
		return nil, fmt.Errorf("构建续存打印内容失败: %w", err)
	}

	// 查询当前存酒记录的续存次数（包含当前这次）
	queryBuilder := dal.Use(model.DBSlave.Self)
	renewalCount, err := queryBuilder.ProductStorageOperationLog.WithContext(ctx).
		Where(queryBuilder.ProductStorageOperationLog.StorageId.Eq(*storage.Id)).
		Where(queryBuilder.ProductStorageOperationLog.OperationType.Eq(_const.OperationTypeExtend)).
		Count()

	if err != nil {
		return nil, fmt.Errorf("查询续存次数失败: %w", err)
	}

	// 生成带序号的续存打印记录标识
	renewalSeq := int(renewalCount) + 1 // 当前是第几次续存（从1开始）

	// 创建续存打印记录
	printRecordService := &PrintRecordService{}

	// 构建打印记录PO
	now := time.Now().UnixMilli()
	printType := "WINE_RENEWAL"                                       // 使用续存单打印类型
	printNo := fmt.Sprintf("RN%s-%02d", *storage.OrderNo, renewalSeq) // 续存单号：RN+存酒记录单号+序号
	businessId := printNo                                             // 使用带序号的续存单号作为BusinessId

	printRecord := &po.PrintRecord{
		VenueId:      storage.VenueId,
		PrintType:    &printType,
		PrintNo:      &printNo,
		PrintTime:    &now,
		BusinessId:   &businessId, // 核心标识字段：RN+原单号+序号，确保多次续存的唯一性
		OperatorId:   storage.OperatorId,
		OperatorName: storage.OperatorName,
		DeviceName:   util.GetItPtr(""), // 设备名称为空
		Status:       util.GetItPtr(0),  // 默认成功状态
		ErrorMsg:     util.GetItPtr(""), // 无错误信息
		Content:      &content,          // 续存单的完整内容JSON
		Remark:       &reqDto.Remark,    // 续存备注
		Ctime:        &now,
		Utime:        &now,
		State:        util.GetItPtr(0),
		Version:      util.GetItPtr(0),
	}

	// 创建续存打印记录，使用虚拟上下文（异步场景）
	err = printRecordService.CreatePrintRecord(&gin.Context{}, printRecord)
	if err != nil {
		return nil, fmt.Errorf("创建续存打印记录失败: %w", err)
	}

	// 返回打印记录信息
	return &vo.PrintRecordInfoVO{
		BusinessId: businessId,
		PrintType:  printType,
		PrintTime:  now,
	}, nil
}
