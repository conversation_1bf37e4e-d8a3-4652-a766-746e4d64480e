package impl

import (
	"encoding/json"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// SyncProductStock 同步产品库存
func (service *OrderService) SyncProductStock(ctx *gin.Context, venue *po.Venue, orderDirection string, orderProducts []po.OrderProduct, venueId, sessionId, operatorId string) error {
	defer func() {
		if r := recover(); r != nil {
			logrus.Error("SyncProductStock panic: ", r)
		}
	}()
	operatorName := ""
	employee, err := employeeService.FindEmployeeById(ctx, operatorId)
	if err != nil {
		logrus.Errorf("OrderServiceStock.SyncProductStock,获取操作人失败: %v", err)
	}
	if employee != nil {
		operatorName = *employee.Name
	}
	shareInfo := vo.ProductStockChangeRecordVOShare{
		VenueId:           venueId,
		VenueName:         *venue.Name,
		BusinessStartHour: *venue.StartHours,
		BusinessEndHour:   *venue.EndHours,
		ActionType:        _const.V2_PRODUCT_STOCK_CHANGE_RECORD_ACTION_TYPE_MINUS,
		Source:            _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_ORDER,
		SessionId:         sessionId,
		OperatorId:        operatorId,
		OperatorName:      operatorName,
	}
	if orderDirection == _const.V2_ORDER_DIRECTION_REFUND {
		shareInfo.ActionType = _const.V2_PRODUCT_STOCK_CHANGE_RECORD_ACTION_TYPE_ADD
		shareInfo.Source = _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_REFUND
	}

	allOrderProduct := []po.OrderProduct{}
	for _, orderProduct := range orderProducts {
		voTmp := orderProductService.ConvertToOrderProductVO(ctx, orderProduct)
		if isPackage, packageProductInfo := service.isPackage(voTmp); isPackage {
			for _, packageProduct := range packageProductInfo {
				count := packageProduct.Count * *orderProduct.Quantity
				allOrderProduct = append(allOrderProduct, po.OrderProduct{
					ProductId:     &packageProduct.Id,
					Quantity:      &count,
					OriginalPrice: &packageProduct.Price,
				})
			}
		} else {
			allOrderProduct = append(allOrderProduct, orderProduct)
		}
	}
	if len(allOrderProduct) <= 0 {
		logrus.Errorf("同步产品库存失败: %v", "没有产品")
		return nil
	}
	mergeByProductIdMapOrderProduct := make(map[string]po.OrderProduct)
	for _, orderProduct := range allOrderProduct {
		if _, ok := mergeByProductIdMapOrderProduct[*orderProduct.ProductId]; !ok {
			mergeByProductIdMapOrderProduct[*orderProduct.ProductId] = orderProduct
		} else {
			*mergeByProductIdMapOrderProduct[*orderProduct.ProductId].Quantity += *orderProduct.Quantity
		}
	}

	productIds := []string{}
	for productId, _ := range mergeByProductIdMapOrderProduct {
		productIds = append(productIds, productId)
	}
	productStocksQuery, err := productStockService.FindProductStockByProductIds(ctx, *venue.Id, productIds)
	if err != nil {
		logrus.Errorf("同步产品库存失败: %v", err)
	}

	multiplier := int64(-1)
	if orderDirection == _const.V2_ORDER_DIRECTION_REFUND {
		multiplier = 1
	}
	newProductStocks := []po.ProductStock{}
	updateProductStockChangeVOs := []vo.ProductStockChangeVO{}
	productIdsInDb := []string{}
	for _, productStock := range productStocksQuery {
		op := mergeByProductIdMapOrderProduct[*productStock.ProductId]
		updateProductStockChangeVOs = append(updateProductStockChangeVOs, vo.ProductStockChangeVO{
			Id:    *productStock.Id,
			Count: *op.Quantity * multiplier,
		})
		productIdsInDb = append(productIdsInDb, *productStock.ProductId)
	}
	for productId, opVO := range mergeByProductIdMapOrderProduct {
		if !util.InList(productId, productIdsInDb) {
			newProductStocks = append(newProductStocks, po.ProductStock{
				ProductId: &productId,
				VenueId:   venue.Id,
				Stock:     util.Ptr(int(*opVO.Quantity * multiplier)),
			})
		}
	}

	return productStockService.SyncProductStockV2(ctx, newProductStocks, updateProductStockChangeVOs, shareInfo)
}

func (service *OrderService) isPackage(orderProductVO vo.OrderProductVO) (bool, []vo.OpPackageProductInfoVO) {
	if orderProductVO.PackageId == "" {
		// 套餐ID为空，不是套餐
		return false, []vo.OpPackageProductInfoVO{}
	}
	if orderProductVO.PackageProductInfo == "" { // 套餐产品信息为空，不是套餐
		return false, []vo.OpPackageProductInfoVO{}
	}
	// 套餐产品信息不为空，是套餐
	var tmpModels []vo.OpPackageProductInfoVO
	err := json.Unmarshal([]byte(orderProductVO.PackageProductInfo), &tmpModels)
	if err != nil {
		// 解析失败，不是套餐
		logrus.Errorf("解析套餐产品信息失败: %v", err)
		return false, []vo.OpPackageProductInfoVO{}
	}
	if len(tmpModels) <= 0 {
		// 套餐产品信息为空，不是套餐
		return false, []vo.OpPackageProductInfoVO{}
	}
	return true, tmpModels
}
