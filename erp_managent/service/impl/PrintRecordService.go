package impl

import (
	"strings"
	"voderpltvv/erp_managent/infra/query"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type PrintRecordService struct {
}

var (
	printRecordService = PrintRecordService{}
)

// ======================= 原有方法（保持兼容性） =======================

// 创建打印记录
func (service *PrintRecordService) CreatePrintRecord(logCtx *gin.Context, printRecord *po.PrintRecord) error {
	return Save(printRecord)
}

// 更新打印记录
func (service *PrintRecordService) UpdatePrintRecord(logCtx *gin.Context, printRecord *po.PrintRecord) error {
	return Update(printRecord)
}

// 更新打印记录（只更新非空字段）
func (service *PrintRecordService) UpdatePrintRecordPartial(logCtx *gin.Context, printRecord *po.PrintRecord) error {
	return UpdateNotNull(printRecord)
}

// 删除打印记录
func (service *PrintRecordService) DeletePrintRecord(logCtx *gin.Context, id string) error {
	return Delete(po.PrintRecord{Id: &id})
}

// 根据ID查询打印记录
func (service *PrintRecordService) FindPrintRecordById(logCtx *gin.Context, id string) (printRecord *po.PrintRecord, err error) {
	printRecord = &po.PrintRecord{}
	err = model.DBMaster.Self.Where("id=?", id).First(printRecord).Error
	return
}

// ======================= 新增方法重载（用于后台任务） =======================

// GetPrintRecordsByBusinessIdAsync 根据业务ID查询打印记录（异步/后台任务专用，无需HTTP上下文）
func (service *PrintRecordService) GetPrintRecordsByBusinessIdAsync(venueId, printType, businessId string) ([]*po.PrintRecord, error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	// 构建查询条件
	db = db.Where("venue_id = ? AND print_type = ? AND business_id = ?", venueId, printType, businessId)
	db = db.Order("print_time desc")

	var records []*po.PrintRecord
	err := db.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}

// GetPrintRecordsByMemberIdAsync 根据会员ID查询打印记录（异步/后台任务专用，无需HTTP上下文）
func (service *PrintRecordService) GetPrintRecordsByMemberIdAsync(venueId, printType, memberId string) ([]*po.PrintRecord, error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	// 构建查询条件
	db = db.Where("venue_id = ? AND print_type = ? AND JSON_EXTRACT(content, '$.phoneNumber') = ?",
		venueId, printType, strings.TrimPrefix(memberId, "C_"))
	db = db.Order("print_time desc")

	var records []*po.PrintRecord
	err := db.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}

// FindUniqueByBusinessIdAsync 根据businessId查询唯一的打印记录（businessId应具有唯一性）
func (service *PrintRecordService) FindUniqueByBusinessIdAsync(venueId, businessId string) (*po.PrintRecord, error) {
	record := &po.PrintRecord{}
	err := model.DBSlave.Self.Where("venue_id = ? AND business_id = ?", venueId, businessId).First(record).Error
	if err != nil {
		return nil, err
	}
	return record, nil
}

// ======================= 原有查询方法（保持兼容性） =======================

// 查询所有打印记录（支持各种条件）
func (service *PrintRecordService) FindAllPrintRecord(logCtx *gin.Context, queryParams *query.PrintRecordQuery) (list *[]po.PrintRecord, err error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	// 构建查询条件
	if queryParams.ID != nil && *queryParams.ID != "" {
		db = db.Where("id=?", *queryParams.ID)
	}
	if queryParams.VenueID != nil && *queryParams.VenueID != "" {
		db = db.Where("venue_id=?", *queryParams.VenueID)
	}
	if queryParams.PrintType != nil && *queryParams.PrintType != "" {
		db = db.Where("print_type=?", *queryParams.PrintType)
	}
	if queryParams.PrintNo != nil && *queryParams.PrintNo != "" {
		db = db.Where("print_no=?", *queryParams.PrintNo)
	}
	if queryParams.SessionID != nil && *queryParams.SessionID != "" {
		db = db.Where("session_id=?", *queryParams.SessionID)
	}
	if queryParams.BusinessID != nil && *queryParams.BusinessID != "" {
		db = db.Where("business_id=?", *queryParams.BusinessID)
	}
	if queryParams.SubBusinessID != nil && *queryParams.SubBusinessID != "" {
		db = db.Where("sub_business_id=?", *queryParams.SubBusinessID)
	}
	if queryParams.OperatorID != nil && *queryParams.OperatorID != "" {
		db = db.Where("operator_id=?", *queryParams.OperatorID)
	}
	if queryParams.Status != nil {
		db = db.Where("status=?", *queryParams.Status)
	}
	if queryParams.StartTime != nil && *queryParams.StartTime > 0 {
		db = db.Where("print_time>=?", *queryParams.StartTime)
	}
	if queryParams.EndTime != nil && *queryParams.EndTime > 0 {
		db = db.Where("print_time<=?", *queryParams.EndTime)
	}

	// 处理排序
	if queryParams.OrderBy != nil && *queryParams.OrderBy != "" {
		orderDir := "desc"
		if queryParams.OrderDir != nil && *queryParams.OrderDir != "" {
			orderDir = *queryParams.OrderDir
		}
		db = db.Order(*queryParams.OrderBy + " " + orderDir)
	} else {
		// 默认按打印时间倒序排列
		db = db.Order("print_time desc")
	}

	list = &[]po.PrintRecord{}
	result := db.Find(list)
	err = result.Error
	return
}

// 分页查询打印记录
func (service *PrintRecordService) FindAllPrintRecordWithPagination(logCtx *gin.Context, queryParams *query.PrintRecordQuery) (list *[]po.PrintRecord, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	if queryParams.PageNum == nil || *queryParams.PageNum <= 0 {
		queryParams.PageNum = util.GetItPtr(1)
	}
	if queryParams.PageSize == nil || *queryParams.PageSize <= 0 {
		queryParams.PageSize = util.GetItPtr(10)
	}

	// 构建查询条件
	if queryParams.ID != nil && *queryParams.ID != "" {
		db = db.Where("id=?", *queryParams.ID)
	}
	if queryParams.VenueID != nil && *queryParams.VenueID != "" {
		db = db.Where("venue_id=?", *queryParams.VenueID)
	}
	if queryParams.PrintType != nil && *queryParams.PrintType != "" {
		db = db.Where("print_type=?", *queryParams.PrintType)
	}
	if queryParams.PrintNo != nil && *queryParams.PrintNo != "" {
		db = db.Where("print_no=?", *queryParams.PrintNo)
	}
	if queryParams.SessionID != nil && *queryParams.SessionID != "" {
		db = db.Where("session_id=?", *queryParams.SessionID)
	}
	if queryParams.BusinessID != nil && *queryParams.BusinessID != "" {
		db = db.Where("business_id=?", *queryParams.BusinessID)
	}
	if queryParams.SubBusinessID != nil && *queryParams.SubBusinessID != "" {
		db = db.Where("sub_business_id=?", *queryParams.SubBusinessID)
	}
	if queryParams.OperatorID != nil && *queryParams.OperatorID != "" {
		db = db.Where("operator_id=?", *queryParams.OperatorID)
	}
	if queryParams.Status != nil {
		db = db.Where("status=?", *queryParams.Status)
	}
	if queryParams.StartTime != nil && *queryParams.StartTime > 0 {
		db = db.Where("print_time>=?", *queryParams.StartTime)
	}
	if queryParams.EndTime != nil && *queryParams.EndTime > 0 {
		db = db.Where("print_time<=?", *queryParams.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.PrintRecord{}
	if total <= 0 {
		return
	}

	// 分页
	db = db.Offset((*queryParams.PageNum - 1) * *queryParams.PageSize).Limit(*queryParams.PageSize)

	// 处理排序
	if queryParams.OrderBy != nil && *queryParams.OrderBy != "" {
		orderDir := "desc"
		if queryParams.OrderDir != nil && *queryParams.OrderDir != "" {
			orderDir = *queryParams.OrderDir
		}
		db = db.Order(*queryParams.OrderBy + " " + orderDir)
	} else {
		// 默认按打印时间倒序排列
		db = db.Order("print_time desc")
	}

	err = db.Find(list).Error
	return
}

// GetPrintRecordsByBusinessId 根据业务ID查询打印记录
func (service *PrintRecordService) GetPrintRecordsByBusinessId(logCtx *gin.Context, venueId, printType, businessId string) ([]*po.PrintRecord, error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	// 构建查询条件
	db = db.Where("venue_id = ? AND print_type = ? AND business_id = ?", venueId, printType, businessId)
	db = db.Order("print_time desc")

	var records []*po.PrintRecord
	err := db.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}

// GetPrintRecordsByMemberId 根据会员ID查询打印记录
func (service *PrintRecordService) GetPrintRecordsByMemberId(logCtx *gin.Context, venueId, printType, memberId string) ([]*po.PrintRecord, error) {
	db := model.DBSlave.Self.Model(&po.PrintRecord{})

	// 构建查询条件
	db = db.Where("venue_id = ? AND print_type = ? AND JSON_EXTRACT(content, '$.phoneNumber') = ?",
		venueId, printType, strings.TrimPrefix(memberId, "C_"))
	db = db.Order("print_time desc")

	var records []*po.PrintRecord
	err := db.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}
