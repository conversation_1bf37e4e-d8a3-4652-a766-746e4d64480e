package impl

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type ProductStorageService struct {
}

var (
	productStorageTransfer = transfer.ProductStorageTransfer{}
)

// GenerateCustomerId 生成客户ID或查找已有客户
// 如果phoneNumber已存在，则返回已有客户ID
// 如果不存在，则自动生成一个新的客户ID
func (service *ProductStorageService) GenerateCustomerId(logCtx *gin.Context, name string, phoneNumber string, venueId string) (string, error) {
	if phoneNumber == "" {
		// 如果电话号码为空，直接生成一个新的客户ID
		return util.GetShortSnowflakeID(), nil
	}

	// 先查询ProductStorageOrder表
	var existingOrders []*po.ProductStorageOrder
	err := model.DBSlave.Self.Where("phone_number = ? AND venue_id = ?", phoneNumber, venueId).Limit(1).Find(&existingOrders).Error
	if err != nil {
		return "", err
	}

	// 如果找到现有客户，返回已有ID
	foundOrder, found := lo.Find(existingOrders, func(order *po.ProductStorageOrder) bool {
		return order != nil && order.CustomerId != nil && *order.CustomerId != ""
	})

	if found && foundOrder != nil {
		return *foundOrder.CustomerId, nil
	}

	// 如果没找到，再查询ProductStorage表
	var existingStorage []*po.ProductStorage
	err = model.DBSlave.Self.Where("phone_number = ? AND venue_id = ?", phoneNumber, venueId).Limit(1).Find(&existingStorage).Error
	if err != nil {
		return "", err
	}

	// 如果找到现有存酒记录，返回已有ID
	foundStorage, found := lo.Find(existingStorage, func(storage *po.ProductStorage) bool {
		return storage != nil && storage.CustomerId != nil && *storage.CustomerId != ""
	})

	if found && foundStorage != nil {
		return *foundStorage.CustomerId, nil
	}

	// 如果没找到，再查询ProductWithdraw表
	var existingWithdraw []*po.ProductWithdraw
	err = model.DBSlave.Self.Where("phone_number = ? AND venue_id = ?", phoneNumber, venueId).Limit(1).Find(&existingWithdraw).Error
	if err != nil {
		return "", err
	}

	// 如果找到现有取酒记录，返回已有ID
	foundWithdraw, found := lo.Find(existingWithdraw, func(withdraw *po.ProductWithdraw) bool {
		return withdraw != nil && withdraw.CustomerId != nil && *withdraw.CustomerId != ""
	})

	if found && foundWithdraw != nil {
		return *foundWithdraw.CustomerId, nil
	}

	// 如果都没找到，生成一个新的ID，格式包含手机号后四位，保证客户ID有一定的识别度
	// 避免仅使用SnowflakeID导致ID变化太大，不便识别
	phoneEnd := lo.Ternary(len(phoneNumber) >= 4, phoneNumber[len(phoneNumber)-4:], "")
	customerId := "C" + phoneEnd + util.GetShortSnowflakeID()[:8]

	return customerId, nil
}

func (service *ProductStorageService) CreateProductStorage(logCtx *gin.Context, productStorage *po.ProductStorage) error {
	// 生成带PS前缀的存酒单号
	if productStorage.VenueId != nil && *productStorage.VenueId != "" {
		needGenerateOrderNo := productStorage.OrderNo == nil || *productStorage.OrderNo == ""
		if needGenerateOrderNo {
			venueIdStr := *productStorage.VenueId
			venueIdSuffix := lo.Ternary(len(venueIdStr) >= 8, venueIdStr[len(venueIdStr)-8:], venueIdStr)
			// 生成存酒单号，格式：PS+venueId后8位+当前时间(年月日时分秒)+4位随机数
			storageNo := "PS" + venueIdSuffix + time.Now().Format("060102150405") + fmt.Sprintf("%04d", rand.Intn(10000))
			productStorage.OrderNo = &storageNo
		}
	}

	// 如果客户ID为空，则自动生成
	needGenerateCustomerId := (productStorage.CustomerId == nil || *productStorage.CustomerId == "") &&
		productStorage.PhoneNumber != nil && *productStorage.PhoneNumber != "" &&
		productStorage.VenueId != nil && *productStorage.VenueId != ""

	if needGenerateCustomerId {
		customerName := ""
		if productStorage.CustomerName != nil {
			customerName = *productStorage.CustomerName
		}

		customerId, err := service.GenerateCustomerId(logCtx,
			customerName,
			*productStorage.PhoneNumber,
			*productStorage.VenueId)
		if err != nil {
			return err
		}
		productStorage.CustomerId = &customerId
	}

	// 设置存入时间
	currentTime := util.TimeNowUnixInt64()

	// 确保存入时间不为0
	if productStorage.StorageTime == nil || *productStorage.StorageTime == 0 {
		productStorage.StorageTime = &currentTime
	}

	// 设置默认首次存酒有效期 (如果未提供)
	if productStorage.ExpireTime == nil || *productStorage.ExpireTime == 0 {
		// 优先从配置中获取存储天数，如果没有配置则使用兜底值
		storageDays := _const.DefaultInitialStorageValidityDays // 兜底值
		util.Wlog(logCtx).Info(fmt.Sprintf("[ProductStorage] 初始存储天数兜底值: %d", storageDays))

		if productStorage.VenueId != nil && *productStorage.VenueId != "" {
			wineService := &WineStorageSettingService{}
			storageDays = wineService.GetStorageDaysConfig(logCtx, *productStorage.VenueId)
			util.Wlog(logCtx).Info(fmt.Sprintf("[ProductStorage] 从配置获取的存储天数: %d", storageDays))
		}

		if productStorage.StorageTime != nil && *productStorage.StorageTime != 0 { // Ensure StorageTime is valid
			expireTime := time.Unix(*productStorage.StorageTime, 0).AddDate(0, 0, storageDays).Unix()
			productStorage.ExpireTime = &expireTime
			util.Wlog(logCtx).Info(fmt.Sprintf("[ProductStorage] 计算过期时间: 存储时间=%d, 存储天数=%d, 过期时间=%d", *productStorage.StorageTime, storageDays, expireTime))
		} else {
			// Fallback if StorageTime is somehow still zero, use current time plus validity
			expireTime := time.Now().AddDate(0, 0, storageDays).Unix()
			productStorage.ExpireTime = &expireTime
			util.Wlog(logCtx).Info(fmt.Sprintf("[ProductStorage] 使用当前时间计算过期时间: 存储天数=%d, 过期时间=%d", storageDays, expireTime))
		}
	}

	// 确保最后操作时间与存入时间一致
	productStorage.LastOperationTime = productStorage.StorageTime

	// 使用BaseService的Save方法自动生成Id和其他基础字段
	return Save(productStorage)
}

func (service *ProductStorageService) UpdateProductStorage(logCtx *gin.Context, productStorage *po.ProductStorage) error {
	// 先查询原始记录，用于对比变更
	oldStorage, err := service.FindProductStorageById(logCtx, *productStorage.Id)
	if err != nil {
		return err
	}

	if oldStorage == nil {
		return fmt.Errorf("存酒记录不存在，ID: %s", *productStorage.Id)
	}

	// 准备要记录的变更内容
	var changes []string

	// 比较并记录变更
	if oldStorage.ProductName != nil && productStorage.ProductName != nil && *oldStorage.ProductName != *productStorage.ProductName {
		changes = append(changes, fmt.Sprintf("商品名称: %s → %s", *oldStorage.ProductName, *productStorage.ProductName))
	}

	if oldStorage.ProductUnit != nil && productStorage.ProductUnit != nil && *oldStorage.ProductUnit != *productStorage.ProductUnit {
		changes = append(changes, fmt.Sprintf("商品单位: %s → %s", *oldStorage.ProductUnit, *productStorage.ProductUnit))
	}

	if oldStorage.ProductSpec != nil && productStorage.ProductSpec != nil && *oldStorage.ProductSpec != *productStorage.ProductSpec {
		changes = append(changes, fmt.Sprintf("商品规格: %s → %s", *oldStorage.ProductSpec, *productStorage.ProductSpec))
	}

	if oldStorage.StorageLocation != nil && productStorage.StorageLocation != nil && *oldStorage.StorageLocation != *productStorage.StorageLocation {
		changes = append(changes, fmt.Sprintf("存放位置: %s → %s", *oldStorage.StorageLocation, *productStorage.StorageLocation))
	}

	// 特别处理数量变更，可能影响剩余数量
	var quantityChange bool = false
	var oldQuantity, newQuantity, oldRemaining, newRemaining int

	if oldStorage.Quantity != nil && productStorage.Quantity != nil && *oldStorage.Quantity != *productStorage.Quantity {
		oldQuantity = *oldStorage.Quantity
		newQuantity = *productStorage.Quantity
		changes = append(changes, fmt.Sprintf("数量: %d → %d", oldQuantity, newQuantity))
		quantityChange = true
	}

	if oldStorage.RemainingQty != nil && productStorage.RemainingQty != nil && *oldStorage.RemainingQty != *productStorage.RemainingQty {
		oldRemaining = *oldStorage.RemainingQty
		newRemaining = *productStorage.RemainingQty
		changes = append(changes, fmt.Sprintf("剩余数量: %d → %d", oldRemaining, newRemaining))
	}

	// 更新最后操作时间
	currentTime := util.TimeNowUnixInt64()
	productStorage.LastOperationTime = &currentTime

	// 使用事务确保数据一致性
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 保存更新
		if err := UpdateNotNull(productStorage); err != nil {
			return err
		}

		// 如果有变更，记录操作日志
		if len(changes) > 0 {
			// 实例化操作日志服务
			opLogService := NewProductStorageOperationLogService()

			// 构建操作备注
			operationRemark := "更新存酒记录: " + strings.Join(changes, ", ")

			// 确定操作数量（如果剩余数量变更）
			operationQty := 0
			if quantityChange {
				// 如果是数量变更，记录变更的数量差异
				operationQty = newQuantity - oldQuantity
			}

			// 记录操作日志
			_, err := opLogService.CreateWithinTransaction(
				tx,
				*productStorage.Id,
				*productStorage.OrderNo,
				"update",
				"更新",
				currentTime,
				operationQty,
				*productStorage.OperatorId,
				*productStorage.OperatorName,
				*productStorage.RemainingQty,
				operationRemark,
			)

			if err != nil {
				return fmt.Errorf("创建更新操作日志记录失败: %s", err.Error())
			}
		}

		return nil
	})
}

func (service *ProductStorageService) FindProductStorageById(logCtx *gin.Context, id string) (productStorage *po.ProductStorage, err error) {
	query := dal.Use(model.DBSlave.Self)
	return query.ProductStorage.WithContext(logCtx).Where(query.ProductStorage.Id.Eq(id)).First()
}

func (service *ProductStorageService) FindAllProductStorage(logCtx *gin.Context, reqDto *req.QueryProductStorageReqDto) (list []*po.ProductStorage, err error) {
	query := dal.Use(model.DBSlave.Self)
	queryDo := query.ProductStorage.WithContext(logCtx)
	// 查询正常状态和已报废的存酒记录，过滤掉已撤销的记录
	queryDo = queryDo.Where(query.ProductStorage.State.Neq(_const.StorageStateCancelled))

	if reqDto.Id != "" {
		queryDo = queryDo.Where(query.ProductStorage.Id.Eq(reqDto.Id))
	}
	if reqDto.OrderNo != "" {
		queryDo = queryDo.Where(query.ProductStorage.OrderNo.Eq(reqDto.OrderNo))
	}
	if reqDto.VenueId != "" {
		queryDo = queryDo.Where(query.ProductStorage.VenueId.Eq(reqDto.VenueId))
	}
	if reqDto.CustomerId != "" {
		queryDo = queryDo.Where(query.ProductStorage.CustomerId.Eq(reqDto.CustomerId))
	}
	if reqDto.CustomerName != "" {
		queryDo = queryDo.Where(query.ProductStorage.CustomerName.Like("%" + reqDto.CustomerName + "%"))
	}
	if reqDto.MemberCardId != "" {
		queryDo = queryDo.Where(query.ProductStorage.MemberCardId.Eq(reqDto.MemberCardId))
	}
	if reqDto.MemberCardNumber != "" {
		queryDo = queryDo.Where(query.ProductStorage.MemberCardNumber.Eq(reqDto.MemberCardNumber))
	}
	if reqDto.ProductId != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductId.Eq(reqDto.ProductId))
	}
	if reqDto.ProductName != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductName.Like("%" + reqDto.ProductName + "%"))
	}
	if reqDto.ProductType != "" {
		// 使用更新后的 dal 包中的 ProductType 字段
		queryDo = queryDo.Where(query.ProductStorage.ProductType.Eq(reqDto.ProductType))
	}
	if reqDto.PhoneNumber != "" {
		queryDo = queryDo.Where(query.ProductStorage.PhoneNumber.Eq(reqDto.PhoneNumber))
	}
	// 综合搜索文本
	if reqDto.SearchText != "" {
		// 支持多字段模糊匹配
		searchText := "%" + reqDto.SearchText + "%"
		queryDo = queryDo.Where(query.ProductStorage.PhoneNumber.Like(searchText))
		queryDo = queryDo.Or(query.ProductStorage.MemberCardNo.Like(searchText))     // 保留旧字段搜索
		queryDo = queryDo.Or(query.ProductStorage.MemberCardNumber.Like(searchText)) // 添加新字段搜索
		queryDo = queryDo.Or(query.ProductStorage.CustomerName.Like(searchText))
	}
	if reqDto.StorageLocation != "" {
		queryDo = queryDo.Where(query.ProductStorage.StorageLocation.Eq(reqDto.StorageLocation))
	}
	if reqDto.OperatorId != "" {
		queryDo = queryDo.Where(query.ProductStorage.OperatorId.Eq(reqDto.OperatorId))
	}
	if reqDto.OperatorName != "" {
		queryDo = queryDo.Where(query.ProductStorage.OperatorName.Like("%" + reqDto.OperatorName + "%"))
	}
	if reqDto.StorageTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Gte(reqDto.StorageTimeStart))
	}
	if reqDto.StorageTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Lte(reqDto.StorageTimeEnd))
	}
	if reqDto.ExpireTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Gte(reqDto.ExpireTimeStart))
	}
	if reqDto.ExpireTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Lte(reqDto.ExpireTimeEnd))
	}
	// 只查询有剩余数量的记录
	if reqDto.OnlyRemaining {
		queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
	}

	// 添加排序和分页
	queryDo = queryDo.Order(query.ProductStorage.LastOperationTime.Desc())

	// 处理分页
	if reqDto.PageNum > 0 && reqDto.PageSize > 0 {
		offset := (reqDto.PageNum - 1) * reqDto.PageSize
		queryDo = queryDo.Offset(offset).Limit(reqDto.PageSize)
	}

	return queryDo.Find()
}

// GetStorageStatistics 获取存取酒统计信息
func (service *ProductStorageService) GetStorageStatistics(logCtx *gin.Context, reqDto *req.GetStorageStatisticsReqDto) (*vo.StorageStatisticsVO, error) {
	// 如果统计类型是按商品统计，则调用另一个方法处理
	if reqDto.StatType == "byProduct" {
		productStats, err := service.GetProductStorageStatistics(logCtx, reqDto)
		if err != nil {
			return nil, err
		}

		// 返回汇总数据
		result := &vo.StorageStatisticsVO{
			TotalStorage:   productStats.TotalStorageQuantity,
			TotalRemaining: productStats.TotalRemainingQuantity,
			TotalWithdraw:  productStats.TotalStorageQuantity - productStats.TotalRemainingQuantity,
		}

		// 计算今日存入和取出
		todayStart := time.Now().Truncate(24 * time.Hour).Unix()
		todayEnd := todayStart + 86399

		db := model.DBSlave.Self

		// 计算今日存入数量
		db.Model(&po.ProductStorageOrder{}).
			Where("venue_id = ? AND storage_time BETWEEN ? AND ?", reqDto.VenueId, todayStart, todayEnd).
			Select("IFNULL(SUM(total_quantity), 0)").
			Row().Scan(&result.TodayStorage)

		// 计算今日取出数量
		db.Model(&po.ProductWithdrawOrder{}).
			Where("venue_id = ? AND withdraw_time BETWEEN ? AND ?", reqDto.VenueId, todayStart, todayEnd).
			Select("IFNULL(SUM(total_quantity), 0)").
			Row().Scan(&result.TodayWithdraw)

		// 计算客户数量
		whereCondition := "venue_id = ? AND remaining_quantity > 0"
		whereParams := []interface{}{reqDto.VenueId}

		db.Model(&po.ProductStorageOrder{}).
			Where(whereCondition, whereParams...).
			Select("COUNT(DISTINCT customer_id)").
			Row().Scan(&result.CustomerCount)

		// 计算即将过期数量 (30天内)
		thirtyDaysLater := time.Now().AddDate(0, 0, 30).Unix()

		db.Model(&po.ProductStorage{}).
			Where("venue_id = ? AND remaining_qty > 0 AND expire_time BETWEEN ? AND ?",
				reqDto.VenueId, time.Now().Unix(), thirtyDaysLater).
			Select("IFNULL(SUM(remaining_qty), 0)").
			Row().Scan(&result.ExpiringSoonCount)

		return result, nil
	}

	// 原有的汇总统计逻辑保持不变
	result := &vo.StorageStatisticsVO{}
	db := model.DBSlave.Self

	// 构建基础查询条件
	whereCondition := "1=1"
	whereParams := []interface{}{}

	if reqDto.VenueId != "" {
		whereCondition += " AND venue_id = ?"
		whereParams = append(whereParams, reqDto.VenueId)
	}

	if reqDto.StartTime > 0 {
		whereCondition += " AND storage_time >= ?"
		whereParams = append(whereParams, reqDto.StartTime)
	}

	if reqDto.EndTime > 0 {
		whereCondition += " AND storage_time <= ?"
		whereParams = append(whereParams, reqDto.EndTime)
	}

	// 修正统计逻辑 - 直接从product_storage表查询，避免订单表的数据不一致问题
	storageWhereCondition := "venue_id = ? AND state != 2" // 排除已撤销记录
	storageWhereParams := []interface{}{reqDto.VenueId}

	if reqDto.StartTime > 0 {
		storageWhereCondition += " AND storage_time >= ?"
		storageWhereParams = append(storageWhereParams, reqDto.StartTime)
	}

	if reqDto.EndTime > 0 {
		storageWhereCondition += " AND storage_time <= ?"
		storageWhereParams = append(storageWhereParams, reqDto.EndTime)
	}

	// 计算存酒总数 - 从product_storage表直接统计
	db.Model(&po.ProductStorage{}).
		Where(storageWhereCondition, storageWhereParams...).
		Select("IFNULL(SUM(quantity), 0)").
		Row().Scan(&result.TotalStorage)

	// 计算取酒总数 - 用总存入减去剩余
	var totalRemaining int
	db.Model(&po.ProductStorage{}).
		Where(storageWhereCondition, storageWhereParams...).
		Select("IFNULL(SUM(remaining_qty), 0)").
		Row().Scan(&totalRemaining)

	result.TotalWithdraw = result.TotalStorage - totalRemaining
	result.TotalRemaining = totalRemaining

	// 计算今日存入数量
	todayStart := time.Now().Truncate(24 * time.Hour).Unix()
	todayEnd := todayStart + 86399

	db.Model(&po.ProductStorageOrder{}).
		Where("venue_id = ? AND storage_time BETWEEN ? AND ?", reqDto.VenueId, todayStart, todayEnd).
		Select("IFNULL(SUM(total_quantity), 0)").
		Row().Scan(&result.TodayStorage)

	// 计算今日取出数量
	db.Model(&po.ProductWithdrawOrder{}).
		Where("venue_id = ? AND withdraw_time BETWEEN ? AND ?", reqDto.VenueId, todayStart, todayEnd).
		Select("IFNULL(SUM(total_quantity), 0)").
		Row().Scan(&result.TodayWithdraw)

	// 计算客户数量 - 从product_storage表统计有剩余数量的客户
	db.Model(&po.ProductStorage{}).
		Where(storageWhereCondition+" AND remaining_qty > 0", storageWhereParams...).
		Select("COUNT(DISTINCT customer_id)").
		Row().Scan(&result.CustomerCount)

	// 计算即将过期数量 (30天内)
	thirtyDaysLater := time.Now().AddDate(0, 0, 30).Unix()

	db.Model(&po.ProductStorage{}).
		Where("venue_id = ? AND remaining_qty > 0 AND expire_time BETWEEN ? AND ?",
			reqDto.VenueId, time.Now().Unix(), thirtyDaysLater).
		Select("IFNULL(SUM(remaining_qty), 0)").
		Row().Scan(&result.ExpiringSoonCount)

	return result, nil
}

// FindCustomerStorageRecords 根据客户ID查询所有存酒记录
// 包括存酒订单和存酒明细
func (service *ProductStorageService) FindCustomerStorageRecords(logCtx *gin.Context, customerId string, venueId string) (*vo.CustomerStorageStatisticsVO, error) {
	if customerId == "" {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	// 1. 查询客户基本信息
	customerInfo := &po.ProductStorageOrder{}
	err := model.DBSlave.Self.Where("customer_id = ? AND venue_id = ?", customerId, venueId).
		Order("ctime desc").
		Limit(1).
		Find(&customerInfo).Error
	if err != nil {
		return nil, err
	}

	result := &vo.CustomerStorageStatisticsVO{
		CustomerId: customerId,
	}

	if customerInfo != nil && customerInfo.CustomerName != nil {
		result.CustomerName = *customerInfo.CustomerName
		if customerInfo.PhoneNumber != nil {
			result.PhoneNumber = *customerInfo.PhoneNumber
		}
	}

	// 2. 查询客户的所有存酒明细 - 排除已撤销的记录
	var storageRecords []*po.ProductStorage
	err = model.DBSlave.Self.Where("customer_id = ? AND venue_id = ? AND state != 2", customerId, venueId).
		Order("storage_time desc").
		Find(&storageRecords).Error
	if err != nil {
		return nil, err
	}

	// 3. 统计数据
	result.TotalStorage = lo.Reduce(storageRecords, func(acc int, record *po.ProductStorage, _ int) int {
		if record.Quantity != nil {
			return acc + *record.Quantity
		}
		return acc
	}, 0)

	result.TotalRemainingQuantity = lo.Reduce(storageRecords, func(acc int, record *po.ProductStorage, _ int) int {
		if record.RemainingQty != nil {
			return acc + *record.RemainingQty
		}
		return acc
	}, 0)

	// 提取所有产品类型ID到集合
	productTypeMap := lo.Reduce(storageRecords, func(acc map[string]bool, record *po.ProductStorage, _ int) map[string]bool {
		if record.ProductId != nil && *record.ProductId != "" {
			acc[*record.ProductId] = true
		}
		return acc
	}, make(map[string]bool))

	// 找到最早存酒时间和最早到期时间
	var earliestStorage int64 = 0
	var earliestExpire int64 = 0

	for _, record := range storageRecords {
		// 记录最早存酒时间
		if record.StorageTime != nil && (*record.StorageTime > 0) &&
			(earliestStorage == 0 || *record.StorageTime < earliestStorage) {
			earliestStorage = *record.StorageTime
		}

		// 记录最早到期时间
		if record.ExpireTime != nil && (*record.ExpireTime > 0) &&
			(earliestExpire == 0 || *record.ExpireTime < earliestExpire) {
			earliestExpire = *record.ExpireTime
		}
	}

	// 4. 构建存酒明细列表
	result.StorageDetails = lo.Map(storageRecords, func(record *po.ProductStorage, _ int) vo.ProductStorageDetailVO {
		detail := vo.ProductStorageDetailVO{
			Id:           *record.Id,
			OrderNo:      *record.OrderNo,
			ProductId:    *record.ProductId,
			ProductName:  *record.ProductName,
			Quantity:     *record.Quantity,
			RemainingQty: *record.RemainingQty,
			StorageTime:  *record.StorageTime,
		}

		if record.ExpireTime != nil {
			detail.ExpireTime = *record.ExpireTime
		}

		if record.StorageLocation != nil {
			detail.StorageLocation = *record.StorageLocation
		}

		return detail
	})

	// 5. 查询客户的取酒记录
	var withdrawRecords []*po.ProductWithdraw
	err = model.DBSlave.Self.Where("customer_id = ? AND venue_id = ?", customerId, venueId).
		Order("withdraw_time desc").
		Find(&withdrawRecords).Error
	if err != nil {
		return nil, err
	}

	// 6. 计算取酒总数和构建取酒明细列表
	result.TotalWithdraw = lo.Reduce(withdrawRecords, func(acc int, record *po.ProductWithdraw, _ int) int {
		if record.Quantity != nil {
			return acc + *record.Quantity
		}
		return acc
	}, 0)

	// 构建取酒明细列表
	result.WithdrawDetails = lo.Map(withdrawRecords, func(record *po.ProductWithdraw, _ int) vo.ProductWithdrawDetailVO {
		detail := vo.ProductWithdrawDetailVO{
			Id:           *record.Id,
			OrderNo:      *record.OrderNo,
			ProductId:    *record.ProductId,
			ProductName:  *record.ProductName,
			Quantity:     *record.Quantity,
			WithdrawTime: *record.WithdrawTime,
		}

		if record.StorageId != nil {
			detail.StorageId = *record.StorageId
		}

		return detail
	})

	// 7. 设置统计结果
	result.ProductTypeCount = len(productTypeMap)
	result.EarliestStorageTime = earliestStorage
	result.EarliestExpireTime = earliestExpire

	return result, nil
}

// FindByParentOrderNo 根据父订单号查询所有存酒明细
func (service *ProductStorageService) FindByParentOrderNo(logCtx *gin.Context, parentOrderNo string) ([]*po.ProductStorage, error) {
	query := dal.Use(model.DBSlave.Self)
	return query.ProductStorage.WithContext(logCtx).Where(query.ProductStorage.ParentOrderNo.Eq(parentOrderNo)).Find()
}

// GetOrderByOrderNo 根据订单号获取存酒单
func (service *ProductStorageService) GetOrderByOrderNo(logCtx *gin.Context, orderNo string) (*po.ProductStorageOrder, error) {
	// 创建订单服务实例并委托调用
	orderService := NewProductStorageOrderService()
	return orderService.GetOrderByOrderNo(logCtx, orderNo)
}

// 添加CountProductStorage方法以支持分页功能
func (service *ProductStorageService) CountProductStorage(ctx *gin.Context, dto *req.QueryProductStorageReqDto) (int64, error) {
	var count int64
	query := dal.Use(model.DBSlave.Self)

	// 构建查询条件
	queryDo := query.ProductStorage.WithContext(ctx)
	// 查询正常状态和已报废的存酒记录，过滤掉已撤销的记录
	queryDo = queryDo.Where(query.ProductStorage.State.Neq(_const.StorageStateCancelled))

	// ID精确匹配
	if dto.Id != "" {
		queryDo = queryDo.Where(query.ProductStorage.Id.Eq(dto.Id))
	}

	// 订单号精确匹配
	if dto.OrderNo != "" {
		queryDo = queryDo.Where(query.ProductStorage.OrderNo.Eq(dto.OrderNo))
	}

	// 场馆ID精确匹配
	if dto.VenueId != "" {
		queryDo = queryDo.Where(query.ProductStorage.VenueId.Eq(dto.VenueId))
	}

	// 客户ID精确匹配
	if dto.CustomerId != "" {
		queryDo = queryDo.Where(query.ProductStorage.CustomerId.Eq(dto.CustomerId))
	}

	// 客户姓名模糊匹配
	if dto.CustomerName != "" {
		queryDo = queryDo.Where(query.ProductStorage.CustomerName.Like("%" + dto.CustomerName + "%"))
	}

	// 会员卡ID精确匹配
	if dto.MemberCardId != "" {
		queryDo = queryDo.Where(query.ProductStorage.MemberCardId.Eq(dto.MemberCardId))
	}

	// 会员卡号精确匹配
	if dto.MemberCardNumber != "" {
		queryDo = queryDo.Where(query.ProductStorage.MemberCardNumber.Eq(dto.MemberCardNumber))
	}

	// 电话号码模糊匹配
	if dto.PhoneNumber != "" {
		queryDo = queryDo.Where(query.ProductStorage.PhoneNumber.Like("%" + dto.PhoneNumber + "%"))
	}

	// 产品ID精确匹配
	if dto.ProductId != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductId.Eq(dto.ProductId))
	}

	// 产品名称模糊匹配
	if dto.ProductName != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductName.Like("%" + dto.ProductName + "%"))
	}

	// 产品类型精确匹配
	if dto.ProductType != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductType.Eq(dto.ProductType))
	}

	// 存放位置精确匹配
	if dto.StorageLocation != "" {
		queryDo = queryDo.Where(query.ProductStorage.StorageLocation.Eq(dto.StorageLocation))
	}

	// 操作员ID精确匹配
	if dto.OperatorId != "" {
		queryDo = queryDo.Where(query.ProductStorage.OperatorId.Eq(dto.OperatorId))
	}

	// 操作员姓名模糊匹配
	if dto.OperatorName != "" {
		queryDo = queryDo.Where(query.ProductStorage.OperatorName.Like("%" + dto.OperatorName + "%"))
	}

	// 存入时间范围
	if dto.StorageTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Gte(dto.StorageTimeStart))
	}
	if dto.StorageTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Lte(dto.StorageTimeEnd))
	}

	// 到期时间范围
	if dto.ExpireTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Gte(dto.ExpireTimeStart))
	}
	if dto.ExpireTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Lte(dto.ExpireTimeEnd))
	}

	// 综合搜索文本
	if dto.SearchText != "" {
		// 支持多字段模糊匹配
		searchText := "%" + dto.SearchText + "%"
		queryDo = queryDo.Where(query.ProductStorage.PhoneNumber.Like(searchText))
		queryDo = queryDo.Or(query.ProductStorage.MemberCardNo.Like(searchText))     // 保留旧字段搜索
		queryDo = queryDo.Or(query.ProductStorage.MemberCardNumber.Like(searchText)) // 添加新字段搜索
		queryDo = queryDo.Or(query.ProductStorage.CustomerName.Like(searchText))
	}

	// 仅剩余量大于0
	if dto.OnlyRemaining {
		queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
	}

	// 统计满足条件的记录数
	count, err := queryDo.Count()
	if err != nil {
		return 0, err
	}

	return count, nil
}

// GetProductStorageStatistics 按商品分组获取存酒统计
func (service *ProductStorageService) GetProductStorageStatistics(logCtx *gin.Context, reqDto *req.GetStorageStatisticsReqDto) (*vo.ProductStorageStatListVO, error) {
	result := &vo.ProductStorageStatListVO{
		List: make([]vo.ProductStorageItemStatVO, 0),
	}

	// 使用gorm的API
	db := model.DBSlave.Self.Table("product_storage").Select("product_id")

	// 构建基础查询条件
	if reqDto.VenueId != "" {
		db = db.Where("venue_id = ?", reqDto.VenueId)
	}

	if reqDto.CustomerId != "" {
		db = db.Where("customer_id = ?", reqDto.CustomerId)
	}

	// 使用SearchText进行模糊搜索
	if reqDto.SearchText != "" {
		searchParam := "%" + reqDto.SearchText + "%"
		db = db.Where("(product_name LIKE ? OR status_code LIKE ?)", searchParam, searchParam)
	}

	if reqDto.StartTime > 0 {
		db = db.Where("storage_time >= ?", reqDto.StartTime)
	}

	if reqDto.EndTime > 0 {
		db = db.Where("storage_time <= ?", reqDto.EndTime)
	}

	// 仅显示有剩余数量的记录
	if reqDto.OnlyRemaining {
		db = db.Where("remaining_qty > 0")
	}

	// 添加分页
	pageSize := reqDto.PageSize
	if pageSize <= 0 {
		pageSize = 200
	}
	pageNum := reqDto.PageNum
	if pageNum <= 0 {
		pageNum = 1
	}
	offset := (pageNum - 1) * pageSize

	// 设置分页信息
	result.PageInfo = vo.PageInfoVO{
		PageNum:  pageNum,
		PageSize: pageSize,
	}

	// 修正后的聚合表达式 - 确保逻辑正确
	// 1. 总存入数量：排除已撤销的记录
	totalQuantityExpr := "SUM(CASE WHEN state != 2 THEN quantity ELSE 0 END) as total_quantity"

	// 2. 现存数量：使用remaining_qty，排除已撤销的记录
	currentQuantityExpr := "SUM(CASE WHEN state != 2 THEN remaining_qty ELSE 0 END) as current_quantity"

	// 3. 剩余数量：与现存数量相同
	remainingQuantityExpr := "SUM(CASE WHEN state != 2 THEN remaining_qty ELSE 0 END) as remaining_quantity"

	// 4. 报废数量：状态为已报废(state=1)的记录的原始数量
	discardedQuantityExpr := "SUM(CASE WHEN state = 1 THEN quantity ELSE 0 END) as discarded_quantity"

	// 先获取总记录数（不带分页）- 添加HAVING子句过滤数量为0的商品
	var count int64
	err := db.Select("product_id").
		Group("product_id").
		Having("SUM(CASE WHEN state != 2 THEN quantity ELSE 0 END) > 0").
		Count(&count).Error
	if err != nil {
		logCtx.Error(err)
		return nil, err
	}
	result.Total = count

	// 构建查询 - 添加HAVING子句过滤数量为0的商品
	query := db.Select("product_id, " +
		"ANY_VALUE(product_name) as product_name, " +
		"ANY_VALUE(storage_location) as storage_location, " +
		"ANY_VALUE(IFNULL(product_unit,'')) as unit, " +
		totalQuantityExpr + ", " +
		currentQuantityExpr + ", " +
		remainingQuantityExpr + ", " +
		discardedQuantityExpr + ", " +
		"ANY_VALUE(IFNULL(product_type,'')) as product_type, " +
		"ANY_VALUE(IFNULL(product_type,'')) as storage_type").
		Group("product_id").
		Having("SUM(CASE WHEN state != 2 THEN quantity ELSE 0 END) > 0").
		Order("total_quantity DESC").
		Offset(offset).
		Limit(pageSize)

	// 执行查询
	type Result struct {
		ProductID         string
		ProductName       string
		StorageLocation   string
		Unit              string
		TotalQuantity     int64
		CurrentQuantity   int64
		RemainingQuantity int64
		DiscardedQuantity int64
		ProductType       string
		StorageType       string
	}

	var results []Result
	err = query.Find(&results).Error
	if err != nil {
		logCtx.Error(err)
		return nil, err
	}

	// 处理结果
	var totalStorageQuantity, totalCurrentQuantity, totalRemainingQuantity, totalDiscardedQuantity int

	for _, row := range results {
		// 创建 VO 对象
		item := vo.ProductStorageItemStatVO{
			ProductId:         row.ProductID,
			ProductName:       row.ProductName,
			ProductType:       row.ProductType,
			StorageLocation:   row.StorageLocation,
			Unit:              row.Unit,
			StorageType:       row.StorageType,
			TotalQuantity:     int(row.TotalQuantity),
			CurrentQuantity:   int(row.CurrentQuantity),
			RemainingQuantity: int(row.RemainingQuantity),
			DiscardedQuantity: int(row.DiscardedQuantity),
		}

		result.List = append(result.List, item)

		// 累加汇总数据
		totalStorageQuantity += int(row.TotalQuantity)
		totalCurrentQuantity += int(row.CurrentQuantity)
		totalRemainingQuantity += int(row.RemainingQuantity)
		totalDiscardedQuantity += int(row.DiscardedQuantity)
	}

	// 设置汇总数据
	result.TotalStorageQuantity = totalStorageQuantity
	result.TotalCurrentQuantity = totalCurrentQuantity
	result.TotalRemainingQuantity = totalRemainingQuantity
	result.TotalDiscardedQuantity = totalDiscardedQuantity

	return result, nil
}

// FindAllProductStorageWithOrder 查询所有存酒记录并包含订单信息
func (service *ProductStorageService) FindAllProductStorageWithOrder(logCtx *gin.Context, reqDto *req.QueryProductStorageReqDto) ([]*po.ProductStorage, map[string]*po.ProductStorageOrder, error) {
	// 先查询存酒记录
	storageList, err := service.FindAllProductStorage(logCtx, reqDto)
	if err != nil {
		return nil, nil, err
	}

	// 提取所有parentOrderNo
	parentOrderNos := make([]string, 0)
	for _, storage := range storageList {
		if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
			parentOrderNos = append(parentOrderNos, *storage.ParentOrderNo)
		}
	}

	// 批量查询订单信息
	orderMap := make(map[string]*po.ProductStorageOrder)
	if len(parentOrderNos) > 0 {
		orderService := NewProductStorageOrderService()
		orders, err := orderService.GetOrdersByOrderNos(logCtx, parentOrderNos)
		if err != nil {
			// 记录错误但不中断流程
			logCtx.Error(fmt.Errorf("批量查询存酒订单失败: %v", err))
		} else {
			// 构建订单映射
			for _, order := range orders {
				if order.OrderNo != nil {
					orderMap[*order.OrderNo] = order
				}
			}
		}
	}

	return storageList, orderMap, nil
}

// FindMemberStorageRecords 根据会员卡ID查询会员的所有存酒记录
// 包括存酒订单和存酒明细，支持时间范围和商品名称查询
func (service *ProductStorageService) FindMemberStorageRecords(logCtx *gin.Context, memberCardId string, venueId string, reqDto *req.QueryProductStorageReqDto) (*vo.CustomerStorageStatisticsVO, error) {
	if memberCardId == "" {
		return nil, fmt.Errorf("会员卡ID不能为空")
	}

	// 1. 查询会员基本信息 - 从最近的存酒订单中获取
	memberInfo := &po.ProductStorageOrder{}
	err := model.DBSlave.Self.Where("member_card_id = ? AND venue_id = ?", memberCardId, venueId).
		Order("ctime desc").
		Limit(1).
		Find(&memberInfo).Error
	if err != nil {
		return nil, err
	}

	result := &vo.CustomerStorageStatisticsVO{
		MemberCardId: memberCardId,
	}

	// 设置会员基本信息
	if memberInfo != nil && memberInfo.CustomerName != nil {
		result.CustomerName = *memberInfo.CustomerName
		if memberInfo.PhoneNumber != nil {
			result.PhoneNumber = *memberInfo.PhoneNumber
		}
		if memberInfo.CustomerId != nil {
			result.CustomerId = *memberInfo.CustomerId
		}
		if memberInfo.MemberCardNumber != nil {
			result.MemberCardNumber = *memberInfo.MemberCardNumber
		}
	}

	// 2. 构建存酒记录查询条件
	query := dal.Use(model.DBSlave.Self)
	queryDo := query.ProductStorage.WithContext(logCtx)

	// 基础查询条件
	queryDo = queryDo.Where(query.ProductStorage.MemberCardId.Eq(memberCardId))
	queryDo = queryDo.Where(query.ProductStorage.VenueId.Eq(venueId))
	queryDo = queryDo.Where(query.ProductStorage.State.Neq(_const.StorageStateCancelled)) // 排除已撤销的记录

	// 应用额外的查询条件
	if reqDto != nil {
		// 商品名称模糊查询
		if reqDto.ProductName != "" {
			queryDo = queryDo.Where(query.ProductStorage.ProductName.Like("%" + reqDto.ProductName + "%"))
		}

		// 商品类型查询
		if reqDto.ProductType != "" {
			queryDo = queryDo.Where(query.ProductStorage.ProductType.Eq(reqDto.ProductType))
		}

		// 存入时间范围查询
		if reqDto.StorageTimeStart > 0 {
			queryDo = queryDo.Where(query.ProductStorage.StorageTime.Gte(reqDto.StorageTimeStart))
		}
		if reqDto.StorageTimeEnd > 0 {
			queryDo = queryDo.Where(query.ProductStorage.StorageTime.Lte(reqDto.StorageTimeEnd))
		}

		// 到期时间范围查询
		if reqDto.ExpireTimeStart > 0 {
			queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Gte(reqDto.ExpireTimeStart))
		}
		if reqDto.ExpireTimeEnd > 0 {
			queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Lte(reqDto.ExpireTimeEnd))
		}

		// 只查询有剩余数量的记录
		if reqDto.OnlyRemaining {
			queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
		}

		// 存放位置查询
		if reqDto.StorageLocation != "" {
			queryDo = queryDo.Where(query.ProductStorage.StorageLocation.Eq(reqDto.StorageLocation))
		}
	}

	// 按时间倒序排列
	queryDo = queryDo.Order(query.ProductStorage.StorageTime.Desc())

	// 查询存酒记录
	var storageRecords []*po.ProductStorage
	storageRecords, err = queryDo.Find()
	if err != nil {
		return nil, err
	}

	// 3. 统计数据
	result.TotalStorage = lo.Reduce(storageRecords, func(acc int, record *po.ProductStorage, _ int) int {
		if record.Quantity != nil {
			return acc + *record.Quantity
		}
		return acc
	}, 0)

	result.TotalRemainingQuantity = lo.Reduce(storageRecords, func(acc int, record *po.ProductStorage, _ int) int {
		if record.RemainingQty != nil {
			return acc + *record.RemainingQty
		}
		return acc
	}, 0)

	// 提取所有产品类型ID到集合
	productTypeMap := lo.Reduce(storageRecords, func(acc map[string]bool, record *po.ProductStorage, _ int) map[string]bool {
		if record.ProductId != nil && *record.ProductId != "" {
			acc[*record.ProductId] = true
		}
		return acc
	}, make(map[string]bool))

	// 找到最早存酒时间和最早到期时间
	var earliestStorage int64 = 0
	var earliestExpire int64 = 0

	for _, record := range storageRecords {
		// 记录最早存酒时间
		if record.StorageTime != nil && (*record.StorageTime > 0) &&
			(earliestStorage == 0 || *record.StorageTime < earliestStorage) {
			earliestStorage = *record.StorageTime
		}

		// 记录最早到期时间
		if record.ExpireTime != nil && (*record.ExpireTime > 0) &&
			(earliestExpire == 0 || *record.ExpireTime < earliestExpire) {
			earliestExpire = *record.ExpireTime
		}
	}

	// 4. 构建存酒明细列表
	result.StorageDetails = lo.Map(storageRecords, func(record *po.ProductStorage, _ int) vo.ProductStorageDetailVO {
		detail := vo.ProductStorageDetailVO{
			Id:           *record.Id,
			OrderNo:      *record.OrderNo,
			ProductId:    *record.ProductId,
			ProductName:  *record.ProductName,
			Quantity:     *record.Quantity,
			RemainingQty: *record.RemainingQty,
			StorageTime:  *record.StorageTime,
		}

		if record.ExpireTime != nil {
			detail.ExpireTime = *record.ExpireTime
		}

		if record.StorageLocation != nil {
			detail.StorageLocation = *record.StorageLocation
		}

		return detail
	})

	// 5. 查询会员的取酒记录
	var withdrawRecords []*po.ProductWithdraw
	err = model.DBSlave.Self.Where("member_card_no = ? AND venue_id = ?", memberCardId, venueId).
		Order("withdraw_time desc").
		Find(&withdrawRecords).Error
	if err != nil {
		// 如果查询取酒记录失败，记录错误但不中断流程
		util.Wlog(logCtx).Error(fmt.Sprintf("查询会员取酒记录失败: %v", err))
		withdrawRecords = []*po.ProductWithdraw{}
	}

	// 6. 计算取酒总数和构建取酒明细列表
	result.TotalWithdraw = lo.Reduce(withdrawRecords, func(acc int, record *po.ProductWithdraw, _ int) int {
		if record.Quantity != nil {
			return acc + *record.Quantity
		}
		return acc
	}, 0)

	// 构建取酒明细列表
	result.WithdrawDetails = lo.Map(withdrawRecords, func(record *po.ProductWithdraw, _ int) vo.ProductWithdrawDetailVO {
		detail := vo.ProductWithdrawDetailVO{
			Id:           *record.Id,
			OrderNo:      *record.OrderNo,
			ProductId:    *record.ProductId,
			ProductName:  *record.ProductName,
			Quantity:     *record.Quantity,
			WithdrawTime: *record.WithdrawTime,
		}

		if record.StorageId != nil {
			detail.StorageId = *record.StorageId
		}

		return detail
	})

	// 7. 设置统计结果
	result.ProductTypeCount = len(productTypeMap)
	result.EarliestStorageTime = earliestStorage
	result.EarliestExpireTime = earliestExpire

	return result, nil
}

// QueryMemberStorageRecords 专门的会员存酒查询接口
// 支持灵活的查询条件、排序和分页
func (service *ProductStorageService) QueryMemberStorageRecords(logCtx *gin.Context, reqDto *req.QueryMemberStorageReqDto) (*vo.MemberStorageQueryResultVO, error) {
	// 参数校验
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	if reqDto.MemberCardId == "" && reqDto.MemberCardNumber == "" && reqDto.PhoneNumber == "" {
		return nil, fmt.Errorf("会员卡ID、会员卡号或手机号至少提供一个")
	}

	// 确定查询方式：优先使用会员卡，其次手机号
	useCard := !(reqDto.MemberCardId == "" && reqDto.MemberCardNumber == "")
	var identifier string

	if useCard {
		// 使用会员卡标识
		identifier = reqDto.MemberCardId
		if identifier == "" {
			identifier = reqDto.MemberCardNumber
		}
	} else {
		// 使用手机号
		identifier = reqDto.PhoneNumber
	}

	// 设置默认分页参数
	pageNum := reqDto.PageNum
	if pageNum <= 0 {
		pageNum = 1
	}
	pageSize := reqDto.PageSize
	if pageSize <= 0 {
		pageSize = 20
	} else if pageSize > 100 {
		pageSize = 100 // 限制最大分页大小
	}

	// 1. 查询会员基本信息
	var memberInfo *vo.MemberBasicInfoVO
	var err error
	if useCard {
		memberInfo, err = service.getMemberBasicInfo(logCtx, identifier, reqDto.VenueId)
	} else {
		memberInfo, err = service.getMemberBasicInfoByPhone(logCtx, identifier, reqDto.VenueId)
	}
	if err != nil {
		return nil, fmt.Errorf("查询会员信息失败: %v", err)
	}

	// 2. 构建查询条件（使用DAL）
	query := dal.Use(model.DBSlave.Self)
	queryDo := query.ProductStorage.WithContext(logCtx)

	// 基础查询条件
	if useCard {
		// 支持三个会员卡字段
		queryDo = queryDo.Where(query.ProductStorage.MemberCardNo.Eq(identifier)).
			Or(query.ProductStorage.MemberCardNumber.Eq(identifier)).
			Or(query.ProductStorage.MemberCardId.Eq(identifier))
	} else {
		queryDo = queryDo.Where(query.ProductStorage.PhoneNumber.Eq(identifier))
	}
	queryDo = queryDo.Where(query.ProductStorage.VenueId.Eq(reqDto.VenueId))
	queryDo = queryDo.Where(query.ProductStorage.State.Neq(_const.StorageStateCancelled)) // 排除已撤销的记录

	// 应用查询条件
	queryDo = service.applyMemberStorageQueryConditions(queryDo, query, reqDto)

	// 3. 获取总数（用于分页）
	totalCount, err := queryDo.Count()
	if err != nil {
		return nil, fmt.Errorf("查询记录总数失败: %v", err)
	}

	// 4. 应用排序
	queryDo = service.applyMemberStorageSort(queryDo, query, reqDto.SortBy, reqDto.SortOrder)

	// 5. 应用分页
	offset := (pageNum - 1) * pageSize
	queryDo = queryDo.Offset(offset).Limit(pageSize)

	// 6. 执行查询
	storageRecords, err := queryDo.Find()
	if err != nil {
		return nil, fmt.Errorf("查询存酒记录失败: %v", err)
	}

	// 7. 转换为VO
	storageItems := service.convertToMemberStorageItemVOs(storageRecords)

	// 8. 构建返回结果
	result := &vo.MemberStorageQueryResultVO{
		Total: totalCount,
		List:  storageItems,
		PageInfo: vo.PageInfoVO{
			PageNum:  pageNum,
			PageSize: pageSize,
		},
		MemberInfo: *memberInfo,
	}

	return result, nil
}

// ================= 会员存酒查询相关的辅助方法 =================

// getMemberBasicInfo 获取会员基本信息
func (service *ProductStorageService) getMemberBasicInfo(logCtx *gin.Context, memberCardIdentifier string, venueId string) (*vo.MemberBasicInfoVO, error) {
	// 从存酒订单表中查询会员信息
	memberInfo := &po.ProductStorageOrder{}
	err := model.DBSlave.Self.Where("member_card_no = ? AND venue_id = ?", memberCardIdentifier, venueId).
		Order("ctime desc").
		Limit(1).
		Find(&memberInfo).Error
	if err != nil {
		return nil, err
	}

	result := &vo.MemberBasicInfoVO{
		MemberCardId: memberCardIdentifier,
	}

	if memberInfo != nil && memberInfo.CustomerName != nil {
		result.CustomerName = *memberInfo.CustomerName
		if memberInfo.PhoneNumber != nil {
			result.PhoneNumber = *memberInfo.PhoneNumber
		}
		if memberInfo.CustomerId != nil {
			result.CustomerId = *memberInfo.CustomerId
		}
		if memberInfo.MemberCardNumber != nil {
			result.MemberCardNumber = *memberInfo.MemberCardNumber
		}
	}

	return result, nil
}

// getMemberBasicInfoByPhone 根据手机号获取会员基本信息
func (service *ProductStorageService) getMemberBasicInfoByPhone(logCtx *gin.Context, phoneNumber string, venueId string) (*vo.MemberBasicInfoVO, error) {
	// 从存酒订单表中查询会员信息
	memberInfo := &po.ProductStorageOrder{}
	err := model.DBSlave.Self.Where("phone_number = ? AND venue_id = ?", phoneNumber, venueId).
		Order("ctime desc").
		Limit(1).
		Find(&memberInfo).Error
	if err != nil {
		return nil, err
	}

	result := &vo.MemberBasicInfoVO{
		PhoneNumber: phoneNumber,
	}

	if memberInfo != nil && memberInfo.CustomerName != nil {
		result.CustomerName = *memberInfo.CustomerName
		if memberInfo.CustomerId != nil {
			result.CustomerId = *memberInfo.CustomerId
		}
		if memberInfo.MemberCardNumber != nil {
			result.MemberCardNumber = *memberInfo.MemberCardNumber
		}
	}

	return result, nil
}

// applyMemberStorageQueryConditions 应用会员存酒查询条件
func (service *ProductStorageService) applyMemberStorageQueryConditions(queryDo dal.IProductStorageDo, query *dal.Query, reqDto *req.QueryMemberStorageReqDto) dal.IProductStorageDo {
	// 商品相关条件
	if reqDto.ProductName != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductName.Like("%" + reqDto.ProductName + "%"))
	}
	if reqDto.ProductType != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductType.Eq(reqDto.ProductType))
	}
	if reqDto.ProductId != "" {
		queryDo = queryDo.Where(query.ProductStorage.ProductId.Eq(reqDto.ProductId))
	}
	if reqDto.StorageLocation != "" {
		queryDo = queryDo.Where(query.ProductStorage.StorageLocation.Eq(reqDto.StorageLocation))
	}

	// 时间范围条件
	if reqDto.StorageTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Gte(reqDto.StorageTimeStart))
	}
	if reqDto.StorageTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.StorageTime.Lte(reqDto.StorageTimeEnd))
	}
	if reqDto.ExpireTimeStart > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Gte(reqDto.ExpireTimeStart))
	}
	if reqDto.ExpireTimeEnd > 0 {
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Lte(reqDto.ExpireTimeEnd))
	}

	// 状态过滤条件
	// 注释：根据用户需求，即使剩余数量为0的记录也应该显示，所以暂时移除OnlyRemaining过滤
	// if reqDto.OnlyRemaining {
	// 	queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
	// }

	// 即将过期条件（30天内）
	if reqDto.OnlyExpiring {
		currentTime := time.Now().Unix()
		thirtyDaysLater := time.Now().AddDate(0, 0, 30).Unix()
		queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Between(currentTime, thirtyDaysLater))
	}

	// 已过期条件
	if reqDto.OnlyExpired {
		currentTime := time.Now().Unix()
		queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))
		queryDo = queryDo.Where(query.ProductStorage.ExpireTime.Lt(currentTime))
	}

	return queryDo
}

// applyMemberStorageSort 应用排序规则
func (service *ProductStorageService) applyMemberStorageSort(queryDo dal.IProductStorageDo, query *dal.Query, sortBy, sortOrder string) dal.IProductStorageDo {
	// 设置默认排序
	if sortBy == "" {
		sortBy = "storageTime"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// 根据排序字段和方向应用排序
	switch sortBy {
	case "storageTime":
		if sortOrder == "asc" {
			queryDo = queryDo.Order(query.ProductStorage.StorageTime.Asc())
		} else {
			queryDo = queryDo.Order(query.ProductStorage.StorageTime.Desc())
		}
	case "expireTime":
		if sortOrder == "asc" {
			queryDo = queryDo.Order(query.ProductStorage.ExpireTime.Asc())
		} else {
			queryDo = queryDo.Order(query.ProductStorage.ExpireTime.Desc())
		}
	case "productName":
		if sortOrder == "asc" {
			queryDo = queryDo.Order(query.ProductStorage.ProductName.Asc())
		} else {
			queryDo = queryDo.Order(query.ProductStorage.ProductName.Desc())
		}
	default:
		// 默认按存入时间倒序
		queryDo = queryDo.Order(query.ProductStorage.StorageTime.Desc())
	}

	return queryDo
}

// convertToMemberStorageItemVOs 转换为会员存酒记录VO
func (service *ProductStorageService) convertToMemberStorageItemVOs(records []*po.ProductStorage) []vo.MemberStorageItemVO {
	items := make([]vo.MemberStorageItemVO, 0, len(records))
	currentTime := time.Now().Unix()

	for _, record := range records {
		item := vo.MemberStorageItemVO{
			Id:             *record.Id,
			OrderNo:        *record.OrderNo,
			ProductId:      *record.ProductId,
			ProductName:    *record.ProductName,
			Quantity:       *record.Quantity,
			RemainingQty:   *record.RemainingQty,
			WithdrawnQty:   *record.Quantity - *record.RemainingQty,
			StorageTime:    *record.StorageTime,
			StorageTimeStr: time.Unix(*record.StorageTime, 0).Format("2006-01-02 15:04:05"),
		}

		// 设置可选字段
		if record.ParentOrderNo != nil {
			item.ParentOrderNo = *record.ParentOrderNo
		}
		if record.ProductType != nil {
			item.ProductType = *record.ProductType
		}
		if record.ProductUnit != nil {
			item.ProductUnit = *record.ProductUnit
		}
		if record.ProductSpec != nil {
			item.ProductSpec = *record.ProductSpec
		}
		if record.StorageLocation != nil {
			item.StorageLocation = *record.StorageLocation
		}
		if record.ExpireTime != nil {
			item.ExpireTime = *record.ExpireTime
			item.ExpireTimeStr = time.Unix(*record.ExpireTime, 0).Format("2006-01-02 15:04:05")

			// 计算到期状态
			daysToExpire := int((*record.ExpireTime - currentTime) / 86400)
			item.DaysToExpire = daysToExpire

			if *record.ExpireTime <= currentTime {
				item.ExpiringStatus = "expired"
			} else if daysToExpire <= 30 {
				item.ExpiringStatus = "expiring"
			} else {
				item.ExpiringStatus = "normal"
			}
		}
		if record.OperatorId != nil {
			item.OperatorId = *record.OperatorId
		}
		if record.OperatorName != nil {
			item.OperatorName = *record.OperatorName
		}
		if record.Remark != nil {
			item.Remark = *record.Remark
		}

		// 设置状态信息
		if *record.RemainingQty <= 0 {
			item.StatusCode = "withdrawn"
			item.StatusName = "已取完"
		} else if *record.RemainingQty < *record.Quantity {
			item.StatusCode = "partial"
			item.StatusName = "部分取用"
		} else {
			item.StatusCode = "stored"
			item.StatusName = "已存放"
		}

		items = append(items, item)
	}

	return items
}

// QueryProductStoragesWithPagination 查询商品存储记录（包含分页和数据转换）
func (service *ProductStorageService) QueryProductStoragesWithPagination(ctx *gin.Context, reqDto *req.QueryProductStorageReqDto) (*vo.ProductStoragePageVO, error) {
	// 设置默认的分页参数
	if reqDto.PageNum <= 0 {
		reqDto.PageNum = 1
	}
	if reqDto.PageSize <= 0 {
		reqDto.PageSize = 200
	}

	// 获取存酒记录列表及关联订单信息
	list, orderMap, err := service.FindAllProductStorageWithOrder(ctx, reqDto)
	if err != nil {
		return nil, fmt.Errorf("查询存酒记录失败: %v", err)
	}

	// 使用lo库转换为VO对象
	voList := lo.Map(list, func(storage *po.ProductStorage, index int) vo.ProductStorageVO {
		// 查找关联的订单信息
		var order *po.ProductStorageOrder
		if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
			order = orderMap[*storage.ParentOrderNo]
		}
		// 使用带订单信息的转换方法
		return transfer.ProductStorageTransfer{}.PoToVoWithOrder(*storage, order)
	})

	// 获取总数以支持分页
	total, err := service.CountProductStorage(ctx, reqDto)
	if err != nil {
		return nil, fmt.Errorf("查询存酒记录总数失败: %v", err)
	}

	// 使用统一的ProductStoragePageVO结构返回结果
	page := &vo.ProductStoragePageVO{
		PageNum:  reqDto.PageNum,
		PageSize: reqDto.PageSize,
		Total:    total,
		Data:     voList,
	}

	return page, nil
}

// GetProductStorageDetailWithHistory 获取存酒详情和操作历史
func (service *ProductStorageService) GetProductStorageDetailWithHistory(ctx *gin.Context, id string) (*vo.ProductStorageDetailWithHistoryVO, error) {
	if id == "" {
		return nil, fmt.Errorf("id不能为空")
	}

	// 获取存酒基本信息
	storage, err := service.FindProductStorageById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询存酒记录失败: %v", err)
	}

	if storage == nil {
		return nil, fmt.Errorf("商品存储记录不存在")
	}

	// 获取关联的存酒订单信息（用于获取OfflineOnly字段）
	var order *po.ProductStorageOrder
	if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
		orderService := NewProductStorageOrderService()
		order, err = orderService.GetOrderByOrderNo(ctx, *storage.ParentOrderNo)
		if err != nil {
			// 记录错误但不中断流程
			fmt.Printf("查询存酒订单失败: %v\n", err)
		}
	}

	// 转换基本信息为VO，包含OfflineOnly信息
	baseInfo := transfer.ProductStorageTransfer{}.PoToVoWithOrder(*storage, order)

	// 创建结果对象
	result := &vo.ProductStorageDetailWithHistoryVO{
		ProductStorageVO: baseInfo,
		OperationHistory: []vo.ProductStorageOperationVO{},
	}

	// 查询操作历史记录
	operationService := NewProductStorageOperationLogService()
	logs, err := operationService.FindByStorageId(ctx, id)

	// 使用lo库确保logs不为nil
	safeLogs := lo.If(err != nil, []*po.ProductStorageOperationLog{}).Else(logs)

	if err != nil {
		// 操作历史查询出错不影响基本信息返回，仅记录错误
		fmt.Printf("查询存酒操作历史失败: %v\n", err)
	}

	if len(safeLogs) > 0 {
		// 使用lo库转换操作历史记录
		result.OperationHistory = lo.Map(safeLogs, func(log *po.ProductStorageOperationLog, index int) vo.ProductStorageOperationVO {
			opVo := vo.ProductStorageOperationVO{
				Type:         *log.OperationType,
				TypeName:     *log.OperationName,
				Time:         *log.OperationTime,
				Quantity:     *log.Quantity,
				OperatorId:   *log.OperatorId,
				OperatorName: *log.OperatorName,
				BalanceQty:   *log.BalanceQty,
				Remark:       *log.Remark,
			}

			// 添加包厢信息（仅对取酒操作）
			opVo.RoomName = lo.FromPtrOr(log.RoomName, "")
			opVo.DeliveryRoomName = lo.FromPtrOr(log.DeliveryRoomName, "")

			return opVo
		})
	} else {
		// 如果没有查询到操作历史，至少添加一条存入记录
		operationTime := baseInfo.StorageTime
		if baseInfo.LastOperationTime > 0 {
			operationTime = baseInfo.LastOperationTime
		} else if operationTime == 0 {
			operationTime = time.Now().Unix()
		}

		result.OperationHistory = []vo.ProductStorageOperationVO{{
			Type:         "storage",
			TypeName:     "存酒",
			Time:         operationTime,
			Quantity:     baseInfo.Quantity,
			OperatorId:   baseInfo.OperatorId,
			OperatorName: baseInfo.OperatorName,
			BalanceQty:   baseInfo.Quantity,
			Remark:       baseInfo.Remark,
		}}
	}

	return result, nil
}

// GetStorageStatisticsWithPagination 获取存酒统计信息（包含分页处理）
func (service *ProductStorageService) GetStorageStatisticsWithPagination(ctx *gin.Context, reqDto *req.GetStorageStatisticsReqDto) (interface{}, error) {
	// 验证必要参数
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("场馆ID不能为空")
	}

	// 设置默认的统计类型为按商品统计
	if reqDto.StatType == "" {
		reqDto.StatType = "byProduct"
	}

	// 设置默认的分页参数
	if reqDto.PageNum <= 0 {
		reqDto.PageNum = 1
	}
	if reqDto.PageSize <= 0 {
		reqDto.PageSize = 200
	}

	// 根据统计类型调用不同的统计方法
	if reqDto.StatType == "byProduct" {
		// 按商品维度统计
		result, err := service.GetProductStorageStatistics(ctx, reqDto)
		if err != nil {
			return nil, fmt.Errorf("获取商品存酒统计失败: %v", err)
		}

		// 创建包含汇总数据的分页结果
		pageWithSummary := &vo.PageWithSummaryVO[[]vo.ProductStorageItemStatVO]{
			PageNum:  reqDto.PageNum,
			PageSize: reqDto.PageSize,
			Total:    result.Total,
			Data:     result.List,
			Summary: vo.StorageSummaryVO{
				TotalStorageQuantity:   result.TotalStorageQuantity,
				TotalCurrentQuantity:   result.TotalCurrentQuantity,
				TotalRemainingQuantity: result.TotalRemainingQuantity,
			},
		}

		return pageWithSummary, nil
	} else {
		// 汇总统计
		statistics, err := service.GetStorageStatistics(ctx, reqDto)
		if err != nil {
			return nil, fmt.Errorf("获取存酒汇总统计失败: %v", err)
		}
		return statistics, nil
	}
}

// OperateProductStorageWithValidation 存酒记录操作（包含参数校验和业务逻辑）
func (service *ProductStorageService) OperateProductStorageWithValidation(ctx *gin.Context, reqDto *req.OperateProductStorageReqDto) (interface{}, error) {
	// 检查必要参数
	if reqDto.Id == "" {
		return nil, fmt.Errorf("id不能为空")
	}

	if reqDto.OperationType == "" {
		return nil, fmt.Errorf("操作类型不能为空")
	}

	// 特殊处理添加商品操作
	if reqDto.OperationType == "addItems" {
		if reqDto.OrderNo == "" {
			return nil, fmt.Errorf("存酒单号不能为空")
		}
		if len(reqDto.Items) == 0 {
			return nil, fmt.Errorf("存酒明细不能为空")
		}

		// 向已有存酒单添加商品
		orderService := NewProductStorageOrderService()
		result, err := orderService.AddItemsToOrder(ctx, reqDto.OrderNo, reqDto.Items, reqDto.OperatorId, reqDto.OperatorName, reqDto.Remark)
		if err != nil {
			return nil, fmt.Errorf("添加商品到存酒单失败: %v", err)
		}
		return result, nil
	}

	// 获取存酒记录
	storage, err := service.FindProductStorageById(ctx, reqDto.Id)
	if err != nil {
		return nil, fmt.Errorf("查询存酒记录失败: %v", err)
	}

	if storage == nil {
		return nil, fmt.Errorf("商品存储记录不存在")
	}

	// 使用ProductStorageOperationService处理操作
	operationManager := NewProductStorageOperationService()

	// 特殊处理续存操作，返回包含printBusinessId的结构
	if reqDto.OperationType == _const.OperationTypeExtend {
		printRecordInfo, err := operationManager.ExtendProductStorage(ctx, storage, reqDto)
		if err != nil {
			return nil, fmt.Errorf("续存操作失败: %v", err)
		}

		// 构建续存操作结果
		extendResult := &vo.ProductStorageExtendResultVO{
			ProductStorageVO: transfer.ProductStorageTransfer{}.PoToVo(*storage),
		}

		// 如果成功创建了打印记录，添加printBusinessId
		if printRecordInfo != nil {
			extendResult.PrintBusinessId = printRecordInfo.BusinessId
		}

		return extendResult, nil
	}

	// 其他操作类型的处理
	err = operationManager.OperateProductStorage(ctx, storage, reqDto)
	if err != nil {
		return nil, fmt.Errorf("操作存酒记录失败: %v", err)
	}

	// 返回更新后的存酒记录
	return transfer.ProductStorageTransfer{}.PoToVo(*storage), nil
}
