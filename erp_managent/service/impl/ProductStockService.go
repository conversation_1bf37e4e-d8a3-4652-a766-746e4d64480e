package impl

import (
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type ProductStockService struct {
}

var (
	productStockService  = &ProductStockService{}
	productStockTransfer = transfer.ProductStockTransfer{}
)

func (service *ProductStockService) CreateProductStock(logCtx *gin.Context, stock *po.ProductStock) error {
	return Save(stock)
}

func (service *ProductStockService) CreateProductStockWithTx(logCtx *gin.Context, stock *po.ProductStock, tx *gorm.DB) error {
	return SaveWithTx(stock, tx)
}

func (service *ProductStockService) UpdateProductStock(logCtx *gin.Context, stock *po.ProductStock) error {
	return Update(stock)
}

func (service *ProductStockService) UpdateProductStockPartial(logCtx *gin.Context, stock *po.ProductStock) error {
	return UpdateNotNull(stock)
}

// UpdateProductStockWithTx 在事务中更新商品库存
func (service *ProductStockService) UpdateProductStockWithTx(logCtx *gin.Context, stock *po.ProductStock, tx *gorm.DB) error {
	return UpdateNotNullWithTx(stock, tx)
}

// BatchUpdateProductStocks 批量更新商品库存
func (service *ProductStockService) BatchUpdateProductStocks(logCtx *gin.Context, stocks []po.ProductStock) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		for _, stock := range stocks {
			if err := service.UpdateProductStockWithTx(logCtx, &stock, tx); err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchUpdateProductStocksWithTx 在事务中批量更新商品库存
func (service *ProductStockService) BatchUpdateProductStocksWithTx(logCtx *gin.Context, stocks []po.ProductStock, tx *gorm.DB) error {
	for _, stock := range stocks {
		if err := service.UpdateProductStockWithTx(logCtx, &stock, tx); err != nil {
			return err
		}
	}
	return nil
}

func (service *ProductStockService) DeleteProductStock(logCtx *gin.Context, id string) error {
	return Delete(po.ProductStock{Id: &id})
}

func (service *ProductStockService) FindProductStockById(logCtx *gin.Context, id string) (stock *po.ProductStock, err error) {
	stock = &po.ProductStock{}
	err = model.DBMaster.Self.Where("id=?", id).First(stock).Error
	return
}

func (service *ProductStockService) FindProductStockByCondition(logCtx *gin.Context, productId, venueId string) (stock *po.ProductStock, err error) {
	stock = &po.ProductStock{}
	err = model.DBSlave.Self.Where("product_id = ? AND venue_id = ?", productId, venueId).First(stock).Error
	return
}

func (service *ProductStockService) FindAllProductStock(logCtx *gin.Context, reqDto *req.QueryProductStockReqDto) (list *[]po.ProductStock, err error) {
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.ProductId != nil && *reqDto.ProductId != "" {
		db = db.Where("product_id=?", *reqDto.ProductId)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}

	db = db.Order("ctime desc")
	list = &[]po.ProductStock{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *ProductStockService) FindAllProductStockWithPagination(logCtx *gin.Context, reqDto *req.QueryProductStockReqDto) (list *[]po.ProductStock, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.ProductStock{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.ProductId != nil && *reqDto.ProductId != "" {
		db = db.Where("product_id=?", *reqDto.ProductId)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.ProductStock{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// extend

// SyncProductStock 同步产品库存（推荐使用）
func (service *ProductStockService) SyncProductStock(ctx *gin.Context, newProductStockList []po.ProductStock, productStockChangeVOs []vo.ProductStockChangeVO) error {
	tx := model.DBMaster.Self.Begin()
	for _, productStock := range newProductStockList {
		if err := productStockService.CreateProductStockWithTx(ctx, &productStock, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, productStockChangeVO := range productStockChangeVOs {
		sign := "+"
		if productStockChangeVO.Count < 0 {
			sign = "-"
			productStockChangeVO.Count = -productStockChangeVO.Count
		}
		utime := util.TimeNowUnixInt64()
		err := tx.Exec("UPDATE product_stock SET stock = stock "+sign+" ?, utime=? WHERE id = ?", productStockChangeVO.Count, utime, productStockChangeVO.Id).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

// SyncProductStock 同步产品库存（推荐使用）
func (service *ProductStockService) SyncProductStockV2(ctx *gin.Context, newProductStockList []po.ProductStock, productStockChangeVOs []vo.ProductStockChangeVO, shareInfo vo.ProductStockChangeRecordVOShare) error {
	if len(newProductStockList) == 0 && len(productStockChangeVOs) == 0 {
		return nil
	}
	tx := model.DBMaster.Self.Begin()
	for _, productStock := range newProductStockList {
		if err := productStockService.CreateProductStockWithTx(ctx, &productStock, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, productStockChangeVO := range productStockChangeVOs {
		sign := "+"
		if productStockChangeVO.Count < 0 {
			sign = "-"
			productStockChangeVO.Count = -productStockChangeVO.Count
		}
		utime := util.TimeNowUnixInt64()
		err := tx.Exec("UPDATE product_stock SET stock = stock "+sign+" ?, utime=? WHERE id = ?", productStockChangeVO.Count, utime, productStockChangeVO.Id).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	service.RecordProductStockChangeRecord(ctx, newProductStockList, productStockChangeVOs, &shareInfo)
	return nil
}

func (service *ProductStockService) RecordProductStockChangeRecord(ctx *gin.Context, newProductStockList []po.ProductStock, productStockChangeVOs []vo.ProductStockChangeVO, productStockChangeRecordVOShare *vo.ProductStockChangeRecordVOShare) error {
	defer func() {
		if err := recover(); err != nil {
			logrus.Errorf("RecordProductStockChangeRecord error: %v", err)
		}
	}()
	dayStartTime, _, err := util.GetCurrentBusinessTimeRange(productStockChangeRecordVOShare.BusinessStartHour)
	if err != nil {
		dayStartTime, _, _ = util.GetCurrentBusinessTimeRange("07:00")
	}
	businessDayStr := util.GetYmd(dayStartTime)

	sourceName := _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_NAME_MAP[productStockChangeRecordVOShare.Source]
	productStockChangeRecord := &po.ProductStockChangeRecord{
		VenueId:          &productStockChangeRecordVOShare.VenueId,
		VenueName:        &productStockChangeRecordVOShare.VenueName,
		BusinessDay:      &dayStartTime,
		BusinessDayStr:   &businessDayStr,
		BusinessStartHour: &productStockChangeRecordVOShare.BusinessStartHour,
		BusinessEndHour:   &productStockChangeRecordVOShare.BusinessEndHour,
		ActionType:       &productStockChangeRecordVOShare.ActionType,
		Source:           &productStockChangeRecordVOShare.Source,
		SourceName:       &sourceName,
		SessionId:        &productStockChangeRecordVOShare.SessionId,
		InventoryId:      &productStockChangeRecordVOShare.InventoryId,
		ProductStorageId: &productStockChangeRecordVOShare.ProductStorageId,
		OperatorId:       &productStockChangeRecordVOShare.OperatorId,
		OperatorName:     &productStockChangeRecordVOShare.OperatorName,
		Info:             &productStockChangeRecordVOShare.Info,
	}
	productStockIds := map[string]int{}
	for _, productStock := range newProductStockList {
		productStockIds[*productStock.Id] += *productStock.Stock
	}
	for _, productStockChangeVO := range productStockChangeVOs {
		productStockIds[productStockChangeVO.Id] += int(productStockChangeVO.Count)
	}
	productStockIdsList := make([]string, 0)
	for productId := range productStockIds {
		productStockIdsList = append(productStockIdsList, productId)
	}
	productStockList, err := service.FindProductStocksByIdsBatchPK(ctx, productStockIdsList)
	if err != nil {
		return err
	}
	productIdsList := make([]string, 0)
	for _, productStock := range *productStockList {
		productIdsList = append(productIdsList, *productStock.ProductId)
	}
	products, err := productService.FindProductsByIds(ctx, *productStockChangeRecord.VenueId, productIdsList)
	if err != nil {
		return err
	}
	productIdToProductVO := make(map[string]vo.ProductVO)
	for _, product := range products {
		pvo := productTransfer.PoToVo(product)
		productIdToProductVO[*product.Id] = pvo
	}
	productStockChangeRecordList := make([]po.ProductStockChangeRecord, 0)
	for _, productStock := range *productStockList {
		actionCount, ok := productStockIds[*productStock.Id]
		cloneVO := util.DeepClone(*productStockChangeRecord)
		cloneVO.ProductStockId = productStock.Id
		if ok {
			cloneVO.Quantity = &actionCount
		}
		productVO, ok := productIdToProductVO[*productStock.ProductId]
		cloneVO.ProductId = productStock.ProductId
		if ok {
			cloneVO.ProductName = &productVO.Name
			cloneVO.ProductCategoryId = &productVO.Category
			cloneVO.ProductCategoryName = util.Ptr("")
		}
		cloneVO.LeftStock = productStock.Stock
		switch productStockChangeRecordVOShare.Source {
		case _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_PRODUCT_STORAGE:
			cloneVO.DisplayNo = &productStockChangeRecordVOShare.ProductStorageId
		case _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_ORDER, _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_REFUND:
			cloneVO.DisplayNo = &productStockChangeRecordVOShare.SessionId
		case _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_IN, _const.V2_PRODUCT_STOCK_CHANGE_RECORD_SOURCE_INVENTORY_OUT:
			cloneVO.DisplayNo = &productStockChangeRecordVOShare.InventoryId
		default:
			cloneVO.DisplayNo = util.Ptr("")
		}
		productStockChangeRecordList = append(productStockChangeRecordList, cloneVO)
	}
	err = productStockChangeRecordService.CreateProductStockChangeRecordBatch(ctx, productStockChangeRecordList)
	if err != nil {
		return err
	}
	return nil
}

func (service *ProductStockService) FindProductStockByProductIds(ctx *gin.Context, venueId string, productIds []string) (productStockList []po.ProductStock, err error) {
	productStockList = []po.ProductStock{}
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	db = db.Where("product_id IN (?)", productIds)
	db = db.Where("venue_id = ?", venueId)
	err = db.Find(&productStockList).Error
	return
}

// FindProductStockByProductIdsWithPagination 根据商品ID列表分页查询库存
func (service *ProductStockService) FindProductStockByProductIdsWithPagination(ctx *gin.Context, venueId string, productIds []string, pageNum, pageSize int) (productStockList []po.ProductStock, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	db = db.Where("product_id IN (?)", productIds)
	db = db.Where("venue_id = ?", venueId)

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if total <= 0 {
		productStockList = []po.ProductStock{}
		return
	}

	// 分页+排序
	db = db.Offset((pageNum - 1) * pageSize).Limit(pageSize)
	db = db.Order("ctime desc")
	err = db.Find(&productStockList).Error
	return
}

func (service *ProductStockService) ConvertToProductStock(productStockVO vo.ProductStockVO) po.ProductStock {
	return productStockTransfer.VoToPo(productStockVO)
}

func (service *ProductStockService) ConvertToProductStockVO(productStock po.ProductStock) vo.ProductStockVO {
	return productStockTransfer.PoToVo(productStock)
}

func (service *ProductStockService) FindProductStocksByVenueId(logCtx *gin.Context, venueId string) (list *[]po.ProductStock, err error) {
	list = &[]po.ProductStock{}
	err = model.DBSlave.Self.Where("venue_id = ?", venueId).Find(list).Error
	return
}

func (service *ProductStockService) FindProductStocksByIds(logCtx *gin.Context, venueId string, productIds []string) (list *[]po.ProductStock, err error) {
	list = &[]po.ProductStock{}
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	db = db.Where("venue_id = ?", venueId)
	db = db.Where("product_id IN (?)", productIds)
	err = db.Find(list).Error
	return
}

func (service *ProductStockService) FindProductStocksByIdsBatch(logCtx *gin.Context, venueId string, productIds []string) (list *[]po.ProductStock, err error) {
	productIdsBatch := util.SplitList(productIds, 100)
	list = &[]po.ProductStock{}
	for _, productIds := range productIdsBatch {
		productStocks, err := service.FindProductStocksByIds(logCtx, venueId, productIds)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		if err == gorm.ErrRecordNotFound {
			continue
		}
		*list = append(*list, *productStocks...)
	}
	return
}

func (service *ProductStockService) FindProductStocksByIdsBatchPK(logCtx *gin.Context, productIds []string) (list *[]po.ProductStock, err error) {
	productIdsBatch := util.SplitList(productIds, 100)
	list = &[]po.ProductStock{}
	for _, productIds := range productIdsBatch {
		productStocks, err := service.FindProductStocksByIdsCachePK(logCtx, productIds)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
		if err == gorm.ErrRecordNotFound {
			continue
		}
		*list = append(*list, *productStocks...)
	}
	return
}

func (service *ProductStockService) FindProductStocksByIdsCachePK(logCtx *gin.Context, productIds []string) (list *[]po.ProductStock, err error) {
	list = &[]po.ProductStock{}
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	db = db.Where("id IN (?)", productIds)
	err = db.Find(list).Error
	return
}
