package impl

import (
	"fmt"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InventoryCheckRecordService struct {
}

// CreateInventoryCheckRecord 创建盘点记录
func (service *InventoryCheckRecordService) CreateInventoryCheckRecord(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return Save(record)
}

// CreateInventoryCheckRecordWithTx 创建盘点记录（带事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordWithTx(logCtx *gin.Context, record *po.InventoryCheckRecord, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

// UpdateInventoryCheckRecord 更新盘点记录
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecord(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return Update(record)
}

// UpdateInventoryCheckRecordPartial 部分更新盘点记录
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordPartial(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return UpdateNotNull(record)
}

// UpdateInventoryCheckRecordPartialWithTx 部分更新盘点记录（带事务）
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordPartialWithTx(logCtx *gin.Context, record *po.InventoryCheckRecord, tx *gorm.DB) error {
	return UpdateNotNullWithTx(record, tx)
}

// DeleteInventoryCheckRecord 删除盘点记录
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecord(logCtx *gin.Context, id string) error {
	return Delete(po.InventoryCheckRecord{Id: &id})
}

// DeleteInventoryCheckRecordWithTx 删除盘点记录（带事务）
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordWithTx(logCtx *gin.Context, id string, tx *gorm.DB) error {
	return DeleteWithTx(po.InventoryCheckRecord{Id: &id}, tx)
}

// FindInventoryCheckRecordById 根据ID查询盘点记录
func (service *InventoryCheckRecordService) FindInventoryCheckRecordById(logCtx *gin.Context, id string) (record *po.InventoryCheckRecord, err error) {
	record = &po.InventoryCheckRecord{}
	err = model.DBMaster.Self.Where("id=? AND state=0", id).First(record).Error
	return
}

// FindAllInventoryCheckRecord 查询所有盘点记录
func (service *InventoryCheckRecordService) FindAllInventoryCheckRecord(logCtx *gin.Context, reqDto *req.QueryInventoryCheckRecordReqDto) (list *[]po.InventoryCheckRecord, err error) {
	db := model.DBSlave.Self.Model(&po.InventoryCheckRecord{}).Where("state=0")

	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.SearchKey != nil && *reqDto.SearchKey != "" {
		searchPattern := "%" + *reqDto.SearchKey + "%"
		db = db.Where("record_number LIKE ? OR handler LIKE ? OR operator LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	db = db.Order("time desc")
	list = &[]po.InventoryCheckRecord{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

// FindAllInventoryCheckRecordWithPagination 分页查询盘点记录
func (service *InventoryCheckRecordService) FindAllInventoryCheckRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryInventoryCheckRecordReqDto) (list *[]po.InventoryCheckRecord, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.InventoryCheckRecord{}).Where("state=0")

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.SearchKey != nil && *reqDto.SearchKey != "" {
		searchPattern := "%" + *reqDto.SearchKey + "%"
		db = db.Where("record_number LIKE ? OR handler LIKE ? OR operator LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.InventoryCheckRecord{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("time desc")
	err = db.Find(list).Error
	return
}

// CreateInventoryCheckRecordItem 创建盘点记录明细
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordItem(logCtx *gin.Context, item *po.InventoryCheckRecordItem) error {
	return Save(item)
}

// CreateInventoryCheckRecordItemWithTx 创建盘点记录明细（带事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordItemWithTx(logCtx *gin.Context, item *po.InventoryCheckRecordItem, tx *gorm.DB) error {
	return SaveWithTx(item, tx)
}

// FindInventoryCheckRecordItemsByRecordId 根据盘点记录ID查询明细
func (service *InventoryCheckRecordService) FindInventoryCheckRecordItemsByRecordId(logCtx *gin.Context, recordId string) (items *[]po.InventoryCheckRecordItem, err error) {
	items = &[]po.InventoryCheckRecordItem{}
	err = model.DBSlave.Self.Where("check_record_id=? AND state=0", recordId).Find(items).Error
	return
}

// DeleteInventoryCheckRecordItemsByRecordId 删除盘点记录的所有明细
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordItemsByRecordId(logCtx *gin.Context, recordId string) error {
	return model.DBMaster.Self.Model(&po.InventoryCheckRecordItem{}).
		Where("check_record_id=?", recordId).
		Update("state", 1).Error
}

// DeleteInventoryCheckRecordItemsByRecordIdWithTx 删除盘点记录的所有明细（带事务）
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordItemsByRecordIdWithTx(logCtx *gin.Context, recordId string, tx *gorm.DB) error {
	return tx.Model(&po.InventoryCheckRecordItem{}).
		Where("check_record_id=?", recordId).
		Update("state", 1).Error
}

// GenerateRecordNumber 生成盘点单号
func (service *InventoryCheckRecordService) GenerateRecordNumber(logCtx *gin.Context, venueId string) (string, error) {
	// 格式：CHECK + 年月日 + 3位序号
	// 例如：CHECK20240120001
	now := time.Now()
	dateStr := now.Format("20060102")

	// 查询当天的盘点记录数量
	var count int64
	err := model.DBMaster.Self.Model(&po.InventoryCheckRecord{}).
		Where("venue_id=? AND DATE(FROM_UNIXTIME(time))=? AND state=0", venueId, now.Format("2006-01-02")).
		Count(&count).Error
	if err != nil {
		return "", err
	}

	// 生成序号（从1开始）
	sequence := count + 1

	return fmt.Sprintf("CHECK%s%03d", dateStr, sequence), nil
}

// CreateInventoryCheckRecordWithItems 创建盘点记录及其明细（事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordWithItems(logCtx *gin.Context, record *po.InventoryCheckRecord, items []po.InventoryCheckRecordItem) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 创建主记录
		if err := SaveWithTx(record, tx); err != nil {
			return fmt.Errorf("创建盘点记录失败: %w", err)
		}

		// 2. 创建明细记录
		for i := range items {
			items[i].CheckRecordId = record.Id
			if err := SaveWithTx(&items[i], tx); err != nil {
				return fmt.Errorf("创建盘点明细失败: %w", err)
			}
		}

		return nil
	})
}

// UpdateInventoryCheckRecordWithItems 更新盘点记录及其明细（事务）
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordWithItems(logCtx *gin.Context, record *po.InventoryCheckRecord, items []po.InventoryCheckRecordItem) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 更新主记录
		if err := UpdateNotNullWithTx(record, tx); err != nil {
			return fmt.Errorf("更新盘点记录失败: %w", err)
		}

		// 2. 删除原有明细
		if err := service.DeleteInventoryCheckRecordItemsByRecordIdWithTx(logCtx, *record.Id, tx); err != nil {
			return fmt.Errorf("删除原有盘点明细失败: %w", err)
		}

		// 3. 创建新明细
		for i := range items {
			items[i].CheckRecordId = record.Id
			if err := SaveWithTx(&items[i], tx); err != nil {
				return fmt.Errorf("创建新盘点明细失败: %w", err)
			}
		}

		return nil
	})
}

// CalculateCheckAdjustment 计算盘点调整增量（用于库存分析）
func (service *InventoryCheckRecordService) CalculateCheckAdjustment(logCtx *gin.Context, venueId, productId string, afterTime int64) (adjustment int, err error) {
	// 查询指定时间后的盘点记录明细
	var items []po.InventoryCheckRecordItem
	err = model.DBSlave.Self.Table("inventory_check_record_item i").
		Joins("JOIN inventory_check_record r ON i.check_record_id = r.id").
		Where("r.venue_id = ? AND r.time > ? AND i.product_id = ? AND i.state = 0 AND r.state = 0",
			venueId, afterTime, productId).
		Select("i.profit_loss_quantity").
		Find(&items).Error

	if err != nil {
		return 0, err
	}

	adjustment = 0
	for _, item := range items {
		if item.ProfitLossQuantity != nil {
			adjustment += *item.ProfitLossQuantity
		}
	}

	return adjustment, nil
}
