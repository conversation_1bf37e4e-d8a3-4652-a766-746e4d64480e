package impl

import (
	"fmt"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InventoryCheckRecordService struct {
}

// CreateInventoryCheckRecord 创建盘点记录
func (service *InventoryCheckRecordService) CreateInventoryCheckRecord(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return Save(record)
}

// CreateInventoryCheckRecordWithTx 创建盘点记录（带事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordWithTx(logCtx *gin.Context, record *po.InventoryCheckRecord, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

// UpdateInventoryCheckRecord 更新盘点记录
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecord(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return Update(record)
}

// UpdateInventoryCheckRecordPartial 部分更新盘点记录
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordPartial(logCtx *gin.Context, record *po.InventoryCheckRecord) error {
	return UpdateNotNull(record)
}

// UpdateInventoryCheckRecordPartialWithTx 部分更新盘点记录（带事务）
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordPartialWithTx(logCtx *gin.Context, record *po.InventoryCheckRecord, tx *gorm.DB) error {
	return UpdateNotNullWithTx(record, tx)
}

// DeleteInventoryCheckRecord 删除盘点记录
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecord(logCtx *gin.Context, id string) error {
	return Delete(po.InventoryCheckRecord{Id: &id})
}

// DeleteInventoryCheckRecordWithTx 删除盘点记录（带事务）
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordWithTx(logCtx *gin.Context, id string, tx *gorm.DB) error {
	return DeleteWithTx(po.InventoryCheckRecord{Id: &id}, tx)
}

// FindInventoryCheckRecordById 根据ID查询盘点记录
func (service *InventoryCheckRecordService) FindInventoryCheckRecordById(logCtx *gin.Context, id string) (record *po.InventoryCheckRecord, err error) {
	record = &po.InventoryCheckRecord{}
	err = model.DBMaster.Self.Where("id=? AND state=0", id).First(record).Error
	return
}

// FindInventoryCheckRecordByRecordNumber 根据盘点单号查询盘点记录
func (service *InventoryCheckRecordService) FindInventoryCheckRecordByRecordNumber(logCtx *gin.Context, recordNumber string) (record *po.InventoryCheckRecord, err error) {
	record = &po.InventoryCheckRecord{}
	err = model.DBMaster.Self.Where("record_number=? AND state=0", recordNumber).First(record).Error
	return
}

// FindInventoryCheckRecordsByVenueId 根据门店ID查询盘点记录列表
func (service *InventoryCheckRecordService) FindInventoryCheckRecordsByVenueId(logCtx *gin.Context, reqDto *req.QueryInventoryCheckRecordReqDto) (list *[]po.InventoryCheckRecord, total int64, err error) {
	list = &[]po.InventoryCheckRecord{}

	query := model.DBMaster.Self.Model(&po.InventoryCheckRecord{}).Where("venue_id=? AND state=0", reqDto.VenueId)

	// 搜索条件
	if reqDto.SearchKey != nil && *reqDto.SearchKey != "" {
		searchPattern := "%" + *reqDto.SearchKey + "%"
		query = query.Where("record_number LIKE ? OR handler LIKE ? OR operator LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 查询总数
	err = query.Count(&total).Error
	if err != nil {
		return
	}

	// 分页查询
	offset := (*reqDto.PageNum - 1) * *reqDto.PageSize
	err = query.Order("time DESC").Offset(offset).Limit(*reqDto.PageSize).Find(list).Error

	return
}

// CreateInventoryCheckRecordItem 创建盘点记录明细
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordItem(logCtx *gin.Context, item *po.InventoryCheckRecordItem) error {
	return Save(item)
}

// CreateInventoryCheckRecordItemWithTx 创建盘点记录明细（带事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordItemWithTx(logCtx *gin.Context, item *po.InventoryCheckRecordItem, tx *gorm.DB) error {
	return SaveWithTx(item, tx)
}

// CreateInventoryCheckRecordItemsBatch 批量创建盘点记录明细
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordItemsBatch(logCtx *gin.Context, items []po.InventoryCheckRecordItem) error {
	if len(items) == 0 {
		return nil
	}

	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		for i := range items {
			if err := SaveWithTx(&items[i], tx); err != nil {
				return err
			}
		}
		return nil
	})
}

// FindInventoryCheckRecordItemsByRecordId 根据盘点记录ID查询明细
func (service *InventoryCheckRecordService) FindInventoryCheckRecordItemsByRecordId(logCtx *gin.Context, recordId string) (items *[]po.InventoryCheckRecordItem, err error) {
	items = &[]po.InventoryCheckRecordItem{}
	err = model.DBMaster.Self.Where("check_record_id=? AND state=0", recordId).Find(items).Error
	return
}

// DeleteInventoryCheckRecordItemsByRecordId 删除盘点记录的所有明细
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordItemsByRecordId(logCtx *gin.Context, recordId string) error {
	return model.DBMaster.Self.Model(&po.InventoryCheckRecordItem{}).
		Where("check_record_id=?", recordId).
		Update("state", 1).Error
}

// DeleteInventoryCheckRecordItemsByRecordIdWithTx 删除盘点记录的所有明细（带事务）
func (service *InventoryCheckRecordService) DeleteInventoryCheckRecordItemsByRecordIdWithTx(logCtx *gin.Context, recordId string, tx *gorm.DB) error {
	return tx.Model(&po.InventoryCheckRecordItem{}).
		Where("check_record_id=?", recordId).
		Update("state", 1).Error
}

// GenerateRecordNumber 生成盘点单号
func (service *InventoryCheckRecordService) GenerateRecordNumber(logCtx *gin.Context, venueId string) (string, error) {
	// 格式：CHECK + 年月日 + 3位序号
	// 例如：CHECK20240120001
	now := time.Now()
	dateStr := now.Format("20060102")

	// 查询当天的盘点记录数量
	var count int64
	err := model.DBMaster.Self.Model(&po.InventoryCheckRecord{}).
		Where("venue_id=? AND DATE(FROM_UNIXTIME(time))=? AND state=0", venueId, now.Format("2006-01-02")).
		Count(&count).Error
	if err != nil {
		return "", err
	}

	// 生成序号（从1开始）
	sequence := count + 1

	return fmt.Sprintf("CHECK%s%03d", dateStr, sequence), nil
}

// CreateInventoryCheckRecordWithItems 创建盘点记录及其明细（事务）
func (service *InventoryCheckRecordService) CreateInventoryCheckRecordWithItems(logCtx *gin.Context, record *po.InventoryCheckRecord, items []po.InventoryCheckRecordItem) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 创建主记录
		if err := SaveWithTx(record, tx); err != nil {
			return fmt.Errorf("创建盘点记录失败: %w", err)
		}

		// 2. 创建明细记录
		for i := range items {
			items[i].CheckRecordId = record.Id
			if err := SaveWithTx(&items[i], tx); err != nil {
				return fmt.Errorf("创建盘点明细失败: %w", err)
			}
		}

		return nil
	})
}

// UpdateInventoryCheckRecordWithItems 更新盘点记录及其明细（事务）
func (service *InventoryCheckRecordService) UpdateInventoryCheckRecordWithItems(logCtx *gin.Context, record *po.InventoryCheckRecord, items []po.InventoryCheckRecordItem) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 更新主记录
		if err := UpdateNotNullWithTx(record, tx); err != nil {
			return fmt.Errorf("更新盘点记录失败: %w", err)
		}

		// 2. 删除原有明细
		if err := service.DeleteInventoryCheckRecordItemsByRecordIdWithTx(logCtx, *record.Id, tx); err != nil {
			return fmt.Errorf("删除原有盘点明细失败: %w", err)
		}

		// 3. 创建新明细
		for i := range items {
			items[i].CheckRecordId = record.Id
			if err := SaveWithTx(&items[i], tx); err != nil {
				return fmt.Errorf("创建新盘点明细失败: %w", err)
			}
		}

		return nil
	})
}

// FindInventoryCheckRecordsAfterTime 查询指定时间后的盘点记录（用于库存计算）
func (service *InventoryCheckRecordService) FindInventoryCheckRecordsAfterTime(logCtx *gin.Context, venueId string, afterTime int64) (records *[]po.InventoryCheckRecord, err error) {
	records = &[]po.InventoryCheckRecord{}
	err = model.DBMaster.Self.Where("venue_id=? AND time>? AND state=0", venueId, afterTime).
		Order("time ASC").Find(records).Error
	return
}

// CountInventoryCheckRecordsByVenueId 统计门店的盘点记录数量
func (service *InventoryCheckRecordService) CountInventoryCheckRecordsByVenueId(logCtx *gin.Context, venueId string) (count int64, err error) {
	err = model.DBMaster.Self.Model(&po.InventoryCheckRecord{}).
		Where("venue_id=? AND state=0", venueId).Count(&count).Error
	return
}

// FindLatestInventoryCheckRecordByVenueId 查询门店最新的盘点记录
func (service *InventoryCheckRecordService) FindLatestInventoryCheckRecordByVenueId(logCtx *gin.Context, venueId string) (record *po.InventoryCheckRecord, err error) {
	record = &po.InventoryCheckRecord{}
	err = model.DBMaster.Self.Where("venue_id=? AND state=0", venueId).
		Order("time DESC").First(record).Error
	return
}

// CalculateCheckAdjustment 计算盘点调整增量（用于库存分析）
func (service *InventoryCheckRecordService) CalculateCheckAdjustment(logCtx *gin.Context, venueId, productId string, afterTime int64) (adjustment int, err error) {
	// 获取指定时间后的盘点记录
	records, err := service.FindInventoryCheckRecordsAfterTime(logCtx, venueId, afterTime)
	if err != nil {
		return 0, err
	}

	adjustment = 0
	for _, record := range *records {
		// 获取该记录的明细
		items, err := service.FindInventoryCheckRecordItemsByRecordId(logCtx, *record.Id)
		if err != nil {
			continue
		}

		// 累计该商品的盈亏数量
		for _, item := range *items {
			if item.ProductId != nil && *item.ProductId == productId && item.ProfitLossQuantity != nil {
				adjustment += *item.ProfitLossQuantity
			}
		}
	}

	return adjustment, nil
}
