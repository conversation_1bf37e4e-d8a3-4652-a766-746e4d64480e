package impl

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// ProductStorageOrderService 存酒单服务
type ProductStorageOrderService struct {
}

// 初始化DAO
func NewProductStorageOrderService() *ProductStorageOrderService {
	return &ProductStorageOrderService{}
}

// ProcessUnifiedProductStorageRequest 处理统一存酒请求（包含参数校验、转换和业务逻辑）
func (service *ProductStorageOrderService) ProcessUnifiedProductStorageRequest(logCtx *gin.Context, reqDto *req.UnifiedProductStorageReqDto) (*vo.ProductStorageAddResultVO, error) {
	// 参数校验
	if reqDto == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("请选择场所")
	}

	if len(reqDto.Items) == 0 {
		return nil, fmt.Errorf("请选择商品")
	}

	// 使用lo库校验所有商品项
	if hasInvalidItem := lo.ContainsBy(reqDto.Items, func(item req.UnifiedStorageItemDto) bool {
		return item.ProductId == ""
	}); hasInvalidItem {
		return nil, fmt.Errorf("请选择商品")
	}

	// 使用lo库校验商品数量
	if invalidItem, found := lo.Find(reqDto.Items, func(item req.UnifiedStorageItemDto) bool {
		return item.Quantity <= 0
	}); found {
		return nil, fmt.Errorf("商品:%s 存酒数量必须大于0", invalidItem.ProductName)
	}

	// 使用lo库转换商品项
	reqItems := lo.Map(reqDto.Items, func(item req.UnifiedStorageItemDto, index int) req.AddProductStorageItem {
		return req.AddProductStorageItem{
			ProductId:       item.ProductId,
			ProductName:     item.ProductName,
			ProductType:     item.ProductType,
			ProductUnit:     item.ProductUnit,
			ProductSpec:     item.ProductSpec,
			Quantity:        item.Quantity,
			StorageLocation: item.StorageLocation,
			ExpireTime:      0, // 忽略前端传入的过期时间，由服务层自动计算
			Remark:          item.Remark,
			StorageRoomId:   item.StorageRoomId,
			OfflineOnly:     item.OfflineOnly,
		}
	})

	// 生成客户ID（如果未提供）
	customerId := reqDto.CustomerId
	if customerId == "" {
		customerId = lo.Ternary(reqDto.PhoneNumber != "",
			"C_"+reqDto.PhoneNumber,
			fmt.Sprintf("C_%d", time.Now().UnixNano()))
	}

	// 创建订单请求
	orderReqDto := &req.AddProductStorageOrderReqDto{
		VenueId:          reqDto.VenueId,
		CustomerId:       customerId,
		CustomerName:     reqDto.CustomerName,
		PhoneNumber:      reqDto.PhoneNumber,
		MemberCardNumber: reqDto.MemberCardNumber,
		MemberCardId:     reqDto.MemberCardId,
		StorageTime:      reqDto.StorageTime,
		StorageRoomId:    reqDto.StorageRoomId,
		StorageRoomName:  reqDto.StorageRoomName,
		OfflineOnly:      reqDto.OfflineOnly,
		SendSms:          reqDto.SendSms,
		Remark:           reqDto.Remark,
		OperatorId:       reqDto.OperatorId,
		OperatorName:     reqDto.OperatorName,
		Items:            reqItems,
	}

	// 基本验证
	if err := service.validateOrderRequest(orderReqDto); err != nil {
		return nil, err
	}

	// 创建存酒单
	result, err := service.CreateProductStorageOrder(logCtx, orderReqDto)
	if err != nil {
		return nil, err
	}

	// 确定返回类型
	resultType := lo.Ternary(len(orderReqDto.Items) > 1, "multiple", "single")

	// 构建返回结果
	addResult := &vo.ProductStorageAddResultVO{
		Success:   true,
		Type:      resultType,
		OrderInfo: result,
	}

	// 单个商品时设置StorageInfo以兼容旧逻辑
	if len(result.Items) == 1 {
		storage := service.buildStorageFromOrderItem(result, result.Items[0])
		addResult.StorageInfo = transfer.ProductStorageTransfer{}.PoToVo(*storage)
	}

	return addResult, nil
}

// validateOrderRequest 验证订单请求参数
func (service *ProductStorageOrderService) validateOrderRequest(orderReqDto *req.AddProductStorageOrderReqDto) error {
	if orderReqDto.VenueId == "" {
		return fmt.Errorf("场馆ID不能为空")
	}

	if len(orderReqDto.Items) == 0 {
		return fmt.Errorf("存酒明细不能为空")
	}

	// 使用lo库检查每个商品项
	for i, item := range orderReqDto.Items {
		if item.ProductId == "" {
			return fmt.Errorf("第%d项商品ID不能为空", i+1)
		}
		if item.ProductName == "" {
			return fmt.Errorf("第%d项商品名称不能为空", i+1)
		}
		if item.ProductUnit == "" {
			return fmt.Errorf("第%d项商品单位不能为空", i+1)
		}
		if item.Quantity <= 0 {
			return fmt.Errorf("第%d项商品数量必须大于0", i+1)
		}
	}

	return nil
}

// buildStorageFromOrderItem 从订单项构建存酒记录（用于兼容性）
func (service *ProductStorageOrderService) buildStorageFromOrderItem(order *vo.ProductStorageOrderVO, item vo.ProductStorageVO) *po.ProductStorage {
	return &po.ProductStorage{
		Id:              util.GetItPtr(item.Id),
		OrderNo:         util.GetItPtr(item.OrderNo),
		ParentOrderNo:   util.GetItPtr(order.OrderNo),
		VenueId:         util.GetItPtr(order.VenueId),
		CustomerId:      util.GetItPtr(order.CustomerId),
		CustomerName:    util.GetItPtr(order.CustomerName),
		PhoneNumber:     util.GetItPtr(order.PhoneNumber),
		ProductId:       util.GetItPtr(item.ProductId),
		ProductName:     util.GetItPtr(item.ProductName),
		ProductType:     util.GetItPtr(item.ProductType),
		ProductUnit:     util.GetItPtr(item.ProductUnit),
		ProductSpec:     util.GetItPtr(item.ProductSpec),
		Quantity:        util.GetItPtr(item.Quantity),
		RemainingQty:    util.GetItPtr(item.RemainingQty),
		StorageLocation: util.GetItPtr(item.StorageLocation),
		StorageTime:     util.GetItPtr(order.StorageTime),
		ExpireTime:      util.GetItPtr(item.ExpireTime),
		Remark:          util.GetItPtr(item.Remark),
		OperatorId:      util.GetItPtr(order.OperatorId),
		OperatorName:    util.GetItPtr(order.OperatorName),
		Ctime:           util.GetItPtr(order.Ctime),
		Utime:           util.GetItPtr(order.Utime),
		State:           util.GetItPtr(order.State),
		Version:         util.GetItPtr(order.Version),
	}
}

// CreateProductStorageOrder 创建存酒单及其明细
func (service *ProductStorageOrderService) CreateProductStorageOrder(logCtx *gin.Context, reqDto *req.AddProductStorageOrderReqDto) (*vo.ProductStorageOrderVO, error) {
	if reqDto == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 获取当前时间作为默认值
	currentTime := time.Now().Unix()

	// 确保存酒时间不为0，如果为0则使用当前时间
	if reqDto.StorageTime == 0 {
		reqDto.StorageTime = currentTime
	}

	// 生成存酒单号
	venueId := reqDto.VenueId
	orderNo := "PS" + venueId + strings.ReplaceAll(fmt.Sprintf("%d", currentTime), "-", "")

	// 获取会员卡号（MemberCardNumber是实际的卡号，MemberCardId是会员卡表的ID）
	memberCardNo := reqDto.MemberCardNumber

	// 创建存酒单
	order := &po.ProductStorageOrder{
		OrderNo:           &orderNo,
		VenueId:           &venueId,
		CustomerId:        &reqDto.CustomerId,
		CustomerName:      &reqDto.CustomerName,
		PhoneNumber:       &reqDto.PhoneNumber,
		MemberCardNo:      &memberCardNo, // 保留原字段，为兼容性设置
		MemberCardNumber:  &reqDto.MemberCardNumber,
		MemberCardId:      &reqDto.MemberCardId,
		StorageTime:       &reqDto.StorageTime,
		TotalItems:        util.GetItPtr(len(reqDto.Items)),
		TotalQuantity:     util.GetItPtr(0), // 初始化为0，后面累加
		RemainingQuantity: util.GetItPtr(0), // 初始化为0，后面累加
		Remark:            &reqDto.Remark,
		StorageRoomId:     &reqDto.StorageRoomId,   // 添加存储房间ID
		StorageRoomName:   &reqDto.StorageRoomName, // 添加存储房间名称
		OperatorId:        &reqDto.OperatorId,
		OperatorName:      &reqDto.OperatorName,
		OfflineOnly:       util.GetItPtr(util.BoolToInt(reqDto.OfflineOnly)),
	}

	var totalQuantity int = 0
	var err error
	var savedStorages []*po.ProductStorage // 保存实际的存储记录

	// 实例化操作日志服务
	opLogService := NewProductStorageOperationLogService()

	// 使用事务确保存酒单和明细一起创建
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 保存存酒单
		if err := Save(order); err != nil {
			return err
		}

		// 2. 保存存酒明细
		for i, item := range reqDto.Items {
			// 生成明细单号
			detailOrderNo := orderNo + "-" + fmt.Sprintf("%03d", i+1)

			// 如果没有设置到期时间，则使用存酒配置的默认天数
			expireTime := item.ExpireTime
			if expireTime == 0 {
				// 优先从配置中获取存储天数，如果没有配置则使用兜底值
				wineService := &WineStorageSettingService{}
				storageDays := wineService.GetStorageDaysConfig(logCtx, venueId)

				expireTime = time.Unix(reqDto.StorageTime, 0).AddDate(0, 0, storageDays).Unix()
			}

			// 获取会员卡号（MemberCardNumber是实际的卡号，MemberCardId是会员卡表的ID）
			memberCardNo := reqDto.MemberCardNumber

			// 保存存酒记录
			storage := &po.ProductStorage{
				OrderNo:           &detailOrderNo,
				ParentOrderNo:     &orderNo, // 关联主订单号
				VenueId:           &venueId,
				CustomerId:        &reqDto.CustomerId,
				CustomerName:      &reqDto.CustomerName,
				PhoneNumber:       &reqDto.PhoneNumber,
				MemberCardNo:      &memberCardNo,            // 保留原字段，为兼容性设置
				MemberCardNumber:  &reqDto.MemberCardNumber, // 添加会员卡号
				MemberCardId:      &reqDto.MemberCardId,     // 添加会员卡ID
				ProductId:         &item.ProductId,
				ProductName:       &item.ProductName,
				ProductType:       &item.ProductType, // 添加商品类型字段
				ProductUnit:       &item.ProductUnit,
				ProductSpec:       &item.ProductSpec,
				Quantity:          &item.Quantity,
				RemainingQty:      &item.Quantity, // 初始剩余等于数量
				StorageLocation:   &item.StorageLocation,
				StorageRoomId:     &reqDto.StorageRoomId,   // 添加存储房间ID
				StorageRoomName:   &reqDto.StorageRoomName, // 添加存储房间名称
				StorageTime:       &reqDto.StorageTime,
				ExpireTime:        &expireTime, // 使用计算后的到期时间
				Remark:            &item.Remark,
				OperatorId:        &reqDto.OperatorId,
				OperatorName:      &reqDto.OperatorName,
				LastOperationTime: &reqDto.StorageTime, // 设置最后操作时间与存酒时间一致
			}

			if err := Save(storage); err != nil {
				return err
			}

			// 保存实际的存储记录以备后用
			savedStorages = append(savedStorages, storage)

			// 记录存酒操作日志
			operationRemark := fmt.Sprintf("存酒单号: %s，商品: %s，数量: %d，存放位置: %s",
				orderNo, *storage.ProductName, *storage.Quantity, lo.FromPtrOr(storage.StorageLocation, ""))
			if item.Remark != "" {
				operationRemark += fmt.Sprintf("，备注: %s", item.Remark)
			}

			_, err = opLogService.CreateWithinTransactionWithRoom(
				tx,
				*storage.Id,
				*storage.OrderNo,
				"storage",
				"存酒",
				reqDto.StorageTime,
				*storage.Quantity,
				*storage.OperatorId,
				*storage.OperatorName,
				*storage.Quantity, // 初始剩余量等于数量
				operationRemark,
				reqDto.StorageRoomName, // 存酒操作记录寄存包厅
				"",                     // 存酒操作不设置送达包厅
			)

			if err != nil {
				return fmt.Errorf("创建存酒操作日志记录失败: %s", err.Error())
			}

			// 累加总数量
			totalQuantity += item.Quantity
		}

		// 3. 更新存酒单总数量
		*order.TotalQuantity = totalQuantity
		*order.RemainingQuantity = totalQuantity

		return tx.Model(&po.ProductStorageOrder{}).
			Where("id = ?", order.Id).
			Updates(map[string]interface{}{
				"total_quantity":     totalQuantity,
				"remaining_quantity": totalQuantity,
			}).Error
	})

	if err != nil {
		return nil, err
	}

	// 直接构建返回结果，避免再次查询数据库
	result := &vo.ProductStorageOrderVO{
		Id:                *order.Id,
		OrderNo:           *order.OrderNo,
		VenueId:           *order.VenueId,
		CustomerId:        *order.CustomerId,
		CustomerName:      *order.CustomerName,
		PhoneNumber:       *order.PhoneNumber,
		MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
		StorageTime:       *order.StorageTime,
		TotalItems:        *order.TotalItems,
		TotalQuantity:     *order.TotalQuantity,
		RemainingQuantity: *order.RemainingQuantity,
		Remark:            *order.Remark,
		OperatorId:        *order.OperatorId,
		OperatorName:      *order.OperatorName,
		Ctime:             *order.Ctime,
		Utime:             *order.Utime,
		State:             *order.State,
		Version:           *order.Version,
		Items:             []vo.ProductStorageVO{},
	}

	// 构建明细列表，使用实际保存的存储记录
	for _, storage := range savedStorages {
		storageVO := vo.ProductStorageVO{
			Id:               *storage.Id, // 使用实际保存的ID
			OrderNo:          *storage.OrderNo,
			ParentOrderNo:    *storage.ParentOrderNo,
			VenueId:          *storage.VenueId,
			CustomerId:       *storage.CustomerId,
			CustomerName:     *storage.CustomerName,
			PhoneNumber:      *storage.PhoneNumber,
			MemberCardNo:     lo.FromPtrOr(storage.MemberCardNo, ""),     // 保留原字段
			MemberCardNumber: lo.FromPtrOr(storage.MemberCardNumber, ""), // 添加会员卡号
			MemberCardId:     lo.FromPtrOr(storage.MemberCardId, ""),     // 添加会员卡ID
			ProductId:        *storage.ProductId,
			ProductName:      *storage.ProductName,
			ProductUnit:      *storage.ProductUnit,
			ProductSpec:      *storage.ProductSpec,
			Quantity:         *storage.Quantity,
			RemainingQty:     *storage.RemainingQty,
			StorageLocation:  *storage.StorageLocation,
			StorageRoomId:    lo.FromPtrOr(storage.StorageRoomId, ""),   // 添加存储房间ID
			StorageRoomName:  lo.FromPtrOr(storage.StorageRoomName, ""), // 添加存储房间名称
			StorageTime:      *storage.StorageTime,
			ExpireTime:       *storage.ExpireTime,
			Remark:           *storage.Remark,
			OperatorId:       *storage.OperatorId,
			OperatorName:     *storage.OperatorName,
			Ctime:            *storage.Ctime,
			Utime:            *storage.Utime,
			State:            *storage.State,
			Version:          *storage.Version,
		}

		result.Items = append(result.Items, storageVO)
	}

	// 发送存酒短信通知 [[memory:612373]] - 只发送一条汇总短信
	// 逻辑：默认发送短信(SendSms为nil)，只有明确传SendSms为false时才不发送
	if reqDto.SendSms == nil || *reqDto.SendSms {
		go func() {
			util.Wlog(logCtx).Info(fmt.Sprintf("[存酒短信通知] 开始处理短信发送，订单号: %s", *order.OrderNo))

			if len(savedStorages) == 0 {
				util.Wlog(logCtx).Info("[存酒短信通知] 没有存酒明细，跳过短信发送")
				return
			}

			smsService := &ProductStorageSmsService{}

			// 查询场馆信息
			venueService := &VenueService{}
			venue, venueErr := venueService.FindVenueById(logCtx, *order.VenueId)
			if venueErr != nil {
				util.Wlog(logCtx).Error(fmt.Sprintf("[存酒短信通知] 查询场馆信息失败: %v", venueErr))
				return
			}

			util.Wlog(logCtx).Info(fmt.Sprintf("[存酒短信通知] 场馆信息查询成功: %s", *venue.Name))

			// 发送一条汇总的存酒短信
			customerName := *order.CustomerName
			phoneNumber := *order.PhoneNumber

			util.Wlog(logCtx).Info(fmt.Sprintf("[存酒短信通知] 准备发送汇总短信: 订单号=%s, 客户=%s, 手机=%s, 商品种类=%d",
				*order.OrderNo, customerName, phoneNumber, len(savedStorages)))

			smsErr := smsService.SendStorageOrderSms(logCtx, venue, customerName, phoneNumber, *order.OrderNo, savedStorages)
			if smsErr != nil {
				util.Wlog(logCtx).Error(fmt.Sprintf("[存酒短信通知] 发送失败: 订单号=%s, 错误=%v", *order.OrderNo, smsErr))
			} else {
				util.Wlog(logCtx).Info(fmt.Sprintf("[存酒短信通知] 发送成功: 订单号=%s, 客户=%s, 商品种类=%d",
					*order.OrderNo, customerName, len(savedStorages)))
			}
		}()
	}

	// 异步创建存酒打印记录（一次存酒操作只打印一张单据）
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("[存酒打印记录] 创建失败: 订单号=%s, 错误=%v\n", *order.OrderNo, r)
			}
		}()

		// 为整个存酒单创建一张打印记录
		if len(savedStorages) > 0 {
			// 构建存酒单的完整打印内容
			content, err := service.buildWineStorageContent(logCtx, order, savedStorages)
			if err != nil {
				fmt.Printf("[存酒打印记录] 构建内容失败: 订单号=%s, 错误=%v\n", *order.OrderNo, err)
				return
			}

			// 直接调用PrintRecordService创建存酒打印记录
			printRecordService := &PrintRecordService{}

			// 构建打印记录PO
			now := time.Now().UnixMilli()
			printType := string(po.PrintTypeWineStorage) // 使用存酒单打印类型
			printNo := *order.OrderNo                    // 使用订单号作为打印单号
			businessId := *order.OrderNo                 // 使用订单号作为BusinessId

			printRecord := &po.PrintRecord{
				VenueId:      order.VenueId,
				PrintType:    &printType,
				PrintNo:      &printNo,
				PrintTime:    &now,
				BusinessId:   &businessId, // 核心标识字段
				OperatorId:   order.OperatorId,
				OperatorName: order.OperatorName,
				DeviceName:   util.GetItPtr(""), // 设备名称为空
				Status:       util.GetItPtr(0),  // 默认成功状态
				ErrorMsg:     util.GetItPtr(""), // 无错误信息
				Content:      &content,          // 存酒单的完整内容JSON
				Remark:       order.Remark,      // 备注
				Ctime:        &now,
				Utime:        &now,
				State:        util.GetItPtr(0),
				Version:      util.GetItPtr(0),
			}

			// 创建存酒打印记录，使用虚拟上下文（异步场景）
			err = printRecordService.CreatePrintRecord(&gin.Context{}, printRecord)
			if err != nil {
				fmt.Printf("[存酒打印记录] 创建失败: 订单号=%s, 错误=%v\n", *order.OrderNo, err)
			} else {
				printRecordId := "未知"
				if printRecord.Id != nil {
					printRecordId = *printRecord.Id
				}
				fmt.Printf("[存酒打印记录] 创建成功: 订单号=%s, 打印记录ID=%s, 商品种类=%d, 总数量=%d\n",
					*order.OrderNo, printRecordId, len(savedStorages), *order.TotalQuantity)
			}
		}
	}()

	return result, nil
}

// GetProductStorageOrderById 根据ID获取存酒单详情
func (service *ProductStorageOrderService) GetProductStorageOrderById(logCtx *gin.Context, id string) (*vo.ProductStorageOrderVO, error) {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	order, err := query.Where(queryBuilder.ProductStorageOrder.Id.Eq(id)).First()
	if err != nil {
		return nil, err
	}

	// 查询存酒单明细
	productStorageQueryBuilder := dal.Use(model.DBSlave.Self)
	items, err := productStorageQueryBuilder.ProductStorage.WithContext(logCtx).Where(productStorageQueryBuilder.ProductStorage.ParentOrderNo.Eq(*order.OrderNo)).Find()
	if err != nil {
		return nil, err
	}

	// 构建返回结果
	result := &vo.ProductStorageOrderVO{
		Id:                *order.Id,
		OrderNo:           *order.OrderNo,
		VenueId:           *order.VenueId,
		CustomerId:        *order.CustomerId,
		CustomerName:      *order.CustomerName,
		PhoneNumber:       *order.PhoneNumber,
		MemberCardNo:      lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
		StorageTime:       *order.StorageTime,
		TotalItems:        *order.TotalItems,
		TotalQuantity:     *order.TotalQuantity,
		RemainingQuantity: *order.RemainingQuantity,
		Remark:            *order.Remark,
		OperatorId:        *order.OperatorId,
		OperatorName:      *order.OperatorName,
		Ctime:             *order.Ctime,
		Utime:             *order.Utime,
		State:             *order.State,
		Version:           *order.Version,
	}

	// 添加明细
	result.Items = make([]vo.ProductStorageVO, 0, len(items))
	for _, item := range items {
		result.Items = append(result.Items, vo.ProductStorageVO{
			Id:               *item.Id,
			OrderNo:          *item.OrderNo,
			ParentOrderNo:    *item.ParentOrderNo,
			VenueId:          *item.VenueId,
			CustomerId:       *item.CustomerId,
			CustomerName:     *item.CustomerName,
			PhoneNumber:      *item.PhoneNumber,
			MemberCardNo:     lo.FromPtrOr(item.MemberCardNo, ""),     // 保留原字段
			MemberCardNumber: lo.FromPtrOr(item.MemberCardNumber, ""), // 添加会员卡号
			MemberCardId:     lo.FromPtrOr(item.MemberCardId, ""),     // 添加会员卡ID
			ProductId:        *item.ProductId,
			ProductName:      *item.ProductName,
			ProductUnit:      lo.FromPtrOr(item.ProductUnit, ""),
			ProductSpec:      lo.FromPtrOr(item.ProductSpec, ""),
			Quantity:         *item.Quantity,
			RemainingQty:     *item.RemainingQty,
			StorageLocation:  *item.StorageLocation,
			StorageTime:      *item.StorageTime,
			ExpireTime:       *item.ExpireTime,
			Remark:           *item.Remark,
			StatusCode:       lo.FromPtrOr(item.StatusCode, ""),
			OperatorId:       *item.OperatorId,
			OperatorName:     *item.OperatorName,
			IsBatch:          lo.FromPtrOr(item.IsBatch, 0),
			BatchTime:        lo.FromPtrOr(item.BatchTime, int64(0)),
			Ctime:            *item.Ctime,
			Utime:            *item.Utime,
			State:            *item.State,
			Version:          *item.Version,
		})
	}

	return result, nil
}

// QueryProductStorageOrders 查询存酒单列表
func (service *ProductStorageOrderService) QueryProductStorageOrders(logCtx *gin.Context, reqDto *req.QueryProductStorageOrderReqDto) (*vo.ProductStorageOrderListVO, error) {
	// 构建查询条件
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)

	if reqDto.Id != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.Id.Eq(reqDto.Id))
	}
	if reqDto.OrderNo != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OrderNo.Eq(reqDto.OrderNo))
	}
	if reqDto.VenueId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.VenueId.Eq(reqDto.VenueId))
	}
	if reqDto.CustomerId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.CustomerId.Eq(reqDto.CustomerId))
	}
	if reqDto.CustomerName != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.CustomerName.Like("%" + reqDto.CustomerName + "%"))
	}
	if reqDto.PhoneNumber != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.PhoneNumber.Eq(reqDto.PhoneNumber))
	}
	if reqDto.OperatorId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OperatorId.Eq(reqDto.OperatorId))
	}
	if reqDto.OperatorName != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OperatorName.Like("%" + reqDto.OperatorName + "%"))
	}
	if reqDto.StorageTimeStart > 0 {
		query = query.Where(queryBuilder.ProductStorageOrder.StorageTime.Gte(reqDto.StorageTimeStart))
	}
	if reqDto.StorageTimeEnd > 0 {
		query = query.Where(queryBuilder.ProductStorageOrder.StorageTime.Lte(reqDto.StorageTimeEnd))
	}
	if reqDto.SearchText != "" {
		query = query.Where(
			queryBuilder.ProductStorageOrder.CustomerName.Like("%" + reqDto.SearchText + "%"))
		query = query.Or(queryBuilder.ProductStorageOrder.PhoneNumber.Like("%" + reqDto.SearchText + "%"))
	}
	if reqDto.OnlyRemaining {
		query = query.Where(queryBuilder.ProductStorageOrder.RemainingQuantity.Gt(0))
	}

	// 计算总数
	total, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	if reqDto.PageNum <= 0 {
		reqDto.PageNum = 1
	}
	if reqDto.PageSize <= 0 {
		reqDto.PageSize = 10
	}

	orders, err := query.Order(queryBuilder.ProductStorageOrder.Ctime.Desc()).
		Offset((reqDto.PageNum - 1) * reqDto.PageSize).
		Limit(reqDto.PageSize).
		Find()
	if err != nil {
		return nil, err
	}

	// 组装响应
	result := &vo.ProductStorageOrderListVO{
		Total: total,
		List:  make([]vo.ProductStorageOrderVO, 0, len(orders)),
	}

	// 如果没有记录，直接返回
	if len(orders) == 0 {
		return result, nil
	}

	// 查询每个存酒单的明细
	for _, order := range orders {
		orderVo := vo.ProductStorageOrderVO{
			Id:                *order.Id,
			OrderNo:           *order.OrderNo,
			VenueId:           *order.VenueId,
			CustomerId:        *order.CustomerId,
			CustomerName:      *order.CustomerName,
			PhoneNumber:       *order.PhoneNumber,
			MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
			MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
			StorageTime:       *order.StorageTime,
			TotalItems:        *order.TotalItems,
			TotalQuantity:     *order.TotalQuantity,
			RemainingQuantity: *order.RemainingQuantity,
			Remark:            *order.Remark,

			OperatorId:   *order.OperatorId,
			OperatorName: *order.OperatorName,
			Ctime:        *order.Ctime,
			Utime:        *order.Utime,
			State:        *order.State,
			Version:      *order.Version,
		}

		// 只有需要明细时才查询
		if reqDto.PageSize <= 10 {
			// 查询存酒单明细
			productStorageQueryBuilder := dal.Use(model.DBSlave.Self)
			productStorageQuery := productStorageQueryBuilder.ProductStorage.WithContext(logCtx)
			items, err := productStorageQuery.Where(productStorageQueryBuilder.ProductStorage.ParentOrderNo.Eq(*order.OrderNo)).Find()
			if err != nil {
				return nil, err
			}

			// 添加明细
			orderVo.Items = make([]vo.ProductStorageVO, 0, len(items))
			for _, item := range items {
				orderVo.Items = append(orderVo.Items, vo.ProductStorageVO{
					Id:               *item.Id,
					OrderNo:          *item.OrderNo,
					ParentOrderNo:    *item.ParentOrderNo,
					VenueId:          *item.VenueId,
					CustomerId:       *item.CustomerId,
					CustomerName:     *item.CustomerName,
					PhoneNumber:      *item.PhoneNumber,
					MemberCardNumber: lo.FromPtrOr(item.MemberCardNumber, ""), // 添加会员卡号
					MemberCardId:     lo.FromPtrOr(item.MemberCardId, ""),     // 添加会员卡ID
					ProductId:        *item.ProductId,
					ProductName:      *item.ProductName,
					ProductUnit:      lo.FromPtrOr(item.ProductUnit, ""),
					ProductSpec:      lo.FromPtrOr(item.ProductSpec, ""),
					Quantity:         *item.Quantity,
					RemainingQty:     *item.RemainingQty,
					StorageLocation:  *item.StorageLocation,
					StorageTime:      *item.StorageTime,
					ExpireTime:       *item.ExpireTime,
					Remark:           *item.Remark,
					OperatorId:       *item.OperatorId,
					OperatorName:     *item.OperatorName,
					IsBatch:          lo.FromPtrOr(item.IsBatch, 0),
					BatchTime:        lo.FromPtrOr(item.BatchTime, int64(0)),
					Ctime:            *item.Ctime,
					Utime:            *item.Utime,
					State:            *item.State,
					Version:          *item.Version,
				})
			}
		}

		result.List = append(result.List, orderVo)
	}

	return result, nil
}

// DeleteProductStorageOrder 删除存酒单
func (service *ProductStorageOrderService) DeleteProductStorageOrder(logCtx *gin.Context, id string) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	order, err := query.QueryOneByID(id)
	if err != nil {
		return err
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 先删除明细
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Delete(&po.ProductStorage{}).Error; err != nil {
			return err
		}

		// 再删除主单
		if err := tx.Delete(&order).Error; err != nil {
			return err
		}

		return nil
	})
}

// UpdateProductStorageOrderStatus 更新存酒单状态
func (service *ProductStorageOrderService) UpdateProductStorageOrderStatus(logCtx *gin.Context, orderNo string, remainingQty int) error {
	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 更新明细记录的剩余数量
		queryBuilder := dal.Use(model.DBSlave.Self)
		query := queryBuilder.ProductStorage.WithContext(logCtx)
		items, err := query.Where(queryBuilder.ProductStorage.ParentOrderNo.Eq(orderNo)).Find()
		if err != nil {
			return err
		}

		// 计算总剩余量
		var totalRemaining int
		for _, item := range items {
			totalRemaining += *item.RemainingQty
		}

		// 更新主单记录
		return tx.Model(&po.ProductStorageOrder{}).
			Where("order_no = ?", orderNo).
			Updates(map[string]interface{}{
				"remaining_quantity": totalRemaining,
				"utime":              time.Now().Unix(),
			}).Error
	})
}

// ExtendProductStorageOrder 延期存酒单
func (service *ProductStorageOrderService) ExtendProductStorageOrder(logCtx *gin.Context, reqDto *req.ExtendProductStorageOrderReqDto) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	order, err := queryBuilder.ProductStorageOrder.WithContext(logCtx).QueryOneByID(reqDto.OrderId)
	if err != nil {
		return err
	}

	// 参数验证
	if reqDto.NewExpireTime <= time.Now().Unix() {
		return fmt.Errorf("新的过期时间必须在当前时间之后")
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 查询存酒明细
		storageItems := []*po.ProductStorage{}
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Find(&storageItems).Error; err != nil {
			return err
		}

		// 更新每个存酒明细的到期时间
		for _, item := range storageItems {
			// 记录原始到期时间（用于操作日志）
			oldExpireTime := *item.ExpireTime

			// 更新到期时间
			item.ExpireTime = &reqDto.NewExpireTime

			// 更新数据库
			if err := tx.Model(item).Update("expire_time", item.ExpireTime).Error; err != nil {
				return err
			}

			// 添加延期操作记录
			now := util.TimeNowUnixInt64()
			opLogService := NewProductStorageOperationLogService()

			// 计算延期天数（用于日志展示）
			oldExpireDate := time.Unix(oldExpireTime, 0)
			newExpireDate := time.Unix(reqDto.NewExpireTime, 0)
			dayDiff := int(newExpireDate.Sub(oldExpireDate).Hours() / 24)

			remark := fmt.Sprintf("延期操作，将到期时间从 %s 延长至 %s（延长%d天）",
				time.Unix(oldExpireTime, 0).Format("2006-01-02"),
				time.Unix(reqDto.NewExpireTime, 0).Format("2006-01-02"),
				dayDiff)

			if reqDto.Remark != "" {
				remark += fmt.Sprintf("，备注: %s", reqDto.Remark)
			}

			_, err := opLogService.CreateWithinTransaction(
				tx,
				*item.Id,
				*item.OrderNo,
				"extend",
				"续存",
				now,
				0, // 续存不改变数量
				reqDto.OperatorId,
				reqDto.OperatorName,
				*item.RemainingQty,
				remark,
			)

			if err != nil {
				return fmt.Errorf("创建延期操作日志失败: %s", err.Error())
			}
		}

		// 异步创建续存打印记录
		go func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("[续存打印记录] 创建失败: 订单号=%s, 错误=%v\n", *order.OrderNo, r)
				}
			}()

			// 为续存操作创建打印记录
			if len(storageItems) > 0 {
				// 构建续存单的完整打印内容
				content, err := service.buildWineRenewalContent(logCtx, order, storageItems, reqDto)
				if err != nil {
					fmt.Printf("[续存打印记录] 构建内容失败: 订单号=%s, 错误=%v\n", *order.OrderNo, err)
					return
				}

				// 创建续存打印记录
				printRecordService := &PrintRecordService{}

				// 构建打印记录PO
				now := time.Now().UnixMilli()
				printType := "WINE_RENEWAL"                    // 使用续存单打印类型
				printNo := fmt.Sprintf("RN%s", *order.OrderNo) // 续存单号：RN+原存酒单号
				businessId := printNo                          // 使用续存单号作为BusinessId

				printRecord := &po.PrintRecord{
					VenueId:      order.VenueId,
					PrintType:    &printType,
					PrintNo:      &printNo,
					PrintTime:    &now,
					BusinessId:   &businessId, // 核心标识字段
					OperatorId:   &reqDto.OperatorId,
					OperatorName: &reqDto.OperatorName,
					DeviceName:   util.GetItPtr(""), // 设备名称为空
					Status:       util.GetItPtr(0),  // 默认成功状态
					ErrorMsg:     util.GetItPtr(""), // 无错误信息
					Content:      &content,          // 续存单的完整内容JSON
					Remark:       &reqDto.Remark,    // 续存备注
					Ctime:        &now,
					Utime:        &now,
					State:        util.GetItPtr(0),
					Version:      util.GetItPtr(0),
				}

				// 创建续存打印记录
				err = printRecordService.CreatePrintRecord(logCtx, printRecord)
				if err != nil {
					fmt.Printf("[续存打印记录] 创建失败: 订单号=%s, 错误=%v\n", *order.OrderNo, err)
				} else {
					printRecordId := "未知"
					if printRecord.Id != nil {
						printRecordId = *printRecord.Id
					}
					fmt.Printf("[续存打印记录] 创建成功: 订单号=%s, 续存单号=%s, 打印记录ID=%s\n",
						*order.OrderNo, printNo, printRecordId)
				}
			}
		}()

		return nil
	})
}

// DiscardProductStorageOrder 报废存酒单
func (service *ProductStorageOrderService) DiscardProductStorageOrder(logCtx *gin.Context, reqDto *req.DiscardProductStorageOrderReqDto) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	order, err := queryBuilder.ProductStorageOrder.WithContext(logCtx).QueryOneByID(reqDto.OrderId)
	if err != nil {
		return err
	}

	// 确保操作者名称不为空，如果为空，则使用操作者ID代替
	if reqDto.OperatorName == "" {
		reqDto.OperatorName = reqDto.OperatorId
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 更新每个存酒明细的状态
		storageItems := []*po.ProductStorage{}
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Find(&storageItems).Error; err != nil {
			return err
		}

		// 更新存酒单主表的剩余数量
		if err := tx.Model(order).Update("remaining_quantity", 0).Error; err != nil {
			return err
		}

		// 更新每个存酒明细记录
		for _, item := range storageItems {
			// 备份原始剩余数量
			originalQty := *item.RemainingQty

			// 更新状态为"已报废"，剩余数量为0
			discardStatus := "discarded"
			zero := 0
			updates := map[string]interface{}{
				"status_code":   discardStatus,
				"remaining_qty": zero,
				"utime":         reqDto.DiscardTime,
			}

			if err := tx.Model(item).Updates(updates).Error; err != nil {
				return err
			}

			// 添加报废操作记录
			opLogService := NewProductStorageOperationLogService()
			_, err := opLogService.CreateWithinTransaction(
				tx,
				*item.Id,
				*item.OrderNo,
				"discard",
				"报废",
				reqDto.DiscardTime,
				originalQty, // 报废全部剩余数量
				reqDto.OperatorId,
				reqDto.OperatorName,
				0, // 报废后剩余为0
				fmt.Sprintf("报废存酒记录，数量: %d", originalQty), // 备注
			)

			if err != nil {
				return err
			}
		}

		return nil
	})
}

// GetOrderByOrderNo 根据订单号获取存酒单
func (service *ProductStorageOrderService) GetOrderByOrderNo(logCtx *gin.Context, orderNo string) (*po.ProductStorageOrder, error) {
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	return query.Where(queryBuilder.ProductStorageOrder.OrderNo.Eq(orderNo)).First()
}

// AddItemsToOrder 向已有订单添加商品项
func (service *ProductStorageOrderService) AddItemsToOrder(logCtx *gin.Context, orderNo string, items []req.AddProductStorageItem, operatorId string, operatorName string, remark string) (*vo.ProductStorageOrderVO, error) {
	// 首先查询现有订单
	orderService := NewProductStorageOrderService()
	existingOrder, err := orderService.GetOrderByOrderNo(logCtx, orderNo)
	if err != nil {
		return nil, err
	}
	if existingOrder == nil {
		return nil, fmt.Errorf("存酒单不存在: %s", orderNo)
	}

	queryBuilder := dal.Use(model.DBSlave.Self)
	existingItems, err := queryBuilder.ProductStorage.WithContext(logCtx).Where(queryBuilder.ProductStorage.ParentOrderNo.Eq(orderNo)).Find()
	if err != nil {
		return nil, err
	}

	// 声明一个标志变量，用于判断订单是否已更新
	var orderUpdated bool = false
	var addedQuantity int = 0

	// 使用事务添加新的商品项
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		now := util.TimeNowUnixInt64()
		totalItems := 0
		totalQuantity := 0
		remainingQuantity := 0

		// 获取现有总数
		if len(existingItems) > 0 {
			// 计算现有的总数量
			for _, item := range existingItems {
				totalItems++
				totalQuantity += *item.Quantity
				remainingQuantity += *item.RemainingQty
			}
		}

		// 添加新的商品项
		for _, newItem := range items {
			// 生成一个新的订单号
			newOrderNo := "PS" + *existingOrder.VenueId + fmt.Sprintf("%d", time.Now().UnixNano())

			// 如果没有设置到期时间，则使用存酒配置的默认天数
			expireTime := newItem.ExpireTime
			if expireTime == 0 {
				// 优先从配置中获取存储天数，如果没有配置则使用兜底值
				wineService := &WineStorageSettingService{}
				storageDays := wineService.GetStorageDaysConfig(logCtx, *existingOrder.VenueId)

				expireTime = time.Unix(now, 0).AddDate(0, 0, storageDays).Unix()
			}

			// 获取会员卡号（MemberCardNumber是实际的卡号，MemberCardId是会员卡表的ID）
			memberCardNo := lo.FromPtrOr(existingOrder.MemberCardNumber, "")

			// 创建存酒明细记录
			productStorage := &po.ProductStorage{
				OrderNo:           &newOrderNo,
				ParentOrderNo:     &orderNo,
				VenueId:           existingOrder.VenueId,
				CustomerId:        existingOrder.CustomerId,
				CustomerName:      existingOrder.CustomerName,
				PhoneNumber:       existingOrder.PhoneNumber,
				MemberCardNo:      &memberCardNo,                  // 保留原字段，为兼容性设置
				MemberCardNumber:  existingOrder.MemberCardNumber, // 使用现有订单的会员卡号
				MemberCardId:      existingOrder.MemberCardId,     // 使用现有订单的会员卡ID
				ProductId:         &newItem.ProductId,
				ProductName:       &newItem.ProductName,
				ProductSpec:       &newItem.ProductSpec,
				ProductUnit:       &newItem.ProductUnit,
				Quantity:          &newItem.Quantity,
				RemainingQty:      &newItem.Quantity, // 初始剩余数量等于原始数量
				StorageLocation:   &newItem.StorageLocation,
				StorageTime:       &now,
				ExpireTime:        &expireTime, // 使用计算后的到期时间
				OperatorId:        &operatorId,
				OperatorName:      &operatorName,
				LastOperationTime: &now, // 设置最后操作时间
				Remark:            &remark,
			}

			// 保存存酒记录
			if err := Save(productStorage); err != nil {
				return err
			}

			// 记录存酒操作日志
			operationRemark := fmt.Sprintf("追加存酒，商品: %s，数量: %d，存放位置: %s",
				*productStorage.ProductName, *productStorage.Quantity, lo.FromPtrOr(productStorage.StorageLocation, ""))
			if newItem.Remark != "" {
				operationRemark += fmt.Sprintf("，备注: %s", newItem.Remark)
			}

			opLogService := NewProductStorageOperationLogService()
			_, err := opLogService.CreateWithinTransactionWithRoom(
				tx,
				*productStorage.Id,
				*productStorage.OrderNo,
				"storage_add",
				"追加存酒",
				now,
				*productStorage.Quantity,
				operatorId,
				operatorName,
				*productStorage.Quantity, // 初始剩余量等于数量
				operationRemark,
				lo.FromPtrOr(existingOrder.StorageRoomName, ""), // 追加存酒使用原订单的寄存包厅
				"", // 追加存酒不设置送达包厅
			)

			if err != nil {
				return fmt.Errorf("创建追加存酒操作日志记录失败: %s", err.Error())
			}

			// 累加新增数量
			addedQuantity += newItem.Quantity
		}

		// 更新存酒单的总数量和总项数
		newTotalItems := *existingOrder.TotalItems + len(items)
		newTotalQuantity := *existingOrder.TotalQuantity + addedQuantity
		newRemainingQuantity := *existingOrder.RemainingQuantity + addedQuantity

		// 更新主单的备注信息
		remarkStr := *existingOrder.Remark
		if remark != "" {
			if remarkStr != "" {
				remarkStr += "; "
			}
			remarkStr += "追加商品: " + remark
		}

		// 更新存酒单
		err := tx.Model(&po.ProductStorageOrder{}).
			Where("order_no = ?", orderNo).
			Updates(map[string]interface{}{
				"total_items":        newTotalItems,
				"total_quantity":     newTotalQuantity,
				"remaining_quantity": newRemainingQuantity,
				"remark":             remarkStr,
				"operator_id":        operatorId,
				"operator_name":      operatorName,
				"utime":              time.Now().Unix(),
			}).Error
		if err != nil {
			return err
		}

		orderUpdated = true
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 如果订单已更新，返回更新后的存酒单完整信息
	if orderUpdated {
		return service.GetProductStorageOrderById(logCtx, *existingOrder.Id)
	}

	return nil, errors.New("添加商品失败")
}

// GetOrdersByOrderNos 批量根据订单号获取存酒单
func (service *ProductStorageOrderService) GetOrdersByOrderNos(logCtx *gin.Context, orderNos []string) ([]*po.ProductStorageOrder, error) {
	if len(orderNos) == 0 {
		return []*po.ProductStorageOrder{}, nil
	}

	query := dal.Use(model.DBSlave.Self)
	return query.ProductStorageOrder.WithContext(logCtx).Where(query.ProductStorageOrder.OrderNo.In(orderNos...)).Find()
}

// GetProductStorageOrderWithItems 获取存酒单详情（包含所有商品明细）
func (service *ProductStorageOrderService) GetProductStorageOrderWithItems(ctx *gin.Context, orderNo string) (*vo.ProductStorageOrderWithItemsVO, error) {
	if orderNo == "" {
		return nil, fmt.Errorf("orderNo不能为空")
	}

	// 获取存酒主单
	order, err := service.GetOrderByOrderNo(ctx, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询存酒单失败: %v", err)
	}

	if order == nil {
		return nil, fmt.Errorf("存酒单不存在")
	}

	// 获取所有关联商品明细
	storageService := &ProductStorageService{}
	items, err := storageService.FindByParentOrderNo(ctx, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询存酒明细失败: %v", err)
	}

	// 使用lo库进行数据转换
	var totalItemsInt64 *int64
	var totalQuantityInt64 *int64
	if order.TotalItems != nil {
		val := int64(*order.TotalItems)
		totalItemsInt64 = &val
	}
	if order.TotalQuantity != nil {
		val := int64(*order.TotalQuantity)
		totalQuantityInt64 = &val
	}

	// 组装结果
	result := &vo.ProductStorageOrderWithItemsVO{
		OrderNo:       order.OrderNo,
		CustomerInfo:  vo.CustomerInfoVO{},
		StorageTime:   order.StorageTime,
		TotalItems:    totalItemsInt64,
		TotalQuantity: totalQuantityInt64,
		OperatorId:    order.OperatorId,
		OperatorName:  order.OperatorName,
		Remark:        order.Remark,
		Items:         []vo.ProductStorageVO{},
	}

	// 设置客户信息
	result.CustomerInfo = vo.CustomerInfoVO{
		CustomerId:       lo.FromPtrOr(order.CustomerId, ""),
		CustomerName:     lo.FromPtrOr(order.CustomerName, ""),
		PhoneNumber:      lo.FromPtrOr(order.PhoneNumber, ""),
		MemberCardId:     lo.FromPtrOr(order.MemberCardId, ""),
		MemberCardNumber: lo.FromPtrOr(order.MemberCardNumber, ""),
	}

	// 使用lo库转换商品明细
	result.Items = lo.Map(items, func(item *po.ProductStorage, index int) vo.ProductStorageVO {
		return transfer.ProductStorageTransfer{}.PoToVo(*item)
	})

	return result, nil
}

// buildWineStorageContent 构建存酒单的完整打印内容
func (s *ProductStorageOrderService) buildWineStorageContent(logCtx *gin.Context, order *po.ProductStorageOrder, items []*po.ProductStorage) (string, error) {
	// 构建存酒单内容JSON结构
	storageData := map[string]interface{}{
		"venueId":       util.GetPtrSafe(order.VenueId),
		"storageNo":     util.GetPtrSafe(order.OrderNo),
		"customerName":  util.GetPtrSafe(order.CustomerName),
		"phoneNumber":   util.GetPtrSafe(order.PhoneNumber),
		"storageTime":   time.Unix(util.GetPtrSafe(order.StorageTime), 0).Format("2006-01-02 15:04:05"),
		"totalQuantity": util.GetPtrSafe(order.TotalQuantity),
		"remark":        util.GetPtrSafe(order.Remark),
		"items":         []map[string]interface{}{},
	}

	// 添加商品明细
	itemsList := make([]map[string]interface{}, 0, len(items))
	for _, item := range items {
		itemData := map[string]interface{}{
			"productId":       util.GetPtrSafe(item.ProductId),
			"productName":     util.GetPtrSafe(item.ProductName),
			"productType":     util.GetPtrSafe(item.ProductType),
			"productSpec":     util.GetPtrSafe(item.ProductSpec),
			"quantity":        util.GetPtrSafe(item.Quantity),
			"storageLocation": util.GetPtrSafe(item.StorageLocation),
			"expireTime":      time.Unix(util.GetPtrSafe(item.ExpireTime), 0).Format("2006-01-02 15:04:05"),
			"remark":          util.GetPtrSafe(item.Remark),
		}
		itemsList = append(itemsList, itemData)
	}
	storageData["items"] = itemsList

	// 序列化为JSON字符串
	jsonBytes, err := json.Marshal(storageData)
	if err != nil {
		return "", fmt.Errorf("序列化存酒单内容失败: %w", err)
	}

	return string(jsonBytes), nil
}

// buildWineRenewalContent 构建续存单的完整打印内容
func (s *ProductStorageOrderService) buildWineRenewalContent(logCtx *gin.Context, order *po.ProductStorageOrder, items []*po.ProductStorage, reqDto *req.ExtendProductStorageOrderReqDto) (string, error) {
	// 构建续存单内容JSON结构
	renewalData := map[string]interface{}{
		"venueId":       util.GetPtrSafe(order.VenueId),
		"storageNo":     util.GetPtrSafe(order.OrderNo),
		"customerName":  util.GetPtrSafe(order.CustomerName),
		"phoneNumber":   util.GetPtrSafe(order.PhoneNumber),
		"storageTime":   time.Unix(util.GetPtrSafe(order.StorageTime), 0).Format("2006-01-02 15:04:05"),
		"totalQuantity": util.GetPtrSafe(order.TotalQuantity),
		"remark":        reqDto.Remark,
		"items":         []map[string]interface{}{},
	}

	// 添加商品明细
	itemsList := make([]map[string]interface{}, 0, len(items))
	for _, item := range items {
		itemData := map[string]interface{}{
			"productId":       util.GetPtrSafe(item.ProductId),
			"productName":     util.GetPtrSafe(item.ProductName),
			"productType":     util.GetPtrSafe(item.ProductType),
			"productSpec":     util.GetPtrSafe(item.ProductSpec),
			"quantity":        util.GetPtrSafe(item.Quantity),
			"storageLocation": util.GetPtrSafe(item.StorageLocation),
			"expireTime":      time.Unix(util.GetPtrSafe(item.ExpireTime), 0).Format("2006-01-02 15:04:05"),
			"remark":          util.GetPtrSafe(item.Remark),
		}
		itemsList = append(itemsList, itemData)
	}
	renewalData["items"] = itemsList

	// 序列化为JSON字符串
	jsonBytes, err := json.Marshal(renewalData)
	if err != nil {
		return "", fmt.Errorf("序列化续存单内容失败: %w", err)
	}

	return string(jsonBytes), nil
}
