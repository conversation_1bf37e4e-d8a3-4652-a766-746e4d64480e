package impl

import (
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductStockSnapshotService struct {
}

var productStockSnapshotService = &ProductStockSnapshotService{}

// GetProductStockSnapshotService 获取单例实例
func GetProductStockSnapshotService() *ProductStockSnapshotService {
	return productStockSnapshotService
}

// CreateProductStockSnapshot 创建库存快照
func (service *ProductStockSnapshotService) CreateProductStockSnapshot(logCtx *gin.Context, snapshot *po.ProductStockSnapshot) error {
	return Save(snapshot)
}

// CreateProductStockSnapshotWithTx 在事务中创建库存快照
func (service *ProductStockSnapshotService) CreateProductStockSnapshotWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	return SaveWithTx(snapshot, tx)
}

// UpdateProductStockSnapshot 更新库存快照
func (service *ProductStockSnapshotService) UpdateProductStockSnapshot(logCtx *gin.Context, snapshot *po.ProductStockSnapshot) error {
	return Update(snapshot)
}

// UpdateProductStockSnapshotPartial 部分更新库存快照（只更新非空字段）
func (service *ProductStockSnapshotService) UpdateProductStockSnapshotPartial(logCtx *gin.Context, snapshot *po.ProductStockSnapshot) error {
	return UpdateNotNull(snapshot)
}

// UpdateProductStockSnapshotPartialWithTx 在事务中部分更新库存快照
func (service *ProductStockSnapshotService) UpdateProductStockSnapshotPartialWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	return UpdateNotNullWithTx(snapshot, tx)
}

// UpdateProductStockSnapshotWithTx 在事务中更新库存快照
func (service *ProductStockSnapshotService) UpdateProductStockSnapshotWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	return UpdateNotNullWithTx(snapshot, tx)
}

// BatchCreateProductStockSnapshots 批量创建库存快照
func (service *ProductStockSnapshotService) BatchCreateProductStockSnapshots(logCtx *gin.Context, snapshots []po.ProductStockSnapshot) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		for _, snapshot := range snapshots {
			if err := service.CreateProductStockSnapshotWithTx(logCtx, &snapshot, tx); err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchCreateProductStockSnapshotsWithTx 在事务中批量创建库存快照
func (service *ProductStockSnapshotService) BatchCreateProductStockSnapshotsWithTx(logCtx *gin.Context, snapshots []po.ProductStockSnapshot, tx *gorm.DB) error {
	for _, snapshot := range snapshots {
		if err := service.CreateProductStockSnapshotWithTx(logCtx, &snapshot, tx); err != nil {
			return err
		}
	}
	return nil
}

// DeleteProductStockSnapshot 删除库存快照
func (service *ProductStockSnapshotService) DeleteProductStockSnapshot(logCtx *gin.Context, id string) error {
	return Delete(po.ProductStockSnapshot{Id: &id})
}

// DeleteProductStockSnapshotWithTx 在事务中删除库存快照
func (service *ProductStockSnapshotService) DeleteProductStockSnapshotWithTx(logCtx *gin.Context, id string, tx *gorm.DB) error {
	return DeleteWithTx(po.ProductStockSnapshot{Id: &id}, tx)
}

// FindProductStockSnapshotById 根据ID查找库存快照
func (service *ProductStockSnapshotService) FindProductStockSnapshotById(logCtx *gin.Context, id string) (snapshot *po.ProductStockSnapshot, err error) {
	snapshot = &po.ProductStockSnapshot{}
	err = model.DBMaster.Self.Where("id=?", id).First(snapshot).Error
	return
}

// FindProductStockSnapshotByCondition 根据条件查找库存快照
func (service *ProductStockSnapshotService) FindProductStockSnapshotByCondition(logCtx *gin.Context, productId, venueId string) (snapshot *po.ProductStockSnapshot, err error) {
	snapshot = &po.ProductStockSnapshot{}
	err = model.DBSlave.Self.Where("product_id = ? AND venue_id = ?", productId, venueId).First(snapshot).Error
	return
}

// FindProductStockSnapshotsByVenueId 根据门店ID查找所有库存快照
func (service *ProductStockSnapshotService) FindProductStockSnapshotsByVenueId(logCtx *gin.Context, venueId string) (snapshots []po.ProductStockSnapshot, err error) {
	snapshots = []po.ProductStockSnapshot{}
	err = model.DBSlave.Self.Where("venue_id = ?", venueId).Find(&snapshots).Error
	return
}

// FindProductStockSnapshotsByProductIds 根据商品ID列表查找库存快照
func (service *ProductStockSnapshotService) FindProductStockSnapshotsByProductIds(logCtx *gin.Context, venueId string, productIds []string) (snapshots []po.ProductStockSnapshot, err error) {
	snapshots = []po.ProductStockSnapshot{}
	db := model.DBSlave.Self.Model(&po.ProductStockSnapshot{})
	db = db.Where("product_id IN (?)", productIds)
	db = db.Where("venue_id = ?", venueId)
	err = db.Find(&snapshots).Error
	return
}

// SaveOrUpdateProductStockSnapshot 保存或更新库存快照（根据venue_id和product_id的唯一性）
func (service *ProductStockSnapshotService) SaveOrUpdateProductStockSnapshot(logCtx *gin.Context, snapshot *po.ProductStockSnapshot) error {
	// 先尝试查找是否存在
	existing, err := service.FindProductStockSnapshotByCondition(logCtx, *snapshot.ProductId, *snapshot.VenueId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if err == gorm.ErrRecordNotFound {
		// 不存在则创建
		return service.CreateProductStockSnapshot(logCtx, snapshot)
	} else {
		// 存在则更新
		snapshot.Id = existing.Id
		return service.UpdateProductStockSnapshotPartial(logCtx, snapshot)
	}
}

// SaveOrUpdateProductStockSnapshotWithTx 在事务中保存或更新库存快照
func (service *ProductStockSnapshotService) SaveOrUpdateProductStockSnapshotWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	// 先尝试查找是否存在
	existing := &po.ProductStockSnapshot{}
	err := tx.Where("product_id = ? AND venue_id = ?", *snapshot.ProductId, *snapshot.VenueId).First(existing).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if err == gorm.ErrRecordNotFound {
		// 不存在则创建
		return service.CreateProductStockSnapshotWithTx(logCtx, snapshot, tx)
	} else {
		// 存在则更新
		snapshot.Id = existing.Id
		return service.UpdateProductStockSnapshotPartialWithTx(logCtx, snapshot, tx)
	}
}

// SaveOrUpdateProductStockSnapshotWithCustomTimeWithTx 在事务中保存或更新库存快照，保留自定义的时间字段
func (service *ProductStockSnapshotService) SaveOrUpdateProductStockSnapshotWithCustomTimeWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	// 先尝试查找是否存在
	existing := &po.ProductStockSnapshot{}
	err := tx.Where("product_id = ? AND venue_id = ?", *snapshot.ProductId, *snapshot.VenueId).First(existing).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if err == gorm.ErrRecordNotFound {
		// 不存在则创建，使用自定义时间
		return service.CreateProductStockSnapshotWithCustomTimeWithTx(logCtx, snapshot, tx)
	} else {
		// 存在则更新，使用自定义时间
		snapshot.Id = existing.Id
		return service.UpdateProductStockSnapshotWithCustomTimeWithTx(logCtx, snapshot, tx)
	}
}

// CreateProductStockSnapshotWithCustomTimeWithTx 在事务中创建库存快照，保留自定义的时间字段
func (service *ProductStockSnapshotService) CreateProductStockSnapshotWithCustomTimeWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	// 手动设置ID，避免BaseService覆盖时间字段
	if snapshot.Id == nil || *snapshot.Id == "" {
		id := util.GetUUID()
		snapshot.Id = &id
	}

	// 设置默认状态和版本
	if snapshot.State == nil {
		state := 0
		snapshot.State = &state
	}
	if snapshot.Version == nil {
		version := 0
		snapshot.Version = &version
	}

	// 直接创建，不使用BaseService的方法
	return tx.Create(snapshot).Error
}

// UpdateProductStockSnapshotWithCustomTimeWithTx 在事务中更新库存快照，保留自定义的时间字段
func (service *ProductStockSnapshotService) UpdateProductStockSnapshotWithCustomTimeWithTx(logCtx *gin.Context, snapshot *po.ProductStockSnapshot, tx *gorm.DB) error {
	// 直接更新，不使用BaseService的方法
	return tx.Model(snapshot).Where("id = ?", *snapshot.Id).Updates(snapshot).Error
}
