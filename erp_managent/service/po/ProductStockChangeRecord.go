package po

// ProductStockChangeRecord 商品库存变更记录实体 (快照表)
type ProductStockChangeRecord struct {
	Id      *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"` // ID
	VenueId *string `gorm:"column:venue_id;type:varchar(64);not null" json:"venueId"` // 门店ID
	VenueName         *string `gorm:"column:venue_name;type:varchar(128);default:''" json:"venueName"`                 // 门店名称
	BusinessDay       *int64  `gorm:"column:business_day;type:bigint;default:0" json:"businessDay"`                    // 营业日期
	BusinessDayStr    *string `gorm:"column:business_day_str;type:varchar(32);default:''" json:"businessDayStr"`       // 营业日期字符串
	BusinessStartHour *string `gorm:"column:business_start_hour;type:varchar(16);default:''" json:"businessStartHour"` // 营业开始时间
	BusinessEndHour   *string `gorm:"column:business_end_hour;type:varchar(16);default:''" json:"businessEndHour"`     // 营业结束时间

	ProductStockId      *string `gorm:"column:product_stock_id;type:varchar(64);not null" json:"productStockId"`            // 商品库存ID
	ProductId           *string `gorm:"column:product_id;type:varchar(64);not null" json:"productId"`                       // 商品ID
	ProductName         *string `gorm:"column:product_name;type:varchar(255);not null" json:"productName"`                  // 商品名称
	ProductCategoryId   *string `gorm:"column:product_category_id;type:varchar(64);not null" json:"productCategoryId"`      // 商品分类ID
	ProductCategoryName *string `gorm:"column:product_category_name;type:varchar(255);not null" json:"productCategoryName"` // 商品分类名称
	Quantity            *int    `gorm:"column:quantity;type:int" json:"quantity"`                                           // 数量变动数量
	ActionType          *string `gorm:"column:action_type;type:varchar(255);not null" json:"actionType"`                    // 操作类型 add: 增加 minus: 减少
	LeftStock           *int    `gorm:"column:left_stock;type:int" json:"leftStock"`                                        // 剩余库存数量
	Source              *string `gorm:"column:source;type:varchar(255);not null" json:"source"`                             // 来源 下单，退单，入库
	SourceName          *string `gorm:"column:source_name;type:varchar(255);not null" json:"sourceName"`                    // 来源名称 下单，退单，入库，存取酒
	SessionId           *string `gorm:"column:session_id;type:varchar(64);not null" json:"sessionId"`                       // 开台ID
	InventoryId         *string `gorm:"column:inventory_id;type:varchar(64);not null" json:"inventoryId"`                   // 库存ID
	ProductStorageId    *string `gorm:"column:product_storage_id;type:varchar(64);not null" json:"productStorageId"`        // 商品存取酒单号
	DisplayNo           *string `gorm:"column:display_no;type:varchar(255);not null" json:"displayNo"`                      // 显示编号 或SessionId 或InventoryId
	OperatorId          *string `gorm:"column:operator_id;type:varchar(64);not null" json:"operatorId"`                     // 操作人ID
	OperatorName        *string `gorm:"column:operator_name;type:varchar(255);not null" json:"operatorName"`                // 操作人名称
	Info                *string `gorm:"column:info;type:varchar(1024);not null" json:"info"`                                // 变更信息

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (ProductStockChangeRecord) TableName() string {
	return "product_stock_change_record"
}

func (p ProductStockChangeRecord) GetId() string {
	return *p.Id
}
