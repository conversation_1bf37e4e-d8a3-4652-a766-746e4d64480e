package po

// InventoryCheckRecordItem 盘点记录明细实体
type InventoryCheckRecordItem struct {
	Id                 *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                 // ID
	CheckRecordId      *string `gorm:"column:check_record_id;type:varchar(64);default:''" json:"checkRecordId"`  // 盘点记录ID
	ProductId          *string `gorm:"column:product_id;type:varchar(64);default:''" json:"productId"`           // 商品ID
	StockQuantity      *int    `gorm:"column:stock_quantity;type:int;default:0" json:"stockQuantity"`            // 库存数量（盘点之前）
	CheckQuantity      *int    `gorm:"column:check_quantity;type:int;default:0" json:"checkQuantity"`            // 盘点数量（盘点之后）
	ProfitLossQuantity *int    `gorm:"column:profit_loss_quantity;type:int;default:0" json:"profitLossQuantity"` // 盈亏数量
	Ctime              *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                             // 创建时间
	Utime              *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                             // 更新时间
	State              *int    `gorm:"column:state;type:int;default:0" json:"state"`                             // 状态
}

// TableName 设置表名
func (InventoryCheckRecordItem) TableName() string {
	return "inventory_check_record_item"
}

// GetId 获取ID
func (i InventoryCheckRecordItem) GetId() string {
	if i.Id == nil {
		return ""
	}
	return *i.Id
}
