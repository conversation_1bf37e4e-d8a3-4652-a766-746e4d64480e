package po

// InventoryCheckRecord 盘点记录实体
type InventoryCheckRecord struct {
	Id                    *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                        // ID
	VenueId               *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                      // 所属门店ID
	Handler               *string `gorm:"column:handler;type:varchar(64);default:''" json:"handler"`                       // 经手人
	Operator              *string `gorm:"column:operator;type:varchar(64);default:''" json:"operator"`                     // 操作人
	Time                  *int64  `gorm:"column:time;type:bigint;default:0" json:"time"`                                   // 盘点时间
	RecordNumber          *string `gorm:"column:record_number;type:varchar(64);default:''" json:"recordNumber"`            // 盘点单号
	Remark                *string `gorm:"column:remark;type:text" json:"remark"`                                           // 备注
	ProfitQuantityTotal   *int    `gorm:"column:profit_quantity_total;type:int;default:0" json:"profitQuantityTotal"`      // 盘盈数量合计
	LossQuantityTotal     *int    `gorm:"column:loss_quantity_total;type:int;default:0" json:"lossQuantityTotal"`          // 盘亏数量合计
	Ctime                 *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`                                 // 创建时间
	Utime                 *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`                                 // 更新时间
	State                 *int    `gorm:"column:state;type:int;default:0" json:"state"`                                    // 状态
	Version               *int    `gorm:"column:version;type:int;default:0" json:"version"`                                // 版本号
}

// InventoryCheckRecordItem 盘点记录明细实体
type InventoryCheckRecordItem struct {
	Id                   *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                    // ID
	CheckRecordId        *string `gorm:"column:check_record_id;type:varchar(64);default:''" json:"checkRecordId"`     // 盘点记录ID
	ProductId            *string `gorm:"column:product_id;type:varchar(64);default:''" json:"productId"`              // 商品ID
	StockQuantity        *int    `gorm:"column:stock_quantity;type:int;default:0" json:"stockQuantity"`               // 库存数量（盘点之前）
	CheckQuantity        *int    `gorm:"column:check_quantity;type:int;default:0" json:"checkQuantity"`               // 盘点数量（盘点之后）
	ProfitLossQuantity   *int    `gorm:"column:profit_loss_quantity;type:int;default:0" json:"profitLossQuantity"`    // 盈亏数量
	Ctime                *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`                             // 创建时间
	Utime                *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`                             // 更新时间
	State                *int    `gorm:"column:state;type:int;default:0" json:"state"`                                // 状态
}

// TableName 设置表名
func (InventoryCheckRecord) TableName() string {
	return "inventory_check_record"
}

// TableName 设置表名
func (InventoryCheckRecordItem) TableName() string {
	return "inventory_check_record_item"
}

// GetId 获取ID
func (i InventoryCheckRecord) GetId() string {
	if i.Id == nil {
		return ""
	}
	return *i.Id
}

// GetId 获取ID
func (i InventoryCheckRecordItem) GetId() string {
	if i.Id == nil {
		return ""
	}
	return *i.Id
}
