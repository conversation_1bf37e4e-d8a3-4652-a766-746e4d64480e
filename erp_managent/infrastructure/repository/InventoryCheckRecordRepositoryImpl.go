package repository

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"erp_managent/domain/inventory/model"
	"erp_managent/domain/inventory/repository"
)

// InventoryCheckRecordRepositoryImpl 盘点记录仓储实现
type InventoryCheckRecordRepositoryImpl struct {
	db *gorm.DB
}

// NewInventoryCheckRecordRepository 创建盘点记录仓储实例
func NewInventoryCheckRecordRepository(db *gorm.DB) repository.InventoryCheckRecordRepository {
	return &InventoryCheckRecordRepositoryImpl{
		db: db,
	}
}

// Create 创建盘点记录
func (r *InventoryCheckRecordRepositoryImpl) Create(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 保存主记录
		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("创建盘点记录失败: %w", err)
		}
		
		// 2. 保存明细记录
		if len(record.Items) > 0 {
			for i := range record.Items {
				record.Items[i].CheckRecordId = record.Id
			}
			if err := tx.Create(&record.Items).Error; err != nil {
				return fmt.Errorf("创建盘点明细失败: %w", err)
			}
		}
		
		return nil
	})
}

// FindById 根据ID查询盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error) {
	var record model.InventoryCheckRecord
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("id = ? AND state = 1", recordId).
		First(&record).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询盘点记录失败: %w", err)
	}
	
	return &record, nil
}

// FindByRecordNumber 根据盘点单号查询盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindByRecordNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error) {
	var record model.InventoryCheckRecord
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("record_number = ? AND state = 1", recordNumber).
		First(&record).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询盘点记录失败: %w", err)
	}
	
	return &record, nil
}

// FindByVenueId 根据门店ID查询盘点记录列表（分页）
func (r *InventoryCheckRecordRepositoryImpl) FindByVenueId(ctx *gin.Context, venueId string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error) {
	var records []*model.InventoryCheckRecord
	var total int64
	
	// 查询总数
	if err := r.db.WithContext(ctx).
		Model(&model.InventoryCheckRecord{}).
		Where("venue_id = ? AND state = 1", venueId).
		Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询盘点记录总数失败: %w", err)
	}
	
	// 查询分页数据
	offset := (pageNum - 1) * pageSize
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("venue_id = ? AND state = 1", venueId).
		Order("time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("查询盘点记录列表失败: %w", err)
	}
	
	return records, total, nil
}

// Search 搜索盘点记录（支持关键词搜索）
func (r *InventoryCheckRecordRepositoryImpl) Search(ctx *gin.Context, venueId, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error) {
	var records []*model.InventoryCheckRecord
	var total int64
	
	query := r.db.WithContext(ctx).
		Model(&model.InventoryCheckRecord{}).
		Where("venue_id = ? AND state = 1", venueId)
	
	if searchKey != "" {
		searchPattern := "%" + searchKey + "%"
		query = query.Where("record_number LIKE ? OR handler LIKE ? OR operator LIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}
	
	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("搜索盘点记录总数失败: %w", err)
	}
	
	// 查询分页数据
	offset := (pageNum - 1) * pageSize
	err := query.
		Preload("Items").
		Order("time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("搜索盘点记录失败: %w", err)
	}
	
	return records, total, nil
}

// Update 更新盘点记录
func (r *InventoryCheckRecordRepositoryImpl) Update(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 更新主记录
		if err := tx.Save(record).Error; err != nil {
			return fmt.Errorf("更新盘点记录失败: %w", err)
		}
		
		// 2. 删除原有明细
		if err := tx.Where("check_record_id = ?", record.Id).Delete(&model.InventoryCheckRecordItem{}).Error; err != nil {
			return fmt.Errorf("删除原有盘点明细失败: %w", err)
		}
		
		// 3. 创建新明细
		if len(record.Items) > 0 {
			for i := range record.Items {
				record.Items[i].CheckRecordId = record.Id
			}
			if err := tx.Create(&record.Items).Error; err != nil {
				return fmt.Errorf("创建新盘点明细失败: %w", err)
			}
		}
		
		return nil
	})
}

// Delete 删除盘点记录（软删除）
func (r *InventoryCheckRecordRepositoryImpl) Delete(ctx *gin.Context, recordId string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 软删除主记录
		if err := tx.Model(&model.InventoryCheckRecord{}).
			Where("id = ?", recordId).
			Update("state", 0).Error; err != nil {
			return fmt.Errorf("删除盘点记录失败: %w", err)
		}
		
		// 2. 软删除明细记录
		if err := tx.Model(&model.InventoryCheckRecordItem{}).
			Where("check_record_id = ?", recordId).
			Update("state", 0).Error; err != nil {
			return fmt.Errorf("删除盘点明细失败: %w", err)
		}
		
		return nil
	})
}

// FindItemsByRecordId 根据盘点记录ID查询明细项
func (r *InventoryCheckRecordRepositoryImpl) FindItemsByRecordId(ctx *gin.Context, recordId string) ([]model.InventoryCheckRecordItem, error) {
	var items []model.InventoryCheckRecordItem
	err := r.db.WithContext(ctx).
		Where("check_record_id = ? AND state = 1", recordId).
		Find(&items).Error
	
	if err != nil {
		return nil, fmt.Errorf("查询盘点明细失败: %w", err)
	}
	
	return items, nil
}

// CreateItems 批量创建明细项
func (r *InventoryCheckRecordRepositoryImpl) CreateItems(ctx *gin.Context, items []model.InventoryCheckRecordItem) error {
	if len(items) == 0 {
		return nil
	}
	
	if err := r.db.WithContext(ctx).Create(&items).Error; err != nil {
		return fmt.Errorf("批量创建盘点明细失败: %w", err)
	}
	
	return nil
}

// UpdateItems 批量更新明细项
func (r *InventoryCheckRecordRepositoryImpl) UpdateItems(ctx *gin.Context, items []model.InventoryCheckRecordItem) error {
	if len(items) == 0 {
		return nil
	}
	
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			if err := tx.Save(&item).Error; err != nil {
				return fmt.Errorf("更新盘点明细失败: %w", err)
			}
		}
		return nil
	})
}

// DeleteItems 删除明细项
func (r *InventoryCheckRecordRepositoryImpl) DeleteItems(ctx *gin.Context, recordId string) error {
	if err := r.db.WithContext(ctx).
		Model(&model.InventoryCheckRecordItem{}).
		Where("check_record_id = ?", recordId).
		Update("state", 0).Error; err != nil {
		return fmt.Errorf("删除盘点明细失败: %w", err)
	}
	
	return nil
}

// FindRecordsAfterTime 查询指定时间后的盘点记录（用于库存计算）
func (r *InventoryCheckRecordRepositoryImpl) FindRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InventoryCheckRecord, error) {
	var records []*model.InventoryCheckRecord
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("venue_id = ? AND time > ? AND state = 1", venueId, afterTime).
		Order("time ASC").
		Find(&records).Error
	
	if err != nil {
		return nil, fmt.Errorf("查询指定时间后的盘点记录失败: %w", err)
	}
	
	return records, nil
}

// CountByVenueId 统计门店的盘点记录数量
func (r *InventoryCheckRecordRepositoryImpl) CountByVenueId(ctx *gin.Context, venueId string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.InventoryCheckRecord{}).
		Where("venue_id = ? AND state = 1", venueId).
		Count(&count).Error
	
	if err != nil {
		return 0, fmt.Errorf("统计盘点记录数量失败: %w", err)
	}
	
	return count, nil
}

// FindLatestByVenueId 查询门店最新的盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindLatestByVenueId(ctx *gin.Context, venueId string) (*model.InventoryCheckRecord, error) {
	var record model.InventoryCheckRecord
	err := r.db.WithContext(ctx).
		Preload("Items").
		Where("venue_id = ? AND state = 1", venueId).
		Order("time DESC").
		First(&record).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询最新盘点记录失败: %w", err)
	}
	
	return &record, nil
}
