package repository

import (
	"voderpltvv/erp_managent/domain/print/model/winestorage"

	"github.com/gin-gonic/gin"
)

// WineStoragePrintRecordRepository 存酒打印记录仓储接口
type WineStoragePrintRecordRepository interface {
	// Save 保存存酒打印记录
	Save(ctx *gin.Context, record *winestorage.WineStoragePrintRecord) error

	// FindByID 根据ID查找存酒打印记录
	FindByID(ctx *gin.Context, id string) (*winestorage.WineStoragePrintRecord, error)

	// Delete 删除存酒打印记录
	Delete(ctx *gin.Context, id string) error

	// FindUniqueByStorageRecordID 根据存酒记录ID查询唯一的打印记录
	FindUniqueByStorageRecordID(ctx *gin.Context, storageRecordID string) (*winestorage.WineStoragePrintRecord, error)

	// Update 更新存酒打印记录
	Update(ctx *gin.Context, record *winestorage.WineStoragePrintRecord) error
}
