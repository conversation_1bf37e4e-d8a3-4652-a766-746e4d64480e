package repository

import (
	"voderpltvv/erp_managent/domain/print/model/winewithdraw"

	"github.com/gin-gonic/gin"
)

// WineWithdrawPrintRecordRepository 取酒打印记录仓储接口
type WineWithdrawPrintRecordRepository interface {
	// Save 保存取酒打印记录
	Save(ctx *gin.Context, record *winewithdraw.WineWithdrawPrintRecord) error

	// FindByID 根据ID查找取酒打印记录
	FindByID(ctx *gin.Context, id string) (*winewithdraw.WineWithdrawPrintRecord, error)

	// Delete 删除取酒打印记录
	Delete(ctx *gin.Context, id string) error

	// FindUniqueByWithdrawalRecordID 根据取酒记录ID查询唯一的打印记录
	FindUniqueByWithdrawalRecordID(ctx *gin.Context, withdrawalRecordID string) (*winewithdraw.WineWithdrawPrintRecord, error)

	// Update 更新取酒打印记录
	Update(ctx *gin.Context, record *winewithdraw.WineWithdrawPrintRecord) error
}
