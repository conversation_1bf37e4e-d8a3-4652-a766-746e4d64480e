package winerenewal

import (
	"encoding/json"
	"fmt"

	"voderpltvv/erp_managent/domain/print/model/valueobject"

	"github.com/samber/lo"
)

// WineRenewalBillData 存酒续存单数据实体
// 与前端定义的续存单数据结构保持一致
type WineRenewalBillData struct {
	VenueName       string                        `json:"venueName,omitempty"`    // 门店名称
	RenewalRecordId string                        `json:"renewalRecordId"`        // 续存记录ID
	StorageNo       string                        `json:"storageNo"`              // 存酒单号
	RenewalDate     string                        `json:"renewalDate"`            // 续存日期 (YYYY-MM-DD格式)
	RenewalTime     string                        `json:"renewalTime"`            // 续存时间 (HH:MM:SS格式)
	MemberInfo      valueobject.MemberInfo        `json:"memberInfo"`             // 会员信息
	RenewalItems    []valueobject.WineRenewalItem `json:"renewalItems"`           // 续存项目列表
	TotalQuantity   int                           `json:"totalQuantity"`          // 总数量
	TotalAmount     float64                       `json:"totalAmount"`            // 总金额
	OperatorName    string                        `json:"operatorName,omitempty"` // 操作人名称
	OperatorId      string                        `json:"operatorId,omitempty"`   // 操作人ID
	PrintTime       string                        `json:"printTime"`              // 打印时间
	Remark          string                        `json:"remark,omitempty"`       // 备注
	// 续存特有字段
	OriginalExpireDate string `json:"originalExpireDate,omitempty"` // 原过期日期
	NewExpireDate      string `json:"newExpireDate,omitempty"`      // 新过期日期
	ExtendedDays       int    `json:"extendedDays,omitempty"`       // 延长天数
}

// NewWineRenewalBillData 创建新的续存单数据实体
func NewWineRenewalBillData(renewalRecordId string) *WineRenewalBillData {
	return &WineRenewalBillData{
		RenewalRecordId: renewalRecordId,
		RenewalItems:    []valueobject.WineRenewalItem{},
		TotalQuantity:   0,
		TotalAmount:     0.0,
	}
}

// ToJSON 将续存单数据转换为JSON字符串
func (d *WineRenewalBillData) ToJSON() (string, error) {
	bytes, err := json.Marshal(d)
	if err != nil {
		return "", fmt.Errorf("序列化续存单数据失败: %w", err)
	}
	return string(bytes), nil
}

// AddRenewalItem 添加续存项目
func (d *WineRenewalBillData) AddRenewalItem(item valueobject.WineRenewalItem) {
	d.RenewalItems = append(d.RenewalItems, item)
	d.TotalQuantity += item.Quantity
	d.TotalAmount += item.TotalAmount
}

// SetMemberInfo 设置会员信息
func (d *WineRenewalBillData) SetMemberInfo(memberID, memberName, memberPhone, cardNo, levelName string) {
	d.MemberInfo = valueobject.MemberInfo{
		MemberID:    memberID,
		MemberName:  memberName,
		MemberPhone: memberPhone,
		CardNo:      cardNo,
		LevelName:   levelName,
	}
}

// CalculateExtendedDays 计算延长天数
func (d *WineRenewalBillData) CalculateExtendedDays() int {
	if len(d.RenewalItems) > 0 {
		// 使用第一个项目的延长天数作为整体延长天数
		return d.RenewalItems[0].ExtendedDays
	}
	return d.ExtendedDays
}

// Validate 验证续存单据数据的完整性
func (d *WineRenewalBillData) Validate() error {
	if d.RenewalRecordId == "" {
		return fmt.Errorf("续存记录ID不能为空")
	}
	if d.StorageNo == "" {
		return fmt.Errorf("存酒单号不能为空")
	}
	if len(d.RenewalItems) == 0 {
		return fmt.Errorf("续存项目列表不能为空")
	}
	if d.OperatorName == "" {
		return fmt.Errorf("操作人不能为空")
	}
	return nil
}

// GetItemsByProductName 根据商品名称获取续存项目
func (d *WineRenewalBillData) GetItemsByProductName(productName string) []valueobject.WineRenewalItem {
	return lo.Filter(d.RenewalItems, func(item valueobject.WineRenewalItem, index int) bool {
		return item.ProductName == productName
	})
}

// GetTotalItemsCount 获取商品种类总数
func (d *WineRenewalBillData) GetTotalItemsCount() int {
	return len(d.RenewalItems)
}

// UpdateTotals 更新总计数据
func (d *WineRenewalBillData) UpdateTotals() {
	d.TotalQuantity = lo.SumBy(d.RenewalItems, func(item valueobject.WineRenewalItem) int {
		return item.Quantity
	})
	d.TotalAmount = lo.SumBy(d.RenewalItems, func(item valueobject.WineRenewalItem) float64 {
		return item.TotalAmount
	})
}
