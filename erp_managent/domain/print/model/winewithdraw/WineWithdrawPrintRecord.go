package winewithdraw

import (
	"encoding/json"
	"fmt"
	"time"

	"voderpltvv/erp_managent/domain/print/model/common"
	"voderpltvv/erp_managent/domain/print/model/valueobject"
)

// WineWithdrawPrintRecord 取酒单打印记录实体
type WineWithdrawPrintRecord struct {
	*common.PrintRecordEntity                         // 基础打印记录实体
	Content                   *WineWithdrawalBillData // 取酒单内容
	WithdrawalRecordId        string                  // 取酒记录ID
}

// NewWineWithdrawPrintRecord 创建取酒单打印记录
func NewWineWithdrawPrintRecord(
	venueID string,
	sessionID string,
	withdrawalRecordId string,
	operatorID string,
	operatorName string,
) (*WineWithdrawPrintRecord, error) {
	// 参数校验
	if venueID == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	// 注意：取酒打印与sessionID无关，ProductStorage系统没有sessionId概念，允许为空
	if withdrawalRecordId == "" {
		return nil, fmt.Errorf("取酒记录ID不能为空")
	}

	// 创建基础实体
	baseRecord, err := common.NewPrintRecordEntity(
		venueID,
		string(valueobject.PrintTypeWineWithdraw),
		sessionID,
		operatorID,
		operatorName,
		"", // 内容为空，将使用专用Content结构
	)

	if err != nil {
		return nil, fmt.Errorf("创建取酒单打印记录失败: %w", err)
	}

	// 创建内容对象
	contentObj := NewWineWithdrawalBillData(sessionID)
	contentObj.WithdrawalRecordId = withdrawalRecordId

	// 创建取酒单记录
	record := &WineWithdrawPrintRecord{
		PrintRecordEntity:  baseRecord,
		Content:            contentObj,
		WithdrawalRecordId: withdrawalRecordId,
	}

	return record, nil
}

// GetWineWithdrawalBillData 获取取酒单数据
func (r *WineWithdrawPrintRecord) GetWineWithdrawalBillData() (*WineWithdrawalBillData, error) {
	if r.Content == nil {
		return nil, fmt.Errorf("取酒单内容为空")
	}
	return r.Content, nil
}

// SetWineWithdrawalBillData 设置取酒单数据
func (r *WineWithdrawPrintRecord) SetWineWithdrawalBillData(data map[string]interface{}) error {
	if r.Content == nil {
		r.Content = NewWineWithdrawalBillData(r.SessionID())
	}
	return r.Content.SetFromMap(data)
}

// ImportWineWithdrawalBillData 从JSON字符串导入取酒单数据
func (r *WineWithdrawPrintRecord) ImportWineWithdrawalBillData(jsonData string) error {
	if r.Content == nil {
		r.Content = NewWineWithdrawalBillData(r.SessionID())
	}
	return json.Unmarshal([]byte(jsonData), r.Content)
}

// ExportWineWithdrawalBillData 导出取酒单数据为JSON字符串
func (r *WineWithdrawPrintRecord) ExportWineWithdrawalBillData() (string, error) {
	if r.Content == nil {
		return "", fmt.Errorf("取酒单内容为空")
	}
	data, err := json.Marshal(r.Content)
	if err != nil {
		return "", fmt.Errorf("序列化取酒单数据失败: %w", err)
	}
	return string(data), nil
}

// SetPrintNoValue 设置打印单号
func (r *WineWithdrawPrintRecord) SetPrintNoValue(printNo string) {
	r.PrintRecordEntity.SetPrintNo(printNo)
}

// SetPrintTimeValue 设置打印时间
func (r *WineWithdrawPrintRecord) SetPrintTimeValue(printTime int64) {
	r.PrintRecordEntity.SetPrintTime(time.Unix(printTime/1000, 0))
}

// SetDeviceNameValue 设置设备名称
func (r *WineWithdrawPrintRecord) SetDeviceNameValue(deviceName string) {
	r.PrintRecordEntity.SetDeviceName(deviceName)
}

// SetStatusValue 设置打印状态
func (r *WineWithdrawPrintRecord) SetStatusValue(status int) {
	r.PrintRecordEntity.SetStatus(status, "")
}

// SetErrorMsgValue 设置错误信息
func (r *WineWithdrawPrintRecord) SetErrorMsgValue(errorMsg string) {
	r.PrintRecordEntity.SetStatus(1, errorMsg) // 设置为失败状态并记录错误信息
}

// SetRemarkValue 设置备注
func (r *WineWithdrawPrintRecord) SetRemarkValue(remark string) {
	r.PrintRecordEntity.SetRemark(remark)
}
