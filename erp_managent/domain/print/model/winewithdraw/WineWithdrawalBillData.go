package winewithdraw

import (
	"encoding/json"
	"fmt"

	"voderpltvv/erp_managent/domain/print/model/valueobject"

	"github.com/samber/lo"
)

// WineWithdrawalBillData 取酒单数据实体
// 与前端定义的取酒单数据结构保持一致
type WineWithdrawalBillData struct {
	VenueName           string                           `json:"venueName,omitempty"`           // 门店名称
	WithdrawalRecordId  string                           `json:"withdrawalRecordId"`            // 取酒记录ID
	SessionId           string                           `json:"sessionId"`                     // 场次ID
	WithdrawalNo        string                           `json:"withdrawalNo"`                  // 取酒单号
	WithdrawalDate      string                           `json:"withdrawalDate"`                // 取酒日期 (YYYY-MM-DD格式)
	WithdrawalTime      string                           `json:"withdrawalTime"`                // 取酒时间 (HH:MM:SS格式)
	MemberInfo          valueobject.MemberInfo           `json:"memberInfo"`                    // 会员信息
	Items               []valueobject.WineWithdrawalItem `json:"items"`                         // 取酒项目列表
	TotalQuantity       int                              `json:"totalQuantity"`                 // 总取酒数量
	TotalAmount         float64                          `json:"totalAmount"`                   // 总金额
	StorageLocation     string                           `json:"storageLocation,omitempty"`     // 原存放位置
	CashierName         string                           `json:"cashierName,omitempty"`         // 收银员名称
	CashierId           string                           `json:"cashierId,omitempty"`           // 收银员ID
	PrintTime           string                           `json:"printTime"`                     // 打印时间
	Remark              string                           `json:"remark,omitempty"`              // 备注
	WithdrawalReason    string                           `json:"withdrawalReason,omitempty"`    // 取酒原因
	RemainingItemsTotal int                              `json:"remainingItemsTotal,omitempty"` // 剩余总数量
}

// NewWineWithdrawalBillData 创建新的取酒单数据实体
func NewWineWithdrawalBillData(sessionId string) *WineWithdrawalBillData {
	return &WineWithdrawalBillData{
		SessionId:           sessionId,
		Items:               []valueobject.WineWithdrawalItem{},
		TotalQuantity:       0,
		TotalAmount:         0.0,
		RemainingItemsTotal: 0,
	}
}

// ToJSON 将取酒单数据转换为JSON字符串
func (d *WineWithdrawalBillData) ToJSON() (string, error) {
	bytes, err := json.Marshal(d)
	if err != nil {
		return "", fmt.Errorf("序列化取酒单数据失败: %w", err)
	}
	return string(bytes), nil
}

// FromJSON 从JSON字符串创建取酒单数据
func FromJSON(jsonStr string) (*WineWithdrawalBillData, error) {
	var data WineWithdrawalBillData
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return nil, fmt.Errorf("反序列化取酒单数据失败: %w", err)
	}
	return &data, nil
}

// SetFromMap 从map设置取酒单数据
func (d *WineWithdrawalBillData) SetFromMap(data map[string]interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("转换map数据失败: %w", err)
	}
	err = json.Unmarshal(bytes, d)
	if err != nil {
		return fmt.Errorf("设置取酒单数据失败: %w", err)
	}
	return nil
}

// AddWithdrawalItem 添加取酒项目
func (d *WineWithdrawalBillData) AddWithdrawalItem(item valueobject.WineWithdrawalItem) {
	d.Items = append(d.Items, item)
	d.calculateTotals()
}

// SetWithdrawalItems 设置取酒项目列表
func (d *WineWithdrawalBillData) SetWithdrawalItems(items []valueobject.WineWithdrawalItem) {
	d.Items = items
	d.calculateTotals()
}

// SetMemberInfo 设置会员信息
func (d *WineWithdrawalBillData) SetMemberInfo(memberInfo valueobject.MemberInfo) {
	d.MemberInfo = memberInfo
}

// SetCashierInfo 设置收银员信息
func (d *WineWithdrawalBillData) SetCashierInfo(cashierId, cashierName string) {
	d.CashierId = cashierId
	d.CashierName = cashierName
}

// GetItemsByProduct 根据商品ID获取取酒项目
func (d *WineWithdrawalBillData) GetItemsByProduct(productId string) []valueobject.WineWithdrawalItem {
	return lo.Filter(d.Items, func(item valueobject.WineWithdrawalItem, _ int) bool {
		return item.ProductID == productId
	})
}

// GetEmptyItems 获取已取完的项目（剩余数量为0）
func (d *WineWithdrawalBillData) GetEmptyItems() []valueobject.WineWithdrawalItem {
	return lo.Filter(d.Items, func(item valueobject.WineWithdrawalItem, _ int) bool {
		return item.RemainQuantity == 0
	})
}

// GetProductNames 获取所有商品名称列表
func (d *WineWithdrawalBillData) GetProductNames() []string {
	return lo.Map(d.Items, func(item valueobject.WineWithdrawalItem, _ int) string {
		return item.ProductName
	})
}

// HasProduct 检查是否包含指定商品
func (d *WineWithdrawalBillData) HasProduct(productId string) bool {
	return lo.ContainsBy(d.Items, func(item valueobject.WineWithdrawalItem) bool {
		return item.ProductID == productId
	})
}

// calculateTotals 计算总数量、总金额和剩余总数量，使用samber/lo优化
func (d *WineWithdrawalBillData) calculateTotals() {
	// 使用 lo.Reduce 计算总取酒数量
	d.TotalQuantity = lo.Reduce(d.Items, func(total int, item valueobject.WineWithdrawalItem, _ int) int {
		return total + item.Quantity
	}, 0)

	// 使用 lo.Reduce 计算总金额
	d.TotalAmount = lo.Reduce(d.Items, func(total float64, item valueobject.WineWithdrawalItem, _ int) float64 {
		return total + item.TotalAmount
	}, 0.0)

	// 使用 lo.Reduce 计算剩余总数量
	d.RemainingItemsTotal = lo.Reduce(d.Items, func(total int, item valueobject.WineWithdrawalItem, _ int) int {
		return total + item.RemainQuantity
	}, 0)
}
