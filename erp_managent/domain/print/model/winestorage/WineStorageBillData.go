package winestorage

import (
	"encoding/json"
	"fmt"

	"voderpltvv/erp_managent/domain/print/model/valueobject"

	"github.com/samber/lo"
)

// WineStorageBillData 存酒单数据实体
// 与前端定义的存酒单数据结构保持一致
type WineStorageBillData struct {
	VenueName         string                        `json:"venueName,omitempty"`         // 门店名称
	StorageRecordId   string                        `json:"storageRecordId"`             // 存酒记录ID
	SessionId         string                        `json:"sessionId"`                   // 场次ID
	StorageNo         string                        `json:"storageNo"`                   // 存酒单号
	StorageDate       string                        `json:"storageDate"`                 // 存酒日期 (YYYY-MM-DD格式)
	StorageTime       string                        `json:"storageTime"`                 // 存酒时间 (HH:MM:SS格式)
	MemberInfo        valueobject.MemberInfo        `json:"memberInfo"`                  // 会员信息
	Items             []valueobject.WineStorageItem `json:"items"`                       // 存酒项目列表
	TotalQuantity     int                           `json:"totalQuantity"`               // 总数量
	TotalAmount       float64                       `json:"totalAmount"`                 // 总金额
	ValidityDate      string                        `json:"validityDate,omitempty"`      // 有效期 (YYYY-MM-DD格式)
	StorageLocation   string                        `json:"storageLocation,omitempty"`   // 存放位置
	CashierName       string                        `json:"cashierName,omitempty"`       // 收银员名称
	CashierId         string                        `json:"cashierId,omitempty"`         // 收银员ID
	PrintTime         string                        `json:"printTime"`                   // 打印时间
	Remark            string                        `json:"remark,omitempty"`            // 备注
	StorageConditions string                        `json:"storageConditions,omitempty"` // 存储条件（如温度要求等）
}

// NewWineStorageBillData 创建新的存酒单数据实体
func NewWineStorageBillData(sessionId string) *WineStorageBillData {
	return &WineStorageBillData{
		SessionId:     sessionId,
		Items:         []valueobject.WineStorageItem{},
		TotalQuantity: 0,
		TotalAmount:   0.0,
	}
}

// ToJSON 将存酒单数据转换为JSON字符串
func (d *WineStorageBillData) ToJSON() (string, error) {
	bytes, err := json.Marshal(d)
	if err != nil {
		return "", fmt.Errorf("序列化存酒单数据失败: %w", err)
	}
	return string(bytes), nil
}

// FromJSON 从JSON字符串创建存酒单数据
func FromJSON(jsonStr string) (*WineStorageBillData, error) {
	var data WineStorageBillData
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return nil, fmt.Errorf("反序列化存酒单数据失败: %w", err)
	}
	return &data, nil
}

// SetFromMap 从map设置存酒单数据
func (d *WineStorageBillData) SetFromMap(data map[string]interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("转换map数据失败: %w", err)
	}
	err = json.Unmarshal(bytes, d)
	if err != nil {
		return fmt.Errorf("设置存酒单数据失败: %w", err)
	}
	return nil
}

// AddStorageItem 添加存酒项目
func (d *WineStorageBillData) AddStorageItem(item valueobject.WineStorageItem) {
	d.Items = append(d.Items, item)
	d.calculateTotals()
}

// SetStorageItems 设置存酒项目列表
func (d *WineStorageBillData) SetStorageItems(items []valueobject.WineStorageItem) {
	d.Items = items
	d.calculateTotals()
}

// SetMemberInfo 设置会员信息
func (d *WineStorageBillData) SetMemberInfo(memberInfo valueobject.MemberInfo) {
	d.MemberInfo = memberInfo
}

// SetCashierInfo 设置收银员信息
func (d *WineStorageBillData) SetCashierInfo(cashierId, cashierName string) {
	d.CashierId = cashierId
	d.CashierName = cashierName
}

// GetExpiredItems 获取已过期的存酒项目
func (d *WineStorageBillData) GetExpiredItems() []valueobject.WineStorageItem {
	return lo.Filter(d.Items, func(item valueobject.WineStorageItem, _ int) bool {
		// 这里需要实现过期判断逻辑，示例简单判断
		return item.ValidityDate != "" // 实际应该比较日期
	})
}

// GetItemsByProduct 根据商品ID获取存酒项目
func (d *WineStorageBillData) GetItemsByProduct(productId string) []valueobject.WineStorageItem {
	return lo.Filter(d.Items, func(item valueobject.WineStorageItem, _ int) bool {
		return item.ProductID == productId
	})
}

// calculateTotals 计算总数量和总金额，使用samber/lo优化
func (d *WineStorageBillData) calculateTotals() {
	// 使用 lo.Reduce 计算总数量
	d.TotalQuantity = lo.Reduce(d.Items, func(total int, item valueobject.WineStorageItem, _ int) int {
		return total + item.Quantity
	}, 0)

	// 使用 lo.Reduce 计算总金额
	d.TotalAmount = lo.Reduce(d.Items, func(total float64, item valueobject.WineStorageItem, _ int) float64 {
		return total + item.TotalAmount
	}, 0.0)
}
