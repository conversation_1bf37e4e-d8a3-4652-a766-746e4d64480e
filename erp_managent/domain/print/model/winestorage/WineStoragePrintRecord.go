package winestorage

import (
	"encoding/json"
	"fmt"
	"time"

	"voderpltvv/erp_managent/domain/print/model/common"
	"voderpltvv/erp_managent/domain/print/model/valueobject"
)

// WineStoragePrintRecord 存酒单打印记录实体
type WineStoragePrintRecord struct {
	*common.PrintRecordEntity                      // 基础打印记录实体
	Content                   *WineStorageBillData // 存酒单内容
	StorageRecordId           string               // 存酒记录ID
}

// NewWineStoragePrintRecord 创建存酒单打印记录
func NewWineStoragePrintRecord(
	venueID string,
	sessionID string,
	storageRecordId string,
	operatorID string,
	operatorName string,
) (*WineStoragePrintRecord, error) {
	// 参数校验
	if venueID == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	// 注意：存酒打印与sessionID无关，ProductStorage系统没有sessionId概念，允许为空
	if storageRecordId == "" {
		return nil, fmt.Errorf("存酒记录ID不能为空")
	}

	// 创建基础实体
	baseRecord, err := common.NewPrintRecordEntity(
		venueID,
		string(valueobject.PrintTypeWineStorage),
		sessionID,
		operatorID,
		operatorName,
		"", // 内容为空，将使用专用Content结构
	)

	if err != nil {
		return nil, fmt.Errorf("创建存酒单打印记录失败: %w", err)
	}

	// 创建内容对象
	contentObj := NewWineStorageBillData(sessionID)
	contentObj.StorageRecordId = storageRecordId

	// 创建存酒单记录
	record := &WineStoragePrintRecord{
		PrintRecordEntity: baseRecord,
		Content:           contentObj,
		StorageRecordId:   storageRecordId,
	}

	return record, nil
}

// GetWineStorageBillData 获取存酒单数据
func (r *WineStoragePrintRecord) GetWineStorageBillData() (*WineStorageBillData, error) {
	if r.Content == nil {
		return nil, fmt.Errorf("存酒单内容为空")
	}
	return r.Content, nil
}

// SetWineStorageBillData 设置存酒单数据
func (r *WineStoragePrintRecord) SetWineStorageBillData(data map[string]interface{}) error {
	if r.Content == nil {
		r.Content = NewWineStorageBillData(r.SessionID())
	}
	return r.Content.SetFromMap(data)
}

// ImportWineStorageBillData 从JSON字符串导入存酒单数据
func (r *WineStoragePrintRecord) ImportWineStorageBillData(jsonData string) error {
	if r.Content == nil {
		r.Content = NewWineStorageBillData(r.SessionID())
	}
	return json.Unmarshal([]byte(jsonData), r.Content)
}

// ExportWineStorageBillData 导出存酒单数据为JSON字符串
func (r *WineStoragePrintRecord) ExportWineStorageBillData() (string, error) {
	if r.Content == nil {
		return "", fmt.Errorf("存酒单内容为空")
	}
	data, err := json.Marshal(r.Content)
	if err != nil {
		return "", fmt.Errorf("序列化存酒单数据失败: %w", err)
	}
	return string(data), nil
}

// SetPrintNoValue 设置打印单号
func (r *WineStoragePrintRecord) SetPrintNoValue(printNo string) {
	r.PrintRecordEntity.SetPrintNo(printNo)
}

// SetPrintTimeValue 设置打印时间
func (r *WineStoragePrintRecord) SetPrintTimeValue(printTime int64) {
	r.PrintRecordEntity.SetPrintTime(time.Unix(printTime/1000, 0))
}

// SetDeviceNameValue 设置设备名称
func (r *WineStoragePrintRecord) SetDeviceNameValue(deviceName string) {
	r.PrintRecordEntity.SetDeviceName(deviceName)
}

// SetStatusValue 设置打印状态
func (r *WineStoragePrintRecord) SetStatusValue(status int) {
	r.PrintRecordEntity.SetStatus(status, "")
}

// SetErrorMsgValue 设置错误信息
func (r *WineStoragePrintRecord) SetErrorMsgValue(errorMsg string) {
	r.PrintRecordEntity.SetStatus(1, errorMsg) // 设置为失败状态并记录错误信息
}

// SetRemarkValue 设置备注
func (r *WineStoragePrintRecord) SetRemarkValue(remark string) {
	r.PrintRecordEntity.SetRemark(remark)
}
