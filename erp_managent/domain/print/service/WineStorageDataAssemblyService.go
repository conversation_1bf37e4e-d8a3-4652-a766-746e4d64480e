package service

import (
	"fmt"
	"time"

	"voderpltvv/erp_managent/domain/print/model/valueobject"
	"voderpltvv/erp_managent/domain/print/model/winestorage"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// WineStorageDataAssemblyService 存酒数据组装服务接口
// 负责封装组装存酒单数据的复杂逻辑
type WineStorageDataAssemblyService interface {
	// AssembleWineStorageData 组装存酒单数据
	// 根据输入参数聚合所有需要的数据
	// 返回完整组装好的WineStorageBillData对象、操作员ID、操作员姓名或错误
	AssembleWineStorageData(ctx *gin.Context, venueId string, storageRecordId string) (*winestorage.WineStorageBillData, string, string, error)
}

// wineStorageDataAssemblyServiceImpl 存酒数据组装服务实现
type wineStorageDataAssemblyServiceImpl struct {
	venueService               *impl.VenueService
	memberService              *impl.MemberService
	productStorageService      *impl.ProductStorageService
	productStorageOrderService *impl.ProductStorageOrderService
}

// NewWineStorageDataAssemblyService 创建存酒数据组装服务实例
func NewWineStorageDataAssemblyService() WineStorageDataAssemblyService {
	return &wineStorageDataAssemblyServiceImpl{
		venueService:               &impl.VenueService{},
		memberService:              &impl.MemberService{},
		productStorageService:      &impl.ProductStorageService{},
		productStorageOrderService: impl.NewProductStorageOrderService(),
	}
}

// AssembleWineStorageData 组装存酒单数据
func (s *wineStorageDataAssemblyServiceImpl) AssembleWineStorageData(
	ctx *gin.Context,
	venueId string,
	storageRecordId string,
) (*winestorage.WineStorageBillData, string, string, error) {
	// 参数验证
	if venueId == "" {
		return nil, "", "", fmt.Errorf("门店ID不能为空")
	}
	if storageRecordId == "" {
		return nil, "", "", fmt.Errorf("存酒记录ID不能为空")
	}

	// 1. 根据storageRecordId获取存酒单信息
	// storageRecordId在打印系统中对应ProductStorageOrder的orderNo
	storageOrder, err := s.productStorageOrderService.GetOrderByOrderNo(ctx, storageRecordId)
	if err != nil {
		return nil, "", "", fmt.Errorf("获取存酒单失败: %v", err)
	}
	if storageOrder == nil {
		return nil, "", "", fmt.Errorf("存酒单不存在: %s", storageRecordId)
	}

	// 2. 获取存酒单对应的商品明细
	storageItems, err := s.productStorageService.FindByParentOrderNo(ctx, *storageOrder.OrderNo)
	if err != nil {
		return nil, "", "", fmt.Errorf("获取存酒明细失败: %v", err)
	}

	// 3. 获取门店信息
	venueName := ""
	if storageOrder.VenueId != nil && *storageOrder.VenueId != "" {
		venue, err := s.venueService.FindVenueById(ctx, *storageOrder.VenueId)
		if err == nil && venue != nil && venue.Name != nil {
			venueName = *venue.Name
		}
	}

	// 4. 组装会员信息
	memberInfo := s.assembleMemberInfo(storageOrder)

	// 5. 组装存酒项目列表
	wineStorageItems := s.assembleWineStorageItems(storageItems)

	// 6. 计算总数量和总金额
	totalQuantity := lo.SumBy(storageItems, func(item *po.ProductStorage) int {
		if item.Quantity != nil {
			return *item.Quantity
		}
		return 0
	})

	// 注意：ProductStorage表中没有价格信息，总金额设为0
	// 如果需要价格信息，需要关联商品表或从订单系统获取
	totalAmount := 0.0

	// 7. 格式化时间
	storageTime := time.Unix(*storageOrder.StorageTime, 0)
	storageDate := storageTime.Format("2006-01-02")
	storageTimeStr := storageTime.Format("15:04:05")
	printTime := time.Now().Format("2006-01-02 15:04:05")

	// 8. 组装存酒单数据
	billData := &winestorage.WineStorageBillData{
		VenueName:         venueName,
		StorageRecordId:   *storageOrder.OrderNo,
		SessionId:         "", // ProductStorage系统中没有sessionId概念，设为空
		StorageNo:         *storageOrder.OrderNo,
		StorageDate:       storageDate,
		StorageTime:       storageTimeStr,
		MemberInfo:        memberInfo,
		Items:             wineStorageItems,
		TotalQuantity:     totalQuantity,
		TotalAmount:       totalAmount,
		ValidityDate:      "", // 如果有过期时间逻辑，需要从业务规则计算
		StorageLocation:   s.getStorageLocation(storageOrder, storageItems),
		CashierName:       s.getCashierName(storageOrder),
		CashierId:         s.getCashierId(storageOrder),
		PrintTime:         printTime,
		Remark:            s.getRemark(storageOrder),
		StorageConditions: "", // 存储条件信息，如果有的话从配置获取
	}

	return billData, s.getCashierId(storageOrder), s.getCashierName(storageOrder), nil
}

// assembleMemberInfo 组装客户信息（包括会员和非会员）
// 对于会员客户：CardNo和LevelName有值
// 对于非会员客户：CardNo和LevelName为空，但MemberID、MemberName、MemberPhone仍有值
func (s *wineStorageDataAssemblyServiceImpl) assembleMemberInfo(order *po.ProductStorageOrder) valueobject.MemberInfo {
	memberInfo := valueobject.MemberInfo{}

	// 基本客户信息（会员和非会员都有）
	if order.CustomerId != nil {
		memberInfo.MemberID = *order.CustomerId
	}
	if order.CustomerName != nil {
		memberInfo.MemberName = *order.CustomerName
	}
	if order.PhoneNumber != nil {
		memberInfo.MemberPhone = *order.PhoneNumber
	}

	// 会员专属信息（仅会员客户有值）
	if order.MemberCardNumber != nil && *order.MemberCardNumber != "" {
		memberInfo.CardNo = *order.MemberCardNumber
	} else if order.MemberCardNo != nil && *order.MemberCardNo != "" {
		memberInfo.CardNo = *order.MemberCardNo
	}

	// 判断是否为会员客户
	isMemb := memberInfo.CardNo != ""

	// 会员等级信息（仅会员有，非会员为空）
	if isMemb {
		// TODO: 通过会员服务获取会员等级信息
		// memberLevel, err := s.memberService.GetMemberLevel(ctx, memberInfo.CardNo)
		// if err == nil && memberLevel != nil {
		//     memberInfo.LevelName = memberLevel.LevelName
		// }
		memberInfo.LevelName = "" // 暂时设为空，待实现会员等级查询
	} else {
		memberInfo.LevelName = "" // 非会员客户等级为空
	}

	return memberInfo
}

// assembleWineStorageItems 组装存酒项目列表
func (s *wineStorageDataAssemblyServiceImpl) assembleWineStorageItems(storageItems []*po.ProductStorage) []valueobject.WineStorageItem {
	return lo.Map(storageItems, func(item *po.ProductStorage, _ int) valueobject.WineStorageItem {
		wineItem := valueobject.WineStorageItem{}

		if item.ProductId != nil {
			wineItem.ProductID = *item.ProductId
		}
		if item.ProductName != nil {
			wineItem.ProductName = *item.ProductName
		}
		if item.ProductType != nil {
			wineItem.Brand = *item.ProductType // 暂时用产品类型作为品牌
		}
		if item.ProductSpec != nil {
			wineItem.Specification = *item.ProductSpec
		}
		if item.Quantity != nil {
			wineItem.Quantity = *item.Quantity
		}
		if item.Remark != nil {
			wineItem.Remark = *item.Remark
		}

		// 价格信息需要从商品表获取，这里暂时设为0
		wineItem.Price = 0.0

		return wineItem
	})
}

// getStorageLocation 获取存放位置
func (s *wineStorageDataAssemblyServiceImpl) getStorageLocation(order *po.ProductStorageOrder, items []*po.ProductStorage) string {
	// 优先使用订单中的存放位置
	if order.StorageRoomName != nil && *order.StorageRoomName != "" {
		return *order.StorageRoomName
	}

	// 如果订单中没有，使用第一个商品的存放位置
	if len(items) > 0 && items[0].StorageLocation != nil {
		return *items[0].StorageLocation
	}

	return ""
}

// getCashierName 获取收银员名称
func (s *wineStorageDataAssemblyServiceImpl) getCashierName(order *po.ProductStorageOrder) string {
	if order.OperatorName != nil {
		return *order.OperatorName
	}
	return ""
}

// getCashierId 获取收银员ID
func (s *wineStorageDataAssemblyServiceImpl) getCashierId(order *po.ProductStorageOrder) string {
	if order.OperatorId != nil {
		return *order.OperatorId
	}
	return ""
}

// getRemark 获取备注
func (s *wineStorageDataAssemblyServiceImpl) getRemark(order *po.ProductStorageOrder) string {
	if order.Remark != nil {
		return *order.Remark
	}
	return ""
}
