package service

import (
	"fmt"
	"time"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/domain/print/model/opentable"
	"voderpltvv/erp_managent/service/impl"

	"github.com/gin-gonic/gin"
)

// CancelOpenTableDataAssemblyService 取消开台单数据组装服务接口
type CancelOpenTableDataAssemblyService interface {
	// AssembleCancelOpenTableReceiptData 组装取消开台单数据
	AssembleCancelOpenTableReceiptData(
		ctx *gin.Context,
		venueId string,
		originalSessionId string,
		operatorId string,
		cancelReason string,
		refundAmount string,
		payBillIds []string,
	) (*opentable.CancelOpenTableReceiptData, string, string, error)
}

// cancelOpenTableDataAssemblyServiceImpl 取消开台单数据组装服务实现
type cancelOpenTableDataAssemblyServiceImpl struct {
	venueService    *impl.VenueService
	employeeService *impl.EmployeeService
	roomService     *impl.RoomService
	sessionService  *impl.SessionService
	payBillService  *impl.PayBillService
}

// NewCancelOpenTableDataAssemblyService 创建取消开台单数据组装服务实例
func NewCancelOpenTableDataAssemblyService() CancelOpenTableDataAssemblyService {
	return &cancelOpenTableDataAssemblyServiceImpl{
		venueService:    &impl.VenueService{},
		employeeService: &impl.EmployeeService{},
		roomService:     &impl.RoomService{},
		sessionService:  &impl.SessionService{},
		payBillService:  &impl.PayBillService{},
	}
}

// AssembleCancelOpenTableReceiptData 组装取消开台单数据
func (s *cancelOpenTableDataAssemblyServiceImpl) AssembleCancelOpenTableReceiptData(
	ctx *gin.Context,
	venueId string,
	originalSessionId string,
	operatorId string,
	cancelReason string,
	refundAmount string,
	payBillIds []string,
) (*opentable.CancelOpenTableReceiptData, string, string, error) {
	// 参数验证
	if venueId == "" {
		return nil, "", "", fmt.Errorf("门店ID不能为空")
	}
	if originalSessionId == "" {
		return nil, "", "", fmt.Errorf("原开台单会话ID不能为空")
	}

	// 创建数据实体
	result := opentable.NewCancelOpenTableReceiptData()

	// 设置会话ID
	result.SetSessionID(originalSessionId)

	// 设置取消时间
	result.SetCancelTime(time.Now().Format("2006-01-02 15:04:05"))

	// 设置取消原因
	result.SetCancelReason(cancelReason)

	// 计算退款金额
	finalRefundAmount := refundAmount
	if finalRefundAmount == "" && len(payBillIds) > 0 {
		// 如果没有设置退款金额，则通过账单ID计算
		totalRefundAmount := int64(0)
		for _, payBillId := range payBillIds {
			payBill, err := s.payBillService.FindPayBillById(ctx, payBillId)
			if err == nil && payBill != nil && payBill.TotalFee != nil {
				totalRefundAmount += *payBill.TotalFee
			}
		}
		finalRefundAmount = fmt.Sprintf("%d", totalRefundAmount)
	}

	// 设置退款金额
	result.SetRefundAmount(finalRefundAmount)

	// 获取操作员信息
	var finalOperatorId, finalOperatorName string
	if operatorId != "" {
		finalOperatorId = operatorId
		employee, err := s.employeeService.FindEmployeeById(ctx, operatorId)
		if err == nil && employee != nil && employee.Name != nil {
			finalOperatorName = *employee.Name
		}
		result.SetCancelOperator(finalOperatorName)
	}

	// 获取店铺信息
	venue, err := s.venueService.FindVenueById(ctx, venueId)
	if err == nil && venue != nil && venue.Name != nil {
		result.SetShopName(*venue.Name)
	}

	// 通过会话ID获取Session信息
	sessions, err := s.sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{
		SessionId: &originalSessionId,
		VenueId:   &venueId,
	})
	if err == nil && sessions != nil && len(*sessions) > 0 {
		session := (*sessions)[0]
		// 获取房间信息
		if session.RoomId != nil {
			room, err := s.roomService.FindRoomById(ctx, *session.RoomId)
			if err == nil && room != nil && room.Name != nil {
				result.SetRoomName(*room.Name)
			}
		}

		// 设置开台时间
		if session.StartTime != nil {
			result.SetOpenTime(time.Unix(*session.StartTime, 0).Format("2006-01-02 15:04:05"))
		}
	}

	return result, finalOperatorId, finalOperatorName, nil
}
