package service

import (
	"fmt"
	"time"

	"voderpltvv/erp_managent/domain/print/model/valueobject"
	"voderpltvv/erp_managent/domain/print/model/winewithdraw"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// WineWithdrawalDataAssemblyService 取酒数据组装服务接口
// 负责封装组装取酒单数据的复杂逻辑
type WineWithdrawalDataAssemblyService interface {
	// AssembleWineWithdrawalData 组装取酒单数据
	// 根据输入参数聚合所有需要的数据
	// 返回完整组装好的WineWithdrawalBillData对象、操作员ID、操作员姓名或错误
	AssembleWineWithdrawalData(ctx *gin.Context, venueId string, withdrawalRecordId string) (*winewithdraw.WineWithdrawalBillData, string, string, error)
}

// wineWithdrawalDataAssemblyServiceImpl 取酒数据组装服务实现
type wineWithdrawalDataAssemblyServiceImpl struct {
	productStorageService          *impl.ProductStorageService
	productStorageOrderService     *impl.ProductStorageOrderService
	productStorageOperationService *impl.ProductStorageOperationLogService
	venueService                   *impl.VenueService
	memberService                  *impl.MemberService
}

// NewWineWithdrawalDataAssemblyService 创建取酒数据组装服务实例
func NewWineWithdrawalDataAssemblyService() WineWithdrawalDataAssemblyService {
	return &wineWithdrawalDataAssemblyServiceImpl{
		productStorageService:          &impl.ProductStorageService{},
		productStorageOrderService:     impl.NewProductStorageOrderService(),
		productStorageOperationService: impl.NewProductStorageOperationLogService(),
		venueService:                   &impl.VenueService{},
		memberService:                  &impl.MemberService{},
	}
}

// AssembleWineWithdrawalData 组装取酒单数据
func (s *wineWithdrawalDataAssemblyServiceImpl) AssembleWineWithdrawalData(
	ctx *gin.Context,
	venueId string,
	withdrawalRecordId string,
) (*winewithdraw.WineWithdrawalBillData, string, string, error) {
	// 参数验证
	if venueId == "" {
		return nil, "", "", fmt.Errorf("门店ID不能为空")
	}
	if withdrawalRecordId == "" {
		return nil, "", "", fmt.Errorf("取酒记录ID不能为空")
	}

	// 1. 根据withdrawalRecordId获取取酒操作记录
	// withdrawalRecordId在打印系统中对应ProductStorageOperationLog的orderNo
	operationLog, err := s.getWithdrawalOperationLog(ctx, withdrawalRecordId)
	if err != nil {
		return nil, "", "", fmt.Errorf("获取取酒操作记录失败: %v", err)
	}
	if operationLog == nil {
		return nil, "", "", fmt.Errorf("取酒操作记录不存在: %s", withdrawalRecordId)
	}

	// 2. 根据操作记录获取原始存酒记录
	storageRecord, err := s.productStorageService.FindProductStorageById(ctx, *operationLog.StorageId)
	if err != nil {
		return nil, "", "", fmt.Errorf("获取原始存酒记录失败: %v", err)
	}
	if storageRecord == nil {
		return nil, "", "", fmt.Errorf("原始存酒记录不存在")
	}

	// 3. 获取存酒单信息
	storageOrder, err := s.productStorageOrderService.GetOrderByOrderNo(ctx, *storageRecord.OrderNo)
	if err != nil {
		return nil, "", "", fmt.Errorf("获取存酒单失败: %v", err)
	}

	// 4. 获取门店信息
	venueName := ""
	if storageRecord.VenueId != nil && *storageRecord.VenueId != "" {
		venue, err := s.venueService.FindVenueById(ctx, *storageRecord.VenueId)
		if err == nil && venue != nil && venue.Name != nil {
			venueName = *venue.Name
		}
	}

	// 5. 组装会员信息
	memberInfo := s.assembleMemberInfo(storageOrder, storageRecord)

	// 6. 组装取酒项目列表
	wineWithdrawalItems := s.assembleWineWithdrawalItems(storageRecord, operationLog)

	// 7. 计算数量信息
	withdrawalQuantity := 0
	if operationLog.Quantity != nil {
		withdrawalQuantity = *operationLog.Quantity
	}

	remainingQuantity := 0
	if operationLog.BalanceQty != nil {
		remainingQuantity = *operationLog.BalanceQty
	}

	// 注意：ProductStorage表中没有价格信息，总金额设为0
	totalAmount := 0.0

	// 8. 格式化时间
	withdrawalTime := time.Unix(*operationLog.OperationTime, 0)
	withdrawalDate := withdrawalTime.Format("2006-01-02")
	withdrawalTimeStr := withdrawalTime.Format("15:04:05")
	printTime := time.Now().Format("2006-01-02 15:04:05")

	// 9. 生成取酒单号（基于操作记录的orderNo）
	withdrawalNo := *operationLog.OrderNo

	// 10. 组装取酒单数据
	billData := &winewithdraw.WineWithdrawalBillData{
		VenueName:           venueName,
		WithdrawalRecordId:  withdrawalRecordId,
		SessionId:           "", // ProductStorage系统中没有sessionId概念，设为空
		WithdrawalNo:        withdrawalNo,
		WithdrawalDate:      withdrawalDate,
		WithdrawalTime:      withdrawalTimeStr,
		MemberInfo:          memberInfo,
		Items:               wineWithdrawalItems,
		TotalQuantity:       withdrawalQuantity,
		TotalAmount:         totalAmount,
		RemainingItemsTotal: remainingQuantity,
		WithdrawalReason:    s.getWithdrawalReason(operationLog),
		StorageLocation:     s.getStorageLocation(storageRecord),
		CashierName:         s.getCashierName(operationLog),
		CashierId:           s.getCashierId(operationLog),
		PrintTime:           printTime,
		Remark:              s.getRemark(operationLog),
	}

	return billData, s.getCashierId(operationLog), s.getCashierName(operationLog), nil
}

// getWithdrawalOperationLog 获取取酒操作记录
func (s *wineWithdrawalDataAssemblyServiceImpl) getWithdrawalOperationLog(ctx *gin.Context, withdrawalRecordId string) (*po.ProductStorageOperationLog, error) {
	// 这里需要根据实际的ProductStorageOperationLogService实现来查询
	// 由于没有直接通过orderNo查询的方法，这里可能需要扩展服务或使用其他方式
	// 暂时使用ID查询，实际应该根据业务需求调整

	// TODO: 需要在ProductStorageOperationLogService中添加通过orderNo查询的方法
	// 或者根据实际的withdrawalRecordId格式进行查询

	// 临时实现：假设withdrawalRecordId就是操作记录的ID
	return s.getOperationLogById(ctx, withdrawalRecordId)
}

// getOperationLogById 根据ID获取操作记录
func (s *wineWithdrawalDataAssemblyServiceImpl) getOperationLogById(ctx *gin.Context, id string) (*po.ProductStorageOperationLog, error) {
	// 使用ProductStorageOperationLogService的FindById方法
	return s.productStorageOperationService.FindById(ctx, id)
}

// assembleMemberInfo 组装客户信息（包括会员和非会员）
// 对于会员客户：CardNo和LevelName有值
// 对于非会员客户：CardNo和LevelName为空，但MemberID、MemberName、MemberPhone仍有值
func (s *wineWithdrawalDataAssemblyServiceImpl) assembleMemberInfo(order *po.ProductStorageOrder, storage *po.ProductStorage) valueobject.MemberInfo {
	memberInfo := valueobject.MemberInfo{}

	// 基本客户信息（会员和非会员都有）
	// 优先使用存酒单中的会员信息
	if order != nil {
		if order.CustomerId != nil {
			memberInfo.MemberID = *order.CustomerId
		}
		if order.CustomerName != nil {
			memberInfo.MemberName = *order.CustomerName
		}
		if order.PhoneNumber != nil {
			memberInfo.MemberPhone = *order.PhoneNumber
		}
	}

	// 如果存酒单信息不完整，使用存酒记录中的信息作为补充
	if memberInfo.MemberID == "" && storage.CustomerId != nil {
		memberInfo.MemberID = *storage.CustomerId
	}
	if memberInfo.MemberName == "" && storage.CustomerName != nil {
		memberInfo.MemberName = *storage.CustomerName
	}
	if memberInfo.MemberPhone == "" && storage.PhoneNumber != nil {
		memberInfo.MemberPhone = *storage.PhoneNumber
	}

	// 会员专属信息（仅会员客户有值）
	// 优先从存酒单获取会员卡信息
	if order != nil {
		if order.MemberCardNumber != nil && *order.MemberCardNumber != "" {
			memberInfo.CardNo = *order.MemberCardNumber
		} else if order.MemberCardNo != nil && *order.MemberCardNo != "" {
			memberInfo.CardNo = *order.MemberCardNo
		}
	}

	// 如果存酒单中没有会员卡信息，从存酒记录中获取
	if memberInfo.CardNo == "" && storage.MemberCardNumber != nil && *storage.MemberCardNumber != "" {
		memberInfo.CardNo = *storage.MemberCardNumber
	} else if memberInfo.CardNo == "" && storage.MemberCardNo != nil && *storage.MemberCardNo != "" {
		memberInfo.CardNo = *storage.MemberCardNo
	}

	// 判断是否为会员客户
	isMemb := memberInfo.CardNo != ""

	// 会员等级信息（仅会员有，非会员为空）
	if isMemb {
		// TODO: 通过会员服务获取会员等级信息
		// memberLevel, err := s.memberService.GetMemberLevel(ctx, memberInfo.CardNo)
		// if err == nil && memberLevel != nil {
		//     memberInfo.LevelName = memberLevel.LevelName
		// }
		memberInfo.LevelName = "" // 暂时设为空，待实现会员等级查询
	} else {
		memberInfo.LevelName = "" // 非会员客户等级为空
	}

	return memberInfo
}

// assembleWineWithdrawalItems 组装取酒项目列表
func (s *wineWithdrawalDataAssemblyServiceImpl) assembleWineWithdrawalItems(storage *po.ProductStorage, operation *po.ProductStorageOperationLog) []valueobject.WineWithdrawalItem {
	// 基于存酒记录和操作记录组装取酒项目
	wineItem := valueobject.WineWithdrawalItem{}

	if storage.ProductId != nil {
		wineItem.ProductID = *storage.ProductId
	}
	if storage.ProductName != nil {
		wineItem.ProductName = *storage.ProductName
	}
	if storage.ProductType != nil {
		wineItem.Brand = *storage.ProductType // 暂时用产品类型作为品牌
	}
	if storage.ProductSpec != nil {
		wineItem.Specification = *storage.ProductSpec
	}
	if operation.Quantity != nil {
		wineItem.Quantity = *operation.Quantity
	}
	if operation.BalanceQty != nil {
		wineItem.RemainQuantity = *operation.BalanceQty
	}
	if operation.Remark != nil {
		wineItem.Remark = *operation.Remark
	}

	// 价格信息需要从商品表获取，这里暂时设为0
	wineItem.Price = 0.0

	return []valueobject.WineWithdrawalItem{wineItem}
}

// getWithdrawalReason 获取取酒原因
func (s *wineWithdrawalDataAssemblyServiceImpl) getWithdrawalReason(operation *po.ProductStorageOperationLog) string {
	if operation.OperationName != nil {
		return *operation.OperationName
	}
	return "正常取酒"
}

// getStorageLocation 获取存放位置
func (s *wineWithdrawalDataAssemblyServiceImpl) getStorageLocation(storage *po.ProductStorage) string {
	if storage.StorageLocation != nil {
		return *storage.StorageLocation
	}
	if storage.StorageRoomName != nil {
		return *storage.StorageRoomName
	}
	return ""
}

// getCashierName 获取收银员名称
func (s *wineWithdrawalDataAssemblyServiceImpl) getCashierName(operation *po.ProductStorageOperationLog) string {
	if operation.OperatorName != nil {
		return *operation.OperatorName
	}
	return ""
}

// getCashierId 获取收银员ID
func (s *wineWithdrawalDataAssemblyServiceImpl) getCashierId(operation *po.ProductStorageOperationLog) string {
	if operation.OperatorId != nil {
		return *operation.OperatorId
	}
	return ""
}

// getRemark 获取备注
func (s *wineWithdrawalDataAssemblyServiceImpl) getRemark(operation *po.ProductStorageOperationLog) string {
	if operation.Remark != nil {
		return *operation.Remark
	}
	return ""
}
