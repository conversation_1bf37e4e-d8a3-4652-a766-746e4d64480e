package service

import (
	"github.com/gin-gonic/gin"
	"erp_managent/domain/inventory/model"
)

// InventoryCheckRecordService 盘点记录领域服务接口
type InventoryCheckRecordService interface {
	// CreateCheckRecord 创建盘点记录
	CreateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error
	
	// GetCheckRecordById 根据ID获取盘点记录
	GetCheckRecordById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error)
	
	// GetCheckRecordByNumber 根据盘点单号获取盘点记录
	GetCheckRecordByNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error)
	
	// GetCheckRecordsByVenue 获取门店的盘点记录列表
	GetCheckRecordsByVenue(ctx *gin.Context, venueId string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
	
	// SearchCheckRecords 搜索盘点记录
	SearchCheckRecords(ctx *gin.Context, venueId, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
	
	// UpdateCheckRecord 更新盘点记录
	UpdateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error
	
	// DeleteCheckRecord 删除盘点记录
	DeleteCheckRecord(ctx *gin.Context, recordId string) error
	
	// GenerateRecordNumber 生成盘点单号
	GenerateRecordNumber(ctx *gin.Context, venueId string) (string, error)
	
	// ValidateCheckRecord 验证盘点记录
	ValidateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error
	
	// CalculateCheckAdjustment 计算盘点调整增量（用于库存分析）
	CalculateCheckAdjustment(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error)
	
	// GetCheckRecordsAfterTime 获取指定时间后的盘点记录
	GetCheckRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InventoryCheckRecord, error)
	
	// GetLatestCheckRecord 获取门店最新的盘点记录
	GetLatestCheckRecord(ctx *gin.Context, venueId string) (*model.InventoryCheckRecord, error)
}
