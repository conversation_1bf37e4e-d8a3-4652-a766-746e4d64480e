package impl

import (
	"fmt"
	"time"
	"github.com/gin-gonic/gin"
	"erp_managent/domain/inventory/model"
	"erp_managent/domain/inventory/repository"
	"erp_managent/domain/inventory/service"
	"erp_managent/utils"
)

// InventoryCheckRecordServiceImpl 盘点记录领域服务实现
type InventoryCheckRecordServiceImpl struct {
	checkRecordRepository repository.InventoryCheckRecordRepository
}

// NewInventoryCheckRecordService 创建盘点记录领域服务实例
func NewInventoryCheckRecordService(checkRecordRepository repository.InventoryCheckRecordRepository) service.InventoryCheckRecordService {
	return &InventoryCheckRecordServiceImpl{
		checkRecordRepository: checkRecordRepository,
	}
}

// CreateCheckRecord 创建盘点记录
func (s *InventoryCheckRecordServiceImpl) CreateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	// 1. 验证盘点记录
	if err := s.ValidateCheckRecord(ctx, record); err != nil {
		return err
	}
	
	// 2. 设置基本信息
	now := time.Now()
	record.SetId(utils.GenerateId())
	record.SetTime(now)
	record.Ctime = now.Unix()
	record.Utime = now.Unix()
	record.State = 1
	record.Version = 1
	
	// 3. 处理明细项
	for i := range record.Items {
		record.Items[i].Id = utils.GenerateId()
		record.Items[i].CheckRecordId = record.GetId()
		record.Items[i].CalculateProfitLoss() // 计算盈亏数量
		record.Items[i].Ctime = now.Unix()
		record.Items[i].Utime = now.Unix()
		record.Items[i].State = 1
	}
	
	// 4. 计算汇总数据
	record.CalculateTotals()
	
	// 5. 生成盘点单号
	if record.GetRecordNumber() == "" {
		recordNumber, err := s.GenerateRecordNumber(ctx, record.GetVenueId())
		if err != nil {
			return err
		}
		record.SetRecordNumber(recordNumber)
	}
	
	// 6. 保存到数据库
	return s.checkRecordRepository.Create(ctx, record)
}

// GetCheckRecordById 根据ID获取盘点记录
func (s *InventoryCheckRecordServiceImpl) GetCheckRecordById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error) {
	if recordId == "" {
		return nil, fmt.Errorf("盘点记录ID不能为空")
	}
	
	return s.checkRecordRepository.FindById(ctx, recordId)
}

// GetCheckRecordByNumber 根据盘点单号获取盘点记录
func (s *InventoryCheckRecordServiceImpl) GetCheckRecordByNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error) {
	if recordNumber == "" {
		return nil, fmt.Errorf("盘点单号不能为空")
	}
	
	return s.checkRecordRepository.FindByRecordNumber(ctx, recordNumber)
}

// GetCheckRecordsByVenue 获取门店的盘点记录列表
func (s *InventoryCheckRecordServiceImpl) GetCheckRecordsByVenue(ctx *gin.Context, venueId string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error) {
	if venueId == "" {
		return nil, 0, fmt.Errorf("门店ID不能为空")
	}
	
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	
	return s.checkRecordRepository.FindByVenueId(ctx, venueId, pageNum, pageSize)
}

// SearchCheckRecords 搜索盘点记录
func (s *InventoryCheckRecordServiceImpl) SearchCheckRecords(ctx *gin.Context, venueId, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error) {
	if venueId == "" {
		return nil, 0, fmt.Errorf("门店ID不能为空")
	}
	
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	
	return s.checkRecordRepository.Search(ctx, venueId, searchKey, pageNum, pageSize)
}

// UpdateCheckRecord 更新盘点记录
func (s *InventoryCheckRecordServiceImpl) UpdateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	// 1. 验证盘点记录
	if err := s.ValidateCheckRecord(ctx, record); err != nil {
		return err
	}
	
	// 2. 更新时间和版本
	record.Utime = time.Now().Unix()
	record.Version++
	
	// 3. 重新计算明细项的盈亏数量
	for i := range record.Items {
		record.Items[i].CalculateProfitLoss()
		record.Items[i].Utime = time.Now().Unix()
	}
	
	// 4. 重新计算汇总数据
	record.CalculateTotals()
	
	// 5. 保存到数据库
	return s.checkRecordRepository.Update(ctx, record)
}

// DeleteCheckRecord 删除盘点记录
func (s *InventoryCheckRecordServiceImpl) DeleteCheckRecord(ctx *gin.Context, recordId string) error {
	if recordId == "" {
		return fmt.Errorf("盘点记录ID不能为空")
	}
	
	return s.checkRecordRepository.Delete(ctx, recordId)
}

// GenerateRecordNumber 生成盘点单号
func (s *InventoryCheckRecordServiceImpl) GenerateRecordNumber(ctx *gin.Context, venueId string) (string, error) {
	// 格式：CHECK + 年月日 + 4位序号
	// 例如：CHECK20240120001
	now := time.Now()
	dateStr := now.Format("20060102")
	
	// 查询当天的盘点记录数量
	count, err := s.checkRecordRepository.CountByVenueId(ctx, venueId)
	if err != nil {
		return "", err
	}
	
	// 生成序号（从1开始）
	sequence := count + 1
	
	return fmt.Sprintf("CHECK%s%03d", dateStr, sequence), nil
}

// ValidateCheckRecord 验证盘点记录
func (s *InventoryCheckRecordServiceImpl) ValidateCheckRecord(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	if record == nil {
		return fmt.Errorf("盘点记录不能为空")
	}
	
	if record.GetVenueId() == "" {
		return fmt.Errorf("门店ID不能为空")
	}
	
	if record.GetHandler() == "" {
		return fmt.Errorf("经手人不能为空")
	}
	
	if record.GetOperator() == "" {
		return fmt.Errorf("操作人不能为空")
	}
	
	if len(record.GetItems()) == 0 {
		return fmt.Errorf("盘点明细不能为空")
	}
	
	// 验证明细项
	for i, item := range record.GetItems() {
		if item.GetProductId() == "" {
			return fmt.Errorf("第%d项商品ID不能为空", i+1)
		}
		
		if item.GetStockQuantity() < 0 {
			return fmt.Errorf("第%d项库存数量不能为负数", i+1)
		}
		
		if item.GetCheckQuantity() < 0 {
			return fmt.Errorf("第%d项盘点数量不能为负数", i+1)
		}
	}
	
	return nil
}

// CalculateCheckAdjustment 计算盘点调整增量（用于库存分析）
func (s *InventoryCheckRecordServiceImpl) CalculateCheckAdjustment(ctx *gin.Context, venueId, productId string, afterTime int64) (int, error) {
	// 获取指定时间后的盘点记录
	records, err := s.checkRecordRepository.FindRecordsAfterTime(ctx, venueId, afterTime)
	if err != nil {
		return 0, err
	}
	
	adjustment := 0
	for _, record := range records {
		for _, item := range record.GetItems() {
			if item.GetProductId() == productId {
				adjustment += item.GetProfitLossQuantity()
			}
		}
	}
	
	return adjustment, nil
}

// GetCheckRecordsAfterTime 获取指定时间后的盘点记录
func (s *InventoryCheckRecordServiceImpl) GetCheckRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InventoryCheckRecord, error) {
	if venueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	
	return s.checkRecordRepository.FindRecordsAfterTime(ctx, venueId, afterTime)
}

// GetLatestCheckRecord 获取门店最新的盘点记录
func (s *InventoryCheckRecordServiceImpl) GetLatestCheckRecord(ctx *gin.Context, venueId string) (*model.InventoryCheckRecord, error) {
	if venueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	
	return s.checkRecordRepository.FindLatestByVenueId(ctx, venueId)
}
