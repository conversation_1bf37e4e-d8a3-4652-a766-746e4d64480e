package repository

import (
	"github.com/gin-gonic/gin"
	"voderpltvv/erp_managent/domain/inventory/model"
)

// InventoryCheckRecordRepository 盘点记录仓储接口
type InventoryCheckRecordRepository interface {
	// Save 保存盘点记录
	Save(ctx *gin.Context, record *model.InventoryCheckRecord) error
	
	// FindById 根据ID查询盘点记录
	FindById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error)
	
	// FindByRecordNumber 根据盘点单号查询盘点记录
	FindByRecordNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error)
	
	// FindByVenueId 根据门店ID查询盘点记录列表（分页）
	FindByVenueId(ctx *gin.Context, venueId string, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error)
	
	// Update 更新盘点记录
	Update(ctx *gin.Context, record *model.InventoryCheckRecord) error
	
	// Delete 删除盘点记录（软删除）
	Delete(ctx *gin.Context, recordId string) error
	
	// FindRecordsAfterTime 查询指定时间后的盘点记录（用于库存计算）
	FindRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InventoryCheckRecord, error)
	
	// CountByVenueId 统计门店的盘点记录数量
	CountByVenueId(ctx *gin.Context, venueId string) (int64, error)
	
	// FindLatestByVenueId 查询门店最新的盘点记录
	FindLatestByVenueId(ctx *gin.Context, venueId string) (*model.InventoryCheckRecord, error)
}
