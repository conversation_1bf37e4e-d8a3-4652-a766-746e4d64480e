package model

import (
	"time"
	"voderpltvv/erp_managent/service/po"
)

// InventoryCheckRecord 盘点记录领域实体
type InventoryCheckRecord struct {
	id                    string
	venueId               string
	handler               string
	operator              string
	time                  time.Time
	recordNumber          string
	remark                string
	profitQuantityTotal   int
	lossQuantityTotal     int
	items                 []InventoryCheckRecordItem
	ctime                 time.Time
	utime                 time.Time
	state                 int
	version               int
}

// InventoryCheckRecordItem 盘点记录明细领域实体
type InventoryCheckRecordItem struct {
	id                   string
	productId            string
	stockQuantity        int
	checkQuantity        int
	profitLossQuantity   int
}

// NewInventoryCheckRecord 创建新的盘点记录
func NewInventoryCheckRecord(venueId, handler, operator, recordNumber, remark string, items []InventoryCheckRecordItem) *InventoryCheckRecord {
	now := time.Now()

	record := &InventoryCheckRecord{
		venueId:      venueId,
		handler:      handler,
		operator:     operator,
		time:         now,
		recordNumber: recordNumber,
		remark:       remark,
		items:        items,
		ctime:        now,
		utime:        now,
		state:        0,
		version:      0,
	}

	// 计算汇总数据
	record.calculateTotals()

	return record
}

// NewInventoryCheckRecordItem 创建新的盘点记录明细
func NewInventoryCheckRecordItem(productId string, stockQuantity, checkQuantity int) InventoryCheckRecordItem {
	item := InventoryCheckRecordItem{
		productId:     productId,
		stockQuantity: stockQuantity,
		checkQuantity: checkQuantity,
	}
	item.calculateProfitLoss()
	return item
}

// calculateProfitLoss 计算盈亏数量
func (item *InventoryCheckRecordItem) calculateProfitLoss() {
	item.profitLossQuantity = item.checkQuantity - item.stockQuantity
}

// calculateTotals 计算汇总数据
func (r *InventoryCheckRecord) calculateTotals() {
	profitTotal := 0
	lossTotal := 0

	for _, item := range r.items {
		if item.profitLossQuantity > 0 {
			profitTotal += item.profitLossQuantity
		} else {
			lossTotal += -item.profitLossQuantity
		}
	}

	r.profitQuantityTotal = profitTotal
	r.lossQuantityTotal = lossTotal
}

// Validate 验证盘点记录
func (r *InventoryCheckRecord) Validate() error {
	if r.venueId == "" {
		return NewDomainError("门店ID不能为空")
	}
	if r.handler == "" {
		return NewDomainError("经手人不能为空")
	}
	if r.operator == "" {
		return NewDomainError("操作人不能为空")
	}
	if r.recordNumber == "" {
		return NewDomainError("盘点单号不能为空")
	}
	if len(r.items) == 0 {
		return NewDomainError("盘点明细不能为空")
	}

	// 验证明细
	for _, item := range r.items {
		if err := item.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证盘点记录明细
func (item *InventoryCheckRecordItem) Validate() error {
	if item.productId == "" {
		return NewDomainError("商品ID不能为空")
	}
	if item.stockQuantity < 0 {
		return NewDomainError("库存数量不能为负数")
	}
	if item.checkQuantity < 0 {
		return NewDomainError("盘点数量不能为负数")
	}
	return nil
}

// ToPO 转换为PO实体
func (r *InventoryCheckRecord) ToPO() *po.InventoryCheckRecord {
	return &po.InventoryCheckRecord{
		Id:                  &r.id,
		VenueId:             &r.venueId,
		Handler:             &r.handler,
		Operator:            &r.operator,
		Time:                timePtr(r.time),
		RecordNumber:        &r.recordNumber,
		Remark:              &r.remark,
		ProfitQuantityTotal: &r.profitQuantityTotal,
		LossQuantityTotal:   &r.lossQuantityTotal,
		Ctime:               timePtr(r.ctime),
		Utime:               timePtr(r.utime),
		State:               &r.state,
		Version:             &r.version,
	}
}

// ItemsToPO 转换明细为PO实体
func (r *InventoryCheckRecord) ItemsToPO() []po.InventoryCheckRecordItem {
	var items []po.InventoryCheckRecordItem
	for _, item := range r.items {
		poItem := po.InventoryCheckRecordItem{
			Id:                 &item.id,
			CheckRecordId:      &r.id,
			ProductId:          &item.productId,
			StockQuantity:      &item.stockQuantity,
			CheckQuantity:      &item.checkQuantity,
			ProfitLossQuantity: &item.profitLossQuantity,
		}
		items = append(items, poItem)
	}
	return items
}

// FromCheckRecordPO 从PO实体创建领域实体
func FromCheckRecordPO(record *po.InventoryCheckRecord, items []po.InventoryCheckRecordItem) *InventoryCheckRecord {
	var domainItems []InventoryCheckRecordItem
	for _, item := range items {
		domainItem := InventoryCheckRecordItem{
			id:                 *item.Id,
			productId:          *item.ProductId,
			stockQuantity:      *item.StockQuantity,
			checkQuantity:      *item.CheckQuantity,
			profitLossQuantity: *item.ProfitLossQuantity,
		}
		domainItems = append(domainItems, domainItem)
	}

	return &InventoryCheckRecord{
		id:                  *record.Id,
		venueId:             *record.VenueId,
		handler:             *record.Handler,
		operator:            *record.Operator,
		time:                timeFromPtr(record.Time),
		recordNumber:        *record.RecordNumber,
		remark:              *record.Remark,
		profitQuantityTotal: *record.ProfitQuantityTotal,
		lossQuantityTotal:   *record.LossQuantityTotal,
		items:               domainItems,
		ctime:               timeFromPtr(record.Ctime),
		utime:               timeFromPtr(record.Utime),
		state:               *record.State,
		version:             *record.Version,
	}
}

// Getters
func (r *InventoryCheckRecord) GetId() string                           { return r.id }
func (r *InventoryCheckRecord) GetVenueId() string                      { return r.venueId }
func (r *InventoryCheckRecord) GetHandler() string                      { return r.handler }
func (r *InventoryCheckRecord) GetOperator() string                     { return r.operator }
func (r *InventoryCheckRecord) GetTime() time.Time                      { return r.time }
func (r *InventoryCheckRecord) GetRecordNumber() string                 { return r.recordNumber }
func (r *InventoryCheckRecord) GetRemark() string                       { return r.remark }
func (r *InventoryCheckRecord) GetProfitQuantityTotal() int             { return r.profitQuantityTotal }
func (r *InventoryCheckRecord) GetLossQuantityTotal() int               { return r.lossQuantityTotal }
func (r *InventoryCheckRecord) GetItems() []InventoryCheckRecordItem    { return r.items }
func (r *InventoryCheckRecord) GetState() int                           { return r.state }
func (r *InventoryCheckRecord) GetVersion() int                         { return r.version }

// Setters
func (r *InventoryCheckRecord) SetId(id string)                         { r.id = id }
func (r *InventoryCheckRecord) SetTime(t time.Time)                     { r.time = t }
func (r *InventoryCheckRecord) SetRemark(remark string)                 { r.remark = remark }
func (r *InventoryCheckRecord) SetItems(items []InventoryCheckRecordItem) { 
	r.items = items
	r.calculateTotals()
}

// Delete 软删除记录
func (r *InventoryCheckRecord) Delete() { r.state = 1 }

// Getters for InventoryCheckRecordItem
func (item *InventoryCheckRecordItem) GetId() string                    { return item.id }
func (item *InventoryCheckRecordItem) GetProductId() string             { return item.productId }
func (item *InventoryCheckRecordItem) GetStockQuantity() int            { return item.stockQuantity }
func (item *InventoryCheckRecordItem) GetCheckQuantity() int            { return item.checkQuantity }
func (item *InventoryCheckRecordItem) GetProfitLossQuantity() int       { return item.profitLossQuantity }

// Setters for InventoryCheckRecordItem
func (item *InventoryCheckRecordItem) SetId(id string)                  { item.id = id }

// 工具函数
func timePtr(t time.Time) *int64 { unix := t.Unix(); return &unix }
func timeFromPtr(ptr *int64) time.Time {
	if ptr == nil {
		return time.Time{}
	}
	return time.Unix(*ptr, 0)
}
