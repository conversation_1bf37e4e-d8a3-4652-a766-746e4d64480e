package model

import (
	"time"
)

// InventoryCheckRecord 盘点记录主表
type InventoryCheckRecord struct {
	Id                    string                        `gorm:"column:id;primaryKey" json:"id"`
	VenueId               string                        `gorm:"column:venue_id" json:"venueId"`
	Handler               string                        `gorm:"column:handler" json:"handler"`                               // 经手人
	Operator              string                        `gorm:"column:operator" json:"operator"`                             // 操作人
	Time                  int64                         `gorm:"column:time" json:"time"`                                     // 盘点时间
	RecordNumber          string                        `gorm:"column:record_number" json:"recordNumber"`                    // 盘点单号
	Remark                string                        `gorm:"column:remark" json:"remark"`                                 // 备注
	ProfitQuantityTotal   int                           `gorm:"column:profit_quantity_total" json:"profitQuantityTotal"`     // 盘盈数量合计
	LossQuantityTotal     int                           `gorm:"column:loss_quantity_total" json:"lossQuantityTotal"`         // 盘亏数量合计
	Items                 []InventoryCheckRecordItem    `gorm:"foreignKey:CheckRecordId" json:"items"`                       // 明细项
	Ctime                 int64                         `gorm:"column:ctime" json:"ctime"`
	Utime                 int64                         `gorm:"column:utime" json:"utime"`
	State                 int                           `gorm:"column:state" json:"state"`
	Version               int                           `gorm:"column:version" json:"version"`
}

// InventoryCheckRecordItem 盘点记录明细表
type InventoryCheckRecordItem struct {
	Id                   string `gorm:"column:id;primaryKey" json:"id"`
	CheckRecordId        string `gorm:"column:check_record_id" json:"checkRecordId"`
	ProductId            string `gorm:"column:product_id" json:"productId"`
	StockQuantity        int    `gorm:"column:stock_quantity" json:"stockQuantity"`        // 库存数量（盘点之前）
	CheckQuantity        int    `gorm:"column:check_quantity" json:"checkQuantity"`        // 盘点数量（盘点之后）
	ProfitLossQuantity   int    `gorm:"column:profit_loss_quantity" json:"profitLossQuantity"` // 盈亏数量
	Ctime                int64  `gorm:"column:ctime" json:"ctime"`
	Utime                int64  `gorm:"column:utime" json:"utime"`
	State                int    `gorm:"column:state" json:"state"`
}

// TableName 指定表名
func (InventoryCheckRecord) TableName() string {
	return "inventory_check_record"
}

// TableName 指定表名
func (InventoryCheckRecordItem) TableName() string {
	return "inventory_check_record_item"
}

// GetId 获取ID
func (r *InventoryCheckRecord) GetId() string {
	return r.Id
}

// GetVenueId 获取门店ID
func (r *InventoryCheckRecord) GetVenueId() string {
	return r.VenueId
}

// GetHandler 获取经手人
func (r *InventoryCheckRecord) GetHandler() string {
	return r.Handler
}

// GetOperator 获取操作人
func (r *InventoryCheckRecord) GetOperator() string {
	return r.Operator
}

// GetTime 获取盘点时间
func (r *InventoryCheckRecord) GetTime() time.Time {
	return time.Unix(r.Time, 0)
}

// GetRecordNumber 获取盘点单号
func (r *InventoryCheckRecord) GetRecordNumber() string {
	return r.RecordNumber
}

// GetRemark 获取备注
func (r *InventoryCheckRecord) GetRemark() string {
	return r.Remark
}

// GetProfitQuantityTotal 获取盘盈数量合计
func (r *InventoryCheckRecord) GetProfitQuantityTotal() int {
	return r.ProfitQuantityTotal
}

// GetLossQuantityTotal 获取盘亏数量合计
func (r *InventoryCheckRecord) GetLossQuantityTotal() int {
	return r.LossQuantityTotal
}

// GetItems 获取明细项
func (r *InventoryCheckRecord) GetItems() []InventoryCheckRecordItem {
	return r.Items
}

// SetId 设置ID
func (r *InventoryCheckRecord) SetId(id string) {
	r.Id = id
}

// SetVenueId 设置门店ID
func (r *InventoryCheckRecord) SetVenueId(venueId string) {
	r.VenueId = venueId
}

// SetHandler 设置经手人
func (r *InventoryCheckRecord) SetHandler(handler string) {
	r.Handler = handler
}

// SetOperator 设置操作人
func (r *InventoryCheckRecord) SetOperator(operator string) {
	r.Operator = operator
}

// SetTime 设置盘点时间
func (r *InventoryCheckRecord) SetTime(t time.Time) {
	r.Time = t.Unix()
}

// SetRecordNumber 设置盘点单号
func (r *InventoryCheckRecord) SetRecordNumber(recordNumber string) {
	r.RecordNumber = recordNumber
}

// SetRemark 设置备注
func (r *InventoryCheckRecord) SetRemark(remark string) {
	r.Remark = remark
}

// SetProfitQuantityTotal 设置盘盈数量合计
func (r *InventoryCheckRecord) SetProfitQuantityTotal(total int) {
	r.ProfitQuantityTotal = total
}

// SetLossQuantityTotal 设置盘亏数量合计
func (r *InventoryCheckRecord) SetLossQuantityTotal(total int) {
	r.LossQuantityTotal = total
}

// SetItems 设置明细项
func (r *InventoryCheckRecord) SetItems(items []InventoryCheckRecordItem) {
	r.Items = items
}

// AddItem 添加明细项
func (r *InventoryCheckRecord) AddItem(item InventoryCheckRecordItem) {
	r.Items = append(r.Items, item)
}

// CalculateTotals 计算汇总数据
func (r *InventoryCheckRecord) CalculateTotals() {
	profitTotal := 0
	lossTotal := 0
	
	for _, item := range r.Items {
		if item.ProfitLossQuantity > 0 {
			profitTotal += item.ProfitLossQuantity
		} else {
			lossTotal += -item.ProfitLossQuantity
		}
	}
	
	r.ProfitQuantityTotal = profitTotal
	r.LossQuantityTotal = lossTotal
}

// GetProductId 获取商品ID
func (i *InventoryCheckRecordItem) GetProductId() string {
	return i.ProductId
}

// GetStockQuantity 获取库存数量
func (i *InventoryCheckRecordItem) GetStockQuantity() int {
	return i.StockQuantity
}

// GetCheckQuantity 获取盘点数量
func (i *InventoryCheckRecordItem) GetCheckQuantity() int {
	return i.CheckQuantity
}

// GetProfitLossQuantity 获取盈亏数量
func (i *InventoryCheckRecordItem) GetProfitLossQuantity() int {
	return i.ProfitLossQuantity
}

// SetProductId 设置商品ID
func (i *InventoryCheckRecordItem) SetProductId(productId string) {
	i.ProductId = productId
}

// SetStockQuantity 设置库存数量
func (i *InventoryCheckRecordItem) SetStockQuantity(quantity int) {
	i.StockQuantity = quantity
}

// SetCheckQuantity 设置盘点数量
func (i *InventoryCheckRecordItem) SetCheckQuantity(quantity int) {
	i.CheckQuantity = quantity
}

// SetProfitLossQuantity 设置盈亏数量
func (i *InventoryCheckRecordItem) SetProfitLossQuantity(quantity int) {
	i.ProfitLossQuantity = quantity
}

// CalculateProfitLoss 计算盈亏数量
func (i *InventoryCheckRecordItem) CalculateProfitLoss() {
	i.ProfitLossQuantity = i.CheckQuantity - i.StockQuantity
}
