package test

import (
	"testing"
	"time"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/stretchr/testify/assert"
)

// TestInventoryCheckRecordPO 测试盘点记录PO模型
func TestInventoryCheckRecordPO(t *testing.T) {
	// 创建测试主记录
	now := time.Now()
	recordId := util.GetUUID()
	record := po.InventoryCheckRecord{
		Id:                  &recordId,
		VenueId:             util.GetItPtr("venue_001"),
		Handler:             util.GetItPtr("张三"),
		Operator:            util.GetItPtr("李四"),
		Time:                util.GetItPtr(now.Unix()),
		RecordNumber:        util.GetItPtr("CHECK20240120001"),
		Remark:              util.GetItPtr("月度盘点"),
		ProfitQuantityTotal: util.GetItPtr(3),
		LossQuantityTotal:   util.GetItPtr(2),
		Ctime:               util.GetItPtr(now.Unix()),
		Utime:               util.GetItPtr(now.Unix()),
		State:               util.GetItPtr(0),
		Version:             util.GetItPtr(0),
	}
	
	// 验证基本信息
	assert.Equal(t, "venue_001", *record.VenueId)
	assert.Equal(t, "张三", *record.Handler)
	assert.Equal(t, "李四", *record.Operator)
	assert.Equal(t, "CHECK20240120001", *record.RecordNumber)
	assert.Equal(t, "月度盘点", *record.Remark)
	assert.Equal(t, 3, *record.ProfitQuantityTotal)
	assert.Equal(t, 2, *record.LossQuantityTotal)
	
	// 创建测试明细项
	item1Id := util.GetUUID()
	item1 := po.InventoryCheckRecordItem{
		Id:                 &item1Id,
		CheckRecordId:      &recordId,
		ProductId:          util.GetItPtr("product_001"),
		StockQuantity:      util.GetItPtr(100),
		CheckQuantity:      util.GetItPtr(103),
		ProfitLossQuantity: util.GetItPtr(3), // 103-100=3
		Ctime:              util.GetItPtr(now.Unix()),
		Utime:              util.GetItPtr(now.Unix()),
		State:              util.GetItPtr(0),
	}
	
	// 验证明细项计算
	assert.Equal(t, 3, *item1.ProfitLossQuantity)
	assert.Equal(t, "product_001", *item1.ProductId)
	assert.Equal(t, 100, *item1.StockQuantity)
	assert.Equal(t, 103, *item1.CheckQuantity)
}

// TestInventoryCheckRecordTableName 测试表名
func TestInventoryCheckRecordTableName(t *testing.T) {
	record := po.InventoryCheckRecord{}
	assert.Equal(t, "inventory_check_record", record.TableName())
	
	item := po.InventoryCheckRecordItem{}
	assert.Equal(t, "inventory_check_record_item", item.TableName())
}

// TestInventoryCheckRecordGetId 测试GetId方法
func TestInventoryCheckRecordGetId(t *testing.T) {
	// 测试有ID的情况
	recordId := "test_id_123"
	record := po.InventoryCheckRecord{Id: &recordId}
	assert.Equal(t, "test_id_123", record.GetId())
	
	// 测试ID为nil的情况
	recordNil := po.InventoryCheckRecord{Id: nil}
	assert.Equal(t, "", recordNil.GetId())
	
	// 测试明细项
	itemId := "item_id_123"
	item := po.InventoryCheckRecordItem{Id: &itemId}
	assert.Equal(t, "item_id_123", item.GetId())
	
	itemNil := po.InventoryCheckRecordItem{Id: nil}
	assert.Equal(t, "", itemNil.GetId())
}

// TestInventoryCheckRecordCalculation 测试盈亏计算
func TestInventoryCheckRecordCalculation(t *testing.T) {
	// 测试盘盈情况
	profitItem := po.InventoryCheckRecordItem{
		StockQuantity:      util.GetItPtr(100),
		CheckQuantity:      util.GetItPtr(105),
		ProfitLossQuantity: util.GetItPtr(5), // 105-100=5
	}
	assert.Equal(t, 5, *profitItem.ProfitLossQuantity)
	
	// 测试盘亏情况
	lossItem := po.InventoryCheckRecordItem{
		StockQuantity:      util.GetItPtr(100),
		CheckQuantity:      util.GetItPtr(95),
		ProfitLossQuantity: util.GetItPtr(-5), // 95-100=-5
	}
	assert.Equal(t, -5, *lossItem.ProfitLossQuantity)
	
	// 测试无差异情况
	normalItem := po.InventoryCheckRecordItem{
		StockQuantity:      util.GetItPtr(100),
		CheckQuantity:      util.GetItPtr(100),
		ProfitLossQuantity: util.GetItPtr(0), // 100-100=0
	}
	assert.Equal(t, 0, *normalItem.ProfitLossQuantity)
}

// BenchmarkInventoryCheckRecordCreation 性能测试：创建盘点记录
func BenchmarkInventoryCheckRecordCreation(b *testing.B) {
	now := time.Now()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		recordId := util.GetUUID()
		record := po.InventoryCheckRecord{
			Id:                  &recordId,
			VenueId:             util.GetItPtr("venue_001"),
			Handler:             util.GetItPtr("张三"),
			Operator:            util.GetItPtr("李四"),
			Time:                util.GetItPtr(now.Unix()),
			RecordNumber:        util.GetItPtr("CHECK20240120001"),
			Remark:              util.GetItPtr("性能测试盘点"),
			ProfitQuantityTotal: util.GetItPtr(10),
			LossQuantityTotal:   util.GetItPtr(5),
			Ctime:               util.GetItPtr(now.Unix()),
			Utime:               util.GetItPtr(now.Unix()),
			State:               util.GetItPtr(0),
			Version:             util.GetItPtr(0),
		}
		_ = record
	}
}

// BenchmarkInventoryCheckRecordItemCreation 性能测试：创建盘点明细
func BenchmarkInventoryCheckRecordItemCreation(b *testing.B) {
	now := time.Now()
	recordId := util.GetUUID()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		itemId := util.GetUUID()
		item := po.InventoryCheckRecordItem{
			Id:                 &itemId,
			CheckRecordId:      &recordId,
			ProductId:          util.GetItPtr("product_001"),
			StockQuantity:      util.GetItPtr(100),
			CheckQuantity:      util.GetItPtr(103),
			ProfitLossQuantity: util.GetItPtr(3),
			Ctime:              util.GetItPtr(now.Unix()),
			Utime:              util.GetItPtr(now.Unix()),
			State:              util.GetItPtr(0),
		}
		_ = item
	}
}
