package test

import (
	"testing"
	"time"
	"github.com/stretchr/testify/assert"
	"erp_managent/domain/inventory/model"
)

// TestInventoryCheckRecord 测试盘点记录模型
func TestInventoryCheckRecord(t *testing.T) {
	// 创建盘点记录
	record := &model.InventoryCheckRecord{}
	record.SetId("test_record_001")
	record.SetVenueId("venue_001")
	record.SetHandler("张三")
	record.SetOperator("李四")
	record.SetTime(time.Now())
	record.SetRecordNumber("CHECK20240120001")
	record.SetRemark("测试盘点")
	
	// 添加明细项
	item1 := model.InventoryCheckRecordItem{}
	item1.SetProductId("product_001")
	item1.SetStockQuantity(100)
	item1.SetCheckQuantity(103)
	item1.CalculateProfitLoss()
	
	item2 := model.InventoryCheckRecordItem{}
	item2.SetProductId("product_002")
	item2.SetStockQuantity(50)
	item2.SetCheckQuantity(48)
	item2.CalculateProfitLoss()
	
	record.AddItem(item1)
	record.AddItem(item2)
	
	// 计算汇总
	record.CalculateTotals()
	
	// 验证结果
	assert.Equal(t, "test_record_001", record.GetId())
	assert.Equal(t, "venue_001", record.GetVenueId())
	assert.Equal(t, "张三", record.GetHandler())
	assert.Equal(t, "李四", record.GetOperator())
	assert.Equal(t, "CHECK20240120001", record.GetRecordNumber())
	assert.Equal(t, "测试盘点", record.GetRemark())
	assert.Equal(t, 2, len(record.GetItems()))
	assert.Equal(t, 3, record.GetProfitQuantityTotal()) // 盘盈3件
	assert.Equal(t, 2, record.GetLossQuantityTotal())   // 盘亏2件
}

// TestInventoryCheckRecordItem 测试盘点记录明细
func TestInventoryCheckRecordItem(t *testing.T) {
	// 测试盘盈情况
	item1 := model.InventoryCheckRecordItem{}
	item1.SetProductId("product_001")
	item1.SetStockQuantity(100)
	item1.SetCheckQuantity(103)
	item1.CalculateProfitLoss()
	
	assert.Equal(t, "product_001", item1.GetProductId())
	assert.Equal(t, 100, item1.GetStockQuantity())
	assert.Equal(t, 103, item1.GetCheckQuantity())
	assert.Equal(t, 3, item1.GetProfitLossQuantity()) // 盘盈3件
	
	// 测试盘亏情况
	item2 := model.InventoryCheckRecordItem{}
	item2.SetProductId("product_002")
	item2.SetStockQuantity(50)
	item2.SetCheckQuantity(48)
	item2.CalculateProfitLoss()
	
	assert.Equal(t, "product_002", item2.GetProductId())
	assert.Equal(t, 50, item2.GetStockQuantity())
	assert.Equal(t, 48, item2.GetCheckQuantity())
	assert.Equal(t, -2, item2.GetProfitLossQuantity()) // 盘亏2件
	
	// 测试无差异情况
	item3 := model.InventoryCheckRecordItem{}
	item3.SetProductId("product_003")
	item3.SetStockQuantity(30)
	item3.SetCheckQuantity(30)
	item3.CalculateProfitLoss()
	
	assert.Equal(t, "product_003", item3.GetProductId())
	assert.Equal(t, 30, item3.GetStockQuantity())
	assert.Equal(t, 30, item3.GetCheckQuantity())
	assert.Equal(t, 0, item3.GetProfitLossQuantity()) // 无差异
}

// TestCalculateTotals 测试汇总计算
func TestCalculateTotals(t *testing.T) {
	record := &model.InventoryCheckRecord{}
	
	// 添加多个明细项
	items := []model.InventoryCheckRecordItem{
		{ProductId: "p1", StockQuantity: 100, CheckQuantity: 105, ProfitLossQuantity: 5},   // 盘盈5
		{ProductId: "p2", StockQuantity: 50, CheckQuantity: 47, ProfitLossQuantity: -3},   // 盘亏3
		{ProductId: "p3", StockQuantity: 30, CheckQuantity: 32, ProfitLossQuantity: 2},    // 盘盈2
		{ProductId: "p4", StockQuantity: 20, CheckQuantity: 18, ProfitLossQuantity: -2},   // 盘亏2
		{ProductId: "p5", StockQuantity: 40, CheckQuantity: 40, ProfitLossQuantity: 0},    // 无差异
	}
	
	record.SetItems(items)
	record.CalculateTotals()
	
	// 验证汇总结果
	assert.Equal(t, 7, record.GetProfitQuantityTotal()) // 盘盈总计：5+2=7
	assert.Equal(t, 5, record.GetLossQuantityTotal())   // 盘亏总计：3+2=5
}

// BenchmarkCalculateTotals 性能测试
func BenchmarkCalculateTotals(b *testing.B) {
	record := &model.InventoryCheckRecord{}
	
	// 创建大量明细项
	var items []model.InventoryCheckRecordItem
	for i := 0; i < 1000; i++ {
		item := model.InventoryCheckRecordItem{
			ProductId:          fmt.Sprintf("product_%d", i),
			StockQuantity:      100,
			CheckQuantity:      100 + (i % 10) - 5, // 随机盈亏
			ProfitLossQuantity: (i % 10) - 5,
		}
		items = append(items, item)
	}
	
	record.SetItems(items)
	
	// 性能测试
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		record.CalculateTotals()
	}
}
