package inventory

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/domain/inventory/model"
	"voderpltvv/erp_managent/domain/inventory/repository"
	"voderpltvv/erp_managent/service/impl"

	"github.com/gin-gonic/gin"
)

// InventoryCheckRecordRepositoryImpl 盘点记录仓储实现
type InventoryCheckRecordRepositoryImpl struct {
	checkRecordService *impl.InventoryCheckRecordService
}

// NewInventoryCheckRecordRepository 创建盘点记录仓储实例
func NewInventoryCheckRecordRepository(checkRecordService *impl.InventoryCheckRecordService) repository.InventoryCheckRecordRepository {
	return &InventoryCheckRecordRepositoryImpl{
		checkRecordService: checkRecordService,
	}
}

// Save 保存盘点记录
func (r *InventoryCheckRecordRepositoryImpl) Save(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	// 转换为PO并保存
	recordPO := record.ToPO()
	itemsPO := record.ItemsToPO()

	return r.checkRecordService.CreateInventoryCheckRecordWithItems(ctx, recordPO, itemsPO)
}

// FindById 根据ID查询盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindById(ctx *gin.Context, recordId string) (*model.InventoryCheckRecord, error) {
	// 查询主记录
	recordPO, err := r.checkRecordService.FindInventoryCheckRecordById(ctx, recordId)
	if err != nil {
		return nil, err
	}
	if recordPO == nil {
		return nil, nil
	}

	// 查询明细
	itemsPO, err := r.checkRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, recordId)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	return model.FromCheckRecordPO(recordPO, *itemsPO), nil
}

// FindByRecordNumber 根据盘点单号查询盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindByRecordNumber(ctx *gin.Context, recordNumber string) (*model.InventoryCheckRecord, error) {
	// 查询主记录
	recordPO, err := r.checkRecordService.FindInventoryCheckRecordByRecordNumber(ctx, recordNumber)
	if err != nil {
		return nil, err
	}
	if recordPO == nil {
		return nil, nil
	}

	// 查询明细
	itemsPO, err := r.checkRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *recordPO.Id)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	return model.FromCheckRecordPO(recordPO, *itemsPO), nil
}

// FindByVenueId 根据门店ID查询盘点记录列表（分页）
func (r *InventoryCheckRecordRepositoryImpl) FindByVenueId(ctx *gin.Context, venueId string, searchKey string, pageNum, pageSize int) ([]*model.InventoryCheckRecord, int64, error) {
	// 构建查询参数
	reqDto := &req.QueryInventoryCheckRecordReqDto{
		VenueId:  &venueId,
		PageNum:  &pageNum,
		PageSize: &pageSize,
	}
	if searchKey != "" {
		reqDto.SearchKey = &searchKey
	}

	// 查询记录列表
	recordsPO, total, err := r.checkRecordService.FindInventoryCheckRecordsByVenueId(ctx, reqDto)
	if err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	var records []*model.InventoryCheckRecord
	for _, recordPO := range *recordsPO {
		// 查询明细
		itemsPO, err := r.checkRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *recordPO.Id)
		if err != nil {
			continue // 跳过有问题的记录
		}

		record := model.FromCheckRecordPO(&recordPO, *itemsPO)
		records = append(records, record)
	}

	return records, total, nil
}

// Update 更新盘点记录
func (r *InventoryCheckRecordRepositoryImpl) Update(ctx *gin.Context, record *model.InventoryCheckRecord) error {
	// 转换为PO并更新
	recordPO := record.ToPO()
	itemsPO := record.ItemsToPO()

	return r.checkRecordService.UpdateInventoryCheckRecordWithItems(ctx, recordPO, itemsPO)
}

// Delete 删除盘点记录（软删除）
func (r *InventoryCheckRecordRepositoryImpl) Delete(ctx *gin.Context, recordId string) error {
	return r.checkRecordService.DeleteInventoryCheckRecord(ctx, recordId)
}

// FindRecordsAfterTime 查询指定时间后的盘点记录（用于库存计算）
func (r *InventoryCheckRecordRepositoryImpl) FindRecordsAfterTime(ctx *gin.Context, venueId string, afterTime int64) ([]*model.InventoryCheckRecord, error) {
	// 查询记录列表
	recordsPO, err := r.checkRecordService.FindInventoryCheckRecordsAfterTime(ctx, venueId, afterTime)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	var records []*model.InventoryCheckRecord
	for _, recordPO := range *recordsPO {
		// 查询明细
		itemsPO, err := r.checkRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *recordPO.Id)
		if err != nil {
			continue // 跳过有问题的记录
		}

		record := model.FromCheckRecordPO(&recordPO, *itemsPO)
		records = append(records, record)
	}

	return records, nil
}

// CountByVenueId 统计门店的盘点记录数量
func (r *InventoryCheckRecordRepositoryImpl) CountByVenueId(ctx *gin.Context, venueId string) (int64, error) {
	return r.checkRecordService.CountInventoryCheckRecordsByVenueId(ctx, venueId)
}

// FindLatestByVenueId 查询门店最新的盘点记录
func (r *InventoryCheckRecordRepositoryImpl) FindLatestByVenueId(ctx *gin.Context, venueId string) (*model.InventoryCheckRecord, error) {
	// 查询最新记录
	recordPO, err := r.checkRecordService.FindLatestInventoryCheckRecordByVenueId(ctx, venueId)
	if err != nil {
		return nil, err
	}
	if recordPO == nil {
		return nil, nil
	}

	// 查询明细
	itemsPO, err := r.checkRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *recordPO.Id)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	return model.FromCheckRecordPO(recordPO, *itemsPO), nil
}
