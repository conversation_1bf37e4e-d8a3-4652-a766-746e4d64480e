package print

import (
	"fmt"

	"voderpltvv/erp_managent/domain/print/model/valueobject"
	"voderpltvv/erp_managent/domain/print/model/winestorage"
	"voderpltvv/erp_managent/domain/print/repository"
	"voderpltvv/erp_managent/infra/query"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// WineStoragePrintRecordRepositoryImpl 存酒打印记录仓储实现
type WineStoragePrintRecordRepositoryImpl struct {
	printRecordService *impl.PrintRecordService
}

// NewWineStoragePrintRecordRepositoryImpl 创建存酒打印记录仓储实现
func NewWineStoragePrintRecordRepositoryImpl() repository.WineStoragePrintRecordRepository {
	return &WineStoragePrintRecordRepositoryImpl{
		printRecordService: &impl.PrintRecordService{},
	}
}

// Save 保存存酒打印记录
func (r *WineStoragePrintRecordRepositoryImpl) Save(ctx *gin.Context, record *winestorage.WineStoragePrintRecord) error {
	// 转换为PO
	printRecordPO, err := r.toPO(record)
	if err != nil {
		return fmt.Errorf("转换存酒打印记录失败: %w", err)
	}

	// 使用服务保存
	if record.ID() == "" {
		// 创建
		err = r.printRecordService.CreatePrintRecord(ctx, printRecordPO)
		if err != nil {
			return err
		}

		// 将生成的ID设置回领域模型
		if printRecordPO.Id != nil {
			record.SetID(*printRecordPO.Id)
		}
	} else {
		// 更新
		err = r.printRecordService.UpdatePrintRecord(ctx, printRecordPO)
		if err != nil {
			return err
		}
	}

	return nil
}

// FindByID 根据ID查找存酒打印记录
func (r *WineStoragePrintRecordRepositoryImpl) FindByID(ctx *gin.Context, id string) (*winestorage.WineStoragePrintRecord, error) {
	printRecordPO, err := r.printRecordService.FindPrintRecordById(ctx, id)
	if err != nil {
		return nil, err
	}

	if printRecordPO == nil {
		return nil, nil
	}

	return r.toDomainModel(printRecordPO)
}

// Delete 删除存酒打印记录
func (r *WineStoragePrintRecordRepositoryImpl) Delete(ctx *gin.Context, id string) error {
	return r.printRecordService.DeletePrintRecord(ctx, id)
}

// FindUniqueByStorageRecordID 根据存酒记录ID查询唯一的打印记录
func (r *WineStoragePrintRecordRepositoryImpl) FindUniqueByStorageRecordID(ctx *gin.Context, storageRecordID string) (*winestorage.WineStoragePrintRecord, error) {
	// 使用单一查询模式，businessId在存取酒系统中是全局唯一的
	record := &po.PrintRecord{}
	err := model.DBSlave.Self.Where("business_id = ? AND print_type = ?",
		storageRecordID, string(valueobject.PrintTypeWineStorage)).First(record).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// 转换为领域模型
	return r.toDomainModel(record)
}

// Update 更新存酒打印记录
func (r *WineStoragePrintRecordRepositoryImpl) Update(ctx *gin.Context, record *winestorage.WineStoragePrintRecord) error {
	return r.Save(ctx, record)
}

// toPO 转换为PO对象
func (r *WineStoragePrintRecordRepositoryImpl) toPO(record *winestorage.WineStoragePrintRecord) (*po.PrintRecord, error) {
	// 导出存酒单数据为JSON
	contentJson, err := record.ExportWineStorageBillData()
	if err != nil {
		return nil, fmt.Errorf("导出存酒单数据失败: %w", err)
	}

	printTimestamp := record.PrintTime().UnixMilli()
	venueID := record.VenueID()
	printType := record.PrintType()
	operatorID := record.OperatorID()
	operatorName := record.OperatorName()
	deviceName := record.DeviceName()
	status := record.Status()
	errorMsg := record.ErrorMsg()
	remark := record.Remark()
	recordID := record.ID()

	printRecordPO := &po.PrintRecord{
		Id:            lo.Ternary(recordID != "", &recordID, nil),
		VenueId:       &venueID,
		PrintType:     &printType,
		PrintTime:     &printTimestamp,
		SessionId:     lo.ToPtr(""),            // 存取酒无SessionId，设置为空字符串
		BusinessId:    &record.StorageRecordId, // 核心标识字段
		SubBusinessId: util.GetItPtr(""),       // 统一设置为空
		OperatorId:    &operatorID,
		OperatorName:  &operatorName,
		DeviceName:    &deviceName,
		Status:        &status,
		ErrorMsg:      &errorMsg,
		Content:       &contentJson,
		Remark:        &remark,
	}

	return printRecordPO, nil
}

// toDomainModel 转换为领域模型
func (r *WineStoragePrintRecordRepositoryImpl) toDomainModel(po *po.PrintRecord) (*winestorage.WineStoragePrintRecord, error) {
	venueID := lo.FromPtrOr(po.VenueId, "")
	storageRecordId := lo.FromPtrOr(po.BusinessId, "")
	operatorID := lo.FromPtrOr(po.OperatorId, "")
	operatorName := lo.FromPtrOr(po.OperatorName, "")

	// 存取酒没有 sessionID 概念，使用 storageRecordId 作为占位符
	sessionID := storageRecordId

	// 创建存酒打印记录
	record, err := winestorage.NewWineStoragePrintRecord(
		venueID,
		sessionID,
		storageRecordId,
		operatorID,
		operatorName,
	)
	if err != nil {
		return nil, err
	}

	// 设置基础属性
	if po.Id != nil {
		record.SetID(*po.Id)
	}

	if po.PrintTime != nil {
		record.SetPrintTimeValue(*po.PrintTime)
	}

	if po.DeviceName != nil {
		record.SetDeviceNameValue(*po.DeviceName)
	}

	if po.Status != nil {
		record.SetStatusValue(*po.Status)
	}

	if po.ErrorMsg != nil {
		record.SetErrorMsgValue(*po.ErrorMsg)
	}

	if po.Remark != nil {
		record.SetRemarkValue(*po.Remark)
	}

	// 导入存酒单数据
	if po.Content != nil && *po.Content != "" {
		err = record.ImportWineStorageBillData(*po.Content)
		if err != nil {
			return nil, fmt.Errorf("导入存酒单数据失败: %w", err)
		}
	}

	return record, nil
}

// findByQuery 根据查询条件查找记录
func (r *WineStoragePrintRecordRepositoryImpl) findByQuery(ctx *gin.Context, queryParams *query.PrintRecordQuery) ([]*winestorage.WineStoragePrintRecord, error) {
	printRecords, err := r.printRecordService.FindAllPrintRecord(ctx, queryParams)
	if err != nil {
		return nil, err
	}

	// 使用 lo.Map 转换为领域模型
	return lo.Map(*printRecords, func(po po.PrintRecord, _ int) *winestorage.WineStoragePrintRecord {
		record, err := r.toDomainModel(&po)
		if err != nil {
			return nil // 如果转换失败，返回nil
		}
		return record
	}), nil
}
