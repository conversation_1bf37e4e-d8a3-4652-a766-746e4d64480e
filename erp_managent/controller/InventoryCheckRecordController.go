package controller

import (
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type InventoryCheckRecordController struct{}

var (
	inventoryCheckRecordService  = impl.InventoryCheckRecordService{}
	inventoryCheckRecordTransfer = transfer.InventoryCheckRecordTransfer{}
	checkProductService          = impl.ProductService{}
)

// @Summary 创建盘点记录
// @Description 创建盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.CreateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.CreateInventoryCheckRecordRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/create [post]
func (controller *InventoryCheckRecordController) CreateInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.CreateInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 生成盘点单号
	recordNumber, err := inventoryCheckRecordService.GenerateRecordNumber(ctx, *reqDto.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 计算汇总数据
	profitTotal := 0
	lossTotal := 0
	for _, item := range reqDto.Items {
		profitLoss := *item.CheckQuantity - *item.StockQuantity
		if profitLoss > 0 {
			profitTotal += profitLoss
		} else {
			lossTotal += -profitLoss
		}
	}

	// 创建主记录
	now := time.Now()
	recordId := util.GetUUID()
	record := po.InventoryCheckRecord{
		Id:                  &recordId,
		VenueId:             reqDto.VenueId,
		Handler:             reqDto.Handler,
		Operator:            reqDto.Operator,
		Time:                util.GetItPtr(now.Unix()),
		RecordNumber:        &recordNumber,
		Remark:              reqDto.Remark,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
		Ctime:               util.GetItPtr(now.Unix()),
		Utime:               util.GetItPtr(now.Unix()),
		State:               util.GetItPtr(0),
		Version:             util.GetItPtr(0),
	}

	// 创建明细记录
	var items []po.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		profitLoss := *itemDto.CheckQuantity - *itemDto.StockQuantity
		itemId := util.GetUUID()
		item := po.InventoryCheckRecordItem{
			Id:                 &itemId,
			CheckRecordId:      record.Id,
			ProductId:          itemDto.ProductId,
			StockQuantity:      itemDto.StockQuantity,
			CheckQuantity:      itemDto.CheckQuantity,
			ProfitLossQuantity: util.GetItPtr(profitLoss),
			Ctime:              util.GetItPtr(now.Unix()),
			Utime:              util.GetItPtr(now.Unix()),
			State:              util.GetItPtr(0),
		}
		items = append(items, item)
	}

	// 保存到数据库
	err = inventoryCheckRecordService.CreateInventoryCheckRecordWithItems(ctx, &record, items)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 构建响应
	respVO := vo.CreateInventoryCheckRecordRespVO{
		RecordId:            record.Id,
		RecordNumber:        record.RecordNumber,
		ProfitQuantityTotal: record.ProfitQuantityTotal,
		LossQuantityTotal:   record.LossQuantityTotal,
	}

	Result_success[any](ctx, respVO)
}

// @Summary 查询盘点记录列表
// @Description 查询盘点记录列表
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.QueryInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.InventoryCheckRecordVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/list [post]
func (controller *InventoryCheckRecordController) ListInventoryCheckRecords(ctx *gin.Context) {
	reqDto := req.QueryInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	list, totalCount, err := inventoryCheckRecordService.FindAllInventoryCheckRecordWithPagination(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	page := vo.PageVO[[]vo.InventoryCheckRecordVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.InventoryCheckRecordVO{}

	for _, v := range *list {
		recordVO := inventoryCheckRecordTransfer.PoToVo(v)
		page.Data = append(page.Data, recordVO)
	}
	Result_success[any](ctx, &page)
}

// @Summary 查询盘点记录详情
// @Description 查询盘点记录详情
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.GetInventoryCheckRecordDetailReqDto true "请求体"
// @Success 200 {object} Result[vo.GetInventoryCheckRecordDetailRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/detail [post]
func (controller *InventoryCheckRecordController) GetInventoryCheckRecordDetail(ctx *gin.Context) {
	reqDto := req.GetInventoryCheckRecordDetailReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 查询主记录
	record, err := inventoryCheckRecordService.FindInventoryCheckRecordById(ctx, *reqDto.RecordId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 查询明细
	items, err := inventoryCheckRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *reqDto.RecordId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 构建响应
	recordDetailVO := inventoryCheckRecordTransfer.PoToDetailVo(*record)

	var itemVOs []vo.InventoryCheckRecordItemVO
	for _, item := range *items {
		// 获取商品信息
		product, err := checkProductService.FindProductById(ctx, *item.ProductId)
		if err != nil {
			// 如果获取商品信息失败，使用默认值
			unknownProduct := "未知商品"
			emptyStr := ""
			itemVO := vo.InventoryCheckRecordItemVO{
				ProductId:          item.ProductId,
				ProductName:        &unknownProduct,
				ProductImage:       &emptyStr,
				Unit:               &emptyStr,
				StockQuantity:      item.StockQuantity,
				CheckQuantity:      item.CheckQuantity,
				ProfitLossQuantity: item.ProfitLossQuantity,
			}
			itemVOs = append(itemVOs, itemVO)
			continue
		}

		emptyUnit := ""
		itemVO := vo.InventoryCheckRecordItemVO{
			ProductId:          item.ProductId,
			ProductName:        product.Name,
			ProductImage:       product.Image,
			Unit:               &emptyUnit, // Product结构体中没有Unit字段
			StockQuantity:      item.StockQuantity,
			CheckQuantity:      item.CheckQuantity,
			ProfitLossQuantity: item.ProfitLossQuantity,
		}
		itemVOs = append(itemVOs, itemVO)
	}

	respVO := vo.GetInventoryCheckRecordDetailRespVO{
		Record: recordDetailVO,
		Items:  itemVOs,
	}

	Result_success[any](ctx, respVO)
}

// @Summary 更新盘点记录
// @Description 更新盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.UpdateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.UpdateInventoryCheckRecordRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/update [post]
func (controller *InventoryCheckRecordController) UpdateInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.UpdateInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 计算汇总数据
	profitTotal := 0
	lossTotal := 0
	for _, item := range reqDto.Items {
		profitLoss := *item.CheckQuantity - *item.StockQuantity
		if profitLoss > 0 {
			profitTotal += profitLoss
		} else {
			lossTotal += -profitLoss
		}
	}

	// 更新主记录
	now := time.Now()
	record := po.InventoryCheckRecord{
		Id:                  reqDto.RecordId,
		Handler:             reqDto.Handler,
		Operator:            reqDto.Operator,
		Remark:              reqDto.Remark,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
		Utime:               util.GetItPtr(now.Unix()),
	}

	// 创建新的明细记录
	var items []po.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		profitLoss := *itemDto.CheckQuantity - *itemDto.StockQuantity
		itemId := util.GetUUID()
		item := po.InventoryCheckRecordItem{
			Id:                 &itemId,
			CheckRecordId:      reqDto.RecordId,
			ProductId:          itemDto.ProductId,
			StockQuantity:      itemDto.StockQuantity,
			CheckQuantity:      itemDto.CheckQuantity,
			ProfitLossQuantity: util.GetItPtr(profitLoss),
			Ctime:              util.GetItPtr(now.Unix()),
			Utime:              util.GetItPtr(now.Unix()),
			State:              util.GetItPtr(0),
		}
		items = append(items, item)
	}

	// 更新到数据库
	err = inventoryCheckRecordService.UpdateInventoryCheckRecordWithItems(ctx, &record, items)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 构建响应
	respVO := vo.UpdateInventoryCheckRecordRespVO{
		RecordId:            reqDto.RecordId,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
	}

	Result_success[any](ctx, respVO)
}

// @Summary 删除盘点记录
// @Description 删除盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.DeleteInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/delete [post]
func (controller *InventoryCheckRecordController) DeleteInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.DeleteInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	err = inventoryCheckRecordService.DeleteInventoryCheckRecord(ctx, *reqDto.RecordId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, nil)
}
