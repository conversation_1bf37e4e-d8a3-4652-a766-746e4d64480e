package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/application/inventory"

	"github.com/gin-gonic/gin"
)

// InventoryCheckRecordController 盘点记录控制器
type InventoryCheckRecordController struct {
	checkRecordAppService *inventory.InventoryCheckRecordAppService
}

// NewInventoryCheckRecordController 创建盘点记录控制器实例
func NewInventoryCheckRecordController(checkRecordAppService *inventory.InventoryCheckRecordAppService) *InventoryCheckRecordController {
	return &InventoryCheckRecordController{
		checkRecordAppService: checkRecordAppService,
	}
}

// CreateCheckRecord 创建盘点记录
// @Summary 创建盘点记录
// @Description 创建新的盘点记录，支持多商品批量盘点
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.CreateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} common.Result[vo.CreateInventoryCheckRecordRespVO] "成功"
// @Router /api/inventory/check/record/create [post]
func (c *InventoryCheckRecordController) CreateCheckRecord(ctx *gin.Context) {
	var reqDto req.CreateInventoryCheckRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := c.checkRecordAppService.CreateCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// GetCheckRecordList 获取盘点记录列表
// @Summary 获取盘点记录列表
// @Description 获取门店的盘点记录列表，支持搜索和分页
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.QueryInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} common.Result[vo.QueryInventoryCheckRecordRespVO] "成功"
// @Router /api/inventory/check/record/list [post]
func (c *InventoryCheckRecordController) GetCheckRecordList(ctx *gin.Context) {
	var reqDto req.QueryInventoryCheckRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 设置默认值
	if reqDto.PageNum == nil {
		defaultPageNum := 1
		reqDto.PageNum = &defaultPageNum
	}
	if reqDto.PageSize == nil {
		defaultPageSize := 20
		reqDto.PageSize = &defaultPageSize
	}

	result, err := c.checkRecordAppService.GetCheckRecordList(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// GetCheckRecordDetail 获取盘点记录详情
// @Summary 获取盘点记录详情
// @Description 获取单个盘点记录的详细信息，包含所有明细项
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.GetInventoryCheckRecordDetailReqDto true "请求体"
// @Success 200 {object} common.Result[vo.GetInventoryCheckRecordDetailRespVO] "成功"
// @Router /api/inventory/check/record/detail [post]
func (c *InventoryCheckRecordController) GetCheckRecordDetail(ctx *gin.Context) {
	var reqDto req.GetInventoryCheckRecordDetailReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := c.checkRecordAppService.GetCheckRecordDetail(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// UpdateCheckRecord 更新盘点记录
// @Summary 更新盘点记录
// @Description 更新已存在的盘点记录信息
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.UpdateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} common.Result[vo.UpdateInventoryCheckRecordRespVO] "成功"
// @Router /api/inventory/check/record/update [post]
func (c *InventoryCheckRecordController) UpdateCheckRecord(ctx *gin.Context) {
	var reqDto req.UpdateInventoryCheckRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := c.checkRecordAppService.UpdateCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// DeleteCheckRecord 删除盘点记录
// @Summary 删除盘点记录
// @Description 删除指定的盘点记录（软删除）
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.DeleteInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} common.Result[vo.DeleteInventoryCheckRecordRespVO] "成功"
// @Router /api/inventory/check/record/delete [post]
func (c *InventoryCheckRecordController) DeleteCheckRecord(ctx *gin.Context) {
	var reqDto req.DeleteInventoryCheckRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := c.checkRecordAppService.DeleteCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}
