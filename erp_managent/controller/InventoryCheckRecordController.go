package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/application/inventory"
	inventoryImpl "voderpltvv/erp_managent/application/inventory/impl"

	"github.com/gin-gonic/gin"
)

type InventoryCheckRecordController struct{}

var (
	inventoryCheckRecordAppService inventory.InventoryCheckRecordAppService = inventoryImpl.NewInventoryCheckRecordAppService()
)

// @Summary 创建盘点记录
// @Description 创建盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.CreateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.CreateInventoryCheckRecordRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/create [post]
func (controller *InventoryCheckRecordController) CreateInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.CreateInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := inventoryCheckRecordAppService.CreateInventoryCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// @Summary 查询盘点记录列表
// @Description 查询盘点记录列表
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.QueryInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.InventoryCheckRecordVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/list [post]
func (controller *InventoryCheckRecordController) ListInventoryCheckRecords(ctx *gin.Context) {
	reqDto := req.QueryInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := inventoryCheckRecordAppService.QueryInventoryCheckRecords(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// @Summary 查询盘点记录详情
// @Description 查询盘点记录详情
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.GetInventoryCheckRecordDetailReqDto true "请求体"
// @Success 200 {object} Result[vo.GetInventoryCheckRecordDetailRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/detail [post]
func (controller *InventoryCheckRecordController) GetInventoryCheckRecordDetail(ctx *gin.Context) {
	reqDto := req.GetInventoryCheckRecordDetailReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := inventoryCheckRecordAppService.GetInventoryCheckRecordDetail(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// @Summary 更新盘点记录
// @Description 更新盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.UpdateInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.UpdateInventoryCheckRecordRespVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/update [post]
func (controller *InventoryCheckRecordController) UpdateInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.UpdateInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := inventoryCheckRecordAppService.UpdateInventoryCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// @Summary 删除盘点记录
// @Description 删除盘点记录
// @Tags 盘点记录
// @Accept json
// @Produce json
// @Param body body req.DeleteInventoryCheckRecordReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/inventory/check/record/delete [post]
func (controller *InventoryCheckRecordController) DeleteInventoryCheckRecord(ctx *gin.Context) {
	reqDto := req.DeleteInventoryCheckRecordReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	err = inventoryCheckRecordAppService.DeleteInventoryCheckRecord(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, nil)
}
