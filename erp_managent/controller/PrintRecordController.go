package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/print"
	rechargePrintApp "voderpltvv/erp_managent/application/print/membercard"
	"voderpltvv/erp_managent/domain/print/service"

	"github.com/gin-gonic/gin"
)

// PrintRecordController 打印记录控制器
type PrintRecordController struct {
	appService                 *print.PrintRecordAppService
	productOutPrintAppService  *print.ProductOutPrintRecordAppService
	shiftChangePrintAppService *print.ShiftChangePrintRecordAppService
	rechargePrintAppService    *rechargePrintApp.RechargePrintRecordAppService
	winePrintAppService        *print.WinePrintRecordAppService
	openCardPrintAppService    *rechargePrintApp.OpenCardPrintRecordAppService
	printDomainService         service.PrintDomainService
}

// NewPrintRecordController 创建打印记录控制器
func NewPrintRecordController() *PrintRecordController {
	return &PrintRecordController{
		appService:                 print.NewPrintRecordAppService(),
		productOutPrintAppService:  print.NewProductOutPrintRecordAppService(),
		shiftChangePrintAppService: print.NewShiftChangePrintRecordAppService(),
		rechargePrintAppService:    rechargePrintApp.NewRechargePrintRecordAppService(),
		winePrintAppService:        print.NewWinePrintRecordAppService(),
		openCardPrintAppService:    rechargePrintApp.NewOpenCardPrintRecordAppService(),
		printDomainService:         service.NewPrintDomainService(),
	}
}

// ======================= 开台单打印记录接口 =======================

// CreateOpenTablePrintRecord 创建开台单打印记录
// @Summary 创建开台单打印记录
// @Description 创建开台单打印记录
// @Tags 打印记录/开台单
// @Accept json
// @Produce json
// @Param body body req.CreateOpenTablePrintRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.OpenTablePrintRecordVO] "成功，返回打印记录实体"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/open-table/create [post]
func (c *PrintRecordController) CreateOpenTablePrintRecord(ctx *gin.Context) {
	var reqDto req.CreateOpenTablePrintRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	record, err := c.appService.CreateOpenTablePrintRecord(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[vo.OpenTablePrintRecordVO](ctx, record)
}

// GetOpenTablePrintRecordsBySessionId 根据会话ID获取开台单打印记录
// @Summary 根据会话ID获取开台单打印记录
// @Description 根据会话ID获取开台单打印记录
// @Tags 打印记录/开台单
// @Accept json
// @Produce json
// @Param body body req.GetOpenTablePrintRecordsBySessionIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.OpenTablePrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/open-table/session [post]
func (c *PrintRecordController) GetOpenTablePrintRecordsBySessionId(ctx *gin.Context) {
	var reqDto req.GetOpenTablePrintRecordsBySessionIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.appService.GetOpenTablePrintRecordsBySessionId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.OpenTablePrintRecordVO](ctx, records)
}

// GetCancelOpenTablePrintRecordsBySessionId 根据会话ID获取取消开台单打印记录
// @Summary 根据会话ID获取取消开台单打印记录
// @Description 根据会话ID获取取消开台单打印记录
// @Tags 打印记录/取消开台单
// @Accept json
// @Produce json
// @Param body body req.GetCancelOpenTablePrintRecordsBySessionIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.CancelOpenTablePrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/cancel-open-table/session [post]
func (c *PrintRecordController) GetCancelOpenTablePrintRecordsBySessionId(ctx *gin.Context) {
	var reqDto req.GetCancelOpenTablePrintRecordsBySessionIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.appService.GetCancelOpenTablePrintRecordsBySessionId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.CancelOpenTablePrintRecordVO](ctx, records)
}

// ======================= 续房单打印记录接口 =======================

// CreateRoomExtensionPrintRecord 创建续房单打印记录
// @Summary 创建续房单打印记录
// @Description 创建续房单打印记录
// @Tags 打印记录/续房单
// @Accept json
// @Produce json
// @Param body body req.CreateRoomExtensionPrintRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.RoomExtensionPrintRecordVO] "成功，返回打印记录实体"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/room-extension/create [post]
func (c *PrintRecordController) CreateRoomExtensionPrintRecord(ctx *gin.Context) {
	var reqDto req.CreateRoomExtensionPrintRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	record, err := c.appService.CreateRoomExtensionPrintRecord(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[vo.RoomExtensionPrintRecordVO](ctx, record)
}

// GetRoomExtensionPrintRecordsBySessionId 根据会话ID获取续房单打印记录
// @Summary 根据会话ID获取续房单打印记录
// @Description 根据会话ID获取续房单打印记录
// @Tags 打印记录/续房单
// @Accept json
// @Produce json
// @Param body body req.GetRoomExtensionPrintRecordsBySessionIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.RoomExtensionPrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/room-extension/session [post]
func (c *PrintRecordController) GetRoomExtensionPrintRecordsBySessionId(ctx *gin.Context) {
	var reqDto req.GetRoomExtensionPrintRecordsBySessionIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.appService.GetRoomExtensionPrintRecordsBySessionId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.RoomExtensionPrintRecordVO](ctx, records)
}

// ======================= 结账单打印记录接口 =======================

// CreateCheckoutPrintRecord 创建结账单打印记录
// @Summary 创建结账单打印记录
// @Description 创建结账单打印记录
// @Tags 打印记录/结账单
// @Accept json
// @Produce json
// @Param body body req.CreateCheckoutPrintRecordReqDto true "请求体"
// @Success 200 {object} Result[vo.CheckoutPrintRecordVO] "成功，返回打印记录实体"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/checkout/create [post]
func (c *PrintRecordController) CreateCheckoutPrintRecord(ctx *gin.Context) {
	var reqDto req.CreateCheckoutPrintRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	record, err := c.appService.CreateCheckoutPrintRecord(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[vo.CheckoutPrintRecordVO](ctx, record)
}

// GetCheckoutPrintRecordsBySessionId 根据会话ID获取结账单打印记录
// @Summary 根据会话ID获取结账单打印记录
// @Description 根据会话ID获取结账单打印记录
// @Tags 打印记录/结账单
// @Accept json
// @Produce json
// @Param body body req.GetCheckoutPrintRecordsBySessionIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.CheckoutPrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/checkout/session [post]
func (c *PrintRecordController) GetCheckoutPrintRecordsBySessionId(ctx *gin.Context) {
	var reqDto req.GetCheckoutPrintRecordsBySessionIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.appService.GetCheckoutPrintRecordsBySessionId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.CheckoutPrintRecordVO](ctx, records)
}

// GetCheckoutPrintRecordsByPayBillIds 根据账单号数组获取结账单打印记录
// @Summary 根据账单号数组获取结账单打印记录
// @Description 根据账单号数组获取结账单打印记录
// @Tags 打印记录/结账单
// @Accept json
// @Produce json
// @Param body body req.GetCheckoutPrintRecordsByPayBillIdsReqDto true "请求体"
// @Success 200 {object} Result[[]vo.CheckoutPrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/checkout/pay-bill-ids [post]
func (c *PrintRecordController) GetCheckoutPrintRecordsByPayBillIds(ctx *gin.Context) {
	var reqDto req.GetCheckoutPrintRecordsByPayBillIdsReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.appService.GetCheckoutPrintRecordsByPayBillIds(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.CheckoutPrintRecordVO](ctx, records)
}

// ======================= 出品单打印记录接口 =======================

// CreateProductOutPrintTasks 创建出品单打印任务
// @Summary 创建出品单打印任务
// @Description 根据订单号创建出品单打印任务，返回PrintTask格式数据
// @Tags 打印记录/出品单
// @Accept json
// @Produce json
// @Param body body req.CreateProductOutPrintRecordReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ProductOutPrintTaskVO] "成功，返回打印任务数据"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/product-out/create [post]
func (c *PrintRecordController) CreateProductOutPrintTasks(ctx *gin.Context) {
	var reqDto req.CreateProductOutPrintRecordReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	printTasks, err := c.productOutPrintAppService.CreateProductOutPrintTasks(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.ProductOutPrintTaskVO](ctx, printTasks)
}

// GetProductOutPrintRecordsBySessionId 根据会话ID获取出品单打印记录
// @Summary 根据会话ID获取出品单打印记录
// @Description 根据会话ID获取出品单打印记录
// @Tags 打印记录/出品单
// @Accept json
// @Produce json
// @Param body body req.GetProductOutPrintRecordsBySessionIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ProductOutPrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/product-out/session [post]
func (c *PrintRecordController) GetProductOutPrintRecordsBySessionId(ctx *gin.Context) {
	var reqDto req.GetProductOutPrintRecordsBySessionIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.productOutPrintAppService.GetProductOutPrintRecordsBySessionId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.ProductOutPrintRecordVO](ctx, records)
}

// ======================= 交班单打印记录接口 =======================

// CreateShiftChangePrintRecord 创建交班单打印记录
// @Summary 创建交班单打印记录
// @Description 创建交班单打印记录
// @Tags 打印记录/交班单
// @Accept json
// @Produce json
// @Param body body req.CreateShiftChangePrintRecordReq true "请求体"
// @Success 200 {object} Result[[]vo.ShiftChangePrintRecordVO] "成功，返回打印记录实体数组"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/shift-change/create [post]
func (c *PrintRecordController) CreateShiftChangePrintRecord(ctx *gin.Context) {
	var reqDto req.CreateShiftChangePrintRecordReq
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.shiftChangePrintAppService.CreateShiftChangePrintRecord(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.ShiftChangePrintRecordVO](ctx, records)
}

// GetShiftChangePrintRecordsByHandNo 根据交班单号获取交班单打印记录
// @Summary 根据交班单号获取交班单打印记录
// @Description 根据交班单号获取交班单打印记录
// @Tags 打印记录/交班单
// @Accept json
// @Produce json
// @Param body body req.GetShiftChangePrintRecordsByHandNoReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ShiftChangePrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/shift-change/hand-no [post]
func (c *PrintRecordController) GetShiftChangePrintRecordsByHandNo(ctx *gin.Context) {
	var reqDto req.GetShiftChangePrintRecordsByHandNoReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.shiftChangePrintAppService.GetShiftChangePrintRecordsByHandNo(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]vo.ShiftChangePrintRecordVO](ctx, records)
}

// ======================= 充值打印记录接口 =======================

// GetRechargePrintRecordsByBillNo 根据账单号获取充值打印记录
// @Summary 根据账单号获取充值打印记录
// @Description 根据账单号获取充值打印记录
// @Tags 打印记录/充值单
// @Accept json
// @Produce json
// @Param body body req.GetRechargePrintRecordsByBillNoReqDto true "请求体"
// @Success 200 {object} Result[[]vo.RechargePrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/recharge/bill-no [post]
func (c *PrintRecordController) GetRechargePrintRecordsByBillNo(ctx *gin.Context) {
	var reqDto req.GetRechargePrintRecordsByBillNoReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.rechargePrintAppService.GetRechargePrintRecordsByBillNo(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]*vo.RechargePrintRecordVO](ctx, records)
}

// GetRechargePrintRecordsByMemberCardId 根据会员卡ID获取充值打印记录
// @Summary 根据会员卡ID获取充值打印记录
// @Description 根据会员卡ID获取充值打印记录
// @Tags 打印记录/充值单
// @Accept json
// @Produce json
// @Param body body req.GetRechargePrintRecordsByMemberCardIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.RechargePrintRecordVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/recharge/member-card-id [post]
func (c *PrintRecordController) GetRechargePrintRecordsByMemberCardId(ctx *gin.Context) {
	var reqDto req.GetRechargePrintRecordsByMemberCardIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.rechargePrintAppService.GetRechargePrintRecordsByMemberCardId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[[]*vo.RechargePrintRecordVO](ctx, records)
}

// ======================= 存取酒打印记录查询接口 =======================

// QueryWinePrintRecordByBusinessID 根据业务单号查询存取酒打印记录
// @Summary 根据业务单号查询存取酒打印记录
// @Description 统一接口查询存取酒打印记录，支持存酒单(PS前缀)、取酒单(PW/PD前缀)、续存单(RNPS前缀)的查询
// @Tags 打印记录/存取酒
// @Accept json
// @Produce json
// @Param body body req.WinePrintRecordQueryReqDto true "请求体"
// @Success 200 {object} Result[vo.WineStoragePrintRecordVO] "成功，返回存酒打印记录对象"
// @Success 200 {object} Result[vo.WineWithdrawPrintRecordVO] "成功，返回取酒打印记录对象"
// @Success 200 {object} Result[vo.WineRenewalPrintRecordVO] "成功，返回续存打印记录对象"
// @Success 200 {object} Result[any] "没有找到记录时返回null"
// @Failure 400 {object} Result[any] "参数验证失败"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/print-record/wine/query [post]
func (c *PrintRecordController) QueryWinePrintRecordByBusinessID(ctx *gin.Context) {
	var reqDto req.WinePrintRecordQueryReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数验证失败: "+err.Error())
		return
	}

	// 参数验证
	if reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	if reqDto.PrintBusinessID == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "业务单号不能为空")
		return
	}

	// 调用应用服务查询打印记录
	result, err := c.winePrintAppService.QueryWinePrintRecordByBusinessID(ctx, reqDto.VenueId, reqDto.PrintBusinessID)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询打印记录失败: "+err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// ======================= 开卡打印记录查询接口 =======================

// GetOpenCardPrintRecordsByBillNo 根据账单号获取开卡打印记录
// @Summary 根据账单号获取开卡打印记录
// @Description 根据账单号获取开卡打印记录
// @Tags 打印记录/开卡单
// @Accept json
// @Produce json
// @Param body body req.GetOpenCardPrintRecordsByBillNoReqDto true "请求体"
// @Success 200 {object} Result[[]vo.OpenCardPrintRecordVO] "成功，返回开卡打印记录数组"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/open-card/bill-no [post]
func (c *PrintRecordController) GetOpenCardPrintRecordsByBillNo(ctx *gin.Context) {
	var reqDto req.GetOpenCardPrintRecordsByBillNoReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.openCardPrintAppService.GetOpenCardPrintRecordsByBillNo(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success(ctx, records)
}

// GetOpenCardPrintRecordsByMemberCardId 根据会员卡ID获取开卡打印记录
// @Summary 根据会员卡ID获取开卡打印记录
// @Description 根据会员卡ID获取开卡打印记录
// @Tags 打印记录/开卡单
// @Accept json
// @Produce json
// @Param body body req.GetOpenCardPrintRecordsByMemberCardIdReqDto true "请求体"
// @Success 200 {object} Result[[]vo.OpenCardPrintRecordVO] "成功，返回开卡打印记录数组"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/open-card/member-card-id [post]
func (c *PrintRecordController) GetOpenCardPrintRecordsByMemberCardId(ctx *gin.Context) {
	var reqDto req.GetOpenCardPrintRecordsByMemberCardIdReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	records, err := c.openCardPrintAppService.GetOpenCardPrintRecordsByMemberCardId(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success(ctx, records)
}

// ======================= 通用打印记录操作接口 =======================

// UpdatePrintRecordStatus 更新打印记录状态
// @Summary 更新打印记录状态
// @Description 更新打印记录状态（如已打印、打印失败等）
// @Tags 打印记录/通用
// @Accept json
// @Produce json
// @Param body body req.UpdatePrintRecordStatusReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/print-record/update-status [post]
func (c *PrintRecordController) UpdatePrintRecordStatus(ctx *gin.Context) {
	var reqDto req.UpdatePrintRecordStatusReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	err := c.appService.UpdatePrintRecordStatus(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, nil)
}
