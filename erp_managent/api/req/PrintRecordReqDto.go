package req

// CreateOpenTablePrintRecordReqDto 创建开台单打印记录请求DTO
type CreateOpenTablePrintRecordReqDto struct {
	VenueId   string   `json:"venueId" binding:"required"`   // 门店ID
	SessionId string   `json:"sessionId" binding:"required"` // 开台单号/场次ID
	OrderNos  []string `json:"orderNos"`                     // 订单编号数组，用于获取订单详情来构建开台单数据
}

// GetOpenTablePrintRecordsBySessionIdReqDto 根据会话ID获取开台单打印记录请求DTO
type GetOpenTablePrintRecordsBySessionIdReqDto struct {
	VenueId   string `json:"venueId" binding:"required"`   // 门店ID
	SessionId string `json:"sessionId" binding:"required"` // 会话ID
}

// GetCancelOpenTablePrintRecordsBySessionIdReqDto 根据会话ID获取取消开台单打印记录请求DTO
type GetCancelOpenTablePrintRecordsBySessionIdReqDto struct {
	VenueId   string `json:"venueId" binding:"required"`   // 门店ID
	SessionId string `json:"sessionId" binding:"required"` // 会话ID
}

// CreateCheckoutPrintRecordReqDto 创建结账单打印记录请求DTO
type CreateCheckoutPrintRecordReqDto struct {
	VenueId   string   `json:"venueId" binding:"required"`   // 门店ID
	SessionId string   `json:"sessionId" binding:"required"` // 会话ID
	PayBillId string   `json:"payBillId" binding:"required"` // 结账单ID
	OrderNos  []string `json:"orderNos"`                     // 订单编号数组，用于获取订单详情来构建结账单数据
}

// GetCheckoutPrintRecordsBySessionIdReqDto 根据会话ID获取结账单打印记录请求DTO
type GetCheckoutPrintRecordsBySessionIdReqDto struct {
	VenueId   string `json:"venueId" binding:"required"`   // 门店ID
	SessionId string `json:"sessionId" binding:"required"` // 会话ID
}

// GetCheckoutPrintRecordsByPayBillIdsReqDto 根据账单号数组获取结账单打印记录请求DTO
type GetCheckoutPrintRecordsByPayBillIdsReqDto struct {
	VenueId    string   `json:"venueId" binding:"required"`    // 门店ID
	PayBillIds []string `json:"payBillIds" binding:"required"` // 账单号数组
}

// CreateRoomExtensionPrintRecordReqDto 创建续房单打印记录请求DTO
type CreateRoomExtensionPrintRecordReqDto struct {
	VenueId   string   `json:"venueId" binding:"required"`   // 门店ID
	SessionId string   `json:"sessionId" binding:"required"` // 续房单号/场次ID
	OrderNos  []string `json:"orderNos"`                     // 订单编号数组，用于获取订单详情来构建续房单数据
}

// GetRoomExtensionPrintRecordsBySessionIdReqDto 根据会话ID获取续房单打印记录请求DTO
type GetRoomExtensionPrintRecordsBySessionIdReqDto struct {
	VenueId   string `json:"venueId" binding:"required"`   // 门店ID
	SessionId string `json:"sessionId" binding:"required"` // 会话ID
}

// ==================== 通用打印记录操作DTO ====================

// UpdatePrintRecordStatusReqDto 更新打印记录状态请求DTO
type UpdatePrintRecordStatusReqDto struct {
	VenueId   string `json:"venueId" binding:"required"` // 门店ID
	PrintId   string `json:"printId" binding:"required"` // 打印记录ID
	Status    int    `json:"status" binding:"required"`  // 打印状态 (0:待打印, 1:打印成功, 2:打印失败)
	ErrorMsg  string `json:"errorMsg"`                   // 错误信息 (打印失败时必填)
	Remark    string `json:"remark"`                     // 备注信息 (可选)
	PrintTime *int64 `json:"printTime"`                  // 打印时间戳 (毫秒) (可选)
}

// ==================== 充值打印记录相关DTO（在MemberCardPrintRecordReqDto.go中定义）====================
// 充值相关DTO已在其他文件中定义，此处仅作注释说明

// ======================= 存酒打印记录请求DTO =======================

// CreateWineStoragePrintRecordReqDto 创建存酒打印记录请求DTO
type CreateWineStoragePrintRecordReqDto struct {
	VenueId         string `json:"venueId" binding:"required"`         // 门店ID
	SessionId       string `json:"sessionId" binding:"required"`       // 场次ID
	StorageRecordId string `json:"storageRecordId" binding:"required"` // 存酒记录ID
}

// ======================= 取酒打印记录请求DTO =======================

// CreateWineWithdrawPrintRecordReqDto 创建取酒打印记录请求DTO
type CreateWineWithdrawPrintRecordReqDto struct {
	VenueId            string `json:"venueId" binding:"required"`            // 门店ID
	SessionId          string `json:"sessionId" binding:"required"`          // 场次ID
	WithdrawalRecordId string `json:"withdrawalRecordId" binding:"required"` // 取酒记录ID
}

// ======================= 交班单相关DTO =======================

// CreateShiftChangePrintRecordReqDto 创建交班单打印记录请求DTO
type CreateShiftChangePrintRecordReqDto struct {
	VenueId string `json:"venueId" binding:"required"` // 门店ID
	HandNo  string `json:"handNo" binding:"required"`  // 交班号
}

// CreateCancelOpenTablePrintRecordReqDto 创建取消开台单打印记录请求DTO
type CreateCancelOpenTablePrintRecordReqDto struct {
	VenueId      string   `json:"venueId" binding:"required"`   // 门店ID
	SessionId    string   `json:"sessionId" binding:"required"` // 会话ID（原开台单号）
	OperatorId   string   `json:"operatorId"`                   // 操作员ID
	CancelReason string   `json:"cancelReason"`                 // 取消原因
	RefundAmount string   `json:"refundAmount"`                 // 退款金额
	PayBillIds   []string `json:"payBillIds"`                   // 账单ID数组（用于计算退款金额）
	DeviceName   string   `json:"deviceName"`                   // 设备名称
}

// 续存打印记录相关DTO

// WinePrintRecordQueryReqDto 存取酒打印记录查询请求DTO（通过printBusinessID查询）
type WinePrintRecordQueryReqDto struct {
	VenueId         string `json:"venueId" binding:"required"`         // 门店ID
	PrintBusinessID string `json:"printBusinessID" binding:"required"` // 打印业务ID（存酒单号、取酒单号或续存单号，支持PS、PW、RN前缀）
}

// 已废弃的续存查询DTO - 已统一使用WinePrintRecordQueryReqDto
// GetWineRenewalPrintRecordsByBusinessIdReqDto 根据业务ID（续存单号）获取续存打印记录请求DTO
// type GetWineRenewalPrintRecordsByBusinessIdReqDto struct {
// 	VenueId    string `json:"venueId" validate:"required" label:"门店ID"`
// 	BusinessId string `json:"businessId" validate:"required" label:"业务ID"`
// }

// GetWineRenewalPrintRecordsByStorageOrderNoReqDto 根据存酒单号获取续存打印记录请求DTO
// type GetWineRenewalPrintRecordsByStorageOrderNoReqDto struct {
// 	VenueId        string `json:"venueId" validate:"required" label:"门店ID"`
// 	StorageOrderNo string `json:"storageOrderNo" validate:"required" label:"存酒单号"`
// }

// GetWineRenewalPrintRecordsByMemberIdReqDto 根据会员ID获取续存打印记录请求DTO
// type GetWineRenewalPrintRecordsByMemberIdReqDto struct {
// 	VenueId  string `json:"venueId" validate:"required" label:"门店ID"`
// 	MemberId string `json:"memberId" validate:"required" label:"会员ID"`
// }
