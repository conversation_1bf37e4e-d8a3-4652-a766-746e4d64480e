package req

// CreateInventoryCheckRecordReqDto 创建盘点记录请求DTO
type CreateInventoryCheckRecordReqDto struct {
	VenueId  *string                             `json:"venueId" binding:"required"`     // 门店ID
	Handler  *string                             `json:"handler" binding:"required"`     // 经手人
	Operator *string                             `json:"operator" binding:"required"`    // 操作人
	Remark   *string                             `json:"remark"`                         // 备注
	Items    []CreateInventoryCheckRecordItemDto `json:"items" binding:"required,min=1"` // 明细项
}

// CreateInventoryCheckRecordItemDto 创建盘点记录明细请求DTO
type CreateInventoryCheckRecordItemDto struct {
	ProductId     *string `json:"productId" binding:"required"`  // 商品ID
	StockQuantity *int    `json:"stockQuantity" binding:"min=0"` // 库存数量（盘点之前）
	CheckQuantity *int    `json:"checkQuantity" binding:"min=0"` // 盘点数量（盘点之后）
}

// UpdateInventoryCheckRecordReqDto 更新盘点记录请求DTO
type UpdateInventoryCheckRecordReqDto struct {
	RecordId *string                             `json:"recordId" binding:"required"`    // 盘点记录ID
	Handler  *string                             `json:"handler" binding:"required"`     // 经手人
	Operator *string                             `json:"operator" binding:"required"`    // 操作人
	Remark   *string                             `json:"remark"`                         // 备注
	Items    []UpdateInventoryCheckRecordItemDto `json:"items" binding:"required,min=1"` // 明细项
}

// UpdateInventoryCheckRecordItemDto 更新盘点记录明细请求DTO
type UpdateInventoryCheckRecordItemDto struct {
	ProductId     *string `json:"productId" binding:"required"`  // 商品ID
	StockQuantity *int    `json:"stockQuantity" binding:"min=0"` // 库存数量（盘点之前）
	CheckQuantity *int    `json:"checkQuantity" binding:"min=0"` // 盘点数量（盘点之后）
}

// QueryInventoryCheckRecordReqDto 查询盘点记录请求DTO
type QueryInventoryCheckRecordReqDto struct {
	VenueId   *string `json:"venueId" binding:"required"`       // 门店ID
	SearchKey *string `json:"searchKey"`                        // 搜索关键词
	PageNum   *int    `json:"pageNum" binding:"min=1"`          // 页码
	PageSize  *int    `json:"pageSize" binding:"min=1,max=100"` // 页大小
}

// GetInventoryCheckRecordDetailReqDto 获取盘点记录详情请求DTO
type GetInventoryCheckRecordDetailReqDto struct {
	RecordId *string `json:"recordId" binding:"required"` // 盘点记录ID
}

// DeleteInventoryCheckRecordReqDto 删除盘点记录请求DTO
type DeleteInventoryCheckRecordReqDto struct {
	RecordId *string `json:"recordId" binding:"required"` // 盘点记录ID
}
