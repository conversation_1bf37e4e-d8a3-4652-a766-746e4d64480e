package vo

import (
	"voderpltvv/erp_managent/domain/print/model/winerenewal"
)

// WineRenewalPrintRecordVO 续存打印记录VO
type WineRenewalPrintRecordVO struct {
	Id                  string                          `json:"id"`                  // 打印记录ID
	VenueId             string                          `json:"venueId"`             // 门店ID
	PrintType           string                          `json:"printType"`           // 打印类型
	PrintNo             string                          `json:"printNo"`             // 打印单号
	PrintTime           int64                           `json:"printTime"`           // 打印时间戳
	BusinessId          string                          `json:"businessId"`          // 业务ID（续存单号）
	OperatorId          string                          `json:"operatorId"`          // 操作员ID
	OperatorName        string                          `json:"operatorName"`        // 操作员姓名
	Status              int                             `json:"status"`              // 打印状态
	WineRenewalBillData winerenewal.WineRenewalBillData `json:"wineRenewalBillData"` // 续存单据数据
}

// WineRenewalBillDataVO 续存单据数据VO
type WineRenewalBillDataVO struct {
	VenueName          string              `json:"venueName,omitempty"`          // 门店名称
	RenewalRecordId    string              `json:"renewalRecordId"`              // 续存记录ID
	StorageNo          string              `json:"storageNo"`                    // 存酒单号
	RenewalDate        string              `json:"renewalDate"`                  // 续存日期
	RenewalTime        string              `json:"renewalTime"`                  // 续存时间
	MemberInfo         MemberInfoVO        `json:"memberInfo"`                   // 会员信息
	RenewalItems       []WineRenewalItemVO `json:"renewalItems"`                 // 续存项目列表
	TotalQuantity      int                 `json:"totalQuantity"`                // 总数量
	TotalAmount        float64             `json:"totalAmount"`                  // 总金额
	OperatorName       string              `json:"operatorName,omitempty"`       // 操作人名称
	OperatorId         string              `json:"operatorId,omitempty"`         // 操作人ID
	PrintTime          string              `json:"printTime"`                    // 打印时间
	Remark             string              `json:"remark,omitempty"`             // 备注
	OriginalExpireDate string              `json:"originalExpireDate,omitempty"` // 原过期日期
	NewExpireDate      string              `json:"newExpireDate,omitempty"`      // 新过期日期
	ExtendedDays       int                 `json:"extendedDays,omitempty"`       // 延长天数
}

// WineRenewalItemVO 续存项目VO
type WineRenewalItemVO struct {
	ProductID          string  `json:"productID"`          // 商品ID
	ProductName        string  `json:"productName"`        // 商品名称
	Brand              string  `json:"brand"`              // 品牌
	Specification      string  `json:"specification"`      // 规格
	Quantity           int     `json:"quantity"`           // 寄存数量
	Unit               string  `json:"unit"`               // 单位
	Price              float64 `json:"price"`              // 单价
	TotalAmount        float64 `json:"totalAmount"`        // 总金额
	OriginalExpireDate string  `json:"originalExpireDate"` // 原过期日期
	NewExpireDate      string  `json:"newExpireDate"`      // 新过期日期
	ExtendedDays       int     `json:"extendedDays"`       // 延长天数
	Remark             string  `json:"remark"`             // 备注
}

// MemberInfoVO 会员信息VO（如果不存在的话）
type MemberInfoVO struct {
	MemberID    string `json:"memberID,omitempty"`    // 客户ID
	MemberName  string `json:"memberName,omitempty"`  // 客户姓名
	MemberPhone string `json:"memberPhone,omitempty"` // 客户手机号
	CardNo      string `json:"cardNo,omitempty"`      // 会员卡号
	LevelName   string `json:"levelName,omitempty"`   // 会员等级名称
}
