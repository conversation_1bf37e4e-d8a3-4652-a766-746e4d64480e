package vo

// CreateInventoryCheckRecordRespVO 创建盘点记录响应VO
type CreateInventoryCheckRecordRespVO struct {
	RecordId            *string `json:"recordId"`            // 盘点记录ID
	RecordNumber        *string `json:"recordNumber"`        // 盘点单号
	ProfitQuantityTotal *int    `json:"profitQuantityTotal"` // 盘盈数量合计
	LossQuantityTotal   *int    `json:"lossQuantityTotal"`   // 盘亏数量合计
}

// QueryInventoryCheckRecordRespVO 查询盘点记录列表响应VO
type QueryInventoryCheckRecordRespVO struct {
	Records []InventoryCheckRecordVO `json:"records"` // 盘点记录列表
	Total   int64                    `json:"total"`   // 总数
}

// InventoryCheckRecordVO 盘点记录VO
type InventoryCheckRecordVO struct {
	RecordId            *string `json:"recordId"`            // 盘点记录ID
	RecordNumber        *string `json:"recordNumber"`        // 盘点单号
	Handler             *string `json:"handler"`             // 经手人
	Operator            *string `json:"operator"`            // 操作人
	CheckTime           *int64  `json:"checkTime"`           // 盘点时间
	Remark              *string `json:"remark"`              // 备注
	TotalProducts       *int    `json:"totalProducts"`       // 商品总数
	ProfitQuantityTotal *int    `json:"profitQuantityTotal"` // 盘盈数量合计
	LossQuantityTotal   *int    `json:"lossQuantityTotal"`   // 盘亏数量合计
}

// GetInventoryCheckRecordDetailRespVO 获取盘点记录详情响应VO
type GetInventoryCheckRecordDetailRespVO struct {
	Record InventoryCheckRecordDetailVO `json:"record"` // 盘点记录详情
	Items  []InventoryCheckRecordItemVO `json:"items"`  // 盘点明细列表
}

// InventoryCheckRecordDetailVO 盘点记录详情VO
type InventoryCheckRecordDetailVO struct {
	RecordId            *string `json:"recordId"`            // 盘点记录ID
	RecordNumber        *string `json:"recordNumber"`        // 盘点单号
	Handler             *string `json:"handler"`             // 经手人
	Operator            *string `json:"operator"`            // 操作人
	CheckTime           *int64  `json:"checkTime"`           // 盘点时间
	Remark              *string `json:"remark"`              // 备注
	TotalProducts       *int    `json:"totalProducts"`       // 商品总数
	ProfitQuantityTotal *int    `json:"profitQuantityTotal"` // 盘盈数量合计
	LossQuantityTotal   *int    `json:"lossQuantityTotal"`   // 盘亏数量合计
}

// InventoryCheckRecordItemVO 盘点记录明细VO
type InventoryCheckRecordItemVO struct {
	ProductId          *string `json:"productId"`          // 商品ID
	ProductName        *string `json:"productName"`        // 商品名称
	ProductImage       *string `json:"productImage"`       // 商品图片
	Unit               *string `json:"unit"`               // 单位
	StockQuantity      *int    `json:"stockQuantity"`      // 库存数量（盘点之前）
	CheckQuantity      *int    `json:"checkQuantity"`      // 盘点数量（盘点之后）
	ProfitLossQuantity *int    `json:"profitLossQuantity"` // 盈亏数量
}

// UpdateInventoryCheckRecordRespVO 更新盘点记录响应VO
type UpdateInventoryCheckRecordRespVO struct {
	RecordId            *string `json:"recordId"`            // 盘点记录ID
	ProfitQuantityTotal *int    `json:"profitQuantityTotal"` // 盘盈数量合计
	LossQuantityTotal   *int    `json:"lossQuantityTotal"`   // 盘亏数量合计
}

// DeleteInventoryCheckRecordRespVO 删除盘点记录响应VO
type DeleteInventoryCheckRecordRespVO struct {
	RecordId *string `json:"recordId"` // 盘点记录ID
	Success  *bool   `json:"success"`  // 是否成功
}
