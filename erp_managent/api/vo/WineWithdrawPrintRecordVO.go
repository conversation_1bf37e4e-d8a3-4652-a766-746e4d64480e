package vo

import (
	"voderpltvv/erp_managent/domain/print/model/valueobject"
)

// WineWithdrawPrintRecordVO 取酒单打印记录VO
type WineWithdrawPrintRecordVO struct {
	ID                 string `json:"id"`                 // 打印记录ID
	VenueID            string `json:"venueId"`            // 门店ID
	PrintType          string `json:"printType"`          // 打印类型
	PrintNo            string `json:"printNo"`            // 打印单号
	PrintTime          int64  `json:"printTime"`          // 打印时间
	SessionID          string `json:"sessionId"`          // 场次ID
	WithdrawalRecordId string `json:"withdrawalRecordId"` // 取酒记录ID
	OperatorID         string `json:"operatorId"`         // 操作员ID
	OperatorName       string `json:"operatorName"`       // 操作员姓名
	DeviceName         string `json:"deviceName"`         // 打印设备名称
	Status             int    `json:"status"`             // 打印状态
	ErrorMsg           string `json:"errorMsg"`           // 错误信息
	Remark             string `json:"remark"`             // 备注
	CreateTime         int64  `json:"createTime"`         // 创建时间
	UpdateTime         int64  `json:"updateTime"`         // 更新时间

	// 取酒单特有字段
	WineWithdrawalBillData *WineWithdrawalBillDataVO `json:"wineWithdrawalBillData,omitempty"` // 取酒单数据
}

// WineWithdrawalBillDataVO 取酒单数据VO
type WineWithdrawalBillDataVO struct {
	VenueName           string                           `json:"venueName,omitempty"`           // 门店名称
	WithdrawalRecordId  string                           `json:"withdrawalRecordId"`            // 取酒记录ID
	SessionId           string                           `json:"sessionId"`                     // 场次ID
	WithdrawalNo        string                           `json:"withdrawalNo"`                  // 取酒单号
	WithdrawalDate      string                           `json:"withdrawalDate"`                // 取酒日期 (YYYY-MM-DD格式)
	WithdrawalTime      string                           `json:"withdrawalTime"`                // 取酒时间 (HH:MM:SS格式)
	MemberInfo          valueobject.MemberInfo           `json:"memberInfo"`                    // 会员信息
	Items               []valueobject.WineWithdrawalItem `json:"items"`                         // 取酒项目列表
	TotalQuantity       int                              `json:"totalQuantity"`                 // 总取酒数量
	TotalAmount         float64                          `json:"totalAmount"`                   // 总金额
	StorageLocation     string                           `json:"storageLocation,omitempty"`     // 原存放位置
	CashierName         string                           `json:"cashierName,omitempty"`         // 收银员名称
	CashierId           string                           `json:"cashierId,omitempty"`           // 收银员ID
	PrintTime           string                           `json:"printTime"`                     // 打印时间
	Remark              string                           `json:"remark,omitempty"`              // 备注
	WithdrawalReason    string                           `json:"withdrawalReason,omitempty"`    // 取酒原因
	RemainingItemsTotal int                              `json:"remainingItemsTotal,omitempty"` // 剩余总数量
}
