package vo

import (
	"voderpltvv/erp_managent/domain/print/model/valueobject"
)

// WineStoragePrintRecordVO 存酒单打印记录VO
type WineStoragePrintRecordVO struct {
	ID              string `json:"id"`              // 打印记录ID
	VenueID         string `json:"venueId"`         // 门店ID
	PrintType       string `json:"printType"`       // 打印类型
	PrintNo         string `json:"printNo"`         // 打印单号
	PrintTime       int64  `json:"printTime"`       // 打印时间
	SessionID       string `json:"sessionId"`       // 场次ID
	StorageRecordId string `json:"storageRecordId"` // 存酒记录ID
	OperatorID      string `json:"operatorId"`      // 操作员ID
	OperatorName    string `json:"operatorName"`    // 操作员姓名
	DeviceName      string `json:"deviceName"`      // 打印设备名称
	Status          int    `json:"status"`          // 打印状态
	ErrorMsg        string `json:"errorMsg"`        // 错误信息
	Remark          string `json:"remark"`          // 备注
	CreateTime      int64  `json:"createTime"`      // 创建时间
	UpdateTime      int64  `json:"updateTime"`      // 更新时间

	// 存酒单特有字段
	WineStorageBillData *WineStorageBillDataVO `json:"wineStorageBillData,omitempty"` // 存酒单数据
}

// WineStorageBillDataVO 存酒单数据VO
type WineStorageBillDataVO struct {
	VenueName         string                        `json:"venueName,omitempty"`         // 门店名称
	StorageRecordId   string                        `json:"storageRecordId"`             // 存酒记录ID
	SessionId         string                        `json:"sessionId"`                   // 场次ID
	StorageNo         string                        `json:"storageNo"`                   // 存酒单号
	StorageDate       string                        `json:"storageDate"`                 // 存酒日期 (YYYY-MM-DD格式)
	StorageTime       string                        `json:"storageTime"`                 // 存酒时间 (HH:MM:SS格式)
	MemberInfo        valueobject.MemberInfo        `json:"memberInfo"`                  // 会员信息
	Items             []valueobject.WineStorageItem `json:"items"`                       // 存酒项目列表
	TotalQuantity     int                           `json:"totalQuantity"`               // 总数量
	TotalAmount       float64                       `json:"totalAmount"`                 // 总金额
	ValidityDate      string                        `json:"validityDate,omitempty"`      // 有效期 (YYYY-MM-DD格式)
	StorageLocation   string                        `json:"storageLocation,omitempty"`   // 存放位置
	CashierName       string                        `json:"cashierName,omitempty"`       // 收银员名称
	CashierId         string                        `json:"cashierId,omitempty"`         // 收银员ID
	PrintTime         string                        `json:"printTime"`                   // 打印时间
	Remark            string                        `json:"remark,omitempty"`            // 备注
	StorageConditions string                        `json:"storageConditions,omitempty"` // 存储条件（如温度要求等）
}
