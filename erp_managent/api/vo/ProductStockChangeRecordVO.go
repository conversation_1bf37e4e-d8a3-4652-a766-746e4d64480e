package vo

// ProductStockChangeRecordVO 商品库存变更记录值对象
type ProductStockChangeRecordVO struct {
	Id                string `json:"id"`                // ID
	VenueId           string `json:"venueId"`           // 门店ID
	VenueName         string `json:"venueName"`         // 门店名称
	BusinessDay       int64  `json:"businessDay"`       // 营业日期
	BusinessDayStr    string `json:"businessDayStr"`    // 营业日期字符串
	BusinessStartHour string `json:"businessStartHour"` // 营业开始时间
	BusinessEndHour   string `json:"businessEndHour"`   // 营业结束时间

	ProductStockId      string `json:"productStockId"`      // 商品库存ID
	ProductId           string `json:"productId"`           // 商品ID
	ProductName         string `json:"productName"`         // 商品名称
	ProductCategoryId   string `json:"productCategoryId"`   // 商品分类ID
	ProductCategoryName string `json:"productCategoryName"` // 商品分类名称
	Quantity            int    `json:"quantity"`            // 数量变动数量
	ActionType          string `json:"actionType"`          // 操作类型 add: 增加 minus: 减少
	LeftStock           int    `json:"leftStock"`           // 剩余库存数量
	Source              string `json:"source"`              // 来源 下单，退单，入库
	SourceName          string `json:"sourceName"`          // 来源名称 下单，退单，入库，存取酒
	SessionId           string `json:"sessionId"`           // 开台ID
	InventoryId         string `json:"inventoryId"`         // 库存ID
	ProductStorageId    string `json:"productStorageId"`    // 商品存取酒单号
	DisplayNo           string `json:"displayNo"`           // 显示编号 或SessionId 或InventoryId
	OperatorId          string `json:"operatorId"`          // 操作人ID
	OperatorName        string `json:"operatorName"`        // 操作人名称

	Info    string `json:"info"`    // 变更信息
	Ctime   int64  `json:"ctime"`   // 创建时间
	Utime   int64  `json:"utime"`   // 更新时间
	State   int    `json:"state"`   // 状态
	Version int    `json:"version"` // 版本
}

type ProductStockChangeRecordVOShare struct {
	VenueId           string `json:"venueId"`           // 门店ID
	VenueName         string `json:"venueName"`         // 门店名称
	BusinessStartHour string `json:"businessStartHour"` // 营业开始时间
	BusinessEndHour   string `json:"businessEndHour"`   // 营业结束时间
	ActionType        string `json:"actionType"`        // 操作类型 add: 增加 minus: 减少
	Source            string `json:"source"`            // 来源 下单，退单，入库、存取酒
	SessionId         string `json:"sessionId"`         // 开台ID
	InventoryId       string `json:"inventoryId"`       // 库存ID
	ProductStorageId  string `json:"productStorageId"`  // 商品存取酒单号
	OperatorId        string `json:"operatorId"`        // 操作人ID
	OperatorName      string `json:"operatorName"`      // 操作人名称
	Info              string `json:"info"`              // 变更信息
}
