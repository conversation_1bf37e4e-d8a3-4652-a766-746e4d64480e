package vo

// StockReconcileItemVO 库存校准项
type StockReconcileItemVO struct {
	ProductId        string `json:"productId" comment:"商品ID"`
	ProductName      string `json:"productName" comment:"商品名称"`
	SnapshotStock    int    `json:"snapshotStock" comment:"快照库存"`
	SnapshotTime     int64  `json:"snapshotTime" comment:"快照时间"`
	InboundIncrement int    `json:"inboundIncrement" comment:"入库增量"`
	ConsumeIncrement int    `json:"consumeIncrement" comment:"消费增量"`
	RefundIncrement  int    `json:"refundIncrement" comment:"退款增量"`
	CalculatedStock  int    `json:"calculatedStock" comment:"计算后库存"`
	CurrentStock     int    `json:"currentStock" comment:"当前库存"`
	StockDifference  int    `json:"stockDifference" comment:"库存差异"`
	NeedReconcile    bool   `json:"needReconcile" comment:"是否需要校准"`
}

// StockReconcilePreviewVO 库存校准预览响应
type StockReconcilePreviewVO struct {
	VenueId            string                 `json:"venueId" comment:"门店ID"`
	ReconcileToken     string                 `json:"reconcileToken" comment:"校准令牌"`
	TokenExpireTime    int64                  `json:"tokenExpireTime" comment:"令牌过期时间"`
	TotalProducts      int                    `json:"totalProducts" comment:"总商品数"`
	NeedReconcileCount int                    `json:"needReconcileCount" comment:"需要校准的商品数"`
	ReconcileItems     []StockReconcileItemVO `json:"reconcileItems" comment:"校准项列表"`
}

// StockReconcileExecuteVO 库存校准执行响应
type StockReconcileExecuteVO struct {
	VenueId        string `json:"venueId" comment:"门店ID"`
	ExecuteTime    int64  `json:"executeTime" comment:"执行时间"`
	ProcessedCount int    `json:"processedCount" comment:"处理的商品数"`
	UpdatedCount   int    `json:"updatedCount" comment:"更新的商品数"`
	SkippedCount   int    `json:"skippedCount" comment:"跳过的商品数"`
	Message        string `json:"message" comment:"执行结果消息"`
}

// StockImportVO 库存导入响应
type StockImportVO struct {
	VenueId      string `json:"venueId" comment:"门店ID"`
	ImportTime   int64  `json:"importTime" comment:"导入时间"`
	TotalCount   int    `json:"totalCount" comment:"总导入数"`
	SuccessCount int    `json:"successCount" comment:"成功导入数"`
	FailedCount  int    `json:"failedCount" comment:"失败导入数"`
	Message      string `json:"message" comment:"导入结果消息"`
}

// StockImportFailedItemVO 导入失败项
type StockImportFailedItemVO struct {
	ProductId string `json:"productId" comment:"商品ID"`
	Stock     int    `json:"stock" comment:"库存数量"`
	Reason    string `json:"reason" comment:"失败原因"`
}

// StockImportPreviewItemVO 库存导入预览项
type StockImportPreviewItemVO struct {
	ProductId    string `json:"productId" comment:"商品ID"`
	ProductName  string `json:"productName" comment:"商品名称"`
	Stock        int    `json:"stock" comment:"导入库存数量"`
	CurrentStock int    `json:"currentStock" comment:"当前库存数量"`
	Operation    string `json:"operation" comment:"操作类型：create/update/skip"`
	Valid        bool   `json:"valid" comment:"是否有效"`
	Reason       string `json:"reason" comment:"无效原因"`
}

// StockImportPreviewVO 库存导入预览响应
type StockImportPreviewVO struct {
	VenueId         string                     `json:"venueId" comment:"门店ID"`
	ImportToken     string                     `json:"importToken" comment:"导入令牌"`
	TokenExpireTime int64                      `json:"tokenExpireTime" comment:"令牌过期时间"`
	TotalItems      int                        `json:"totalItems" comment:"总导入项数"`
	ValidItems      int                        `json:"validItems" comment:"有效项数"`
	InvalidItems    int                        `json:"invalidItems" comment:"无效项数"`
	PreviewItems    []StockImportPreviewItemVO `json:"previewItems" comment:"预览项列表"`
	Ctime           int64                      `json:"ctime" comment:"创建时间"`
	Utime           int64                      `json:"utime" comment:"更新时间"`
}

// StockImportExecuteVO 库存导入执行响应
type StockImportExecuteVO struct {
	StockImportVO
	FailedItems []StockImportFailedItemVO `json:"failedItems,omitempty" comment:"失败项列表"`
}

// VenueStockItemVO 门店库存项
type VenueStockItemVO struct {
	ProductId   string `json:"productId" comment:"商品ID"`
	ProductName string `json:"productName" comment:"商品名称"`
	Stock       int    `json:"stock" comment:"库存数量"`
	UpdateTime  int64  `json:"updateTime" comment:"更新时间"`
}

// VenueStockListVO 门店库存列表响应
type VenueStockListVO struct {
	VenueId  string             `json:"venueId" comment:"门店ID"`
	Total    int                `json:"total" comment:"总数"`
	PageNum  int                `json:"pageNum" comment:"页码"`
	PageSize int                `json:"pageSize" comment:"每页数量"`
	Items    []VenueStockItemVO `json:"items" comment:"库存项列表"`
}
