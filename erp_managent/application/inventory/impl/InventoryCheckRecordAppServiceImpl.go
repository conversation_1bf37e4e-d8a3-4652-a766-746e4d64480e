package impl

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"erp_managent/api/req"
	"erp_managent/api/vo"
	"erp_managent/application/inventory"
	"erp_managent/domain/inventory/model"
	"erp_managent/domain/inventory/service"
	productService "erp_managent/service/impl"
)

// InventoryCheckRecordAppServiceImpl 盘点记录应用服务实现
type InventoryCheckRecordAppServiceImpl struct {
	checkRecordService service.InventoryCheckRecordService
	productService     *productService.ProductService
}

// NewInventoryCheckRecordAppService 创建盘点记录应用服务实例
func NewInventoryCheckRecordAppService(
	checkRecordService service.InventoryCheckRecordService,
	productService *productService.ProductService,
) inventory.InventoryCheckRecordAppService {
	return &InventoryCheckRecordAppServiceImpl{
		checkRecordService: checkRecordService,
		productService:     productService,
	}
}

// CreateCheckRecord 创建盘点记录
func (s *InventoryCheckRecordAppServiceImpl) CreateCheckRecord(ctx *gin.Context, reqDto req.CreateCheckRecordReqDto) (*vo.CreateCheckRecordRespVO, error) {
	// 1. 构建盘点记录模型
	record := &model.InventoryCheckRecord{}
	record.SetVenueId(reqDto.VenueId)
	record.SetHandler(reqDto.Handler)
	record.SetOperator(reqDto.Operator)
	record.SetRemark(reqDto.Remark)
	
	// 2. 构建明细项
	var items []model.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		item := model.InventoryCheckRecordItem{}
		item.SetProductId(itemDto.ProductId)
		item.SetStockQuantity(itemDto.StockQuantity)
		item.SetCheckQuantity(itemDto.CheckQuantity)
		item.CalculateProfitLoss() // 计算盈亏数量
		
		items = append(items, item)
	}
	record.SetItems(items)
	
	// 3. 创建盘点记录
	if err := s.checkRecordService.CreateCheckRecord(ctx, record); err != nil {
		return nil, fmt.Errorf("创建盘点记录失败: %w", err)
	}
	
	// 4. 构建响应
	return &vo.CreateCheckRecordRespVO{
		RecordId:            record.GetId(),
		RecordNumber:        record.GetRecordNumber(),
		ProfitQuantityTotal: record.GetProfitQuantityTotal(),
		LossQuantityTotal:   record.GetLossQuantityTotal(),
	}, nil
}

// GetCheckRecordList 获取盘点记录列表
func (s *InventoryCheckRecordAppServiceImpl) GetCheckRecordList(ctx *gin.Context, reqDto req.CheckRecordListReqDto) (*vo.CheckRecordListRespVO, error) {
	// 1. 获取盘点记录列表
	var records []*model.InventoryCheckRecord
	var total int64
	var err error
	
	if reqDto.SearchKey != "" {
		records, total, err = s.checkRecordService.SearchCheckRecords(ctx, reqDto.VenueId, reqDto.SearchKey, reqDto.PageNum, reqDto.PageSize)
	} else {
		records, total, err = s.checkRecordService.GetCheckRecordsByVenue(ctx, reqDto.VenueId, reqDto.PageNum, reqDto.PageSize)
	}
	
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录列表失败: %w", err)
	}
	
	// 2. 转换为VO
	var recordVOs []vo.CheckRecordVO
	for _, record := range records {
		recordVO := vo.CheckRecordVO{
			RecordId:            record.GetId(),
			RecordNumber:        record.GetRecordNumber(),
			Handler:             record.GetHandler(),
			Operator:            record.GetOperator(),
			CheckTime:           record.Time,
			Remark:              record.GetRemark(),
			TotalProducts:       len(record.GetItems()),
			ProfitQuantityTotal: record.GetProfitQuantityTotal(),
			LossQuantityTotal:   record.GetLossQuantityTotal(),
		}
		recordVOs = append(recordVOs, recordVO)
	}
	
	return &vo.CheckRecordListRespVO{
		Records: recordVOs,
		Total:   total,
	}, nil
}

// GetCheckRecordDetail 获取盘点记录详情
func (s *InventoryCheckRecordAppServiceImpl) GetCheckRecordDetail(ctx *gin.Context, reqDto req.CheckRecordDetailReqDto) (*vo.CheckRecordDetailRespVO, error) {
	// 1. 获取盘点记录
	record, err := s.checkRecordService.GetCheckRecordById(ctx, reqDto.RecordId)
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录失败: %w", err)
	}
	
	if record == nil {
		return nil, fmt.Errorf("盘点记录不存在")
	}
	
	// 2. 构建记录详情VO
	recordDetailVO := vo.CheckRecordDetailVO{
		RecordId:            record.GetId(),
		RecordNumber:        record.GetRecordNumber(),
		Handler:             record.GetHandler(),
		Operator:            record.GetOperator(),
		CheckTime:           record.Time,
		Remark:              record.GetRemark(),
		TotalProducts:       len(record.GetItems()),
		ProfitQuantityTotal: record.GetProfitQuantityTotal(),
		LossQuantityTotal:   record.GetLossQuantityTotal(),
	}
	
	// 3. 构建明细项VO
	var itemVOs []vo.CheckRecordItemVO
	for _, item := range record.GetItems() {
		// 获取商品信息
		product, err := s.productService.FindProductById(ctx, item.GetProductId())
		if err != nil {
			// 如果获取商品信息失败，使用默认值
			itemVO := vo.CheckRecordItemVO{
				ProductId:          item.GetProductId(),
				ProductName:        "未知商品",
				ProductImage:       "",
				Unit:               "",
				StockQuantity:      item.GetStockQuantity(),
				CheckQuantity:      item.GetCheckQuantity(),
				ProfitLossQuantity: item.GetProfitLossQuantity(),
			}
			itemVOs = append(itemVOs, itemVO)
			continue
		}
		
		itemVO := vo.CheckRecordItemVO{
			ProductId:          item.GetProductId(),
			ProductName:        *product.Name,
			ProductImage:       *product.ImageUrl,
			Unit:               *product.Unit,
			StockQuantity:      item.GetStockQuantity(),
			CheckQuantity:      item.GetCheckQuantity(),
			ProfitLossQuantity: item.GetProfitLossQuantity(),
		}
		itemVOs = append(itemVOs, itemVO)
	}
	
	return &vo.CheckRecordDetailRespVO{
		Record: recordDetailVO,
		Items:  itemVOs,
	}, nil
}

// UpdateCheckRecord 更新盘点记录
func (s *InventoryCheckRecordAppServiceImpl) UpdateCheckRecord(ctx *gin.Context, reqDto req.UpdateCheckRecordReqDto) (*vo.UpdateCheckRecordRespVO, error) {
	// 1. 获取原有记录
	record, err := s.checkRecordService.GetCheckRecordById(ctx, reqDto.RecordId)
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录失败: %w", err)
	}
	
	if record == nil {
		return nil, fmt.Errorf("盘点记录不存在")
	}
	
	// 2. 更新基本信息
	record.SetHandler(reqDto.Handler)
	record.SetOperator(reqDto.Operator)
	record.SetRemark(reqDto.Remark)
	
	// 3. 更新明细项
	var items []model.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		item := model.InventoryCheckRecordItem{}
		item.SetProductId(itemDto.ProductId)
		item.SetStockQuantity(itemDto.StockQuantity)
		item.SetCheckQuantity(itemDto.CheckQuantity)
		item.CalculateProfitLoss() // 计算盈亏数量
		
		items = append(items, item)
	}
	record.SetItems(items)
	
	// 4. 更新盘点记录
	if err := s.checkRecordService.UpdateCheckRecord(ctx, record); err != nil {
		return nil, fmt.Errorf("更新盘点记录失败: %w", err)
	}
	
	// 5. 构建响应
	return &vo.UpdateCheckRecordRespVO{
		RecordId:            record.GetId(),
		ProfitQuantityTotal: record.GetProfitQuantityTotal(),
		LossQuantityTotal:   record.GetLossQuantityTotal(),
	}, nil
}

// DeleteCheckRecord 删除盘点记录
func (s *InventoryCheckRecordAppServiceImpl) DeleteCheckRecord(ctx *gin.Context, reqDto req.DeleteCheckRecordReqDto) (*vo.DeleteCheckRecordRespVO, error) {
	// 1. 删除盘点记录
	if err := s.checkRecordService.DeleteCheckRecord(ctx, reqDto.RecordId); err != nil {
		return nil, fmt.Errorf("删除盘点记录失败: %w", err)
	}
	
	// 2. 构建响应
	return &vo.DeleteCheckRecordRespVO{
		RecordId: reqDto.RecordId,
		Success:  true,
	}, nil
}
