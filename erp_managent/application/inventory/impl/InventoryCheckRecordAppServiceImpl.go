package impl

import (
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/inventory"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// InventoryCheckRecordAppServiceImpl 库存盘点记录应用服务实现
type InventoryCheckRecordAppServiceImpl struct {
	inventoryCheckRecordService  impl.InventoryCheckRecordService
	inventoryCheckRecordTransfer transfer.InventoryCheckRecordTransfer
	productService               impl.ProductService
}

// NewInventoryCheckRecordAppService 创建库存盘点记录应用服务实例
func NewInventoryCheckRecordAppService() inventory.InventoryCheckRecordAppService {
	return &InventoryCheckRecordAppServiceImpl{
		inventoryCheckRecordService:  impl.InventoryCheckRecordService{},
		inventoryCheckRecordTransfer: transfer.InventoryCheckRecordTransfer{},
		productService:               impl.ProductService{},
	}
}

// CreateInventoryCheckRecord 创建盘点记录
func (s *InventoryCheckRecordAppServiceImpl) CreateInventoryCheckRecord(ctx *gin.Context, reqDto req.CreateInventoryCheckRecordReqDto) (*vo.CreateInventoryCheckRecordRespVO, error) {
	// 1. 数据验证
	if err := s.validateCreateRequest(reqDto); err != nil {
		return nil, err
	}

	// 2. 生成盘点单号
	recordNumber, err := s.inventoryCheckRecordService.GenerateRecordNumber(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	// 3. 计算汇总数据
	profitTotal := 0
	lossTotal := 0
	for _, item := range reqDto.Items {
		profitLoss := *item.CheckQuantity - *item.StockQuantity
		if profitLoss > 0 {
			profitTotal += profitLoss
		} else {
			lossTotal += -profitLoss
		}
	}

	// 4. 构建主记录PO
	now := time.Now()
	recordId := util.GetUUID()
	record := po.InventoryCheckRecord{
		Id:                  &recordId,
		VenueId:             reqDto.VenueId,
		Handler:             reqDto.Handler,
		Operator:            reqDto.Operator,
		Time:                util.GetItPtr(now.Unix()),
		RecordNumber:        &recordNumber,
		Remark:              reqDto.Remark,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
		Ctime:               util.GetItPtr(now.Unix()),
		Utime:               util.GetItPtr(now.Unix()),
		State:               util.GetItPtr(0),
		Version:             util.GetItPtr(0),
	}

	// 5. 构建明细记录PO
	var items []po.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		profitLoss := *itemDto.CheckQuantity - *itemDto.StockQuantity
		itemId := util.GetUUID()
		item := po.InventoryCheckRecordItem{
			Id:                 &itemId,
			CheckRecordId:      &recordId,
			ProductId:          itemDto.ProductId,
			StockQuantity:      itemDto.StockQuantity,
			CheckQuantity:      itemDto.CheckQuantity,
			ProfitLossQuantity: util.GetItPtr(profitLoss),
			Ctime:              util.GetItPtr(now.Unix()),
			Utime:              util.GetItPtr(now.Unix()),
			State:              util.GetItPtr(0),
		}
		items = append(items, item)
	}

	// 6. 调用Service层保存数据
	if err := s.inventoryCheckRecordService.CreateInventoryCheckRecordWithItems(ctx, &record, items); err != nil {
		return nil, err
	}

	// 7. 构建响应VO
	respVO := &vo.CreateInventoryCheckRecordRespVO{
		RecordId:            &recordId,
		RecordNumber:        &recordNumber,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
	}

	return respVO, nil
}

// QueryInventoryCheckRecords 查询盘点记录列表
func (s *InventoryCheckRecordAppServiceImpl) QueryInventoryCheckRecords(ctx *gin.Context, reqDto req.QueryInventoryCheckRecordReqDto) (*vo.PageVO[[]vo.InventoryCheckRecordVO], error) {
	// 1. 调用Service层查询数据
	list, totalCount, err := s.inventoryCheckRecordService.FindAllInventoryCheckRecordWithPagination(ctx, &reqDto)
	if err != nil {
		return nil, err
	}

	// 2. 转换为VO
	var recordVOs []vo.InventoryCheckRecordVO
	for _, record := range *list {
		recordVO := s.inventoryCheckRecordTransfer.PoToVo(record)
		recordVOs = append(recordVOs, recordVO)
	}

	// 3. 构建分页响应
	page := &vo.PageVO[[]vo.InventoryCheckRecordVO]{
		PageNum:  *reqDto.PageNum,
		PageSize: *reqDto.PageSize,
		Total:    totalCount,
		Data:     recordVOs,
	}

	return page, nil
}

// GetInventoryCheckRecordDetail 查询盘点记录详情
func (s *InventoryCheckRecordAppServiceImpl) GetInventoryCheckRecordDetail(ctx *gin.Context, reqDto req.GetInventoryCheckRecordDetailReqDto) (*vo.GetInventoryCheckRecordDetailRespVO, error) {
	// 1. 查询主记录
	record, err := s.inventoryCheckRecordService.FindInventoryCheckRecordById(ctx, *reqDto.RecordId)
	if err != nil {
		return nil, err
	}

	// 2. 查询明细记录
	items, err := s.inventoryCheckRecordService.FindInventoryCheckRecordItemsByRecordId(ctx, *reqDto.RecordId)
	if err != nil {
		return nil, err
	}

	// 3. 转换主记录为VO
	recordDetailVO := s.inventoryCheckRecordTransfer.PoToDetailVo(*record)

	// 4. 转换明细记录为VO
	var itemVOs []vo.InventoryCheckRecordItemVO
	for _, item := range *items {
		// 获取商品信息
		product, err := s.productService.FindProductById(ctx, *item.ProductId)
		if err != nil {
			// 如果获取商品信息失败，使用默认值
			unknownProduct := "未知商品"
			emptyStr := ""
			itemVO := vo.InventoryCheckRecordItemVO{
				ProductId:          item.ProductId,
				ProductName:        &unknownProduct,
				ProductImage:       &emptyStr,
				Unit:               &emptyStr,
				StockQuantity:      item.StockQuantity,
				CheckQuantity:      item.CheckQuantity,
				ProfitLossQuantity: item.ProfitLossQuantity,
			}
			itemVOs = append(itemVOs, itemVO)
			continue
		}

		emptyUnit := ""
		itemVO := vo.InventoryCheckRecordItemVO{
			ProductId:          item.ProductId,
			ProductName:        product.Name,
			ProductImage:       product.Image,
			Unit:               &emptyUnit, // Product结构体中没有Unit字段
			StockQuantity:      item.StockQuantity,
			CheckQuantity:      item.CheckQuantity,
			ProfitLossQuantity: item.ProfitLossQuantity,
		}
		itemVOs = append(itemVOs, itemVO)
	}

	// 5. 构建响应VO
	respVO := &vo.GetInventoryCheckRecordDetailRespVO{
		Record: recordDetailVO,
		Items:  itemVOs,
	}

	return respVO, nil
}

// UpdateInventoryCheckRecord 更新盘点记录
func (s *InventoryCheckRecordAppServiceImpl) UpdateInventoryCheckRecord(ctx *gin.Context, reqDto req.UpdateInventoryCheckRecordReqDto) (*vo.UpdateInventoryCheckRecordRespVO, error) {
	// 1. 数据验证
	if err := s.validateUpdateRequest(reqDto); err != nil {
		return nil, err
	}

	// 2. 计算汇总数据
	profitTotal := 0
	lossTotal := 0
	for _, item := range reqDto.Items {
		profitLoss := *item.CheckQuantity - *item.StockQuantity
		if profitLoss > 0 {
			profitTotal += profitLoss
		} else {
			lossTotal += -profitLoss
		}
	}

	// 3. 构建主记录PO
	now := time.Now()
	record := po.InventoryCheckRecord{
		Id:                  reqDto.RecordId,
		Handler:             reqDto.Handler,
		Operator:            reqDto.Operator,
		Remark:              reqDto.Remark,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
		Utime:               util.GetItPtr(now.Unix()),
	}

	// 4. 构建明细记录PO
	var items []po.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		profitLoss := *itemDto.CheckQuantity - *itemDto.StockQuantity
		itemId := util.GetUUID()
		item := po.InventoryCheckRecordItem{
			Id:                 &itemId,
			CheckRecordId:      reqDto.RecordId,
			ProductId:          itemDto.ProductId,
			StockQuantity:      itemDto.StockQuantity,
			CheckQuantity:      itemDto.CheckQuantity,
			ProfitLossQuantity: util.GetItPtr(profitLoss),
			Ctime:              util.GetItPtr(now.Unix()),
			Utime:              util.GetItPtr(now.Unix()),
			State:              util.GetItPtr(0),
		}
		items = append(items, item)
	}

	// 5. 调用Service层更新数据
	if err := s.inventoryCheckRecordService.UpdateInventoryCheckRecordWithItems(ctx, &record, items); err != nil {
		return nil, err
	}

	// 6. 构建响应VO
	respVO := &vo.UpdateInventoryCheckRecordRespVO{
		RecordId:            reqDto.RecordId,
		ProfitQuantityTotal: util.GetItPtr(profitTotal),
		LossQuantityTotal:   util.GetItPtr(lossTotal),
	}

	return respVO, nil
}

// DeleteInventoryCheckRecord 删除盘点记录
func (s *InventoryCheckRecordAppServiceImpl) DeleteInventoryCheckRecord(ctx *gin.Context, reqDto req.DeleteInventoryCheckRecordReqDto) error {
	return s.inventoryCheckRecordService.DeleteInventoryCheckRecord(ctx, *reqDto.RecordId)
}

// validateCreateRequest 验证创建请求
func (s *InventoryCheckRecordAppServiceImpl) validateCreateRequest(reqDto req.CreateInventoryCheckRecordReqDto) error {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return NewAppError("门店ID不能为空")
	}
	if reqDto.Handler == nil || *reqDto.Handler == "" {
		return NewAppError("经手人不能为空")
	}
	if reqDto.Operator == nil || *reqDto.Operator == "" {
		return NewAppError("操作人不能为空")
	}
	if len(reqDto.Items) == 0 {
		return NewAppError("盘点明细不能为空")
	}
	return nil
}

// validateUpdateRequest 验证更新请求
func (s *InventoryCheckRecordAppServiceImpl) validateUpdateRequest(reqDto req.UpdateInventoryCheckRecordReqDto) error {
	if reqDto.RecordId == nil || *reqDto.RecordId == "" {
		return NewAppError("盘点记录ID不能为空")
	}
	if reqDto.Handler == nil || *reqDto.Handler == "" {
		return NewAppError("经手人不能为空")
	}
	if reqDto.Operator == nil || *reqDto.Operator == "" {
		return NewAppError("操作人不能为空")
	}
	if len(reqDto.Items) == 0 {
		return NewAppError("盘点明细不能为空")
	}
	return nil
}

// NewAppError 创建应用层错误
func NewAppError(message string) error {
	return &AppError{Message: message}
}

// AppError 应用层错误
type AppError struct {
	Message string
}

func (e *AppError) Error() string {
	return e.Message
}
