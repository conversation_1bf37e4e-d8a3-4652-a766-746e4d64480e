package inventory

import (
	"fmt"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/inventory/repository"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// StockReconcileAppServiceImpl 库存校准应用层服务实现
type StockReconcileAppServiceImpl struct {
	inventoryRepository          repository.InventoryRepository
	productStockSnapshotService  *impl.ProductStockSnapshotService
	productStockService          *impl.ProductStockService
	productService               *impl.ProductService
	reconcileTokenService        *impl.ReconcileTokenService
	systemOperationRecordService impl.SystemOperationRecordService
}

// NewStockReconcileAppService 创建库存校准应用层服务
func NewStockReconcileAppService(
	inventoryRepository repository.InventoryRepository,
	productStockSnapshotService *impl.ProductStockSnapshotService,
	productStockService *impl.ProductStockService,
	productService *impl.ProductService,
	reconcileTokenService *impl.ReconcileTokenService,
) StockReconcileAppService {
	return &StockReconcileAppServiceImpl{
		inventoryRepository:          inventoryRepository,
		productStockSnapshotService:  productStockSnapshotService,
		productStockService:          productStockService,
		productService:               productService,
		reconcileTokenService:        reconcileTokenService,
		systemOperationRecordService: impl.NewSystemOperationRecordService(),
	}
}

// PreviewReconcile 预览库存校准
func (s *StockReconcileAppServiceImpl) PreviewReconcile(ctx *gin.Context, request *req.StockReconcilePreviewReqDto) (*vo.StockReconcilePreviewVO, error) {
	// 1. 生成校准令牌
	reconcileToken, expireTime, err := s.reconcileTokenService.GenerateReconcileToken(ctx, request.VenueId)
	if err != nil {
		return nil, fmt.Errorf("failed to generate reconcile token: %v", err)
	}

	// 2. 获取当前门店的所有库存快照
	snapshots, err := s.productStockSnapshotService.FindProductStockSnapshotsByVenueId(ctx, request.VenueId)
	if err != nil {
		return nil, fmt.Errorf("failed to get stock snapshots: %v", err)
	}

	if len(snapshots) == 0 {
		return &vo.StockReconcilePreviewVO{
			VenueId:            request.VenueId,
			ReconcileToken:     reconcileToken,
			TokenExpireTime:    expireTime,
			TotalProducts:      0,
			NeedReconcileCount: 0,
			ReconcileItems:     []vo.StockReconcileItemVO{},
		}, nil
	}

	// 3. 计算每个商品的校准信息
	var reconcileItems []vo.StockReconcileItemVO
	needReconcileCount := 0

	for _, snapshot := range snapshots {
		item, err := s.calculateReconcileItem(ctx, &snapshot)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate reconcile for product %s: %v", *snapshot.ProductId, err)
		}

		reconcileItems = append(reconcileItems, *item)
		if item.NeedReconcile {
			needReconcileCount++
		}
	}

	return &vo.StockReconcilePreviewVO{
		VenueId:            request.VenueId,
		ReconcileToken:     reconcileToken,
		TokenExpireTime:    expireTime,
		TotalProducts:      len(snapshots),
		NeedReconcileCount: needReconcileCount,
		ReconcileItems:     reconcileItems,
	}, nil
}

// ExecuteReconcile 执行库存校准
func (s *StockReconcileAppServiceImpl) ExecuteReconcile(ctx *gin.Context, request *req.StockReconcileExecuteReqDto) (*vo.StockReconcileExecuteVO, error) {
	// 1. 验证并消费令牌
	valid, err := s.reconcileTokenService.ConsumeReconcileToken(ctx, request.ReconcileToken, request.VenueId)
	if !valid || err != nil {
		return nil, fmt.Errorf("invalid reconcile token: %v", err)
	}

	executeTime := time.Now().Unix()
	processedCount := 0
	updatedCount := 0
	skippedCount := 0
	var updateDetails []map[string]interface{}

	// 2. 在事务中执行校准
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 获取当前门店的所有库存快照
		snapshots, err := s.productStockSnapshotService.FindProductStockSnapshotsByVenueId(ctx, request.VenueId)
		if err != nil {
			return fmt.Errorf("failed to get stock snapshots: %v", err)
		}

		for _, snapshot := range snapshots {
			processedCount++

			// 计算校准信息
			item, err := s.calculateReconcileItem(ctx, &snapshot)
			if err != nil {
				return fmt.Errorf("failed to calculate reconcile for product %s: %v", *snapshot.ProductId, err)
			}

			// 如果需要校准
			if item.NeedReconcile {
				// 记录变更详情
				updateDetail := map[string]interface{}{
					"productId":       *snapshot.ProductId,
					"productName":     item.ProductName,
					"beforeStock":     item.CurrentStock,
					"afterStock":      item.CalculatedStock,
					"stockDifference": item.StockDifference,
				}
				updateDetails = append(updateDetails, updateDetail)

				// 更新ProductStock表
				productStock, err := s.productStockService.FindProductStockByCondition(ctx, *snapshot.ProductId, *snapshot.VenueId)
				if err != nil && err != gorm.ErrRecordNotFound {
					return fmt.Errorf("failed to get product stock: %v", err)
				}

				if err == gorm.ErrRecordNotFound {
					// 创建新的库存记录
					newStock := &po.ProductStock{
						ProductId: snapshot.ProductId,
						VenueId:   snapshot.VenueId,
						Stock:     &item.CalculatedStock,
					}
					err = s.productStockService.CreateProductStockWithTx(ctx, newStock, tx)
					if err != nil {
						return fmt.Errorf("failed to create product stock: %v", err)
					}
				} else {
					// 更新现有库存记录
					productStock.Stock = &item.CalculatedStock
					err = s.productStockService.UpdateProductStockWithTx(ctx, productStock, tx)
					if err != nil {
						return fmt.Errorf("failed to update product stock: %v", err)
					}
				}

				// 更新快照记录
				snapshot.Stock = &item.CalculatedStock
				snapshot.Utime = &executeTime
				err = s.productStockSnapshotService.UpdateProductStockSnapshotPartialWithTx(ctx, &snapshot, tx)
				if err != nil {
					return fmt.Errorf("failed to update snapshot: %v", err)
				}

				updatedCount++
			} else {
				skippedCount++
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 3. 记录操作日志
	operatorId := "system" // 可以从context中获取操作人信息
	if userInfo, exists := ctx.Get("user"); exists {
		if user, ok := userInfo.(map[string]interface{}); ok {
			if id, ok := user["id"].(string); ok {
				operatorId = id
			}
		}
	}

	// 记录校准操作日志
	logErr := s.systemOperationRecordService.RecordOperation(
		ctx,
		operatorId,
		"inventory_service", // 客户端标识
		"stock_reconcile",   // 操作类型
		"venue_stock",       // 操作对象类型
		request.VenueId,     // 操作对象ID
		impl.RecordParams{
			OldValue: map[string]interface{}{
				"reconcileToken": request.ReconcileToken,
				"beforeCount":    processedCount,
			},
			NewValue: map[string]interface{}{
				"executeTime":    executeTime,
				"processedCount": processedCount,
				"updatedCount":   updatedCount,
				"skippedCount":   skippedCount,
				"updateDetails":  updateDetails,
			},
			ExtraInfo: map[string]interface{}{
				"operation": "stock_reconcile_execute",
				"venueId":   request.VenueId,
			},
		},
		nil, // 非事务操作
	)

	if logErr != nil {
		// 日志记录失败不应该影响主流程，只记录错误
		fmt.Printf("Failed to record operation log: %v\n", logErr)
	}

	return &vo.StockReconcileExecuteVO{
		VenueId:        request.VenueId,
		ExecuteTime:    executeTime,
		ProcessedCount: processedCount,
		UpdatedCount:   updatedCount,
		SkippedCount:   skippedCount,
		Message:        fmt.Sprintf("Successfully reconciled %d products, updated %d, skipped %d", processedCount, updatedCount, skippedCount),
	}, nil
}

// PreviewImportStockData 预览导入库存数据
func (s *StockReconcileAppServiceImpl) PreviewImportStockData(ctx *gin.Context, request *req.StockImportPreviewReqDto) (*vo.StockImportPreviewVO, error) {
	totalItems := len(request.Items)
	validItems := 0
	invalidItems := 0
	var previewItems []vo.StockImportPreviewItemVO

	// 提取所有商品ID
	var productIds []string
	productIdSet := make(map[string]bool)
	for _, item := range request.Items {
		if item.ProductId != "" && !productIdSet[item.ProductId] {
			productIds = append(productIds, item.ProductId)
			productIdSet[item.ProductId] = true
		}
	}

	// 批量查询商品信息
	var productMap map[string]*po.Product
	if len(productIds) > 0 {
		products, err := s.productService.FindProductsByIds(ctx, request.VenueId, productIds)
		if err != nil {
			return nil, fmt.Errorf("failed to query products: %v", err)
		}

		// 构建商品ID到商品信息的映射
		productMap = make(map[string]*po.Product)
		for _, product := range products {
			if product.Id != nil {
				productMap[*product.Id] = &product
			}
		}
	}

	// 批量查询当前库存
	var currentStockMap map[string]int
	if len(productIds) > 0 {
		stocks, err := s.productStockService.FindProductStocksByIds(ctx, request.VenueId, productIds)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to query current stocks: %v", err)
		}

		// 构建商品ID到当前库存的映射
		currentStockMap = make(map[string]int)
		if stocks != nil {
			for _, stock := range *stocks {
				if stock.ProductId != nil && stock.Stock != nil {
					currentStockMap[*stock.ProductId] = *stock.Stock
				}
			}
		}
	}

	// 批量查询快照信息
	var snapshotMap map[string]*po.ProductStockSnapshot
	if len(productIds) > 0 {
		snapshots, err := s.productStockSnapshotService.FindProductStockSnapshotsByVenueId(ctx, request.VenueId)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to query snapshots: %v", err)
		}

		// 构建商品ID到快照的映射
		snapshotMap = make(map[string]*po.ProductStockSnapshot)
		for _, snapshot := range snapshots {
			if snapshot.ProductId != nil {
				snapshotMap[*snapshot.ProductId] = &snapshot
			}
		}
	}

	// 验证每个导入项
	for _, item := range request.Items {
		previewItem := vo.StockImportPreviewItemVO{
			ProductId:    item.ProductId,
			Stock:        item.Stock,
			CurrentStock: 0,
			Operation:    "create_snapshot",
			Valid:        true,
			Reason:       "",
		}

		// 基础验证：商品ID和库存数量
		if item.ProductId == "" {
			previewItem.ProductName = ""
			previewItem.Valid = false
			previewItem.Reason = "Product ID is empty"
			previewItem.Operation = "skip"
			invalidItems++
			previewItems = append(previewItems, previewItem)
			continue
		}

		if item.Stock < 0 {
			previewItem.Valid = false
			previewItem.Reason = "Stock cannot be negative"
			previewItem.Operation = "skip"
			invalidItems++
		}

		// 验证商品是否存在
		product, exists := productMap[item.ProductId]
		if !exists {
			previewItem.ProductName = ""
			previewItem.Valid = false
			previewItem.Reason = "Product not found"
			previewItem.Operation = "skip"
			invalidItems++
		} else {
			previewItem.ProductName = *product.Name

			// 验证商品是否属于指定门店
			if product.VenueId != nil && *product.VenueId != request.VenueId {
				previewItem.Valid = false
				previewItem.Reason = "Product does not belong to this venue"
				previewItem.Operation = "skip"
				if previewItem.Valid {
					validItems--
				}
				invalidItems++
			} else {
				validItems++

				// 显示当前库存信息（仅用于展示，不影响导入）
				if currentStock, hasStock := currentStockMap[item.ProductId]; hasStock {
					previewItem.CurrentStock = currentStock
				} else {
					previewItem.CurrentStock = 0
				}

				// 检查快照是否存在
				if existingSnapshot, hasSnapshot := snapshotMap[item.ProductId]; hasSnapshot {
					// 快照已存在，检查时间戳
					if existingSnapshot.Utime != nil && *existingSnapshot.Utime > request.Utime {
						previewItem.Valid = false
						previewItem.Reason = "Snapshot already exists with newer timestamp"
						previewItem.Operation = "skip"
						validItems--
						invalidItems++
					} else {
						previewItem.Operation = "update_snapshot"
					}
				} else {
					previewItem.Operation = "create_snapshot"
				}
			}
		}

		previewItems = append(previewItems, previewItem)
	}

	// 记录预览操作日志
	operatorId := "system"
	if userInfo, exists := ctx.Get("user"); exists {
		if user, ok := userInfo.(map[string]interface{}); ok {
			if id, ok := user["id"].(string); ok {
				operatorId = id
			}
		}
	}

	logErr := s.systemOperationRecordService.RecordOperation(
		ctx,
		operatorId,
		"inventory_service",
		"preview_import_stock",
		"venue_stock",
		request.VenueId,
		impl.RecordParams{
			OldValue: nil,
			NewValue: map[string]interface{}{
				"venueId":      request.VenueId,
				"totalItems":   totalItems,
				"validItems":   validItems,
				"invalidItems": invalidItems,
				"ctime":        request.Ctime,
				"utime":        request.Utime,
			},
			ExtraInfo: map[string]interface{}{
				"operation": "preview_import_stock",
				"message":   fmt.Sprintf("Preview import stock for venue %s: %d total items, %d valid, %d invalid", request.VenueId, totalItems, validItems, invalidItems),
			},
		},
		nil, // 非事务操作
	)
	if logErr != nil {
		// 日志记录失败不影响主流程
		fmt.Printf("Failed to record preview import operation log: %v\n", logErr)
	}

	// 如果有有效项，生成导入令牌
	var importToken string
	var tokenExpireTime int64
	if validItems > 0 {
		// 构建导入项信息
		var tokenItems []impl.ImportTokenItemInfo
		for _, item := range request.Items {
			tokenItems = append(tokenItems, impl.ImportTokenItemInfo{
				ProductId: item.ProductId,
				Stock:     item.Stock,
			})
		}

		token, expireTime, err := s.reconcileTokenService.GenerateImportToken(ctx, request.VenueId, request.Ctime, request.Utime, tokenItems)
		if err != nil {
			return nil, fmt.Errorf("failed to generate import token: %v", err)
		}
		importToken = token
		tokenExpireTime = expireTime
	}

	return &vo.StockImportPreviewVO{
		VenueId:         request.VenueId,
		ImportToken:     importToken,
		TokenExpireTime: tokenExpireTime,
		TotalItems:      totalItems,
		ValidItems:      validItems,
		InvalidItems:    invalidItems,
		PreviewItems:    previewItems,
		Ctime:           request.Ctime,
		Utime:           request.Utime,
	}, nil
}

// ExecuteImportStockData 执行导入库存数据
func (s *StockReconcileAppServiceImpl) ExecuteImportStockData(ctx *gin.Context, request *req.StockImportExecuteReqDto) (*vo.StockImportExecuteVO, error) {
	// 验证并消费导入令牌
	importTokenInfo, err := s.reconcileTokenService.ConsumeImportToken(ctx, request.ImportToken, request.VenueId)
	if err != nil {
		return nil, fmt.Errorf("invalid import token: %v", err)
	}

	importTime := time.Now().Unix()
	totalCount := len(importTokenInfo.Items)
	successCount := 0
	failedCount := 0
	var failedItems []vo.StockImportFailedItemVO
	var successItems []map[string]interface{}

	// 在事务中执行导入（仅导入到快照表）
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		for _, item := range importTokenInfo.Items {
			// 验证商品是否存在
			_, err := s.productService.FindProductById(ctx, item.ProductId)
			if err != nil {
				failedItems = append(failedItems, vo.StockImportFailedItemVO{
					ProductId: item.ProductId,
					Stock:     item.Stock,
					Reason:    "Product not found",
				})
				failedCount++
				continue
			}

			// 仅创建或更新快照记录，不操作库存表
			snapshot := &po.ProductStockSnapshot{
				ProductId: &item.ProductId,
				VenueId:   &importTokenInfo.VenueId,
				Stock:     &item.Stock,
				Ctime:     &importTokenInfo.Ctime,
				Utime:     &importTokenInfo.Utime,
			}

			err = s.productStockSnapshotService.SaveOrUpdateProductStockSnapshotWithCustomTimeWithTx(ctx, snapshot, tx)
			if err != nil {
				failedItems = append(failedItems, vo.StockImportFailedItemVO{
					ProductId: item.ProductId,
					Stock:     item.Stock,
					Reason:    fmt.Sprintf("Failed to save snapshot: %v", err),
				})
				failedCount++
				continue
			}

			// 记录成功导入的项目
			successItems = append(successItems, map[string]interface{}{
				"productId": item.ProductId,
				"stock":     item.Stock,
				"operation": "snapshot_only",
			})
			successCount++
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 记录执行导入操作日志
	operatorId := "system"
	if userInfo, exists := ctx.Get("user"); exists {
		if user, ok := userInfo.(map[string]interface{}); ok {
			if id, ok := user["id"].(string); ok {
				operatorId = id
			}
		}
	}

	logErr := s.systemOperationRecordService.RecordOperation(
		ctx,
		operatorId,
		"inventory_service",
		"execute_import_stock",
		"venue_stock",
		request.VenueId,
		impl.RecordParams{
			OldValue: map[string]interface{}{
				"importToken": request.ImportToken,
				"venueId":     request.VenueId,
			},
			NewValue: map[string]interface{}{
				"executeTime":  importTime,
				"totalCount":   totalCount,
				"successCount": successCount,
				"failedCount":  failedCount,
				"failedItems":  failedItems,
			},
			ExtraInfo: map[string]interface{}{
				"operation": "execute_import_stock",
				"message":   fmt.Sprintf("Execute import stock to snapshot for venue %s: %d total, %d success, %d failed", request.VenueId, totalCount, successCount, failedCount),
			},
		},
		nil, // 非事务操作
	)
	if logErr != nil {
		// 日志记录失败不影响主流程
		fmt.Printf("Failed to record execute import operation log: %v\n", logErr)
	}

	return &vo.StockImportExecuteVO{
		StockImportVO: vo.StockImportVO{
			VenueId:      request.VenueId,
			ImportTime:   importTime,
			TotalCount:   totalCount,
			SuccessCount: successCount,
			FailedCount:  failedCount,
			Message:      fmt.Sprintf("导入完成！成功导入 %d/%d 项到快照，请使用库存校准功能更新实际库存", successCount, totalCount),
		},
		FailedItems: failedItems,
	}, nil
}

// calculateReconcileItem 计算单个商品的校准信息
func (s *StockReconcileAppServiceImpl) calculateReconcileItem(ctx *gin.Context, snapshot *po.ProductStockSnapshot) (*vo.StockReconcileItemVO, error) {
	// 获取商品信息
	product, err := s.productService.FindProductById(ctx, *snapshot.ProductId)
	if err != nil {
		return nil, fmt.Errorf("failed to get product info: %v", err)
	}

	// 获取当前库存
	currentStock, err := s.productStockService.FindProductStockByCondition(ctx, *snapshot.ProductId, *snapshot.VenueId)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to get current stock: %v", err)
	}

	currentStockValue := 0
	if err != gorm.ErrRecordNotFound && currentStock.Stock != nil {
		currentStockValue = *currentStock.Stock
	}

	// 计算增量（基于快照时间后的变化）
	inboundIncrement, consumeIncrement, refundIncrement, err := s.inventoryRepository.CalculateStockIncrements(ctx, *snapshot.VenueId, *snapshot.ProductId, *snapshot.Utime)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate increments: %v", err)
	}

	// 计算校准后库存：快照库存 + 入库增量 - 消费增量 + 退款增量
	calculatedStock := *snapshot.Stock + inboundIncrement - consumeIncrement + refundIncrement
	stockDifference := calculatedStock - currentStockValue
	needReconcile := stockDifference != 0

	return &vo.StockReconcileItemVO{
		ProductId:        *snapshot.ProductId,
		ProductName:      *product.Name,
		SnapshotStock:    *snapshot.Stock,
		SnapshotTime:     *snapshot.Utime,
		InboundIncrement: inboundIncrement,
		ConsumeIncrement: consumeIncrement,
		RefundIncrement:  refundIncrement,
		CalculatedStock:  calculatedStock,
		CurrentStock:     currentStockValue,
		StockDifference:  stockDifference,
		NeedReconcile:    needReconcile,
	}, nil
}
