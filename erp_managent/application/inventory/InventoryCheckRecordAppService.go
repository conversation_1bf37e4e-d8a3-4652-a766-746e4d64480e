package inventory

import (
	"github.com/gin-gonic/gin"
	"erp_managent/api/req"
	"erp_managent/api/vo"
)

// InventoryCheckRecordAppService 盘点记录应用服务接口
type InventoryCheckRecordAppService interface {
	// CreateCheckRecord 创建盘点记录
	CreateCheckRecord(ctx *gin.Context, reqDto req.CreateCheckRecordReqDto) (*vo.CreateCheckRecordRespVO, error)
	
	// GetCheckRecordList 获取盘点记录列表
	GetCheckRecordList(ctx *gin.Context, reqDto req.CheckRecordListReqDto) (*vo.CheckRecordListRespVO, error)
	
	// GetCheckRecordDetail 获取盘点记录详情
	GetCheckRecordDetail(ctx *gin.Context, reqDto req.CheckRecordDetailReqDto) (*vo.CheckRecordDetailRespVO, error)
	
	// UpdateCheckRecord 更新盘点记录
	UpdateCheckRecord(ctx *gin.Context, reqDto req.UpdateCheckRecordReqDto) (*vo.UpdateCheckRecordRespVO, error)
	
	// DeleteCheckRecord 删除盘点记录
	DeleteCheckRecord(ctx *gin.Context, reqDto req.DeleteCheckRecordReqDto) (*vo.DeleteCheckRecordRespVO, error)
}
