package inventory

import (
	"fmt"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/inventory/model"
	"voderpltvv/erp_managent/domain/inventory/service"
	"voderpltvv/erp_managent/service/impl"

	"github.com/gin-gonic/gin"
)

// 辅助函数：将值转换为指针
func stringPtr(s string) *string { return &s }
func intPtr(i int) *int          { return &i }
func int64Ptr(i int64) *int64    { return &i }

// InventoryCheckRecordAppService 盘点记录应用服务
type InventoryCheckRecordAppService struct {
	checkRecordDomainService service.InventoryCheckRecordDomainService
	productService           *impl.ProductService
}

// NewInventoryCheckRecordAppService 创建盘点记录应用服务实例
func NewInventoryCheckRecordAppService(
	checkRecordDomainService service.InventoryCheckRecordDomainService,
	productService *impl.ProductService,
) *InventoryCheckRecordAppService {
	return &InventoryCheckRecordAppService{
		checkRecordDomainService: checkRecordDomainService,
		productService:           productService,
	}
}

// CreateCheckRecord 创建盘点记录
func (s *InventoryCheckRecordAppService) CreateCheckRecord(ctx *gin.Context, reqDto req.CreateInventoryCheckRecordReqDto) (*vo.CreateInventoryCheckRecordRespVO, error) {
	// 1. 构建明细项
	var items []model.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		item := model.NewInventoryCheckRecordItem(*itemDto.ProductId, *itemDto.StockQuantity, *itemDto.CheckQuantity)
		items = append(items, item)
	}

	// 2. 构建盘点记录
	record := model.NewInventoryCheckRecord(*reqDto.VenueId, *reqDto.Handler, *reqDto.Operator, "", *reqDto.Remark, items)

	// 3. 创建盘点记录
	if err := s.checkRecordDomainService.CreateCheckRecord(ctx, record); err != nil {
		return nil, fmt.Errorf("创建盘点记录失败: %w", err)
	}

	// 4. 构建响应
	return &vo.CreateInventoryCheckRecordRespVO{
		RecordId:            stringPtr(record.GetId()),
		RecordNumber:        stringPtr(record.GetRecordNumber()),
		ProfitQuantityTotal: intPtr(record.GetProfitQuantityTotal()),
		LossQuantityTotal:   intPtr(record.GetLossQuantityTotal()),
	}, nil
}

// GetCheckRecordList 获取盘点记录列表
func (s *InventoryCheckRecordAppService) GetCheckRecordList(ctx *gin.Context, reqDto req.QueryInventoryCheckRecordReqDto) (*vo.QueryInventoryCheckRecordRespVO, error) {
	// 1. 获取盘点记录列表
	searchKey := ""
	if reqDto.SearchKey != nil {
		searchKey = *reqDto.SearchKey
	}

	records, total, err := s.checkRecordDomainService.GetCheckRecordsByVenue(ctx, *reqDto.VenueId, searchKey, *reqDto.PageNum, *reqDto.PageSize)
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录列表失败: %w", err)
	}

	// 2. 转换为VO
	var recordVOs []vo.InventoryCheckRecordVO
	for _, record := range records {
		totalProducts := len(record.GetItems())
		recordVO := vo.InventoryCheckRecordVO{
			RecordId:            stringPtr(record.GetId()),
			RecordNumber:        stringPtr(record.GetRecordNumber()),
			Handler:             stringPtr(record.GetHandler()),
			Operator:            stringPtr(record.GetOperator()),
			CheckTime:           int64Ptr(record.GetTime().Unix()),
			Remark:              stringPtr(record.GetRemark()),
			TotalProducts:       intPtr(totalProducts),
			ProfitQuantityTotal: intPtr(record.GetProfitQuantityTotal()),
			LossQuantityTotal:   intPtr(record.GetLossQuantityTotal()),
		}
		recordVOs = append(recordVOs, recordVO)
	}

	return &vo.QueryInventoryCheckRecordRespVO{
		Records: recordVOs,
		Total:   total,
	}, nil
}

// GetCheckRecordDetail 获取盘点记录详情
func (s *InventoryCheckRecordAppService) GetCheckRecordDetail(ctx *gin.Context, reqDto req.GetInventoryCheckRecordDetailReqDto) (*vo.GetInventoryCheckRecordDetailRespVO, error) {
	// 1. 获取盘点记录
	record, err := s.checkRecordDomainService.GetCheckRecordById(ctx, *reqDto.RecordId)
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录失败: %w", err)
	}

	if record == nil {
		return nil, fmt.Errorf("盘点记录不存在")
	}

	// 2. 构建记录详情VO
	totalProducts := len(record.GetItems())
	recordDetailVO := vo.InventoryCheckRecordDetailVO{
		RecordId:            stringPtr(record.GetId()),
		RecordNumber:        stringPtr(record.GetRecordNumber()),
		Handler:             stringPtr(record.GetHandler()),
		Operator:            stringPtr(record.GetOperator()),
		CheckTime:           int64Ptr(record.GetTime().Unix()),
		Remark:              stringPtr(record.GetRemark()),
		TotalProducts:       intPtr(totalProducts),
		ProfitQuantityTotal: intPtr(record.GetProfitQuantityTotal()),
		LossQuantityTotal:   intPtr(record.GetLossQuantityTotal()),
	}

	// 3. 构建明细项VO
	var itemVOs []vo.InventoryCheckRecordItemVO
	for _, item := range record.GetItems() {
		// 获取商品信息
		product, err := s.productService.FindProductById(ctx, item.GetProductId())
		if err != nil {
			// 如果获取商品信息失败，使用默认值
			unknownName := "未知商品"
			emptyImage := ""
			emptyUnit := ""
			itemVO := vo.InventoryCheckRecordItemVO{
				ProductId:          stringPtr(item.GetProductId()),
				ProductName:        stringPtr(unknownName),
				ProductImage:       stringPtr(emptyImage),
				Unit:               stringPtr(emptyUnit),
				StockQuantity:      intPtr(item.GetStockQuantity()),
				CheckQuantity:      intPtr(item.GetCheckQuantity()),
				ProfitLossQuantity: intPtr(item.GetProfitLossQuantity()),
			}
			itemVOs = append(itemVOs, itemVO)
			continue
		}

		itemVO := vo.InventoryCheckRecordItemVO{
			ProductId:          stringPtr(item.GetProductId()),
			ProductName:        product.Name,
			ProductImage:       product.ImageUrl,
			Unit:               product.Unit,
			StockQuantity:      intPtr(item.GetStockQuantity()),
			CheckQuantity:      intPtr(item.GetCheckQuantity()),
			ProfitLossQuantity: intPtr(item.GetProfitLossQuantity()),
		}
		itemVOs = append(itemVOs, itemVO)
	}

	return &vo.GetInventoryCheckRecordDetailRespVO{
		Record: recordDetailVO,
		Items:  itemVOs,
	}, nil
}

// UpdateCheckRecord 更新盘点记录
func (s *InventoryCheckRecordAppService) UpdateCheckRecord(ctx *gin.Context, reqDto req.UpdateInventoryCheckRecordReqDto) (*vo.UpdateInventoryCheckRecordRespVO, error) {
	// 1. 获取原有记录
	record, err := s.checkRecordDomainService.GetCheckRecordById(ctx, *reqDto.RecordId)
	if err != nil {
		return nil, fmt.Errorf("获取盘点记录失败: %w", err)
	}

	if record == nil {
		return nil, fmt.Errorf("盘点记录不存在")
	}

	// 2. 更新基本信息
	record.SetRemark(*reqDto.Remark)

	// 3. 更新明细项
	var items []model.InventoryCheckRecordItem
	for _, itemDto := range reqDto.Items {
		item := model.NewInventoryCheckRecordItem(*itemDto.ProductId, *itemDto.StockQuantity, *itemDto.CheckQuantity)
		items = append(items, item)
	}
	record.SetItems(items)

	// 4. 更新盘点记录
	if err := s.checkRecordDomainService.UpdateCheckRecord(ctx, record); err != nil {
		return nil, fmt.Errorf("更新盘点记录失败: %w", err)
	}

	// 5. 构建响应
	return &vo.UpdateInventoryCheckRecordRespVO{
		RecordId:            stringPtr(record.GetId()),
		ProfitQuantityTotal: intPtr(record.GetProfitQuantityTotal()),
		LossQuantityTotal:   intPtr(record.GetLossQuantityTotal()),
	}, nil
}

// DeleteCheckRecord 删除盘点记录
func (s *InventoryCheckRecordAppService) DeleteCheckRecord(ctx *gin.Context, reqDto req.DeleteInventoryCheckRecordReqDto) (*vo.DeleteInventoryCheckRecordRespVO, error) {
	// 1. 删除盘点记录
	if err := s.checkRecordDomainService.DeleteCheckRecord(ctx, *reqDto.RecordId); err != nil {
		return nil, fmt.Errorf("删除盘点记录失败: %w", err)
	}

	// 2. 构建响应
	success := true
	return &vo.DeleteInventoryCheckRecordRespVO{
		RecordId: reqDto.RecordId,
		Success:  &success,
	}, nil
}
