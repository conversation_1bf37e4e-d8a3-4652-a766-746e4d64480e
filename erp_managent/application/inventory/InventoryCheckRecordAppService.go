package inventory

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// InventoryCheckRecordAppService 库存盘点记录应用服务接口
type InventoryCheckRecordAppService interface {
	// 创建盘点记录
	CreateInventoryCheckRecord(ctx *gin.Context, reqDto req.CreateInventoryCheckRecordReqDto) (*vo.CreateInventoryCheckRecordRespVO, error)

	// 查询盘点记录列表（分页）
	QueryInventoryCheckRecords(ctx *gin.Context, reqDto req.QueryInventoryCheckRecordReqDto) (*vo.PageVO[[]vo.InventoryCheckRecordVO], error)

	// 根据ID查询盘点记录详情
	GetInventoryCheckRecordDetail(ctx *gin.Context, reqDto req.GetInventoryCheckRecordDetailReqDto) (*vo.GetInventoryCheckRecordDetailRespVO, error)

	// 更新盘点记录
	UpdateInventoryCheckRecord(ctx *gin.Context, reqDto req.UpdateInventoryCheckRecordReqDto) (*vo.UpdateInventoryCheckRecordRespVO, error)

	// 删除盘点记录（软删除）
	DeleteInventoryCheckRecord(ctx *gin.Context, reqDto req.DeleteInventoryCheckRecordReqDto) error
}
