package print

import (
	"fmt"
	"strings"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/print/model/winestorage"
	"voderpltvv/erp_managent/domain/print/model/winewithdraw"
	"voderpltvv/erp_managent/domain/print/repository"
	"voderpltvv/erp_managent/domain/print/service"
	"voderpltvv/erp_managent/infra/repository/print"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/transfer"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// WinePrintRecordAppService 存酒和取酒打印记录应用服务
type WinePrintRecordAppService struct {
	wineStoragePrintRecordRepo        repository.WineStoragePrintRecordRepository
	wineStorageDataAssemblyService    service.WineStorageDataAssemblyService
	wineWithdrawPrintRecordRepo       repository.WineWithdrawPrintRecordRepository
	wineWithdrawalDataAssemblyService service.WineWithdrawalDataAssemblyService
}

// NewWinePrintRecordAppService 创建存酒和取酒打印记录应用服务
func NewWinePrintRecordAppService() *WinePrintRecordAppService {
	return &WinePrintRecordAppService{
		wineStoragePrintRecordRepo:        print.NewWineStoragePrintRecordRepositoryImpl(),
		wineStorageDataAssemblyService:    service.NewWineStorageDataAssemblyService(),
		wineWithdrawPrintRecordRepo:       print.NewWineWithdrawPrintRecordRepositoryImpl(),
		wineWithdrawalDataAssemblyService: service.NewWineWithdrawalDataAssemblyService(),
	}
}

// ========================== 存酒打印记录服务 ==========================

// CreateWineStoragePrintRecord 创建存酒打印记录
func (s *WinePrintRecordAppService) CreateWineStoragePrintRecord(ctx *gin.Context, reqDto *req.CreateWineStoragePrintRecordReqDto) (vo.WineStoragePrintRecordVO, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(reqDto.VenueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}
	if err := lo.Ternary(reqDto.StorageRecordId == "", fmt.Errorf("存酒记录ID不能为空"), nil); err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 使用领域服务组装存酒单数据
	wineStorageBillData, operatorId, operatorName, err := s.wineStorageDataAssemblyService.AssembleWineStorageData(
		ctx,
		reqDto.VenueId,
		reqDto.StorageRecordId,
	)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 创建领域模型 - 使用真实的操作员信息
	record, err := winestorage.NewWineStoragePrintRecord(
		reqDto.VenueId,
		"", // 存酒打印与sessionId无关，ProductStorage系统没有sessionId概念
		reqDto.StorageRecordId,
		operatorId,   // 使用真实的操作员ID
		operatorName, // 使用真实的操作员姓名
	)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 设置内容
	record.Content = wineStorageBillData

	// 保存记录
	err = s.wineStoragePrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 转换为VO返回
	return transfer.ConvertToWineStorageVO(record), nil
}

// ========================== 取酒打印记录服务 ==========================

// CreateWineWithdrawPrintRecord 创建取酒打印记录
func (s *WinePrintRecordAppService) CreateWineWithdrawPrintRecord(ctx *gin.Context, reqDto *req.CreateWineWithdrawPrintRecordReqDto) (vo.WineWithdrawPrintRecordVO, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(reqDto.VenueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}
	if err := lo.Ternary(reqDto.WithdrawalRecordId == "", fmt.Errorf("取酒记录ID不能为空"), nil); err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 使用领域服务组装取酒单数据
	wineWithdrawalBillData, operatorId, operatorName, err := s.wineWithdrawalDataAssemblyService.AssembleWineWithdrawalData(
		ctx,
		reqDto.VenueId,
		reqDto.WithdrawalRecordId,
	)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 创建领域模型 - 使用真实的操作员信息
	record, err := winewithdraw.NewWineWithdrawPrintRecord(
		reqDto.VenueId,
		"", // 取酒打印与sessionId无关
		reqDto.WithdrawalRecordId,
		operatorId,   // 使用真实的操作员ID
		operatorName, // 使用真实的操作员姓名
	)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 设置内容
	record.Content = wineWithdrawalBillData

	// 保存记录
	err = s.wineWithdrawPrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 转换为VO返回
	return transfer.ConvertToWineWithdrawVO(record), nil
}

// QueryWinePrintRecordByBusinessID 根据业务单号查询存取酒打印记录
func (s *WinePrintRecordAppService) QueryWinePrintRecordByBusinessID(ctx *gin.Context, venueId, printBusinessID string) (interface{}, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(venueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return nil, err
	}
	if err := lo.Ternary(printBusinessID == "", fmt.Errorf("printBusinessID不能为空"), nil); err != nil {
		return nil, err
	}

	// 定义前缀到查询函数的映射 - 使用lo.Map优化前缀判断
	prefixHandlers := []struct {
		prefix  string
		handler func() interface{}
	}{
		{
			prefix: "PS",
			handler: func() interface{} {
				// 存酒记录 (PS前缀)
				if record, err := s.wineStoragePrintRecordRepo.FindUniqueByStorageRecordID(ctx, printBusinessID); err == nil && record != nil {
					return transfer.ConvertToWineStorageVO(record)
				}
				return nil
			},
		},
		{
			prefix: "PW",
			handler: func() interface{} {
				// 取酒记录 (PW前缀 - 产品取酒单)
				if record, err := s.wineWithdrawPrintRecordRepo.FindUniqueByWithdrawalRecordID(ctx, printBusinessID); err == nil && record != nil {
					return transfer.ConvertToWineWithdrawVO(record)
				}
				return nil
			},
		},
		{
			prefix: "PD",
			handler: func() interface{} {
				// 取酒记录 (PD前缀 - 直接取酒单)
				if record, err := s.wineWithdrawPrintRecordRepo.FindUniqueByWithdrawalRecordID(ctx, printBusinessID); err == nil && record != nil {
					return transfer.ConvertToWineWithdrawVO(record)
				}
				return nil
			},
		},
		{
			prefix: "BATCH",
			handler: func() interface{} {
				// 批量取酒记录 (BATCH前缀) - 适配现有业务架构
				if record, err := s.wineWithdrawPrintRecordRepo.FindUniqueByWithdrawalRecordID(ctx, printBusinessID); err == nil && record != nil {
					return transfer.ConvertToWineWithdrawVO(record)
				}
				return nil
			},
		},
		{
			prefix: "RNPS",
			handler: func() interface{} {
				// 续存记录 (RNPS前缀)
				if record, err := s.findUniqueRenewalRecord(ctx, venueId, printBusinessID); err == nil && record != nil {
					return record
				}
				return nil
			},
		},
	}

	// 使用lo.Find查找匹配的前缀处理器
	if matchedHandler, found := lo.Find(prefixHandlers, func(h struct {
		prefix  string
		handler func() interface{}
	}) bool {
		return strings.HasPrefix(printBusinessID, h.prefix)
	}); found {
		return matchedHandler.handler(), nil
	}

	// 如果都没找到，返回空结果而不是错误
	return nil, nil
}

// findUniqueRenewalRecord 根据业务ID查询唯一的续存打印记录
func (s *WinePrintRecordAppService) findUniqueRenewalRecord(ctx *gin.Context, venueId, printBusinessID string) (*vo.WineRenewalPrintRecordVO, error) {
	printRecordService := &impl.PrintRecordService{}

	// 直接使用businessId进行单一查询，printBusinessID具有唯一性
	record, err := printRecordService.FindUniqueByBusinessIdAsync(venueId, printBusinessID)
	if err != nil {
		// 如果是记录不存在的错误，返回nil而不是错误
		if strings.Contains(err.Error(), "record not found") {
			return nil, nil
		}
		return nil, err
	}

	// 使用转换函数组装详细数据
	result := transfer.ConvertToWineRenewalVO(record)
	return &result, nil
}
