package print

import (
	"fmt"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/print/model/checkout"
	"voderpltvv/erp_managent/domain/print/model/opentable"
	"voderpltvv/erp_managent/domain/print/model/roomextension"
	"voderpltvv/erp_managent/domain/print/repository"
	"voderpltvv/erp_managent/domain/print/service"
	"voderpltvv/erp_managent/infra/repository/print"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// PrintRecordAppService 打印记录应用服务
type PrintRecordAppService struct {
	openTablePrintRecordRepo       repository.OpenTablePrintRecordRepository
	openTableDataAssemblyService   service.OpenTableDataAssemblyService
	roomExtensionPrintRecordRepo   repository.RoomExtensionPrintRecordRepository
	checkoutPrintRecordRepo        repository.CheckoutPrintRecordRepository
	checkoutDataAssemblyService    service.CheckoutDataAssemblyService
	cancelOpenTablePrintRecordRepo repository.CancelOpenTablePrintRecordRepository
	cancelOpenTableTransfer        *transfer.CancelOpenTablePrintRecordTransfer
	printRecordService             *impl.PrintRecordService
}

// NewPrintRecordAppService 创建打印记录应用服务
func NewPrintRecordAppService() *PrintRecordAppService {
	return &PrintRecordAppService{
		openTablePrintRecordRepo:       print.NewOpenTablePrintRecordRepositoryImpl(),
		openTableDataAssemblyService:   service.NewOpenTableDataAssemblyService(),
		roomExtensionPrintRecordRepo:   print.NewRoomExtensionPrintRecordRepositoryImpl(),
		checkoutPrintRecordRepo:        print.NewCheckoutPrintRecordRepositoryImpl(),
		checkoutDataAssemblyService:    service.NewCheckoutDataAssemblyService(),
		cancelOpenTablePrintRecordRepo: print.NewCancelOpenTablePrintRecordRepositoryImpl(),
		cancelOpenTableTransfer:        &transfer.CancelOpenTablePrintRecordTransfer{},
		printRecordService:             &impl.PrintRecordService{},
	}
}

// ========================== 开台单打印记录服务 ==========================

// CreateOpenTablePrintRecord 创建开台单打印记录
func (s *PrintRecordAppService) CreateOpenTablePrintRecord(ctx *gin.Context, reqDto *req.CreateOpenTablePrintRecordReqDto) (vo.OpenTablePrintRecordVO, error) {
	// 参数验证
	if reqDto.VenueId == "" {
		return vo.OpenTablePrintRecordVO{}, fmt.Errorf("门店ID不能为空")
	}
	if reqDto.SessionId == "" {
		return vo.OpenTablePrintRecordVO{}, fmt.Errorf("会话ID不能为空")
	}

	// 使用领域服务组装开台单数据
	sessionOrderData, operatorId, operatorName, err := s.openTableDataAssemblyService.AssembleOpenTableData(
		ctx,
		reqDto.VenueId,
		reqDto.SessionId,
		reqDto.OrderNos,
	)
	if err != nil {
		return vo.OpenTablePrintRecordVO{}, err
	}

	// 创建领域模型
	record, err := opentable.NewOpenTablePrintRecord(
		reqDto.VenueId,
		reqDto.SessionId,
		operatorId,
		operatorName,
	)
	if err != nil {
		return vo.OpenTablePrintRecordVO{}, err
	}

	// 设置内容
	record.Content = sessionOrderData

	// 保存记录
	err = s.openTablePrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.OpenTablePrintRecordVO{}, err
	}

	// 转换为VO并返回
	return transfer.ConvertToOpenTableVO(record), nil
}

// GetOpenTablePrintRecordsBySessionId 根据会话ID获取开台单打印记录
func (s *PrintRecordAppService) GetOpenTablePrintRecordsBySessionId(ctx *gin.Context, reqDto *req.GetOpenTablePrintRecordsBySessionIdReqDto) ([]vo.OpenTablePrintRecordVO, error) {
	// 参数验证
	if reqDto.SessionId == "" {
		return nil, fmt.Errorf("会话ID不能为空")
	}
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	// 查询记录
	records, err := s.openTablePrintRecordRepo.FindBySessionID(ctx, reqDto.SessionId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	vos := make([]vo.OpenTablePrintRecordVO, 0, len(records))
	for _, record := range records {
		vos = append(vos, transfer.ConvertToOpenTableVO(record))
	}

	return vos, nil
}

// GetCancelOpenTablePrintRecordsBySessionId 根据会话ID获取取消开台单打印记录
func (s *PrintRecordAppService) GetCancelOpenTablePrintRecordsBySessionId(ctx *gin.Context, reqDto *req.GetCancelOpenTablePrintRecordsBySessionIdReqDto) ([]vo.CancelOpenTablePrintRecordVO, error) {
	// 参数验证
	if reqDto.SessionId == "" {
		return nil, fmt.Errorf("会话ID不能为空")
	}
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	// 查询记录
	records, err := s.cancelOpenTablePrintRecordRepo.FindBySessionID(ctx, reqDto.SessionId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	vos := make([]vo.CancelOpenTablePrintRecordVO, 0, len(records))
	for _, record := range records {
		vo := s.cancelOpenTableTransfer.ConvertToVO(record)
		if vo != nil {
			vos = append(vos, *vo)
		}
	}

	return vos, nil
}

// ========================== 续房单打印记录服务 ==========================

// CreateRoomExtensionPrintRecord 创建续房单打印记录
func (s *PrintRecordAppService) CreateRoomExtensionPrintRecord(ctx *gin.Context, reqDto *req.CreateRoomExtensionPrintRecordReqDto) (vo.RoomExtensionPrintRecordVO, error) {
	// 参数验证
	if reqDto.VenueId == "" {
		return vo.RoomExtensionPrintRecordVO{}, fmt.Errorf("门店ID不能为空")
	}
	if reqDto.SessionId == "" {
		return vo.RoomExtensionPrintRecordVO{}, fmt.Errorf("会话ID不能为空")
	}

	// 使用领域服务组装续房单数据（复用开台单的数据组装逻辑）
	sessionOrderData, operatorId, operatorName, err := s.openTableDataAssemblyService.AssembleOpenTableData(
		ctx,
		reqDto.VenueId,
		reqDto.SessionId,
		reqDto.OrderNos,
	)
	if err != nil {
		return vo.RoomExtensionPrintRecordVO{}, err
	}

	// 创建领域模型
	record, err := roomextension.NewRoomExtensionPrintRecord(
		reqDto.VenueId,
		reqDto.SessionId,
		operatorId,
		operatorName,
	)
	if err != nil {
		return vo.RoomExtensionPrintRecordVO{}, err
	}

	// 设置内容
	record.Content = sessionOrderData

	// 保存记录
	err = s.roomExtensionPrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.RoomExtensionPrintRecordVO{}, err
	}

	// 转换为VO并返回
	return transfer.ConvertToRoomExtensionVO(record), nil
}

// GetRoomExtensionPrintRecordsBySessionId 根据会话ID获取续房单打印记录
func (s *PrintRecordAppService) GetRoomExtensionPrintRecordsBySessionId(ctx *gin.Context, reqDto *req.GetRoomExtensionPrintRecordsBySessionIdReqDto) ([]vo.RoomExtensionPrintRecordVO, error) {
	// 参数验证
	if reqDto.SessionId == "" {
		return nil, fmt.Errorf("会话ID不能为空")
	}
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	// 查询记录
	records, err := s.roomExtensionPrintRecordRepo.FindBySessionID(ctx, reqDto.SessionId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	vos := make([]vo.RoomExtensionPrintRecordVO, 0, len(records))
	for _, record := range records {
		vos = append(vos, transfer.ConvertToRoomExtensionVO(record))
	}

	return vos, nil
}

// ========================== 结账单打印记录服务 ==========================

// CreateCheckoutPrintRecord 创建结账单打印记录
func (s *PrintRecordAppService) CreateCheckoutPrintRecord(ctx *gin.Context, reqDto *req.CreateCheckoutPrintRecordReqDto) (vo.CheckoutPrintRecordVO, error) {
	// 参数验证
	if reqDto.VenueId == "" {
		return vo.CheckoutPrintRecordVO{}, fmt.Errorf("门店ID不能为空")
	}
	if reqDto.SessionId == "" {
		return vo.CheckoutPrintRecordVO{}, fmt.Errorf("会话ID不能为空")
	}
	if reqDto.PayBillId == "" {
		return vo.CheckoutPrintRecordVO{}, fmt.Errorf("结账单ID不能为空")
	}

	// 使用领域服务组装结账单数据
	checkoutBillData, operatorId, operatorName, err := s.checkoutDataAssemblyService.AssembleCheckoutBillData(
		ctx,
		reqDto.VenueId,
		reqDto.SessionId,
		reqDto.PayBillId,
		reqDto.OrderNos,
	)
	if err != nil {
		return vo.CheckoutPrintRecordVO{}, err
	}

	// 创建领域模型
	record, err := checkout.NewCheckoutPrintRecord(
		reqDto.VenueId,
		reqDto.SessionId,
		reqDto.PayBillId,
		operatorId,
		operatorName,
	)
	if err != nil {
		return vo.CheckoutPrintRecordVO{}, err
	}

	// 设置内容
	record.Content = checkoutBillData

	// 保存记录
	err = s.checkoutPrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.CheckoutPrintRecordVO{}, err
	}

	// 转换为VO并返回
	return transfer.ConvertToCheckoutVO(record), nil
}

// GetCheckoutPrintRecordsBySessionId 根据会话ID获取结账单打印记录
func (s *PrintRecordAppService) GetCheckoutPrintRecordsBySessionId(ctx *gin.Context, reqDto *req.GetCheckoutPrintRecordsBySessionIdReqDto) ([]vo.CheckoutPrintRecordVO, error) {
	// 参数验证
	if reqDto.SessionId == "" {
		return nil, fmt.Errorf("会话ID不能为空")
	}
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	// 查询记录
	records, err := s.checkoutPrintRecordRepo.FindBySessionID(ctx, reqDto.SessionId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	vos := make([]vo.CheckoutPrintRecordVO, 0, len(records))
	for _, record := range records {
		vos = append(vos, transfer.ConvertToCheckoutVO(record))
	}

	return vos, nil
}

// GetCheckoutPrintRecordsByPayBillIds 根据账单号数组获取结账单打印记录
func (s *PrintRecordAppService) GetCheckoutPrintRecordsByPayBillIds(ctx *gin.Context, reqDto *req.GetCheckoutPrintRecordsByPayBillIdsReqDto) ([]vo.CheckoutPrintRecordVO, error) {
	// 参数验证
	if len(reqDto.PayBillIds) == 0 {
		return nil, fmt.Errorf("账单号数组不能为空")
	}
	if reqDto.VenueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}

	// 查询记录
	var allRecords []*checkout.CheckoutPrintRecord
	for _, payBillId := range reqDto.PayBillIds {
		if payBillId == "" {
			continue // 跳过空的账单号
		}
		records, err := s.checkoutPrintRecordRepo.FindByPayBillID(ctx, payBillId)
		if err != nil {
			return nil, fmt.Errorf("查询账单号[%s]的打印记录失败: %w", payBillId, err)
		}
		allRecords = append(allRecords, records...)
	}

	// 转换为VO
	vos := make([]vo.CheckoutPrintRecordVO, 0, len(allRecords))
	for _, record := range allRecords {
		vos = append(vos, transfer.ConvertToCheckoutVO(record))
	}

	return vos, nil
}

// ========================== 通用打印记录服务 ==========================

// UpdatePrintRecordStatus 更新打印记录状态
func (s *PrintRecordAppService) UpdatePrintRecordStatus(ctx *gin.Context, reqDto *req.UpdatePrintRecordStatusReqDto) error {
	// 参数验证
	if reqDto.VenueId == "" {
		return fmt.Errorf("门店ID不能为空")
	}
	if reqDto.PrintId == "" {
		return fmt.Errorf("打印记录ID不能为空")
	}
	if reqDto.Status < 0 || reqDto.Status > 2 {
		return fmt.Errorf("打印状态值无效，必须为0(待打印)、1(打印成功)或2(打印失败)")
	}
	if reqDto.Status == 2 && reqDto.ErrorMsg == "" {
		return fmt.Errorf("打印失败时错误信息不能为空")
	}

	// 先查询现有记录
	existingRecord, err := s.printRecordService.FindPrintRecordById(ctx, reqDto.PrintId)
	if err != nil {
		return fmt.Errorf("查询打印记录失败: %w", err)
	}
	if existingRecord == nil {
		return fmt.Errorf("打印记录不存在")
	}

	// 验证门店权限
	if existingRecord.VenueId == nil || *existingRecord.VenueId != reqDto.VenueId {
		return fmt.Errorf("无权限操作该打印记录")
	}

	// 更新记录
	updateRecord := &po.PrintRecord{
		Id:        util.GetItPtr(reqDto.PrintId),
		Status:    util.GetItPtr(reqDto.Status),
		ErrorMsg:  util.GetItPtr(reqDto.ErrorMsg),
		Remark:    util.GetItPtr(reqDto.Remark),
		PrintTime: reqDto.PrintTime,
	}

	err = s.printRecordService.UpdatePrintRecordPartial(ctx, updateRecord)
	if err != nil {
		return fmt.Errorf("更新打印记录状态失败: %w", err)
	}

	return nil
}

// ========================== 通用打印记录查询服务 ==========================

// GetPrintRecordsByBusinessId 根据业务ID查询打印记录
func (s *PrintRecordAppService) GetPrintRecordsByBusinessId(ctx *gin.Context, venueId, printType, businessId string) ([]*po.PrintRecord, error) {
	// 参数验证
	if venueId == "" {
		return nil, fmt.Errorf("门店ID不能为空")
	}
	if printType == "" {
		return nil, fmt.Errorf("打印类型不能为空")
	}
	if businessId == "" {
		return nil, fmt.Errorf("业务ID不能为空")
	}

	// 查询打印记录
	return s.printRecordService.GetPrintRecordsByBusinessId(ctx, venueId, printType, businessId)
}

// GetPrintRecordsByMemberId 根据会员ID查询打印记录
func (s *PrintRecordAppService) GetPrintRecordsByMemberId(ctx *gin.Context, venueId, printType, memberId string) ([]*po.PrintRecord, error) {
	return s.printRecordService.GetPrintRecordsByMemberId(ctx, venueId, printType, memberId)
}
