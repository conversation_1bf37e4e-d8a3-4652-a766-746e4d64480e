package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

// PrintRecordRouter 打印记录路由
type PrintRecordRouter struct{}

// InitPrintRecordRouter 初始化打印记录路由
func (r *PrintRecordRouter) InitPrintRecordRouter(g *gin.Engine) {
	printRecordController := controller.NewPrintRecordController()

	// 路由组
	routeGroup := g.Group("/api/print-record")
	{
		// 开台单打印记录接口
		openTableGroup := routeGroup.Group("/open-table")
		{
			// 创建开台单打印记录
			openTableGroup.POST("/create", printRecordController.CreateOpenTablePrintRecord)
			// 根据会话ID获取开台单打印记录
			openTableGroup.POST("/session", printRecordController.GetOpenTablePrintRecordsBySessionId)
		}

		// 取消开台单打印记录接口
		cancelOpenTableGroup := routeGroup.Group("/cancel-open-table")
		{
			// 根据会话ID获取取消开台单打印记录
			cancelOpenTableGroup.POST("/session", printRecordController.GetCancelOpenTablePrintRecordsBySessionId)
		}

		// 续房单打印记录接口
		roomExtensionGroup := routeGroup.Group("/room-extension")
		{
			// 创建续房单打印记录
			roomExtensionGroup.POST("/create", printRecordController.CreateRoomExtensionPrintRecord)
			// 根据会话ID获取续房单打印记录
			roomExtensionGroup.POST("/session", printRecordController.GetRoomExtensionPrintRecordsBySessionId)
		}

		// 出品单打印记录接口
		productOutGroup := routeGroup.Group("/product-out")
		{
			// 创建出品单打印任务（返回PrintTask格式数据）
			productOutGroup.POST("/create", printRecordController.CreateProductOutPrintTasks)
			// 根据会话ID获取出品单打印记录
			productOutGroup.POST("/session", printRecordController.GetProductOutPrintRecordsBySessionId)
		}

		// 结账单打印记录接口
		checkoutGroup := routeGroup.Group("/checkout")
		{
			// 创建结账单打印记录
			checkoutGroup.POST("/create", printRecordController.CreateCheckoutPrintRecord)
			// 根据会话ID获取结账单打印记录
			checkoutGroup.POST("/session", printRecordController.GetCheckoutPrintRecordsBySessionId)
			// 根据账单号数组获取结账单打印记录
			checkoutGroup.POST("/pay-bill-ids", printRecordController.GetCheckoutPrintRecordsByPayBillIds)
		}

		// 交班单打印记录接口
		shiftChangeGroup := routeGroup.Group("/shift-change")
		{
			// 创建交班单打印记录
			shiftChangeGroup.POST("/create", printRecordController.CreateShiftChangePrintRecord)
			// 根据交班单号获取交班单打印记录
			shiftChangeGroup.POST("/hand-no", printRecordController.GetShiftChangePrintRecordsByHandNo)
		}

		// 充值打印记录接口
		rechargeGroup := routeGroup.Group("/recharge")
		{
			// 根据账单号获取充值打印记录
			rechargeGroup.POST("/bill-no", printRecordController.GetRechargePrintRecordsByBillNo)
			// 根据会员卡ID获取充值打印记录
			rechargeGroup.POST("/member-card-id", printRecordController.GetRechargePrintRecordsByMemberCardId)
		}

		// 开卡打印记录接口
		openCardGroup := routeGroup.Group("/open-card")
		{
			// 根据账单号获取开卡打印记录
			openCardGroup.POST("/bill-no", printRecordController.GetOpenCardPrintRecordsByBillNo)
			// 根据会员卡ID获取开卡打印记录
			openCardGroup.POST("/member-card-id", printRecordController.GetOpenCardPrintRecordsByMemberCardId)
		}

		// 通用打印记录接口
		routeGroup.POST("/update-status", printRecordController.UpdatePrintRecordStatus)

		// 存取酒通用查询（根据printBusinessID）- 支持PS、PW、RN前缀
		wineGroup := routeGroup.Group("/wine")
		{
			wineGroup.POST("/query", printRecordController.QueryWinePrintRecordByBusinessID)
		}
	}
}
