package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

// InventoryRoute 库存管理路由
type InventoryRoute struct {
}

// InitInventoryRouter 初始化库存管理路由
func (s *InventoryRoute) InitInventoryRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	inboundRecordController := controller.NewInboundRecordController()
	stockQueryController := controller.NewStockQueryController()
	stockReconcileController := controller.NewStockReconcileController()
	inventoryCheckRecordController := controller.InventoryCheckRecordController{}

	route := g.Group("")
	{
		// 入库管理路由
		route.POST("/api/inventory/inbound/create", inboundRecordController.CreateInboundRecord)      // 创建入库单
		route.POST("/api/inventory/inbound/detail", inboundRecordController.GetInboundRecordById)     // 查询入库单详情
		route.POST("/api/inventory/inbound/list", inboundRecordController.QueryCurrentInboundRecords) // 分页查询入库单列表（不包括冲销记录）
		route.POST("/api/inventory/inbound/list-all", inboundRecordController.QueryInboundRecords)    // 分页查询所有入库单列表（包括冲销记录）
		route.POST("/api/inventory/inbound/modify", inboundRecordController.ModifyInboundRecord)      // 修改入库单（冲销重开）
		route.POST("/api/inventory/inbound/delete", inboundRecordController.DeleteInboundRecord)      // 删除入库单

		// 库存查询路由
		route.POST("/api/inventory/stock/detail", stockQueryController.GetProductStock)          // 查询单个商品库存
		route.POST("/api/inventory/stock/batch", stockQueryController.BatchGetProductStock)      // 批量查询商品库存
		route.POST("/api/inventory/stock/list", stockQueryController.GetVenueAllStock)           // 分页查询门店所有商品库存
		route.POST("/api/inventory/stock/reconcile", stockQueryController.ReconcileStock)        // 校准单个商品库存
		route.POST("/api/inventory/stock/reconcile-all", stockQueryController.ReconcileAllStock) // 校准门店所有商品库存
		route.POST("/api/inventory/stock/history", stockQueryController.GetStockHistory)         // 分页获取库存变动历史

		// 库存校准路由（新增）
		route.POST("/api/inventory/reconcile/preview", stockReconcileController.PreviewReconcile) // 预览库存校准
		route.POST("/api/inventory/reconcile/execute", stockReconcileController.ExecuteReconcile) // 执行库存校准
		route.POST("/api/inventory/import/preview", stockReconcileController.PreviewImportStock)  // 预览导入库存
		route.POST("/api/inventory/import/execute", stockReconcileController.ExecuteImportStock)  // 执行导入库存

		// 盘点管理路由
		route.POST("/api/inventory/check/record/create", inventoryCheckRecordController.CreateInventoryCheckRecord)    // 创建盘点记录
		route.POST("/api/inventory/check/record/list", inventoryCheckRecordController.ListInventoryCheckRecords)       // 分页查询盘点记录列表
		route.POST("/api/inventory/check/record/detail", inventoryCheckRecordController.GetInventoryCheckRecordDetail) // 查询盘点记录详情
		route.POST("/api/inventory/check/record/update", inventoryCheckRecordController.UpdateInventoryCheckRecord)    // 更新盘点记录
		route.POST("/api/inventory/check/record/delete", inventoryCheckRecordController.DeleteInventoryCheckRecord)    // 删除盘点记录
	}
}
